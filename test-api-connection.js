#!/usr/bin/env node

/**
 * Test script to verify API connections
 * This script tests the connection between the UI and the unified worker
 */

const { apiClient, checkApiHealth } = require('./src/lib/api/config.ts');

async function testApiConnection() {
  console.log('🧪 Testing API Connection...\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing Health Check...');
    try {
      const isHealthy = await checkApiHealth();
      console.log(`   ✅ Health Check: ${isHealthy ? 'PASSED' : 'FAILED'}`);
    } catch (error) {
      console.log(`   ❌ Health Check: FAILED - ${error.message}`);
    }

    // Test 2: Trending Models
    console.log('\n2. Testing Trending Models...');
    try {
      const trending = await apiClient.getTrendingModels('printables', 3);
      console.log(`   ✅ Trending Models: PASSED - Got ${trending.data?.models?.length || 0} models`);
    } catch (error) {
      console.log(`   ❌ Trending Models: FAILED - ${error.message}`);
    }

    // Test 3: Popular Models
    console.log('\n3. Testing Popular Models...');
    try {
      const popular = await apiClient.getPopularModels();
      console.log(`   ✅ Popular Models: PASSED - Got ${popular.data?.models?.length || 0} models`);
    } catch (error) {
      console.log(`   ❌ Popular Models: FAILED - ${error.message}`);
    }

    // Test 4: Recent Models
    console.log('\n4. Testing Recent Models...');
    try {
      const recent = await apiClient.getRecentModels();
      console.log(`   ✅ Recent Models: PASSED - Got ${recent.data?.models?.length || 0} models`);
    } catch (error) {
      console.log(`   ❌ Recent Models: FAILED - ${error.message}`);
    }

    // Test 5: Stats
    console.log('\n5. Testing Stats...');
    try {
      const stats = await apiClient.getStats();
      console.log(`   ✅ Stats: PASSED - Total models: ${stats.data?.totalModels || 0}`);
    } catch (error) {
      console.log(`   ❌ Stats: FAILED - ${error.message}`);
    }

    console.log('\n🎉 API Connection Test Completed!');

  } catch (error) {
    console.error('\n❌ API Connection Test Failed:', error.message);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testApiConnection();
}

module.exports = { testApiConnection };
