/**
 * Приклад використання Bright Data MCP з токеном аутентифікації
 */

import { EnhancedBrightDataMCPClient } from '../src/lib/bright-data/enhanced-mcp-client';
import { AutomatedScraper } from '../src/lib/bright-data/automated-scraper';
import { ScrapingScheduler } from '../src/lib/bright-data/scraping-scheduler';
import { AIModelAnalyzer } from '../src/lib/bright-data/ai-model-analyzer';

async function demonstrateBrightDataMCP() {
  console.log('🚀 Демонстрація Bright Data MCP інтеграції');
  console.log('=' .repeat(50));

  // 1. Ініціалізація Enhanced MCP Client з токеном
  console.log('\n1️⃣ Ініціалізація MCP Client...');
  const mcpClient = new EnhancedBrightDataMCPClient({
    // Токен буде автоматично завантажений з .env.local
    enableFallback: true
  });

  // 2. Тестування базового скрапінгу
  console.log('\n2️⃣ Тестування скрапінгу сторінки...');
  try {
    const result = await mcpClient.scrapePage(
      'https://www.printables.com/model/1286204-dummy-13-terminator-t800-armor',
      {
        format: 'markdown',
        useStructuredData: true,
        extractImages: true
      }
    );

    console.log('✅ Результат скрапінгу:', {
      success: result.success,
      platform: result.platform,
      processingTime: result.processingTime,
      dataSize: result.dataSize
    });

    if (result.data?.authenticated) {
      console.log('🔐 Використано аутентифікований MCP виклик');
    }
  } catch (error) {
    console.error('❌ Помилка скрапінгу:', error);
  }

  // 3. Демонстрація автоматизованого скрапера
  console.log('\n3️⃣ Запуск автоматизованого скрапера...');
  const scraper = new AutomatedScraper();
  
  try {
    const jobId = await scraper.scrapePopularModels(['printables.com']);
    console.log('✅ Скрапінг запущено:', jobId);

    // Перевіряємо статус через 2 секунди
    setTimeout(() => {
      const jobStatus = scraper.getJobStatus(jobId);
      console.log('📊 Статус завдання:', {
        id: jobStatus?.id,
        status: jobStatus?.status,
        progress: jobStatus?.progress,
        resultsCount: jobStatus?.results.length
      });
    }, 2000);

  } catch (error) {
    console.error('❌ Помилка автоматизованого скрапера:', error);
  }

  // 4. Демонстрація планувальника
  console.log('\n4️⃣ Налаштування планувальника...');
  const scheduler = new ScrapingScheduler();
  
  try {
    // Створюємо тестове завдання
    const taskId = scheduler.createTask({
      name: 'Demo Scraping Task',
      description: 'Демонстраційне завдання скрапінгу',
      cronExpression: '0 */6 * * *', // Кожні 6 годин
      platforms: ['printables.com'],
      config: {
        maxModelsPerPlatform: 50,
        qualityThreshold: 0.8,
        enableAIAnalysis: true,
        notifyOnCompletion: false,
        retryFailedJobs: true,
        cleanupOldData: false,
        dataRetentionDays: 7
      }
    });

    console.log('✅ Завдання створено:', taskId);

    // Отримуємо статистику планувальника
    const stats = scheduler.getStats();
    console.log('📈 Статистика планувальника:', {
      totalTasks: stats.totalTasks,
      activeTasks: stats.activeTasks,
      upcomingTasks: stats.upcomingTasks.length
    });

  } catch (error) {
    console.error('❌ Помилка планувальника:', error);
  }

  // 5. Демонстрація AI аналізатора
  console.log('\n5️⃣ AI аналіз моделі...');
  const analyzer = new AIModelAnalyzer();
  
  try {
    const mockModelData = {
      title: 'Demo 3D Model',
      description: 'Демонстраційна 3D модель для тестування AI аналізу',
      designer: 'Demo Designer',
      platform: 'printables.com',
      url: 'https://printables.com/model/demo',
      downloadCount: 5000,
      likeCount: 500,
      viewCount: 25000,
      rating: 4.5,
      tags: ['demo', 'test', 'popular'],
      category: 'Tools',
      license: 'Creative Commons',
      files: [
        { name: 'model.stl', url: 'demo.stl', size: '1MB', format: 'STL' }
      ],
      qualityScore: 0.85,
      scrapedAt: new Date().toISOString()
    };

    const analysis = await analyzer.analyzeModel(mockModelData);
    
    console.log('🤖 Результат AI аналізу:', {
      popularityScore: analysis.popularityScore.toFixed(2),
      qualityScore: analysis.qualityScore.toFixed(2),
      trendingScore: analysis.trendingScore.toFixed(2),
      marketPotential: analysis.marketPotential.toFixed(2),
      recommendationScore: analysis.recommendationScore.toFixed(2),
      insightsCount: analysis.insights.length,
      predictionsCount: analysis.predictions.length
    });

    // Показуємо найважливіші інсайти
    const topInsights = analysis.insights
      .filter(insight => insight.impact === 'high')
      .slice(0, 3);

    if (topInsights.length > 0) {
      console.log('💡 Ключові інсайти:');
      topInsights.forEach((insight, index) => {
        console.log(`   ${index + 1}. ${insight.title} (${insight.confidence.toFixed(2)} впевненості)`);
      });
    }

  } catch (error) {
    console.error('❌ Помилка AI аналізу:', error);
  }

  // 6. Статистика MCP Client
  console.log('\n6️⃣ Статистика MCP Client...');
  try {
    const stats = mcpClient.getOverallStats();
    console.log('📊 Загальна статистика:', {
      totalRequests: stats.totalRequests,
      totalPlatforms: stats.totalPlatforms,
      averageSuccessRate: (stats.averageSuccessRate * 100).toFixed(1) + '%',
      averageResponseTime: Math.round(stats.averageResponseTime) + 'ms'
    });

    const platformStats = mcpClient.getStats();
    console.log('🌐 Статистика платформ:');
    for (const [platform, stats] of platformStats) {
      console.log(`   ${platform}: ${stats.totalRequests} запитів, ${(stats.errorRate * 100).toFixed(1)}% помилок`);
    }

  } catch (error) {
    console.error('❌ Помилка отримання статистики:', error);
  }

  console.log('\n✅ Демонстрація завершена!');
  console.log('=' .repeat(50));
}

// Функція для тестування конфігурації
async function testConfiguration() {
  console.log('🔧 Перевірка конфігурації...');
  
  // Перевіряємо змінні середовища
  const requiredEnvVars = [
    'MCP_API_TOKEN',
    'BRIGHT_DATA_ENABLED',
    'SCRAPING_TIMEOUT_MS',
    'SCRAPING_RETRY_ATTEMPTS',
    'SCRAPING_RATE_LIMIT_MS'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.warn('⚠️ Відсутні змінні середовища:', missingVars);
    console.log('💡 Переконайтеся, що файл .env.local налаштований правильно');
  } else {
    console.log('✅ Всі необхідні змінні середовища налаштовані');
  }

  // Перевіряємо MCP токен
  const mcpToken = process.env.MCP_API_TOKEN;
  if (mcpToken) {
    console.log(`🔐 MCP токен знайдено: ${mcpToken.substring(0, 8)}...`);
  } else {
    console.warn('⚠️ MCP токен не знайдено');
  }

  // Перевіряємо Bright Data налаштування
  const brightDataEnabled = process.env.BRIGHT_DATA_ENABLED === 'true';
  console.log(`🌐 Bright Data MCP: ${brightDataEnabled ? 'увімкнено' : 'вимкнено'}`);

  return missingVars.length === 0;
}

// Головна функція
async function main() {
  console.log('🎯 Bright Data MCP Integration Demo');
  console.log('Версія: 1.0.0');
  console.log('Дата:', new Date().toLocaleString('uk-UA'));
  console.log('');

  // Перевіряємо конфігурацію
  const configOk = await testConfiguration();
  
  if (!configOk) {
    console.log('\n❌ Конфігурація не повна. Виправте помилки та спробуйте знову.');
    return;
  }

  console.log('');

  // Запускаємо демонстрацію
  await demonstrateBrightDataMCP();
}

// Запуск, якщо файл виконується напряму
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Критична помилка:', error);
    process.exit(1);
  });
}

export {
  demonstrateBrightDataMCP,
  testConfiguration,
  main
};
