#!/usr/bin/env node

/**
 * Автоматичний скрипт розгортання 3D Marketplace на Cloudflare Pages
 * з повною інтеграцією всіх сервісів
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Кольори для консолі
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description) {
  log(`\n🔧 ${description}...`, 'cyan');
  try {
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${description} - Успішно`, 'green');
    return result;
  } catch (error) {
    log(`❌ ${description} - Помилка: ${error.message}`, 'red');
    throw error;
  }
}

async function deployToCloudflare() {
  log('🚀 Початок розгортання 3D Marketplace на Cloudflare Pages', 'bright');
  log('=' .repeat(60), 'blue');

  try {
    // 1. Перевірка wrangler CLI
    log('\n📋 Крок 1: Перевірка середовища', 'yellow');
    try {
      execCommand('wrangler --version', 'Перевірка Wrangler CLI');
    } catch (error) {
      log('❌ Wrangler CLI не знайдено. Встановлюємо...', 'red');
      execCommand('npm install -g wrangler', 'Встановлення Wrangler CLI');
    }

    // 2. Аутентифікація
    log('\n🔐 Крок 2: Аутентифікація', 'yellow');
    try {
      execCommand('wrangler whoami', 'Перевірка аутентифікації');
    } catch (error) {
      log('❌ Потрібна аутентифікація. Запускаємо wrangler login...', 'red');
      execCommand('wrangler login', 'Аутентифікація в Cloudflare');
    }

    // 3. Створення D1 баз даних
    log('\n🗄️ Крок 3: Створення D1 баз даних', 'yellow');
    
    const databases = [
      { name: '3d-marketplace-production', env: 'production' },
      { name: '3d-marketplace-staging', env: 'staging' },
      { name: '3d-marketplace-development', env: 'development' }
    ];

    const dbIds = {};
    for (const db of databases) {
      try {
        const result = execCommand(
          `wrangler d1 create ${db.name}`,
          `Створення D1 бази ${db.name}`
        );
        
        // Витягуємо ID з результату
        const idMatch = result.match(/database_id = "([^"]+)"/);
        if (idMatch) {
          dbIds[db.env] = idMatch[1];
          log(`📝 ${db.env} DB ID: ${idMatch[1]}`, 'blue');
        }
      } catch (error) {
        log(`⚠️ База ${db.name} можливо вже існує`, 'yellow');
      }
    }

    // 4. Створення KV namespaces
    log('\n🗂️ Крок 4: Створення KV namespaces', 'yellow');
    
    const kvNamespaces = [
      { name: '3d-marketplace-cache-prod', env: 'production' },
      { name: '3d-marketplace-cache-staging', env: 'staging' },
      { name: '3d-marketplace-cache-dev', env: 'development' }
    ];

    const kvIds = {};
    for (const kv of kvNamespaces) {
      try {
        const result = execCommand(
          `wrangler kv:namespace create ${kv.name}`,
          `Створення KV namespace ${kv.name}`
        );
        
        // Витягуємо ID з результату
        const idMatch = result.match(/id = "([^"]+)"/);
        if (idMatch) {
          kvIds[kv.env] = idMatch[1];
          log(`📝 ${kv.env} KV ID: ${idMatch[1]}`, 'blue');
        }
      } catch (error) {
        log(`⚠️ KV namespace ${kv.name} можливо вже існує`, 'yellow');
      }
    }

    // 5. Створення R2 buckets
    log('\n🪣 Крок 5: Створення R2 buckets', 'yellow');
    
    const r2Buckets = [
      '3d-marketplace-models-prod',
      '3d-marketplace-models-staging', 
      '3d-marketplace-models-dev'
    ];

    for (const bucket of r2Buckets) {
      try {
        execCommand(
          `wrangler r2 bucket create ${bucket}`,
          `Створення R2 bucket ${bucket}`
        );
      } catch (error) {
        log(`⚠️ R2 bucket ${bucket} можливо вже існує`, 'yellow');
      }
    }

    // 6. Застосування SQL схеми
    log('\n📊 Крок 6: Застосування SQL схеми', 'yellow');
    
    const schemaPath = path.join(__dirname, '..', 'src', 'sql', 'cloudflare-d1-schema.sql');
    if (fs.existsSync(schemaPath)) {
      for (const db of databases) {
        if (dbIds[db.env]) {
          try {
            execCommand(
              `wrangler d1 execute ${db.name} --file=${schemaPath}`,
              `Застосування схеми до ${db.name}`
            );
          } catch (error) {
            log(`⚠️ Помилка застосування схеми до ${db.name}`, 'yellow');
          }
        }
      }
    } else {
      log('⚠️ SQL схема не знайдена', 'yellow');
    }

    // 7. Оновлення wrangler.toml
    log('\n⚙️ Крок 7: Оновлення конфігурації', 'yellow');
    updateWranglerConfig(dbIds, kvIds);

    // 8. Збірка проекту
    log('\n🔨 Крок 8: Збірка проекту', 'yellow');
    execCommand('npm run build', 'Збірка Next.js проекту');

    // 9. Розгортання на Pages
    log('\n🚀 Крок 9: Розгортання на Cloudflare Pages', 'yellow');
    execCommand(
      'wrangler pages deploy .next --project-name=3d-marketplace --compatibility-date=2024-01-01',
      'Розгортання на Cloudflare Pages'
    );

    // 10. Налаштування секретів
    log('\n🔐 Крок 10: Налаштування секретів', 'yellow');
    setupSecrets();

    // Успішне завершення
    log('\n🎉 РОЗГОРТАННЯ ЗАВЕРШЕНО УСПІШНО!', 'green');
    log('=' .repeat(60), 'green');
    log('🌐 Ваш 3D Marketplace розгорнуто на Cloudflare Pages!', 'bright');
    log('📊 Всі сервіси налаштовано та готові до роботи', 'green');
    log('🔗 URL: https://3d-marketplace.pages.dev', 'cyan');

  } catch (error) {
    log('\n💥 ПОМИЛКА РОЗГОРТАННЯ!', 'red');
    log(`❌ ${error.message}`, 'red');
    process.exit(1);
  }
}

function updateWranglerConfig(dbIds, kvIds) {
  const wranglerPath = path.join(__dirname, '..', 'wrangler.toml');
  
  if (!fs.existsSync(wranglerPath)) {
    log('⚠️ wrangler.toml не знайдено', 'yellow');
    return;
  }

  let config = fs.readFileSync(wranglerPath, 'utf8');
  
  // Оновлюємо ID баз даних
  if (dbIds.production) {
    config = config.replace(
      /database_id = "9dc65021-64f6-48f8-b6c3-bfc817746584"/,
      `database_id = "${dbIds.production}"`
    );
  }
  
  // Оновлюємо KV IDs
  if (kvIds.production) {
    config = config.replace(
      /id = "768cd42bbee14bce81819a0ef3666930"/,
      `id = "${kvIds.production}"`
    );
  }

  fs.writeFileSync(wranglerPath, config);
  log('✅ wrangler.toml оновлено з новими ID', 'green');
}

function setupSecrets() {
  const secrets = [
    'NEXTAUTH_SECRET',
    'GOOGLE_CLIENT_SECRET', 
    'GITHUB_CLIENT_SECRET',
    'ADMIN_API_KEY'
  ];

  log('🔐 Налаштування секретів:', 'cyan');
  log('Виконайте наступні команди для налаштування секретів:', 'yellow');
  
  secrets.forEach(secret => {
    log(`wrangler secret put ${secret} --env production`, 'blue');
  });
  
  log('\n💡 Рекомендовані значення:', 'yellow');
  log('NEXTAUTH_SECRET: Згенеруйте на https://generate-secret.vercel.app/32', 'blue');
  log('ADMIN_API_KEY: Згенеруйте безпечний ключ для адмін API', 'blue');
}

// Запуск скрипту
if (require.main === module) {
  deployToCloudflare().catch(console.error);
}

module.exports = { deployToCloudflare };
