#!/usr/bin/env node

/**
 * Enhanced 3D Marketplace Deployment Script
 * Deploys the complete marketplace with Bright Data MCP integration
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class MarketplaceDeployer {
  constructor() {
    this.environment = process.argv[2] || 'production';
    this.skipBuild = process.argv.includes('--skip-build');
    this.skipTests = process.argv.includes('--skip-tests');
    this.verbose = process.argv.includes('--verbose');
    
    console.log(`🚀 Enhanced 3D Marketplace Deployment`);
    console.log(`📦 Environment: ${this.environment}`);
    console.log(`⚙️ Skip Build: ${this.skipBuild}`);
    console.log(`🧪 Skip Tests: ${this.skipTests}`);
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      info: '📝',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      step: '🔄'
    }[type] || '📝';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  exec(command, description) {
    this.log(`${description}...`, 'step');
    try {
      const result = execSync(command, { 
        stdio: this.verbose ? 'inherit' : 'pipe',
        encoding: 'utf8'
      });
      this.log(`${description} completed`, 'success');
      return result;
    } catch (error) {
      this.log(`${description} failed: ${error.message}`, 'error');
      throw error;
    }
  }

  async checkPrerequisites() {
    this.log('Checking deployment prerequisites', 'step');
    
    // Check if wrangler is installed
    try {
      this.exec('wrangler --version', 'Checking Wrangler CLI');
    } catch (error) {
      this.log('Wrangler CLI not found. Please install it: npm install -g wrangler', 'error');
      process.exit(1);
    }

    // Check if logged in to Cloudflare
    try {
      this.exec('wrangler whoami', 'Checking Cloudflare authentication');
    } catch (error) {
      this.log('Not logged in to Cloudflare. Please run: wrangler login', 'error');
      process.exit(1);
    }

    // Check if required files exist
    const requiredFiles = [
      'wrangler.toml',
      'next.config.js',
      'package.json'
    ];

    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        this.log(`Required file missing: ${file}`, 'error');
        process.exit(1);
      }
    }

    this.log('Prerequisites check passed', 'success');
  }

  async runTests() {
    if (this.skipTests) {
      this.log('Skipping tests', 'warning');
      return;
    }

    this.log('Running test suite', 'step');
    
    try {
      // Run unit tests
      this.exec('npm run test:run', 'Running unit tests');
      
      // Test Bright Data integration
      this.exec('node test-bright-data-scraping.js', 'Testing Bright Data integration');
      
      this.log('All tests passed', 'success');
    } catch (error) {
      this.log('Tests failed. Use --skip-tests to deploy anyway', 'error');
      throw error;
    }
  }

  async buildApplication() {
    if (this.skipBuild) {
      this.log('Skipping build', 'warning');
      return;
    }

    this.log('Building application', 'step');
    
    // Clean previous builds
    this.exec('npm run clean', 'Cleaning previous builds');
    
    // Install dependencies
    this.exec('npm ci', 'Installing dependencies');
    
    // Type check (skip for now due to issues)
    // this.exec('npm run type-check', 'Type checking');
    
    // Build Next.js application
    this.exec('npm run build', 'Building Next.js application');
    
    // Build for Cloudflare Pages
    this.exec('npx @opennextjs/cloudflare@latest build', 'Building for Cloudflare Pages');
    
    this.log('Build completed successfully', 'success');
  }

  async setupCloudflareResources() {
    this.log('Setting up Cloudflare resources', 'step');
    
    try {
      // Create D1 database if it doesn't exist
      this.log('Checking D1 database', 'step');
      try {
        this.exec(`wrangler d1 info 3d-marketplace-${this.environment}`, 'Checking existing database');
      } catch (error) {
        this.exec(`wrangler d1 create 3d-marketplace-${this.environment}`, 'Creating D1 database');
      }

      // Create R2 bucket if it doesn't exist
      this.log('Checking R2 storage', 'step');
      try {
        this.exec(`wrangler r2 bucket list | grep 3d-marketplace-models-${this.environment === 'production' ? 'prod' : this.environment}`, 'Checking existing bucket');
      } catch (error) {
        this.exec(`wrangler r2 bucket create 3d-marketplace-models-${this.environment === 'production' ? 'prod' : this.environment}`, 'Creating R2 bucket');
      }

      // Create KV namespaces if they don't exist
      this.log('Checking KV namespaces', 'step');
      const kvNamespaces = ['CACHE_KV', 'SCRAPING_CACHE', 'MODEL_CACHE'];
      
      for (const namespace of kvNamespaces) {
        try {
          this.exec(`wrangler kv:namespace list | grep ${namespace.toLowerCase()}_${this.environment}`, `Checking ${namespace}`);
        } catch (error) {
          this.exec(`wrangler kv:namespace create ${namespace.toLowerCase()}_${this.environment}`, `Creating ${namespace}`);
        }
      }

      this.log('Cloudflare resources setup completed', 'success');
    } catch (error) {
      this.log('Failed to setup Cloudflare resources', 'error');
      throw error;
    }
  }

  async deployDatabase() {
    this.log('Deploying database schema', 'step');
    
    try {
      // Check if schema file exists
      if (fs.existsSync('schema.sql')) {
        this.exec(`wrangler d1 execute 3d-marketplace-${this.environment} --file=schema.sql --env=${this.environment}`, 'Executing database schema');
      } else {
        this.log('No schema.sql found, skipping database deployment', 'warning');
      }
      
      this.log('Database deployment completed', 'success');
    } catch (error) {
      this.log('Database deployment failed', 'error');
      throw error;
    }
  }

  async deployApplication() {
    this.log('Deploying application to Cloudflare Pages', 'step');
    
    try {
      const projectName = `3d-marketplace${this.environment !== 'production' ? `-${this.environment}` : ''}`;
      
      // Deploy to Cloudflare Pages
      this.exec(
        `wrangler pages deploy .vercel/output/static --project-name=${projectName} --env=${this.environment} --commit-dirty=true`,
        'Deploying to Cloudflare Pages'
      );
      
      this.log('Application deployment completed', 'success');
    } catch (error) {
      this.log('Application deployment failed', 'error');
      throw error;
    }
  }

  async setSecrets() {
    this.log('Checking required secrets', 'step');
    
    const requiredSecrets = [
      'NEXTAUTH_SECRET',
      'STRIPE_SECRET_KEY',
      'STRIPE_WEBHOOK_SECRET'
    ];

    const missingSecrets = [];
    
    for (const secret of requiredSecrets) {
      try {
        this.exec(`wrangler secret list --env=${this.environment} | grep ${secret}`, `Checking ${secret}`);
      } catch (error) {
        missingSecrets.push(secret);
      }
    }

    if (missingSecrets.length > 0) {
      this.log(`Missing secrets: ${missingSecrets.join(', ')}`, 'warning');
      this.log('Please set them using: wrangler secret put <SECRET_NAME> --env=' + this.environment, 'warning');
    } else {
      this.log('All required secrets are set', 'success');
    }
  }

  async verifyDeployment() {
    this.log('Verifying deployment', 'step');
    
    const projectName = `3d-marketplace${this.environment !== 'production' ? `-${this.environment}` : ''}`;
    const url = this.environment === 'production' 
      ? 'https://3d-marketplace.pages.dev'
      : `https://3d-marketplace-${this.environment}.pages.dev`;
    
    this.log(`Deployment URL: ${url}`, 'info');
    
    // Test basic connectivity
    try {
      const response = await fetch(url);
      if (response.ok) {
        this.log('Deployment verification successful', 'success');
      } else {
        this.log(`Deployment verification failed: HTTP ${response.status}`, 'warning');
      }
    } catch (error) {
      this.log('Could not verify deployment (this is normal for new deployments)', 'warning');
    }
  }

  async deploy() {
    try {
      console.log('\n🚀 Starting Enhanced 3D Marketplace Deployment\n');
      
      await this.checkPrerequisites();
      await this.runTests();
      await this.buildApplication();
      await this.setupCloudflareResources();
      await this.deployDatabase();
      await this.deployApplication();
      await this.setSecrets();
      await this.verifyDeployment();
      
      console.log('\n🎉 Deployment completed successfully!\n');
      
      const url = this.environment === 'production' 
        ? 'https://3d-marketplace.pages.dev'
        : `https://3d-marketplace-${this.environment}.pages.dev`;
      
      console.log(`🌐 Your marketplace is available at: ${url}`);
      console.log(`📊 Monitor your deployment: https://dash.cloudflare.com/`);
      console.log(`🔧 Manage your resources: wrangler pages project list`);
      
    } catch (error) {
      console.log('\n❌ Deployment failed!\n');
      console.error(error.message);
      process.exit(1);
    }
  }
}

// Run deployment if script is executed directly
if (require.main === module) {
  const deployer = new MarketplaceDeployer();
  deployer.deploy().catch(console.error);
}

module.exports = MarketplaceDeployer;
