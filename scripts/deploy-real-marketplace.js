#!/usr/bin/env node

/**
 * Deploy Real 3D Marketplace to Cloudflare
 * Complete deployment with Bright Data MCP integration
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const DEPLOYMENT_CONFIG = {
  projectName: '3d-marketplace',
  environment: process.argv[2] || 'production',
  skipTests: process.argv.includes('--skip-tests'),
  skipBuild: process.argv.includes('--skip-build'),
  verbose: process.argv.includes('--verbose')
};

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📋',
    success: '✅',
    warning: '⚠️',
    error: '❌',
    step: '🚀'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function execCommand(command, description) {
  log(`${description}...`, 'step');
  try {
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: DEPLOYMENT_CONFIG.verbose ? 'inherit' : 'pipe' 
    });
    log(`${description} completed successfully`, 'success');
    return output;
  } catch (error) {
    log(`${description} failed: ${error.message}`, 'error');
    throw error;
  }
}

function checkPrerequisites() {
  log('Checking deployment prerequisites...', 'step');
  
  // Check if wrangler is installed
  try {
    execSync('wrangler --version', { stdio: 'pipe' });
    log('Wrangler CLI is available', 'success');
  } catch (error) {
    log('Wrangler CLI not found. Please install it: npm install -g wrangler', 'error');
    process.exit(1);
  }
  
  // Check if logged in to Cloudflare
  try {
    execSync('wrangler whoami', { stdio: 'pipe' });
    log('Authenticated with Cloudflare', 'success');
  } catch (error) {
    log('Not authenticated with Cloudflare. Please run: wrangler login', 'error');
    process.exit(1);
  }
  
  // Check if required files exist
  const requiredFiles = [
    'wrangler.toml',
    'package.json',
    'next.config.js'
  ];
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      log(`Required file missing: ${file}`, 'error');
      process.exit(1);
    }
  }
  
  log('All prerequisites met', 'success');
}

function setupEnvironment() {
  log(`Setting up ${DEPLOYMENT_CONFIG.environment} environment...`, 'step');
  
  // Set environment variables for the deployment
  const envVars = {
    NODE_ENV: DEPLOYMENT_CONFIG.environment,
    NEXT_PUBLIC_APP_URL: DEPLOYMENT_CONFIG.environment === 'production' 
      ? 'https://3d-marketplace.pages.dev'
      : `https://3d-marketplace-${DEPLOYMENT_CONFIG.environment}.pages.dev`,
    BRIGHT_DATA_ENABLED: 'true',
    SCRAPING_SCHEDULER_ENABLED: 'true',
    CLOUDFLARE_MCP_ENABLED: 'true',
    BRIGHT_DATA_MCP_ENABLED: 'true'
  };
  
  // Write environment variables to .env.local for build
  const envContent = Object.entries(envVars)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');
  
  fs.writeFileSync('.env.local', envContent);
  log('Environment variables configured', 'success');
}

function runTests() {
  if (DEPLOYMENT_CONFIG.skipTests) {
    log('Skipping tests (--skip-tests flag)', 'warning');
    return;
  }
  
  log('Running test suite...', 'step');
  
  try {
    // Run unit tests
    execCommand('npm run test:run', 'Running unit tests');
    
    // Test real scraping functionality
    execCommand('npm run test:real-scraping', 'Testing real scraping');
    
    // Test MCP integration
    execCommand('npm run test:bright-data-credentials', 'Testing Bright Data credentials');
    
    log('All tests passed', 'success');
  } catch (error) {
    log('Tests failed. Use --skip-tests to deploy anyway', 'error');
    process.exit(1);
  }
}

function buildApplication() {
  if (DEPLOYMENT_CONFIG.skipBuild) {
    log('Skipping build (--skip-build flag)', 'warning');
    return;
  }
  
  log('Building application for Cloudflare...', 'step');
  
  // Clean previous builds
  execCommand('rm -rf .next dist .vercel/output', 'Cleaning previous builds');
  
  // Install dependencies
  execCommand('npm ci', 'Installing dependencies');
  
  // Build Next.js application
  execCommand('npm run build', 'Building Next.js application');
  
  // Build for Cloudflare Pages
  execCommand('npx @cloudflare/next-on-pages', 'Building for Cloudflare Pages');
  
  log('Application built successfully', 'success');
}

function setupCloudflareResources() {
  log('Setting up Cloudflare resources...', 'step');
  
  const environment = DEPLOYMENT_CONFIG.environment;
  
  try {
    // Create D1 database if it doesn't exist
    log('Setting up D1 database...', 'step');
    try {
      execCommand(`wrangler d1 info 3d-marketplace-${environment}`, 'Checking D1 database');
    } catch (error) {
      execCommand(`wrangler d1 create 3d-marketplace-${environment}`, 'Creating D1 database');
    }
    
    // Create R2 bucket if it doesn't exist
    log('Setting up R2 storage...', 'step');
    try {
      execCommand(`wrangler r2 bucket list | grep 3d-marketplace-models-${environment}`, 'Checking R2 bucket');
    } catch (error) {
      execCommand(`wrangler r2 bucket create 3d-marketplace-models-${environment}`, 'Creating R2 bucket');
    }
    
    // Create KV namespaces if they don't exist
    log('Setting up KV storage...', 'step');
    const kvNamespaces = ['CACHE_KV', 'SCRAPING_CACHE', 'MODEL_CACHE'];
    
    for (const namespace of kvNamespaces) {
      try {
        execCommand(`wrangler kv:namespace list | grep ${namespace.toLowerCase()}-${environment}`, `Checking ${namespace}`);
      } catch (error) {
        execCommand(`wrangler kv:namespace create ${namespace.toLowerCase()}-${environment}`, `Creating ${namespace}`);
      }
    }
    
    log('Cloudflare resources configured', 'success');
  } catch (error) {
    log('Failed to setup Cloudflare resources', 'error');
    throw error;
  }
}

function deployToCloudflare() {
  log('Deploying to Cloudflare Pages...', 'step');
  
  const deployCommand = DEPLOYMENT_CONFIG.environment === 'production'
    ? `wrangler pages deploy .vercel/output/static --project-name=${DEPLOYMENT_CONFIG.projectName} --compatibility-date=2024-01-01`
    : `wrangler pages deploy .vercel/output/static --project-name=${DEPLOYMENT_CONFIG.projectName}-${DEPLOYMENT_CONFIG.environment} --compatibility-date=2024-01-01`;
  
  execCommand(deployCommand, 'Deploying to Cloudflare Pages');
  
  log('Deployment completed successfully', 'success');
}

function verifyDeployment() {
  log('Verifying deployment...', 'step');
  
  const baseUrl = DEPLOYMENT_CONFIG.environment === 'production'
    ? 'https://3d-marketplace.pages.dev'
    : `https://3d-marketplace-${DEPLOYMENT_CONFIG.environment}.pages.dev`;
  
  log(`Deployment URL: ${baseUrl}`, 'info');
  log('Please verify the following endpoints:', 'info');
  log(`  - Homepage: ${baseUrl}`, 'info');
  log(`  - Marketplace: ${baseUrl}/marketplace`, 'info');
  log(`  - API Health: ${baseUrl}/api/mcp/bright-data?action=health`, 'info');
  log(`  - Real Scraping: ${baseUrl}/api/scraping/popular-models?limit=5`, 'info');
  
  log('Deployment verification complete', 'success');
}

function main() {
  log(`Starting deployment to ${DEPLOYMENT_CONFIG.environment}`, 'step');
  log(`Configuration: ${JSON.stringify(DEPLOYMENT_CONFIG, null, 2)}`, 'info');
  
  try {
    checkPrerequisites();
    setupEnvironment();
    runTests();
    buildApplication();
    setupCloudflareResources();
    deployToCloudflare();
    verifyDeployment();
    
    log('🎉 Real 3D Marketplace deployed successfully!', 'success');
    log('Your marketplace now uses real Bright Data MCP for scraping 3D models', 'success');
    
  } catch (error) {
    log(`Deployment failed: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Run deployment
if (require.main === module) {
  main();
}

module.exports = { main, DEPLOYMENT_CONFIG };
