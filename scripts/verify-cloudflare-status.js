#!/usr/bin/env node

/**
 * Cloudflare Environment Status Verification Script
 * Checks the current status of all Cloudflare resources for 3D Marketplace
 */

const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, silent = false) {
  try {
    const result = execSync(command, { encoding: 'utf8', stdio: silent ? 'pipe' : 'inherit' });
    return { success: true, output: result };
  } catch (error) {
    return { success: false, error: error.message, output: error.stdout || '' };
  }
}

function checkAuthentication() {
  log('\n🔐 Checking Authentication...', 'cyan');
  
  const whoami = execCommand('wrangler whoami', true);
  if (whoami.success && whoami.output.includes('You are logged in')) {
    log('✅ Wrangler authenticated', 'green');
    
    // Extract account info
    const accountMatch = whoami.output.match(/Account ID\s+│\s+([a-f0-9]+)/);
    if (accountMatch) {
      log(`✅ Account ID: ${accountMatch[1]}`, 'green');
    }
    return true;
  } else {
    log('❌ Wrangler not authenticated', 'red');
    log('Run: wrangler auth login', 'yellow');
    return false;
  }
}

function checkWorkers() {
  log('\n⚡ Checking Workers...', 'cyan');
  
  const workers = execCommand('wrangler list', true);
  if (workers.success) {
    if (workers.output.includes('3d-marketplace')) {
      log('✅ 3d-marketplace worker deployed', 'green');
    } else {
      log('❌ 3d-marketplace worker not found', 'red');
    }
    
    // Count total workers
    const workerCount = (workers.output.match(/│/g) || []).length / 4; // Rough estimate
    log(`📊 Total workers: ${Math.floor(workerCount)}`, 'blue');
  } else {
    log('❌ Failed to check workers', 'red');
  }
}

function checkDatabases() {
  log('\n🗄️ Checking D1 Databases...', 'cyan');
  
  const databases = execCommand('wrangler d1 list', true);
  if (databases.success) {
    const requiredDbs = [
      '3d-marketplace-enhanced',
      '3d-marketplace-staging', 
      '3d-marketplace-development'
    ];
    
    requiredDbs.forEach(db => {
      if (databases.output.includes(db)) {
        log(`✅ Database ${db} exists`, 'green');
      } else {
        log(`❌ Database ${db} not found`, 'red');
      }
    });
  } else {
    log('❌ Failed to check databases', 'red');
    log('This might be due to permissions or missing databases', 'yellow');
  }
}

function checkR2Buckets() {
  log('\n🪣 Checking R2 Buckets...', 'cyan');
  
  const buckets = execCommand('wrangler r2 bucket list', true);
  if (buckets.success) {
    const requiredBuckets = [
      '3d-marketplace-models-prod',
      '3d-marketplace-models-staging',
      '3d-marketplace-models-dev'
    ];
    
    requiredBuckets.forEach(bucket => {
      if (buckets.output.includes(bucket)) {
        log(`✅ R2 bucket ${bucket} exists`, 'green');
      } else {
        log(`❌ R2 bucket ${bucket} not found`, 'red');
      }
    });
    
    // Count total buckets
    const bucketCount = (buckets.output.match(/│/g) || []).length / 3; // Rough estimate
    log(`📊 Total R2 buckets: ${Math.floor(bucketCount)}`, 'blue');
  } else {
    log('❌ Failed to check R2 buckets', 'red');
  }
}

function checkKVNamespaces() {
  log('\n🗂️ Checking KV Namespaces...', 'cyan');
  
  const namespaces = execCommand('wrangler kv:namespace list', true);
  if (namespaces.success) {
    const requiredNamespaces = [
      'CACHE_KV',
      '3d-marketplace-enhanced-cache',
      'marketplace-kv'
    ];
    
    requiredNamespaces.forEach(ns => {
      if (namespaces.output.includes(ns)) {
        log(`✅ KV namespace ${ns} exists`, 'green');
      } else {
        log(`❌ KV namespace ${ns} not found`, 'red');
      }
    });
    
    // Count total namespaces
    const nsCount = (namespaces.output.match(/│/g) || []).length / 3; // Rough estimate
    log(`📊 Total KV namespaces: ${Math.floor(nsCount)}`, 'blue');
  } else {
    log('❌ Failed to check KV namespaces', 'red');
  }
}

function checkPages() {
  log('\n📄 Checking Pages Projects...', 'cyan');
  
  const pages = execCommand('wrangler pages project list', true);
  if (pages.success) {
    if (pages.output.includes('3d-marketplace')) {
      log('✅ 3d-marketplace Pages project exists', 'green');
    } else {
      log('❌ 3d-marketplace Pages project not found', 'red');
    }
  } else {
    log('❌ Failed to check Pages projects', 'red');
  }
}

function checkSecrets() {
  log('\n🔐 Checking Secrets...', 'cyan');
  
  const environments = ['production', 'staging', 'development'];
  
  environments.forEach(env => {
    const secrets = execCommand(`wrangler secret list --env ${env}`, true);
    if (secrets.success) {
      const secretCount = (secrets.output.match(/│/g) || []).length / 3; // Rough estimate
      log(`✅ ${env}: ${Math.floor(secretCount)} secrets configured`, 'green');
    } else {
      log(`❌ Failed to check ${env} secrets`, 'red');
    }
  });
}

function generateReport() {
  log('\n📊 Environment Status Report', 'bright');
  log('=' * 50, 'cyan');
  
  const checks = [
    { name: 'Authentication', status: checkAuthentication() },
    { name: 'Workers', status: true }, // We'll assume this passes if auth works
    { name: 'Databases', status: true },
    { name: 'R2 Buckets', status: true },
    { name: 'KV Namespaces', status: true },
    { name: 'Pages Projects', status: true },
    { name: 'Secrets', status: true }
  ];
  
  // Run all checks
  checkWorkers();
  checkDatabases();
  checkR2Buckets();
  checkKVNamespaces();
  checkPages();
  checkSecrets();
  
  log('\n🎯 Next Steps:', 'cyan');
  log('1. If any resources are missing, run: npm run setup:cloudflare', 'blue');
  log('2. Configure missing secrets using: wrangler secret put <SECRET_NAME> --env production', 'blue');
  log('3. Deploy your application: npm run deploy:cloudflare', 'blue');
  log('4. Test your deployment: npm run verify:deployment', 'blue');
  
  log('\n📚 Documentation:', 'cyan');
  log('- Setup Guide: docs/CLOUDFLARE_ENVIRONMENT_SETUP.md', 'blue');
  log('- Deployment Checklist: DEPLOYMENT_CHECKLIST.md', 'blue');
}

function main() {
  log('🔍 Cloudflare Environment Status Check', 'bright');
  log('3D Marketplace Infrastructure Verification', 'cyan');
  log('=' * 60, 'cyan');
  
  generateReport();
  
  log('\n✅ Status check complete!', 'green');
}

// Run the verification
if (require.main === module) {
  main();
}

module.exports = {
  checkAuthentication,
  checkWorkers,
  checkDatabases,
  checkR2Buckets,
  checkKVNamespaces,
  checkPages,
  checkSecrets
};
