#!/usr/bin/env node

/**
 * Test script for competitor scraping functionality
 */

const { CompetitorScraperManager } = require('../src/lib/bright-data/competitor-scrapers/competitor-scraper-manager');

async function testCompetitorScraping() {
  console.log('🚀 Testing Bright Data Competitor Scraping Integration\n');

  try {
    // Initialize the scraper manager
    const scraperManager = new CompetitorScraperManager({
      maxModelsPerPlatform: 5, // Small number for testing
      rateLimitDelay: 1000,
      enableCaching: true
    });

    console.log('✅ Competitor Scraper Manager initialized\n');

    // Test 1: Get platform metrics
    console.log('📊 Test 1: Platform Metrics');
    const metrics = scraperManager.getPlatformMetrics();
    console.log('Platform metrics:', Object.keys(metrics));
    console.log('');

    // Test 2: Test Sketchfab trending scraping
    console.log('🔥 Test 2: Sketchfab Trending Models (Limited)');
    try {
      const trendingResults = await scraperManager.scrapeTrendingModels();
      console.log(`✅ Scraped trending models from ${trendingResults.length} platforms`);
      
      trendingResults.forEach(result => {
        console.log(`  - ${result.platform}: ${result.scrapedModels} models, avg price: $${result.averagePrice.toFixed(2)}`);
        console.log(`    Top categories: ${result.topCategories.slice(0, 3).map(c => c.category).join(', ')}`);
        console.log(`    Popular tags: ${result.popularTags.slice(0, 5).map(t => t.tag).join(', ')}`);
      });
    } catch (error) {
      console.log(`❌ Trending scraping failed: ${error.message}`);
    }
    console.log('');

    // Test 3: Test category scraping
    console.log('📂 Test 3: Category Scraping (Characters)');
    try {
      const categoryResults = await scraperManager.scrapeCategoryModels('characters');
      console.log(`✅ Scraped character models from ${categoryResults.length} platforms`);
      
      categoryResults.forEach(result => {
        console.log(`  - ${result.platform}: ${result.scrapedModels} character models`);
      });
    } catch (error) {
      console.log(`❌ Category scraping failed: ${error.message}`);
    }
    console.log('');

    // Test 4: Test search functionality
    console.log('🔍 Test 4: Search Functionality (car)');
    try {
      const searchResults = await scraperManager.searchModels('car');
      console.log(`✅ Search completed across ${searchResults.length} platforms`);
      
      searchResults.forEach(result => {
        console.log(`  - ${result.platform}: ${result.scrapedModels} car models found`);
      });
    } catch (error) {
      console.log(`❌ Search failed: ${error.message}`);
    }
    console.log('');

    // Test 5: Check active jobs
    console.log('📋 Test 5: Active Jobs');
    const activeJobs = scraperManager.getActiveJobs();
    console.log(`Active jobs: ${activeJobs.length}`);
    activeJobs.forEach(job => {
      console.log(`  - ${job.id}: ${job.status} (${job.progress}%)`);
    });
    console.log('');

    // Test 6: Final metrics
    console.log('📈 Test 6: Final Platform Metrics');
    const finalMetrics = scraperManager.getPlatformMetrics();
    Object.entries(finalMetrics).forEach(([platform, metrics]) => {
      console.log(`  - ${platform}:`);
      console.log(`    Total requests: ${metrics.totalRequests}`);
      console.log(`    Success rate: ${((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(1)}%`);
      console.log(`    Health status: ${metrics.healthStatus}`);
      console.log(`    Avg response time: ${metrics.averageResponseTime.toFixed(0)}ms`);
    });

    console.log('\n🎉 Competitor scraping test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testCompetitorScraping().catch(console.error);
}

module.exports = { testCompetitorScraping };
