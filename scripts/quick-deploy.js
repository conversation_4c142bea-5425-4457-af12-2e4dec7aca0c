#!/usr/bin/env node

/**
 * Швидкий скрипт розгортання для 3D Marketplace
 * Використовує існуючі ресурси Cloudflare
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Кольори для консолі
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description) {
  log(`\n🔧 ${description}...`, 'cyan');
  try {
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${description} - Успішно`, 'green');
    return result;
  } catch (error) {
    log(`❌ ${description} - Помилка: ${error.message}`, 'red');
    throw error;
  }
}

async function quickDeploy() {
  log('⚡ Швидке розгортання 3D Marketplace на Cloudflare Pages', 'bright');
  log('=' .repeat(60), 'blue');

  try {
    // 1. Перевірка середовища
    log('\n📋 Крок 1: Перевірка середовища', 'yellow');
    
    // Перевірка Node.js
    const nodeVersion = execCommand('node --version', 'Перевірка Node.js');
    log(`📝 Node.js версія: ${nodeVersion.trim()}`, 'blue');
    
    // Перевірка npm
    const npmVersion = execCommand('npm --version', 'Перевірка npm');
    log(`📝 npm версія: ${npmVersion.trim()}`, 'blue');

    // 2. Встановлення залежностей
    log('\n📦 Крок 2: Встановлення залежностей', 'yellow');
    execCommand('npm ci', 'Встановлення залежностей');

    // 3. Перевірка типів
    log('\n🔍 Крок 3: Перевірка типів', 'yellow');
    try {
      execCommand('npm run type-check', 'Перевірка TypeScript');
    } catch (error) {
      log('⚠️ Помилки типів знайдено, але продовжуємо...', 'yellow');
    }

    // 4. Збірка проекту
    log('\n🔨 Крок 4: Збірка проекту', 'yellow');
    execCommand('npm run build', 'Збірка Next.js проекту');

    // 5. Перевірка wrangler
    log('\n🛠️ Крок 5: Перевірка Wrangler CLI', 'yellow');
    try {
      const wranglerVersion = execCommand('wrangler --version', 'Перевірка Wrangler');
      log(`📝 Wrangler версія: ${wranglerVersion.trim()}`, 'blue');
    } catch (error) {
      log('❌ Wrangler не знайдено. Встановлюємо...', 'red');
      execCommand('npm install -g wrangler', 'Встановлення Wrangler');
    }

    // 6. Аутентифікація
    log('\n🔐 Крок 6: Перевірка аутентифікації', 'yellow');
    try {
      const whoami = execCommand('wrangler whoami', 'Перевірка аутентифікації');
      log(`👤 Увійшли як: ${whoami.trim()}`, 'blue');
    } catch (error) {
      log('❌ Потрібна аутентифікація!', 'red');
      log('🔗 Виконайте: wrangler login', 'yellow');
      throw new Error('Authentication required');
    }

    // 7. Застосування SQL схеми (якщо потрібно)
    log('\n📊 Крок 7: Перевірка бази даних', 'yellow');
    await checkAndSetupDatabase();

    // 8. Розгортання на Pages
    log('\n🚀 Крок 8: Розгортання на Cloudflare Pages', 'yellow');
    
    // Використовуємо @cloudflare/next-on-pages для оптимізації
    try {
      execCommand('npx @cloudflare/next-on-pages@1', 'Оптимізація для Cloudflare Pages');
    } catch (error) {
      log('⚠️ Оптимізація не вдалася, використовуємо стандартну збірку', 'yellow');
    }

    // Розгортання
    const deployCommand = 'wrangler pages deploy .vercel/output/static --project-name=3d-marketplace --compatibility-date=2024-01-01';
    
    try {
      execCommand(deployCommand, 'Розгортання на Cloudflare Pages');
    } catch (error) {
      // Fallback до стандартної збірки
      log('⚠️ Спробуємо з .next директорією...', 'yellow');
      execCommand('wrangler pages deploy .next --project-name=3d-marketplace --compatibility-date=2024-01-01', 'Розгортання (fallback)');
    }

    // 9. Перевірка розгортання
    log('\n🔍 Крок 9: Перевірка розгортання', 'yellow');
    await verifyDeployment();

    // 10. Налаштування секретів (якщо потрібно)
    log('\n🔐 Крок 10: Перевірка секретів', 'yellow');
    checkSecrets();

    // Успішне завершення
    log('\n🎉 РОЗГОРТАННЯ ЗАВЕРШЕНО УСПІШНО!', 'green');
    log('=' .repeat(60), 'green');
    log('🌐 Ваш 3D Marketplace доступний за адресою:', 'bright');
    log('🔗 https://3d-marketplace.pages.dev', 'cyan');
    log('📊 Дашборд моніторингу: https://3d-marketplace.pages.dev/admin/import-demo', 'cyan');
    log('🔧 API тестування: https://3d-marketplace.pages.dev/api/test-import', 'cyan');

  } catch (error) {
    log('\n💥 ПОМИЛКА РОЗГОРТАННЯ!', 'red');
    log(`❌ ${error.message}`, 'red');
    
    // Поради по вирішенню проблем
    log('\n🔧 Поради по вирішенню проблем:', 'yellow');
    log('1. Перевірте аутентифікацію: wrangler login', 'blue');
    log('2. Перевірте права доступу до Cloudflare account', 'blue');
    log('3. Переконайтеся, що всі залежності встановлені', 'blue');
    log('4. Перевірте логи збірки на помилки', 'blue');
    
    process.exit(1);
  }
}

async function checkAndSetupDatabase() {
  try {
    // Перевіряємо чи існує база даних
    const dbList = execCommand('wrangler d1 list', 'Перевірка баз даних');
    
    if (dbList.includes('3d-marketplace-enhanced')) {
      log('✅ База даних знайдена', 'green');
      
      // Перевіряємо чи застосована схема
      try {
        const schemaCheck = execCommand(
          'wrangler d1 execute 3d-marketplace-enhanced --command="SELECT name FROM sqlite_master WHERE type=\'table\' AND name=\'job_queue\';"',
          'Перевірка схеми бази даних'
        );
        
        if (schemaCheck.includes('job_queue')) {
          log('✅ Схема бази даних актуальна', 'green');
        } else {
          log('⚠️ Потрібно застосувати схему бази даних', 'yellow');
          applyDatabaseSchema();
        }
      } catch (error) {
        log('⚠️ Не вдалося перевірити схему, застосовуємо...', 'yellow');
        applyDatabaseSchema();
      }
    } else {
      log('❌ База даних не знайдена', 'red');
      log('🔧 Створіть базу даних: wrangler d1 create 3d-marketplace-enhanced', 'yellow');
    }
  } catch (error) {
    log('⚠️ Помилка перевірки бази даних', 'yellow');
  }
}

function applyDatabaseSchema() {
  const schemaPath = path.join(__dirname, '..', 'src', 'sql', 'cloudflare-d1-schema.sql');
  
  if (fs.existsSync(schemaPath)) {
    try {
      execCommand(
        `wrangler d1 execute 3d-marketplace-enhanced --file=${schemaPath}`,
        'Застосування схеми бази даних'
      );
    } catch (error) {
      log('⚠️ Помилка застосування схеми', 'yellow');
    }
  } else {
    log('⚠️ Файл схеми не знайдено', 'yellow');
  }
}

async function verifyDeployment() {
  log('🔍 Перевіряємо доступність сайту...', 'cyan');
  
  // Чекаємо 10 секунд для розгортання
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  try {
    // Простий curl запит для перевірки
    execCommand('curl -f -s https://3d-marketplace.pages.dev > /dev/null', 'Перевірка доступності сайту');
    log('✅ Сайт доступний', 'green');
  } catch (error) {
    log('⚠️ Сайт ще не доступний, може потребувати часу для розгортання', 'yellow');
  }
}

function checkSecrets() {
  const requiredSecrets = [
    'NEXTAUTH_SECRET',
    'ADMIN_API_KEY'
  ];

  log('🔐 Рекомендовані секрети для налаштування:', 'cyan');
  requiredSecrets.forEach(secret => {
    log(`  wrangler secret put ${secret}`, 'blue');
  });
  
  log('\n💡 Згенеруйте NEXTAUTH_SECRET: https://generate-secret.vercel.app/32', 'yellow');
}

// Запуск скрипту
if (require.main === module) {
  quickDeploy().catch(console.error);
}

module.exports = { quickDeploy };
