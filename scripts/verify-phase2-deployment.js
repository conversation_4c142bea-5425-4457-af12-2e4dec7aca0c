#!/usr/bin/env node

/**
 * Phase 2 Deployment Verification Script
 * Tests all advanced competitor intelligence features
 */

const https = require('https');
const { execSync } = require('child_process');

const COLORS = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${COLORS[color]}${message}${COLORS.reset}`);
}

async function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({ status: res.statusCode, data: parsed });
        } catch (error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

async function verifyPhase2Deployment() {
  log('🔍 Verifying Phase 2 Deployment - Advanced Competitor Intelligence', 'cyan');
  log('=' .repeat(80), 'blue');

  const baseUrl = 'https://3d-marketplace.pages.dev';
  let passedTests = 0;
  let totalTests = 0;

  // Test 1: Basic API Health
  log('\n📡 Test 1: API Health Check', 'yellow');
  totalTests++;
  try {
    const response = await makeRequest(`${baseUrl}/api/competitors?action=metrics`);
    if (response.status === 200) {
      log('✅ Competitor API is responding', 'green');
      passedTests++;
    } else {
      log(`❌ Competitor API returned status: ${response.status}`, 'red');
    }
  } catch (error) {
    log(`❌ Competitor API failed: ${error.message}`, 'red');
  }

  // Test 2: Advanced Analytics API
  log('\n📊 Test 2: Advanced Analytics API', 'yellow');
  totalTests++;
  try {
    const response = await makeRequest(`${baseUrl}/api/analytics/advanced?action=dashboard-summary`);
    if (response.status === 200 && response.data.success) {
      log('✅ Advanced Analytics API is working', 'green');
      log(`   ROI: ${response.data.data?.roi?.current || 'N/A'}%`, 'blue');
      log(`   Market Share: ${response.data.data?.marketShare?.current || 'N/A'}%`, 'blue');
      passedTests++;
    } else {
      log(`❌ Advanced Analytics API failed: ${response.status}`, 'red');
    }
  } catch (error) {
    log(`❌ Advanced Analytics API error: ${error.message}`, 'red');
  }

  // Test 3: Real-time Alerts API
  log('\n🚨 Test 3: Real-time Alerts API', 'yellow');
  totalTests++;
  try {
    const response = await makeRequest(`${baseUrl}/api/alerts?action=statistics`);
    if (response.status === 200) {
      log('✅ Alerts API is responding', 'green');
      if (response.data.success) {
        const stats = response.data.data;
        log(`   Total Rules: ${stats.totalRules || 0}`, 'blue');
        log(`   Active Rules: ${stats.activeRules || 0}`, 'blue');
        log(`   Total Notifications: ${stats.totalNotifications || 0}`, 'blue');
      }
      passedTests++;
    } else {
      log(`❌ Alerts API returned status: ${response.status}`, 'red');
    }
  } catch (error) {
    log(`❌ Alerts API error: ${error.message}`, 'red');
  }

  // Test 4: Scheduler API
  log('\n⏰ Test 4: Scheduler Management API', 'yellow');
  totalTests++;
  try {
    const response = await makeRequest(`${baseUrl}/api/competitors/scheduler?action=status`);
    if (response.status === 200 && response.data.success) {
      log('✅ Scheduler API is working', 'green');
      const status = response.data.data;
      log(`   Scheduler Running: ${status.isRunning ? 'Yes' : 'No'}`, 'blue');
      log(`   Active Tasks: ${status.activeTasks || 0}`, 'blue');
      log(`   Total Tasks: ${status.totalTasks || 0}`, 'blue');
      passedTests++;
    } else {
      log(`❌ Scheduler API failed: ${response.status}`, 'red');
    }
  } catch (error) {
    log(`❌ Scheduler API error: ${error.message}`, 'red');
  }

  // Test 5: Database Connectivity
  log('\n🗄️ Test 5: Database Connectivity', 'yellow');
  totalTests++;
  try {
    // Test by trying to get pricing trends
    const response = await makeRequest(`${baseUrl}/api/analytics/advanced?action=price-predictions&platform=sketchfab&category=characters`);
    if (response.status === 200) {
      log('✅ Database connectivity verified', 'green');
      passedTests++;
    } else {
      log(`❌ Database test failed: ${response.status}`, 'red');
    }
  } catch (error) {
    log(`❌ Database connectivity error: ${error.message}`, 'red');
  }

  // Test 6: AI/ML Predictions
  log('\n🧠 Test 6: AI/ML Prediction Engine', 'yellow');
  totalTests++;
  try {
    const response = await makeRequest(`${baseUrl}/api/analytics/advanced?action=predictive-insights&platform=all&category=characters`);
    if (response.status === 200 && response.data.success) {
      log('✅ AI Prediction Engine is working', 'green');
      const insights = response.data.data;
      if (insights && insights.length > 0) {
        log(`   Generated ${insights.length} predictive insights`, 'blue');
        insights.slice(0, 2).forEach((insight, index) => {
          log(`   ${index + 1}. ${insight.title} (${insight.prediction?.confidence || 0}% confidence)`, 'blue');
        });
      }
      passedTests++;
    } else {
      log(`❌ AI Prediction Engine failed: ${response.status}`, 'red');
    }
  } catch (error) {
    log(`❌ AI Prediction Engine error: ${error.message}`, 'red');
  }

  // Test 7: Market Opportunities
  log('\n🎯 Test 7: Market Opportunity Scoring', 'yellow');
  totalTests++;
  try {
    const response = await makeRequest(`${baseUrl}/api/analytics/advanced?action=market-opportunities&platform=all`);
    if (response.status === 200 && response.data.success) {
      log('✅ Market Opportunity Scoring is working', 'green');
      const opportunities = response.data.data;
      if (opportunities && opportunities.length > 0) {
        log(`   Found ${opportunities.length} market opportunities`, 'blue');
        opportunities.slice(0, 2).forEach((opp, index) => {
          log(`   ${index + 1}. ${opp.category} on ${opp.platform} (Score: ${opp.opportunityScore}/100)`, 'blue');
        });
      }
      passedTests++;
    } else {
      log(`❌ Market Opportunity Scoring failed: ${response.status}`, 'red');
    }
  } catch (error) {
    log(`❌ Market Opportunity Scoring error: ${error.message}`, 'red');
  }

  // Test 8: Alert System Test
  log('\n🧪 Test 8: Alert System Functionality', 'yellow');
  totalTests++;
  try {
    const response = await makeRequest(`${baseUrl}/api/alerts?action=test-alerts`);
    if (response.status === 200 && response.data.success) {
      log('✅ Alert System is functional', 'green');
      const testData = response.data.data;
      log(`   Events Processed: ${testData.eventsProcessed || 0}`, 'blue');
      log(`   Notifications Generated: ${testData.notificationsGenerated || 0}`, 'blue');
      passedTests++;
    } else {
      log(`❌ Alert System test failed: ${response.status}`, 'red');
    }
  } catch (error) {
    log(`❌ Alert System test error: ${error.message}`, 'red');
  }

  // Test 9: Performance Benchmarks
  log('\n📈 Test 9: Performance Benchmarking', 'yellow');
  totalTests++;
  try {
    const response = await makeRequest(`${baseUrl}/api/analytics/advanced?action=performance-benchmarks&sellerId=test`);
    if (response.status === 200 && response.data.success) {
      log('✅ Performance Benchmarking is working', 'green');
      const benchmarks = response.data.data;
      if (benchmarks) {
        log(`   Revenue per Model: $${benchmarks.metrics?.revenuePerModel || 0}`, 'blue');
        log(`   Percentile Rank: ${benchmarks.benchmarks?.percentileRank || 0}th`, 'blue');
      }
      passedTests++;
    } else {
      log(`❌ Performance Benchmarking failed: ${response.status}`, 'red');
    }
  } catch (error) {
    log(`❌ Performance Benchmarking error: ${error.message}`, 'red');
  }

  // Test 10: ROI Analysis
  log('\n💰 Test 10: ROI Analysis Engine', 'yellow');
  totalTests++;
  try {
    const response = await makeRequest(`${baseUrl}/api/analytics/advanced?action=roi-analysis&sellerId=test&period=3months`);
    if (response.status === 200 && response.data.success) {
      log('✅ ROI Analysis Engine is working', 'green');
      const roi = response.data.data;
      if (roi) {
        log(`   Current ROI: ${roi.roi?.percentage || 0}%`, 'blue');
        log(`   Revenue Growth: ${roi.revenue?.growthPercentage || 0}%`, 'blue');
        log(`   Recommendations: ${roi.recommendations?.length || 0}`, 'blue');
      }
      passedTests++;
    } else {
      log(`❌ ROI Analysis Engine failed: ${response.status}`, 'red');
    }
  } catch (error) {
    log(`❌ ROI Analysis Engine error: ${error.message}`, 'red');
  }

  // Summary
  log('\n📊 Verification Summary', 'cyan');
  log('=' .repeat(50), 'blue');
  
  const successRate = (passedTests / totalTests) * 100;
  log(`Tests Passed: ${passedTests}/${totalTests} (${successRate.toFixed(1)}%)`, 
      successRate >= 80 ? 'green' : successRate >= 60 ? 'yellow' : 'red');

  if (successRate >= 80) {
    log('\n🎉 Phase 2 Deployment Verification: SUCCESS!', 'green');
    log('✅ Advanced Competitor Intelligence is fully operational', 'green');
    
    log('\n🚀 Available Features:', 'cyan');
    log('• Historical data tracking and trend analysis', 'blue');
    log('• AI-powered price predictions and market forecasting', 'blue');
    log('• Real-time alerts for market changes', 'blue');
    log('• Advanced ROI optimization and benchmarking', 'blue');
    log('• Market opportunity scoring and recommendations', 'blue');
    
    log('\n🎯 Next Steps:', 'cyan');
    log('1. Access the admin dashboard: https://3d-marketplace.pages.dev/admin', 'blue');
    log('2. Configure alert rules for your marketplace', 'blue');
    log('3. Monitor the analytics dashboard for insights', 'blue');
    log('4. Set up custom scraping schedules', 'blue');
    
  } else if (successRate >= 60) {
    log('\n⚠️ Phase 2 Deployment Verification: PARTIAL SUCCESS', 'yellow');
    log('Some features may need additional configuration', 'yellow');
    
  } else {
    log('\n❌ Phase 2 Deployment Verification: FAILED', 'red');
    log('Multiple critical issues detected - please review deployment', 'red');
  }

  log('\n📚 Documentation:', 'cyan');
  log('• Implementation Guide: docs/PHASE_2_ADVANCED_INTELLIGENCE.md', 'blue');
  log('• API Documentation: docs/COMPETITOR_INTELLIGENCE_IMPLEMENTATION.md', 'blue');
  log('• Database Schema: src/lib/database/competitor-schema.sql', 'blue');

  return successRate >= 80;
}

// Run verification
if (require.main === module) {
  verifyPhase2Deployment()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      log(`\n❌ Verification failed: ${error.message}`, 'red');
      process.exit(1);
    });
}

module.exports = { verifyPhase2Deployment };
