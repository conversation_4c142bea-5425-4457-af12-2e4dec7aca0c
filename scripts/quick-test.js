#!/usr/bin/env node

/**
 * Швидкий тест для перевірки основної функціональності
 */

async function quickTest() {
  console.log('🧪 Швидкий тест покращеної сторінки філаментів\n');

  try {
    // Тест 1: Перевірка доступності сервера
    console.log('📋 Тест 1: Перевірка сервера');
    const response = await fetch('http://localhost:3002/marketplace/filament');
    console.log(`✅ Сервер доступний: ${response.status === 200 ? 'Так' : 'Ні'}`);
    console.log(`📊 Статус: ${response.status}`);
    console.log('');

    // Тест 2: Перевірка API здоров'я
    console.log('📋 Тест 2: Перевірка API');
    try {
      const apiResponse = await fetch('http://localhost:3002/api/filament/scrape', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          keyword: 'test',
          maxResults: 1,
          source: 'amazon'
        })
      });
      
      console.log(`✅ API доступний: ${apiResponse.status === 200 ? 'Так' : 'Ні'}`);
      console.log(`📊 Статус API: ${apiResponse.status}`);
      
      if (apiResponse.ok) {
        const data = await apiResponse.json();
        console.log(`📦 Отримано відповідь: ${data.success ? 'Успішно' : 'Помилка'}`);
      }
    } catch (apiError) {
      console.log(`❌ API недоступний: ${apiError.message}`);
    }
    console.log('');

    // Тест 3: Перевірка зображень
    console.log('📋 Тест 3: Перевірка зображень');
    const testImageUrl = 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400&h=400&fit=crop&crop=center';
    
    try {
      const imageResponse = await fetch(testImageUrl, { method: 'HEAD' });
      console.log(`✅ Зображення доступні: ${imageResponse.ok ? 'Так' : 'Ні'}`);
      console.log(`📊 Статус зображення: ${imageResponse.status}`);
    } catch (imageError) {
      console.log(`❌ Зображення недоступні: ${imageError.message}`);
    }
    console.log('');

    console.log('🎉 Швидкий тест завершено!');
    console.log('\n📖 Результати:');
    console.log('🌐 Сторінка філаментів: http://localhost:3002/marketplace/filament');
    console.log('🔧 API ендпоінти: /api/filament/scrape, /api/filament/compare, /api/filament/recommendations');
    console.log('🖼️ Зображення: Використовуються з Unsplash');

  } catch (error) {
    console.error('❌ Помилка під час тестування:', error.message);
    
    if (error.message.includes('fetch')) {
      console.log('\n💡 Поради:');
      console.log('1. Переконайтеся, що сервер запущено: npm run dev');
      console.log('2. Перевірте, що порт 3002 доступний');
      console.log('3. Перевірте інтернет-з\'єднання для зображень');
    }
  }
}

// Запуск тесту
quickTest().catch(console.error);
