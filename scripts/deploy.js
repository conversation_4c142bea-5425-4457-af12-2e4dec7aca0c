#!/usr/bin/env node

/**
 * Скрипт для автоматичного деплою 3D маркетплейсу на Cloudflare Pages
 * Включає ініціалізацію бази даних, секретів та деплой
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Кольори для консолі
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description, options = {}) {
  try {
    log(`\n${description}...`, 'cyan');
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options 
    });
    log(`✅ ${description} completed`, 'green');
    return output;
  } catch (error) {
    log(`❌ ${description} failed: ${error.message}`, 'red');
    if (!options.continueOnError) {
      throw error;
    }
    return null;
  }
}

function checkPrerequisites() {
  log('🔍 Перевірка передумов...', 'bright');
  
  // Перевіряємо wrangler
  try {
    execSync('wrangler --version', { stdio: 'pipe' });
    log('✅ Wrangler CLI встановлений', 'green');
  } catch (error) {
    log('❌ Wrangler CLI не встановлений. Встановіть: npm install -g wrangler', 'red');
    process.exit(1);
  }

  // Перевіряємо аутентифікацію
  try {
    execSync('wrangler whoami', { stdio: 'pipe' });
    log('✅ Wrangler аутентифікований', 'green');
  } catch (error) {
    log('❌ Wrangler не аутентифікований. Запустіть: wrangler login', 'red');
    process.exit(1);
  }

  // Перевіряємо файли проекту
  const requiredFiles = [
    'package.json',
    'wrangler.toml',
    'src/lib/auth/schema.sql'
  ];

  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      log(`❌ Файл ${file} не знайдено`, 'red');
      process.exit(1);
    }
  }
  
  log('✅ Всі передумови виконані', 'green');
}

function initializeCloudflareResources() {
  log('\n🚀 Ініціалізація Cloudflare ресурсів...', 'bright');
  
  // Ініціалізуємо аутентифікацію
  execCommand(
    'node scripts/init-auth-db.js',
    'Ініціалізація D1 баз даних та KV namespaces',
    { continueOnError: true }
  );
}

function buildProject() {
  log('\n🔨 Збірка проекту...', 'bright');
  
  // Очищуємо попередню збірку
  execCommand('rm -rf .next', 'Очищення попередньої збірки', { continueOnError: true });
  
  // Збираємо проект
  execCommand('npm run build', 'Збірка Next.js проекту');
}

function deployToCloudflare() {
  log('\n🚀 Деплой на Cloudflare Pages...', 'bright');
  
  // Деплоїмо з opennextjs-cloudflare
  execCommand(
    'npx @opennextjs/cloudflare@latest build',
    'Збірка для Cloudflare Pages'
  );
  
  execCommand(
    'wrangler pages deploy .vercel/output/static --project-name=3d-marketplace --compatibility-date=2024-01-01 --compatibility-flags=nodejs_compat',
    'Деплой на Cloudflare Pages'
  );
}

function setupSecrets() {
  log('\n🔐 Налаштування секретів...', 'bright');
  
  const secrets = [
    'NEXTAUTH_SECRET',
    'GOOGLE_CLIENT_SECRET',
    'GITHUB_CLIENT_SECRET',
    'STRIPE_SECRET_KEY'
  ];

  log('📝 Необхідно додати наступні секрети:', 'yellow');
  secrets.forEach(secret => {
    log(`   - ${secret}`, 'cyan');
  });

  log('\n💡 Для додавання секретів використовуйте:', 'blue');
  secrets.forEach(secret => {
    log(`   wrangler secret put ${secret} --env production`, 'cyan');
  });
}

function displayDeploymentInfo() {
  log('\n🎉 Деплой завершено!', 'bright');
  
  log('\n📋 Інформація про деплой:', 'blue');
  log('   • Проект: 3d-marketplace', 'cyan');
  log('   • Платформа: Cloudflare Pages', 'cyan');
  log('   • URL: https://3d-marketplace.pages.dev', 'cyan');
  
  log('\n🔧 Наступні кроки:', 'yellow');
  log('1. Додайте секрети через wrangler secret put', 'cyan');
  log('2. Налаштуйте Google OAuth redirect URIs', 'cyan');
  log('3. Перевірте роботу аутентифікації', 'cyan');
  log('4. Налаштуйте кастомний домен (опціонально)', 'cyan');
  
  log('\n📖 Документація:', 'blue');
  log('   • Google OAuth: docs/GOOGLE_AUTH_SETUP.md', 'cyan');
  log('   • Cloudflare: https://developers.cloudflare.com/pages/', 'cyan');
}

async function main() {
  log('🚀 Початок деплою 3D Marketplace на Cloudflare Pages', 'bright');
  
  try {
    // Перевіряємо передумови
    checkPrerequisites();
    
    // Ініціалізуємо Cloudflare ресурси
    initializeCloudflareResources();
    
    // Збираємо проект
    buildProject();
    
    // Деплоїмо на Cloudflare
    deployToCloudflare();
    
    // Показуємо інформацію про секрети
    setupSecrets();
    
    // Показуємо інформацію про деплой
    displayDeploymentInfo();
    
  } catch (error) {
    log(`\n❌ Деплой не вдався: ${error.message}`, 'red');
    log('\n🔍 Перевірте:', 'yellow');
    log('   • Чи встановлений та налаштований Wrangler CLI', 'cyan');
    log('   • Чи є доступ до Cloudflare акаунту', 'cyan');
    log('   • Чи правильно налаштований wrangler.toml', 'cyan');
    process.exit(1);
  }
}

// Запускаємо деплой
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
