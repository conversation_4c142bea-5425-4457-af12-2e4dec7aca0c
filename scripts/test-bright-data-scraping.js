/**
 * Тест Bright Data скрапінгу
 * Перевіряємо роботу MCP tools для скрапінгу 3D моделей
 */

async function testBrightDataScraping() {
  console.log('🚀 Початок тестування Bright Data скрапінгу...');

  try {
    // Тест 1: Пошук в Google
    console.log('\n📍 Тест 1: Пошук 3D моделей в Google');
    const searchResponse = await fetch('http://localhost:3000/api/scraping/enhanced', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'search-web',
        query: '3D printing models dragon',
        searchEngine: 'google'
      })
    });

    if (searchResponse.ok) {
      const searchData = await searchResponse.json();
      console.log('✅ Веб-пошук успішний:', searchData.success);
      if (searchData.data) {
        console.log('📄 Знайдено результатів:', searchData.data.length || 'N/A');
      }
    } else {
      console.log('❌ Помилка веб-пошуку:', searchResponse.status);
    }

    // Тест 2: Скрапінг популярних моделей
    console.log('\n📍 Тест 2: Скрапінг популярних моделей');
    const scrapeResponse = await fetch('http://localhost:3000/api/scrape', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'bright-data-scrape',
        count: 5
      })
    });

    if (scrapeResponse.ok) {
      const scrapeData = await scrapeResponse.json();
      console.log('✅ Скрапінг успішний:', scrapeData.success);
      console.log('📝 Повідомлення:', scrapeData.message);
      if (scrapeData.data) {
        console.log('📦 Знайдено моделей:', scrapeData.data.length);
      }
      if (scrapeData.stats) {
        console.log('📊 Статистика:', scrapeData.stats);
      }
    } else {
      console.log('❌ Помилка скрапінгу:', scrapeResponse.status);
    }

    // Тест 3: Перевірка збережених моделей
    console.log('\n📍 Тест 3: Перевірка збережених моделей');
    const modelsResponse = await fetch('http://localhost:3000/api/scraped-models?limit=5');
    
    if (modelsResponse.ok) {
      const modelsData = await modelsResponse.json();
      console.log('✅ Отримання моделей успішне');
      if (modelsData.data && modelsData.data.models) {
        console.log('📦 Моделей в базі:', modelsData.data.models.length);
        console.log('📊 Загальна кількість:', modelsData.data.pagination?.total || 'N/A');
        
        // Показуємо перші кілька моделей
        modelsData.data.models.slice(0, 3).forEach((model, index) => {
          console.log(`   ${index + 1}. ${model.title || model.name} (${model.platform || 'unknown'})`);
        });
      }
    } else {
      console.log('❌ Помилка отримання моделей:', modelsResponse.status);
    }

    // Тест 4: Генерація тестових даних
    console.log('\n📍 Тест 4: Генерація тестових даних');
    const fakeDataResponse = await fetch('http://localhost:3000/api/scrape', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'fake-data',
        count: 10
      })
    });

    if (fakeDataResponse.ok) {
      const fakeData = await fakeDataResponse.json();
      console.log('✅ Генерація тестових даних успішна:', fakeData.success);
      console.log('📝 Повідомлення:', fakeData.message);
    } else {
      console.log('❌ Помилка генерації тестових даних:', fakeDataResponse.status);
    }

    console.log('\n🎉 Тестування завершено!');

  } catch (error) {
    console.error('❌ Помилка під час тестування:', error.message);
  }
}

// Запуск тестування
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch');
  testBrightDataScraping();
} else {
  // Browser environment
  testBrightDataScraping();
}

module.exports = { testBrightDataScraping };
