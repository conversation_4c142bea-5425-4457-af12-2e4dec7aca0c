#!/usr/bin/env node

/**
 * Cloudflare Deployment Script for 3D Marketplace
 * Deploys the full stack to Cloudflare services
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Cloudflare deployment for 3D Marketplace...\n');

// Configuration
const config = {
  projectName: '3d-marketplace',
  accountId: process.env.CLOUDFLARE_ACCOUNT_ID,
  apiToken: process.env.CLOUDFLARE_API_TOKEN,
  domain: process.env.CUSTOM_DOMAIN || '3d-marketplace.pages.dev'
};

// Deployment steps
const deploymentSteps = [
  {
    name: 'Building Next.js application',
    command: 'npm run build',
    description: 'Creating optimized production build'
  },
  {
    name: 'Setting up Cloudflare D1 Database',
    command: 'wrangler d1 create marketplace_db',
    description: 'Creating serverless SQL database',
    optional: true
  },
  {
    name: 'Creating R2 Storage bucket',
    command: 'wrangler r2 bucket create marketplace-storage',
    description: 'Setting up object storage for 3D models',
    optional: true
  },
  {
    name: 'Creating KV namespace',
    command: 'wrangler kv:namespace create marketplace-kv',
    description: 'Setting up key-value storage for caching',
    optional: true
  },
  {
    name: 'Deploying to Cloudflare Pages',
    command: 'wrangler pages deploy .next --project-name=3d-marketplace --compatibility-date=2023-10-30',
    description: 'Deploying application to Cloudflare Pages'
  }
];

// Helper functions
function runCommand(command, options = {}) {
  try {
    console.log(`📦 Running: ${command}`);
    const result = execSync(command, { 
      stdio: 'inherit', 
      cwd: process.cwd(),
      ...options 
    });
    return true;
  } catch (error) {
    if (options.optional) {
      console.log(`⚠️  Optional step failed: ${error.message}`);
      return false;
    }
    console.error(`❌ Command failed: ${error.message}`);
    throw error;
  }
}

function checkPrerequisites() {
  console.log('🔍 Checking prerequisites...\n');
  
  // Check if wrangler is installed
  try {
    execSync('wrangler --version', { stdio: 'pipe' });
    console.log('✅ Wrangler CLI is installed');
  } catch (error) {
    console.log('❌ Wrangler CLI not found. Installing...');
    runCommand('npm install -g wrangler');
  }
  
  // Check if user is logged in
  try {
    execSync('wrangler whoami', { stdio: 'pipe' });
    console.log('✅ Authenticated with Cloudflare');
  } catch (error) {
    console.log('❌ Not authenticated. Please run: wrangler login');
    process.exit(1);
  }
  
  // Check if build directory exists
  if (!fs.existsSync('.next')) {
    console.log('⚠️  No build directory found. Will build first.');
  }
  
  console.log('');
}

function setupEnvironmentVariables() {
  console.log('🔧 Setting up environment variables...\n');
  
  const envVars = [
    'NEXTAUTH_URL=https://3d-marketplace.pages.dev',
    'NEXTAUTH_SECRET=your-secret-key-here',
    'NODE_ENV=production',
    'CLOUDFLARE_ACCOUNT_ID=' + (config.accountId || 'your-account-id'),
    'DATABASE_URL=d1://marketplace_db',
    'STORAGE_URL=r2://marketplace-storage'
  ];
  
  envVars.forEach(envVar => {
    const [key, value] = envVar.split('=');
    try {
      runCommand(`wrangler pages secret put ${key} --project-name=3d-marketplace`, {
        input: value,
        optional: true
      });
      console.log(`✅ Set ${key}`);
    } catch (error) {
      console.log(`⚠️  Could not set ${key}: ${error.message}`);
    }
  });
  
  console.log('');
}

function createWranglerConfig() {
  console.log('📝 Creating wrangler.toml configuration...\n');
  
  const wranglerConfig = `
name = "3d-marketplace"
compatibility_date = "2023-10-30"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "3d-marketplace"

[[env.production.d1_databases]]
binding = "DB"
database_name = "marketplace_db"
database_id = "your-database-id"

[[env.production.r2_buckets]]
binding = "STORAGE"
bucket_name = "marketplace-storage"

[[env.production.kv_namespaces]]
binding = "KV"
id = "your-kv-id"

[env.production.vars]
NODE_ENV = "production"
NEXTAUTH_URL = "https://3d-marketplace.pages.dev"
`;

  fs.writeFileSync('wrangler.toml', wranglerConfig.trim());
  console.log('✅ Created wrangler.toml configuration\n');
}

async function main() {
  try {
    console.log('🎨 3D Marketplace - Cloudflare Deployment\n');
    console.log('========================================\n');
    
    // Step 1: Check prerequisites
    checkPrerequisites();
    
    // Step 2: Create configuration
    createWranglerConfig();
    
    // Step 3: Run deployment steps
    console.log('🚀 Starting deployment process...\n');
    
    for (const step of deploymentSteps) {
      console.log(`📦 ${step.name}`);
      console.log(`   ${step.description}`);
      
      const success = runCommand(step.command, { optional: step.optional });
      
      if (success) {
        console.log(`✅ ${step.name} completed\n`);
      } else if (step.optional) {
        console.log(`⚠️  ${step.name} skipped (optional)\n`);
      }
    }
    
    // Step 4: Setup environment variables
    setupEnvironmentVariables();
    
    // Step 5: Final steps
    console.log('🎉 Deployment completed successfully!\n');
    console.log('📱 Your 3D Marketplace is now live at:');
    console.log(`   https://3d-marketplace.pages.dev\n`);
    
    console.log('🔧 Next steps:');
    console.log('   1. Visit your site and test functionality');
    console.log('   2. Go to /admin/scraper to generate test data');
    console.log('   3. Check /marketplace to see your models');
    console.log('   4. Configure custom domain if needed\n');
    
    console.log('📚 Useful commands:');
    console.log('   wrangler pages deployment list --project-name=3d-marketplace');
    console.log('   wrangler d1 info marketplace_db');
    console.log('   wrangler r2 bucket list\n');
    
  } catch (error) {
    console.error('❌ Deployment failed:', error.message);
    console.log('\n🆘 Troubleshooting:');
    console.log('   1. Make sure you are logged in: wrangler login');
    console.log('   2. Check your account ID and API token');
    console.log('   3. Verify your project builds locally: npm run build');
    console.log('   4. Check Cloudflare dashboard for any issues\n');
    process.exit(1);
  }
}

// Run deployment
if (require.main === module) {
  main();
}

module.exports = { main, runCommand, checkPrerequisites };
