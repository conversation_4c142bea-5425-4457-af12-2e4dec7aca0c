#!/usr/bin/env node

/**
 * Deploy script for the unified API worker
 * This script deploys the unified worker to Cloudflare
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const WORKER_NAME = '3d-marketplace-unified-api';
const WRANGLER_CONFIG = 'wrangler-unified.toml';

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📝',
    success: '✅',
    error: '❌',
    warning: '⚠️',
    step: '🔧'
  }[type] || '📝';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function runCommand(command, description) {
  log(`${description}...`, 'step');
  try {
    const result = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'pipe',
      cwd: process.cwd()
    });
    log(`${description} completed successfully`, 'success');
    return result;
  } catch (error) {
    log(`${description} failed: ${error.message}`, 'error');
    throw error;
  }
}

function checkPrerequisites() {
  log('Checking prerequisites...', 'step');
  
  // Check if wrangler is installed
  try {
    execSync('wrangler --version', { stdio: 'pipe' });
    log('Wrangler CLI is available', 'success');
  } catch (error) {
    log('Wrangler CLI not found. Please install it with: npm install -g wrangler', 'error');
    process.exit(1);
  }
  
  // Check if wrangler config exists
  if (!fs.existsSync(WRANGLER_CONFIG)) {
    log(`Wrangler config file ${WRANGLER_CONFIG} not found`, 'error');
    process.exit(1);
  }
  
  // Check if worker file exists
  const workerPath = 'src/workers/unified-api-worker.ts';
  if (!fs.existsSync(workerPath)) {
    log(`Worker file ${workerPath} not found`, 'error');
    process.exit(1);
  }
  
  log('All prerequisites met', 'success');
}

function deployWorker() {
  log(`Deploying ${WORKER_NAME} to Cloudflare...`, 'step');
  
  try {
    // Deploy using wrangler
    const deployCommand = `wrangler deploy --config ${WRANGLER_CONFIG}`;
    const result = runCommand(deployCommand, 'Deploying worker');
    
    // Extract worker URL from output
    const urlMatch = result.match(/https:\/\/[^\s]+/);
    const workerUrl = urlMatch ? urlMatch[0] : `https://${WORKER_NAME}.gcp-inspiration.workers.dev`;
    
    log(`Worker deployed successfully!`, 'success');
    log(`Worker URL: ${workerUrl}`, 'info');
    
    return workerUrl;
  } catch (error) {
    log(`Deployment failed: ${error.message}`, 'error');
    throw error;
  }
}

function testWorker(workerUrl) {
  log('Testing deployed worker...', 'step');
  
  try {
    // Test health endpoint
    const testCommand = `curl -s "${workerUrl}/health"`;
    const result = runCommand(testCommand, 'Testing health endpoint');
    
    const healthData = JSON.parse(result);
    if (healthData.success && healthData.data.status === 'healthy') {
      log('Worker health check passed', 'success');
    } else {
      log('Worker health check failed', 'warning');
      log(`Health response: ${result}`, 'info');
    }
    
    // Test trending endpoint
    const trendingCommand = `curl -s "${workerUrl}/api/bright-data/trending?platform=printables&limit=3"`;
    const trendingResult = runCommand(trendingCommand, 'Testing trending endpoint');
    
    const trendingData = JSON.parse(trendingResult);
    if (trendingData.success && trendingData.data) {
      log(`Trending endpoint working - got ${trendingData.data.models?.length || 0} models`, 'success');
    } else {
      log('Trending endpoint test failed', 'warning');
      log(`Trending response: ${trendingResult}`, 'info');
    }
    
  } catch (error) {
    log(`Worker testing failed: ${error.message}`, 'warning');
    log('Worker deployed but tests failed - this might be expected for new deployments', 'info');
  }
}

function updateEnvironmentFile(workerUrl) {
  log('Updating environment configuration...', 'step');
  
  try {
    const envPath = '.env.local';
    let envContent = '';
    
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');
    }
    
    // Update or add worker URL
    const workerUrlLine = `NEXT_PUBLIC_API_WORKER_URL=${workerUrl}`;
    const apiUrlLine = `API_WORKER_URL=${workerUrl}`;
    
    if (envContent.includes('NEXT_PUBLIC_API_WORKER_URL=')) {
      envContent = envContent.replace(/NEXT_PUBLIC_API_WORKER_URL=.*/, workerUrlLine);
    } else {
      envContent += `\n${workerUrlLine}`;
    }
    
    if (envContent.includes('API_WORKER_URL=')) {
      envContent = envContent.replace(/API_WORKER_URL=.*/, apiUrlLine);
    } else {
      envContent += `\n${apiUrlLine}`;
    }
    
    fs.writeFileSync(envPath, envContent);
    log('Environment configuration updated', 'success');
    
  } catch (error) {
    log(`Failed to update environment: ${error.message}`, 'warning');
  }
}

function main() {
  log(`Starting deployment of ${WORKER_NAME}`, 'info');
  
  try {
    checkPrerequisites();
    const workerUrl = deployWorker();
    testWorker(workerUrl);
    updateEnvironmentFile(workerUrl);
    
    log('🎉 Deployment completed successfully!', 'success');
    log(`Your unified API worker is now available at: ${workerUrl}`, 'info');
    log('You can now test the API endpoints:', 'info');
    log(`  Health: ${workerUrl}/health`, 'info');
    log(`  Trending: ${workerUrl}/api/bright-data/trending`, 'info');
    log(`  Popular: ${workerUrl}/api/bright-data/popular`, 'info');
    log(`  Stats: ${workerUrl}/api/stats`, 'info');
    
  } catch (error) {
    log(`Deployment failed: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Run the deployment
if (require.main === module) {
  main();
}

module.exports = { main };
