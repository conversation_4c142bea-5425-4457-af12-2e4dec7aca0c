#!/usr/bin/env node

/**
 * Cloudflare Environment Setup Script
 * Automates the setup of Cloudflare infrastructure for 3D Marketplace
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description) {
  try {
    log(`\n🔄 ${description}...`, 'blue');
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${description} completed`, 'green');
    return result;
  } catch (error) {
    log(`❌ ${description} failed: ${error.message}`, 'red');
    throw error;
  }
}

function checkPrerequisites() {
  log('\n📋 Checking Prerequisites...', 'cyan');
  
  try {
    // Check Wrangler installation
    const wranglerVersion = execSync('wrangler --version', { encoding: 'utf8' });
    log(`✅ Wrangler installed: ${wranglerVersion.trim()}`, 'green');
    
    // Check authentication
    const whoami = execSync('wrangler whoami', { encoding: 'utf8' });
    if (whoami.includes('You are logged in')) {
      log('✅ Wrangler authenticated', 'green');
    } else {
      throw new Error('Wrangler not authenticated');
    }
    
    // Check Node.js version
    const nodeVersion = process.version;
    log(`✅ Node.js version: ${nodeVersion}`, 'green');
    
    return true;
  } catch (error) {
    log(`❌ Prerequisites check failed: ${error.message}`, 'red');
    return false;
  }
}

function setupDatabases() {
  log('\n🗄️ Setting up D1 Databases...', 'cyan');
  
  const environments = ['production', 'staging', 'development'];
  const databases = {
    production: '3d-marketplace-enhanced',
    staging: '3d-marketplace-staging',
    development: '3d-marketplace-development'
  };
  
  environments.forEach(env => {
    try {
      // Check if database exists
      const dbList = execSync('wrangler d1 list', { encoding: 'utf8' });
      
      if (!dbList.includes(databases[env])) {
        execCommand(
          `wrangler d1 create ${databases[env]} --env ${env}`,
          `Creating ${env} database`
        );
      } else {
        log(`✅ Database ${databases[env]} already exists`, 'green');
      }
      
      // Apply schema if schema.sql exists
      if (fs.existsSync('schema.sql')) {
        execCommand(
          `wrangler d1 execute ${databases[env]} --env ${env} --file schema.sql`,
          `Applying schema to ${env} database`
        );
      }
    } catch (error) {
      log(`⚠️ Database setup for ${env} failed: ${error.message}`, 'yellow');
    }
  });
}

function verifyStorage() {
  log('\n🪣 Verifying Storage Setup...', 'cyan');
  
  try {
    // Check R2 buckets
    const r2Buckets = execSync('wrangler r2 bucket list', { encoding: 'utf8' });
    const requiredBuckets = [
      '3d-marketplace-models-prod',
      '3d-marketplace-models-staging',
      '3d-marketplace-models-dev'
    ];
    
    requiredBuckets.forEach(bucket => {
      if (r2Buckets.includes(bucket)) {
        log(`✅ R2 bucket ${bucket} exists`, 'green');
      } else {
        log(`⚠️ R2 bucket ${bucket} not found`, 'yellow');
      }
    });
    
    // Check KV namespaces
    const kvNamespaces = execSync('wrangler kv:namespace list', { encoding: 'utf8' });
    log('✅ KV namespaces verified', 'green');
    
  } catch (error) {
    log(`⚠️ Storage verification failed: ${error.message}`, 'yellow');
  }
}

function deployApplication() {
  log('\n🚀 Deploying Application...', 'cyan');
  
  try {
    // Install dependencies
    execCommand('npm install', 'Installing dependencies');
    
    // Build application
    execCommand('npm run build', 'Building application');
    
    // Deploy workers
    execCommand('wrangler deploy --env production', 'Deploying production worker');
    
    // Build and deploy pages
    if (fs.existsSync('package.json')) {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      if (packageJson.scripts && packageJson.scripts['pages:build']) {
        execCommand('npm run pages:build', 'Building Pages application');
        execCommand(
          'wrangler pages deploy .vercel/output/static --project-name=3d-marketplace --env production',
          'Deploying Pages application'
        );
      }
    }
    
  } catch (error) {
    log(`❌ Application deployment failed: ${error.message}`, 'red');
    throw error;
  }
}

function setupSecrets() {
  log('\n🔐 Setting up Secrets...', 'cyan');
  log('⚠️ Manual secret setup required. Please run:', 'yellow');
  
  const requiredSecrets = [
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
    'GITHUB_CLIENT_ID',
    'GITHUB_CLIENT_SECRET',
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'STRIPE_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET'
  ];
  
  requiredSecrets.forEach(secret => {
    log(`wrangler secret put ${secret} --env production`, 'blue');
  });
}

function testDeployment() {
  log('\n🧪 Testing Deployment...', 'cyan');
  
  try {
    // Test worker deployment
    const workerList = execSync('wrangler list', { encoding: 'utf8' });
    if (workerList.includes('3d-marketplace')) {
      log('✅ Worker deployed successfully', 'green');
    }
    
    // Test database connection
    execCommand(
      'wrangler d1 execute 3d-marketplace-enhanced --env production --command "SELECT 1;"',
      'Testing database connection'
    );
    
    log('✅ Basic deployment tests passed', 'green');
    
  } catch (error) {
    log(`⚠️ Some tests failed: ${error.message}`, 'yellow');
  }
}

function main() {
  log('🚀 Starting Cloudflare Environment Setup for 3D Marketplace', 'bright');
  log('=' * 60, 'cyan');
  
  try {
    // Check prerequisites
    if (!checkPrerequisites()) {
      log('\n❌ Prerequisites not met. Please fix issues and try again.', 'red');
      process.exit(1);
    }
    
    // Setup databases
    setupDatabases();
    
    // Verify storage
    verifyStorage();
    
    // Deploy application
    deployApplication();
    
    // Setup secrets (manual step)
    setupSecrets();
    
    // Test deployment
    testDeployment();
    
    log('\n🎉 Cloudflare Environment Setup Complete!', 'green');
    log('\n📚 Next Steps:', 'cyan');
    log('1. Configure secrets using the commands shown above', 'blue');
    log('2. Test your application at https://3d-marketplace.pages.dev', 'blue');
    log('3. Review the monitoring dashboard', 'blue');
    log('4. Set up Bright Data integration if needed', 'blue');
    
  } catch (error) {
    log(`\n💥 Setup failed: ${error.message}`, 'red');
    log('\n📖 Check the troubleshooting guide in docs/CLOUDFLARE_ENVIRONMENT_SETUP.md', 'yellow');
    process.exit(1);
  }
}

// Run the setup
if (require.main === module) {
  main();
}

module.exports = {
  checkPrerequisites,
  setupDatabases,
  verifyStorage,
  deployApplication,
  testDeployment
};
