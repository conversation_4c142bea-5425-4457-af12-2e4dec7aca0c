#!/usr/bin/env node

/**
 * Add Edge Runtime to API routes for Cloudflare Pages compatibility
 */

const fs = require('fs');
const path = require('path');

const API_ROUTES_DIR = 'src/app/api';

// Routes that should use edge runtime for Cloudflare Pages
const EDGE_RUNTIME_ROUTES = [
  'mcp/bright-data/route.ts',
  'scraping/popular-models/route.ts',
  'bright-data/popular/route.ts',
  'bright-data/trending/route.ts',
  'bright-data/recent/route.ts',
  'models/route.ts',
  'scraped-models/route.ts',
  'marketplace/trending/route.ts',
  'marketplace/stats/route.ts',
  'stats/route.ts'
];

function addEdgeRuntime(filePath) {
  try {
    const fullPath = path.join(API_ROUTES_DIR, filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️ File not found: ${fullPath}`);
      return false;
    }

    let content = fs.readFileSync(fullPath, 'utf8');
    
    // Check if edge runtime is already added
    if (content.includes("export const runtime = 'edge'")) {
      console.log(`✅ Edge runtime already present: ${filePath}`);
      return true;
    }

    // Find the first import statement and add edge runtime after imports
    const lines = content.split('\n');
    let insertIndex = -1;
    let lastImportIndex = -1;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Find last import statement
      if (line.startsWith('import ') || line.startsWith('import{') || line.startsWith('import {')) {
        lastImportIndex = i;
      }
      
      // Find first non-import, non-comment, non-empty line after imports
      if (lastImportIndex >= 0 && 
          !line.startsWith('import ') && 
          !line.startsWith('import{') && 
          !line.startsWith('import {') &&
          !line.startsWith('//') && 
          !line.startsWith('/*') && 
          !line.startsWith('*') &&
          line.length > 0) {
        insertIndex = i;
        break;
      }
    }

    if (insertIndex === -1) {
      insertIndex = lastImportIndex + 1;
    }

    // Insert edge runtime export
    lines.splice(insertIndex, 0, '', "export const runtime = 'edge';");
    
    const newContent = lines.join('\n');
    fs.writeFileSync(fullPath, newContent, 'utf8');
    
    console.log(`✅ Added edge runtime to: ${filePath}`);
    return true;

  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🚀 Adding Edge Runtime to API routes for Cloudflare Pages...');
  console.log('=' .repeat(60));

  let successCount = 0;
  let totalCount = 0;

  for (const route of EDGE_RUNTIME_ROUTES) {
    totalCount++;
    if (addEdgeRuntime(route)) {
      successCount++;
    }
  }

  console.log('=' .repeat(60));
  console.log(`📊 Summary: ${successCount}/${totalCount} routes updated`);
  
  if (successCount === totalCount) {
    console.log('🎉 All API routes are now compatible with Cloudflare Pages!');
  } else {
    console.log('⚠️ Some routes may need manual attention');
  }
}

if (require.main === module) {
  main();
}

module.exports = { addEdgeRuntime, EDGE_RUNTIME_ROUTES };
