#!/usr/bin/env node

/**
 * Simple deployment script for quick deployment
 */

const { execSync } = require('child_process');

function exec(command, description) {
  console.log(`🔄 ${description}...`);
  try {
    const result = execSync(command, { 
      stdio: 'inherit',
      encoding: 'utf8'
    });
    console.log(`✅ ${description} completed`);
    return result;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    throw error;
  }
}

async function simpleDeploy() {
  try {
    console.log('🚀 Simple Enhanced Marketplace Deployment\n');
    
    // Check prerequisites
    console.log('📋 Checking prerequisites...');
    exec('wrangler whoami', 'Checking Cloudflare authentication');
    
    // Clean and prepare
    console.log('\n🧹 Preparing for deployment...');
    exec('rm -rf .next .vercel/output', 'Cleaning build directories');
    
    // Build with minimal configuration
    console.log('\n🔨 Building application...');
    process.env.NODE_ENV = 'production';
    process.env.SKIP_TYPE_CHECK = 'true';
    
    exec('npm run build', 'Building Next.js application');
    
    // Build for Cloudflare
    console.log('\n☁️ Building for Cloudflare...');
    exec('npx @opennextjs/cloudflare@latest build', 'Building for Cloudflare Pages');
    
    // Deploy
    console.log('\n🚀 Deploying to Cloudflare Pages...');
    exec('wrangler pages deploy .vercel/output/static --project-name=3d-marketplace --env=production --commit-dirty=true', 'Deploying to Cloudflare Pages');
    
    console.log('\n🎉 Deployment completed successfully!');
    console.log('🌐 Your marketplace should be available at: https://3d-marketplace.pages.dev');
    
  } catch (error) {
    console.error('\n❌ Deployment failed:', error.message);
    console.log('\n💡 Try these troubleshooting steps:');
    console.log('1. Check your Cloudflare authentication: wrangler whoami');
    console.log('2. Verify your project exists: wrangler pages project list');
    console.log('3. Check build output: ls -la .vercel/output/static');
    process.exit(1);
  }
}

if (require.main === module) {
  simpleDeploy().catch(console.error);
}

module.exports = simpleDeploy;
