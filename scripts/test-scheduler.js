/**
 * Тестовий скрипт для перевірки системи планувальника
 */

const { SchedulerManager } = require('../src/lib/scheduler/scheduler-manager');
const { validateCronExpression, cronToHuman } = require('../src/lib/scheduler/cron-utils');

async function testScheduler() {
  console.log('🧪 Тестування системи планувальника...\n');

  // Тест 1: Валідація cron виразів
  console.log('1️⃣ Тестування валідації cron виразів:');
  const testCrons = [
    '0 2 * * *',      // Щоденно о 2:00 - валідний
    '0 */6 * * *',    // Кожні 6 годин - валідний
    '* * * * *',      // Кожну хвилину - валідний
    'invalid cron',   // Невалідний
    '60 25 * * *'     // Невалідний (60 хвилин, 25 година)
  ];

  testCrons.forEach(cron => {
    const isValid = validateCronExpression(cron);
    const human = isValid ? cronToHuman(cron) : 'Невалідний';
    console.log(`  ${cron} -> ${isValid ? '✅' : '❌'} ${human}`);
  });

  console.log('\n2️⃣ Тестування SchedulerManager:');
  
  try {
    const manager = new SchedulerManager();
    console.log('  ✅ SchedulerManager створено успішно');

    // Тест створення завдання
    const jobRequest = {
      name: 'Тестове завдання',
      description: 'Тест автоматичного скрапінгу',
      cronExpression: '0 2 * * *',
      platforms: ['makerworld', 'printables'],
      modelsPerPlatform: 10
    };

    console.log('  📝 Створення тестового завдання...');
    const job = await manager.createJob(jobRequest);
    console.log(`  ✅ Завдання створено: ${job.name} (${job.id})`);

    // Тест отримання завдань
    const jobs = manager.getAllJobs();
    console.log(`  📋 Всього завдань: ${jobs.length}`);

    // Тест статистики
    const stats = await manager.getStats();
    console.log(`  📊 Статистика: ${stats.totalJobs} завдань, ${stats.activeJobs} активних`);

    // Тест видалення завдання
    await manager.deleteJob(job.id);
    console.log(`  🗑️ Завдання видалено: ${job.id}`);

    console.log('  ✅ Всі тести SchedulerManager пройдені успішно!');

  } catch (error) {
    console.error('  ❌ Помилка тестування SchedulerManager:', error.message);
  }

  console.log('\n🎉 Тестування завершено!');
}

// Запуск тестів
if (require.main === module) {
  testScheduler().catch(console.error);
}

module.exports = { testScheduler };
