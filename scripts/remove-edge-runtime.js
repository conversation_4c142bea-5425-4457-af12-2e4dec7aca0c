#!/usr/bin/env node

/**
 * Remove Edge Runtime from API routes for OpenNext compatibility
 */

const fs = require('fs');
const { execSync } = require('child_process');

function removeEdgeRuntime() {
  try {
    // Find all files with edge runtime
    const files = execSync("find src/app/api -name '*.ts' -exec grep -l \"export const runtime = 'edge'\" {} \\;", { encoding: 'utf8' })
      .trim()
      .split('\n')
      .filter(file => file.length > 0);

    console.log('🔧 Removing Edge Runtime from API routes for OpenNext compatibility...');
    console.log('=' .repeat(70));

    let processedCount = 0;

    for (const file of files) {
      try {
        let content = fs.readFileSync(file, 'utf8');
        
        // Replace edge runtime export with comment
        const originalContent = content;
        content = content.replace(
          /export const runtime = 'edge';/g,
          "// Note: Edge runtime disabled for OpenNext compatibility\n// export const runtime = 'edge';"
        );

        if (content !== originalContent) {
          fs.writeFileSync(file, content, 'utf8');
          console.log(`✅ Removed edge runtime from: ${file}`);
          processedCount++;
        } else {
          console.log(`⚠️ No changes needed: ${file}`);
        }

      } catch (error) {
        console.error(`❌ Error processing ${file}:`, error.message);
      }
    }

    console.log('=' .repeat(70));
    console.log(`📊 Summary: ${processedCount} files updated`);
    console.log('🎉 All API routes are now compatible with OpenNext!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  removeEdgeRuntime();
}

module.exports = { removeEdgeRuntime };
