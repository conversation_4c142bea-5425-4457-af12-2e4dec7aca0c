#!/usr/bin/env node

/**
 * Швидкий тест MCP токена та Bright Data інтеграції
 */

const fs = require('fs');
const path = require('path');

// Кольори для консолі
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('');
  log('='.repeat(60), 'cyan');
  log(`🔧 ${message}`, 'bold');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️ ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️ ${message}`, 'blue');
}

async function testMCPToken() {
  logHeader('Тест MCP токена та Bright Data інтеграції');

  let allTestsPassed = true;

  // 1. Перевірка файлу .env.local
  logInfo('1. Перевірка файлу .env.local...');
  
  const envPath = path.join(process.cwd(), '.env.local');
  if (!fs.existsSync(envPath)) {
    logError('Файл .env.local не знайдено');
    allTestsPassed = false;
  } else {
    logSuccess('Файл .env.local знайдено');
    
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    // Перевірка MCP токена
    if (envContent.includes('MCP_API_TOKEN=')) {
      const tokenMatch = envContent.match(/MCP_API_TOKEN=([^\n\r]+)/);
      if (tokenMatch && tokenMatch[1]) {
        const token = tokenMatch[1].trim();
        if (token === 'e7682aa6-8c84-4941-8f29-ec1d3ff2c9c4') {
          logSuccess(`MCP токен знайдено: ${token.substring(0, 8)}...`);
        } else {
          logWarning(`MCP токен відрізняється: ${token.substring(0, 8)}...`);
        }
      } else {
        logError('MCP токен порожній');
        allTestsPassed = false;
      }
    } else {
      logError('MCP_API_TOKEN не знайдено в .env.local');
      allTestsPassed = false;
    }

    // Перевірка інших змінних
    const requiredVars = [
      'BRIGHT_DATA_ENABLED',
      'SCRAPING_TIMEOUT_MS',
      'SCRAPING_RETRY_ATTEMPTS',
      'SCRAPING_RATE_LIMIT_MS'
    ];

    requiredVars.forEach(varName => {
      if (envContent.includes(`${varName}=`)) {
        logSuccess(`${varName} налаштовано`);
      } else {
        logWarning(`${varName} не знайдено`);
      }
    });
  }

  // 2. Перевірка змінних середовища
  logInfo('2. Перевірка змінних середовища...');
  
  // Завантажуємо .env.local
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = {};
    
    envContent.split('\n').forEach(line => {
      const match = line.match(/^([^#][^=]+)=(.*)$/);
      if (match) {
        envVars[match[1].trim()] = match[2].trim();
      }
    });

    // Встановлюємо змінні середовища
    Object.assign(process.env, envVars);
  }

  const mcpToken = process.env.MCP_API_TOKEN;
  if (mcpToken) {
    logSuccess(`MCP токен доступний: ${mcpToken.substring(0, 8)}...`);
  } else {
    logError('MCP токен не доступний через process.env');
    allTestsPassed = false;
  }

  // 3. Перевірка .gitignore
  logInfo('3. Перевірка .gitignore...');
  
  const gitignorePath = path.join(process.cwd(), '.gitignore');
  if (fs.existsSync(gitignorePath)) {
    const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
    
    if (gitignoreContent.includes('.kilocode')) {
      logSuccess('.kilocode додано в .gitignore');
    } else {
      logWarning('.kilocode не знайдено в .gitignore');
    }

    if (gitignoreContent.includes('.env.local')) {
      logSuccess('.env.local додано в .gitignore');
    } else {
      logWarning('.env.local не знайдено в .gitignore');
    }
  } else {
    logError('.gitignore не знайдено');
    allTestsPassed = false;
  }

  // 4. Перевірка структури файлів
  logInfo('4. Перевірка структури файлів Bright Data MCP...');
  
  const requiredFiles = [
    'src/lib/bright-data/enhanced-mcp-client.ts',
    'src/lib/bright-data/automated-scraper.ts',
    'src/lib/bright-data/ai-model-analyzer.ts',
    'src/lib/bright-data/scraping-scheduler.ts',
    'src/app/api/scraping/automated/route.ts',
    'src/components/admin/scraping-dashboard.tsx',
    'examples/bright-data-mcp-usage.ts'
  ];

  requiredFiles.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
      logSuccess(`${filePath} ✓`);
    } else {
      logError(`${filePath} не знайдено`);
      allTestsPassed = false;
    }
  });

  // 5. Перевірка package.json скриптів
  logInfo('5. Перевірка package.json скриптів...');
  
  const packagePath = path.join(process.cwd(), 'package.json');
  if (fs.existsSync(packagePath)) {
    const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    const scripts = packageContent.scripts || {};

    const requiredScripts = [
      'demo:bright-data',
      'test:bright-data',
      'check:env'
    ];

    requiredScripts.forEach(scriptName => {
      if (scripts[scriptName]) {
        logSuccess(`Скрипт ${scriptName} налаштовано`);
      } else {
        logError(`Скрипт ${scriptName} не знайдено`);
        allTestsPassed = false;
      }
    });
  } else {
    logError('package.json не знайдено');
    allTestsPassed = false;
  }

  // 6. Симуляція MCP виклику
  logInfo('6. Симуляція MCP виклику...');
  
  try {
    // Імітуємо створення MCP клієнта
    const mockConfig = {
      mcpApiToken: process.env.MCP_API_TOKEN,
      timeout: parseInt(process.env.SCRAPING_TIMEOUT_MS || '20000'),
      retryAttempts: parseInt(process.env.SCRAPING_RETRY_ATTEMPTS || '2'),
      rateLimitDelay: parseInt(process.env.SCRAPING_RATE_LIMIT_MS || '1000'),
      enableFallback: true
    };

    logSuccess('MCP клієнт конфігурація:');
    console.log(`   Токен: ${mockConfig.mcpApiToken ? mockConfig.mcpApiToken.substring(0, 8) + '...' : 'Відсутній'}`);
    console.log(`   Таймаут: ${mockConfig.timeout}ms`);
    console.log(`   Повтори: ${mockConfig.retryAttempts}`);
    console.log(`   Rate Limit: ${mockConfig.rateLimitDelay}ms`);
    console.log(`   Fallback: ${mockConfig.enableFallback ? 'Увімкнено' : 'Вимкнено'}`);

    if (mockConfig.mcpApiToken) {
      logSuccess('Симуляція аутентифікованого MCP виклику пройшла успішно');
    } else {
      logWarning('MCP токен відсутній, буде використано fallback режим');
    }

  } catch (error) {
    logError(`Помилка симуляції MCP виклику: ${error.message}`);
    allTestsPassed = false;
  }

  // 7. Перевірка wrangler.toml
  logInfo('7. Перевірка wrangler.toml...');
  
  const wranglerPath = path.join(process.cwd(), 'wrangler.toml');
  if (fs.existsSync(wranglerPath)) {
    const wranglerContent = fs.readFileSync(wranglerPath, 'utf8');
    
    if (wranglerContent.includes('SCRAPING_COORDINATOR')) {
      logSuccess('Durable Objects для скрапінгу налаштовано');
    } else {
      logWarning('Durable Objects для скрапінгу не знайдено');
    }

    if (wranglerContent.includes('SCRAPING_CACHE')) {
      logSuccess('KV namespaces для кешування налаштовано');
    } else {
      logWarning('KV namespaces для кешування не знайдено');
    }

    if (wranglerContent.includes('crons')) {
      logSuccess('Cron triggers для автоматизації налаштовано');
    } else {
      logWarning('Cron triggers не знайдено');
    }
  } else {
    logWarning('wrangler.toml не знайдено (опціонально для локальної розробки)');
  }

  // Підсумок
  logHeader('Підсумок тестування');
  
  if (allTestsPassed) {
    logSuccess('🎉 Всі тести пройшли успішно!');
    logInfo('Система готова до використання:');
    console.log('   • npm run demo:bright-data    - Демонстрація функцій');
    console.log('   • npm run test:bright-data    - Запуск тестів');
    console.log('   • npm run dev                 - Локальний сервер');
    console.log('   • http://localhost:3000/admin/scraping - Адмін панель');
  } else {
    logError('❌ Деякі тести не пройшли');
    logInfo('Рекомендації:');
    console.log('   • Перевірте файл .env.local');
    console.log('   • Переконайтеся, що всі файли на місці');
    console.log('   • Запустіть npm install');
  }

  return allTestsPassed;
}

// Запуск тестів
if (require.main === module) {
  testMCPToken()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      logError(`Критична помилка: ${error.message}`);
      process.exit(1);
    });
}

module.exports = { testMCPToken };
