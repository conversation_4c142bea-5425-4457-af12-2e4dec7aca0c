#!/usr/bin/env node

/**
 * Тестовий скрипт для перевірки скрапінгу філаментів
 * Використання: node scripts/test-filament-scraping.js
 */

const API_BASE = 'http://localhost:3002';

async function testFilamentScraping() {
  console.log('🧪 Тестування скрапінгу філаментів...\n');

  try {
    // Тест 1: Скрапінг з ключовим словом
    console.log('📋 Тест 1: Скрапінг PLA філаментів');
    const response1 = await fetch(`${API_BASE}/api/filament/scrape`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-session-id': 'test-session',
      },
      body: JSON.stringify({
        keyword: 'PLA filament 1.75mm',
        maxResults: 5,
        source: 'amazon',
        useCache: false
      })
    });

    const result1 = await response1.json();
    console.log(`✅ Результат: ${result1.success ? 'Успішно' : 'Помилка'}`);
    console.log(`📊 Знайдено товарів: ${result1.data?.products?.length || 0}`);
    
    if (result1.data?.products?.length > 0) {
      const firstProduct = result1.data.products[0];
      console.log(`🎯 Перший товар: ${firstProduct.title}`);
      console.log(`💰 Ціна: $${firstProduct.price}`);
      console.log(`⭐ Рейтинг: ${firstProduct.rating}`);
    }
    console.log('');

    // Тест 2: Отримання трендових філаментів
    console.log('📋 Тест 2: Трендові філаменти');
    const response2 = await fetch(`${API_BASE}/api/filament/scrape?limit=3`, {
      method: 'PUT',
      headers: {
        'x-session-id': 'test-session',
      }
    });

    const result2 = await response2.json();
    console.log(`✅ Результат: ${result2.success ? 'Успішно' : 'Помилка'}`);
    console.log(`📈 Трендових товарів: ${result2.data?.trending?.length || 0}`);
    console.log(`📊 Популярних товарів: ${result2.data?.popular?.length || 0}`);
    console.log('');

    // Тест 3: Порівняння філаментів
    console.log('📋 Тест 3: Порівняння філаментів');
    const response3 = await fetch(`${API_BASE}/api/filament/compare`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-session-id': 'test-session',
      },
      body: JSON.stringify({
        filamentIds: ['1', '2', '3'],
        includeAnalytics: true
      })
    });

    const result3 = await response3.json();
    console.log(`✅ Результат: ${result3.success ? 'Успішно' : 'Помилка'}`);
    
    if (result3.data?.analysis) {
      const analysis = result3.data.analysis;
      console.log(`💰 Ціновий діапазон: $${analysis.priceRange.min} - $${analysis.priceRange.max}`);
      console.log(`⭐ Рейтинговий діапазон: ${analysis.ratingRange.min} - ${analysis.ratingRange.max}`);
      console.log(`🏆 Найкраща цінність: ${analysis.bestValue.title}`);
      console.log(`🔥 Найпопулярніший: ${analysis.mostPopular.title}`);
    }
    console.log('');

    // Тест 4: Рекомендації
    console.log('📋 Тест 4: Рекомендації філаментів');
    const response4 = await fetch(`${API_BASE}/api/filament/recommendations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-session-id': 'test-session',
      },
      body: JSON.stringify({
        userId: 'test-user',
        basedOn: 'similar',
        filamentId: '1',
        preferences: {
          priceRange: [20, 40],
          materials: ['PLA', 'PETG']
        },
        limit: 5
      })
    });

    const result4 = await response4.json();
    console.log(`✅ Результат: ${result4.success ? 'Успішно' : 'Помилка'}`);
    console.log(`🎯 Рекомендацій: ${result4.data?.recommendations?.length || 0}`);
    
    if (result4.data?.recommendations?.length > 0) {
      const topRecommendation = result4.data.recommendations[0];
      console.log(`🥇 Топ рекомендація: ${topRecommendation.title}`);
      console.log(`📈 Скор: ${topRecommendation.recommendationScore?.toFixed(1)}`);
      console.log(`💡 Причина: ${topRecommendation.recommendationReason}`);
    }
    console.log('');

    console.log('🎉 Всі тести завершено успішно!');

  } catch (error) {
    console.error('❌ Помилка під час тестування:', error.message);
    
    if (error.message.includes('fetch')) {
      console.log('💡 Переконайтеся, що сервер запущено на http://localhost:3002');
      console.log('💡 Запустіть: npm run dev');
    }
  }
}

// Функція для тестування зображень
async function testImageGeneration() {
  console.log('\n🖼️ Тестування генерації зображень...\n');

  const testCases = [
    { material: 'PLA', color: 'black' },
    { material: 'PETG', color: 'transparent' },
    { material: 'TPU', color: 'white' },
    { material: 'ABS', color: 'red' },
    { material: 'PLA', color: 'galaxy blue' },
    { material: 'PLA', color: 'wood' }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`🎨 Тест ${index + 1}: ${testCase.material} - ${testCase.color}`);
    
    // Симуляція генерації URL (в реальності це буде імпорт з utils)
    const baseImages = [
      'photo-1581833971358-2c8b550f87b3',
      'photo-1612198188060-c7c2a3b66eae', 
      'photo-1606107557195-0e29a4b5b4aa',
      'photo-1599305445671-ac291c95aaa9'
    ];
    
    const imageIndex = testCase.material === 'PLA' ? 0 : 
                     testCase.material === 'PETG' ? 1 :
                     testCase.material === 'TPU' ? 2 : 3;
    
    const baseImage = baseImages[imageIndex];
    const url = `https://images.unsplash.com/${baseImage}?w=400&h=400&fit=crop&crop=center`;
    
    console.log(`🔗 URL: ${url}`);
    console.log('✅ Зображення згенеровано');
    console.log('');
  });

  console.log('🎉 Тестування зображень завершено!');
}

// Запуск тестів
async function runAllTests() {
  console.log('🚀 Запуск тестів для покращеної сторінки філаментів\n');
  console.log('=' .repeat(60));
  
  await testFilamentScraping();
  await testImageGeneration();
  
  console.log('\n' + '='.repeat(60));
  console.log('✨ Всі тести завершено! Сторінка філаментів готова до використання.');
  console.log('\n📖 Для перегляду результатів відкрийте:');
  console.log('🌐 http://localhost:3002/marketplace/filament');
}

// Запуск, якщо скрипт викликається напряму
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testFilamentScraping,
  testImageGeneration,
  runAllTests
};
