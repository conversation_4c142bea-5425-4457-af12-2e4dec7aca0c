/**
 * End-to-end integration tests for scraping system
 * These tests verify the complete flow from API to scrapers
 */

import { printablesScraper } from '@/lib/api/printables';
import { makerWorldScraper } from '@/lib/api/makerworld';
import { thangsScraper } from '@/lib/api/thangs';
import { DataNormalizer } from '@/lib/scraping/data-normalizer';
import { platformRateLimiters } from '@/lib/scraping/rate-limiter';

describe('Scraping Integration Tests', () => {
  // Test URLs (these should be real, publicly available models)
  const testUrls = {
    printables: 'https://www.printables.com/model/1', // Use a known working model ID
    makerworld: 'https://makerworld.com/en/models/1',
    thangs: 'https://thangs.com/designer/test/model/1',
  };

  beforeEach(() => {
    // Reset rate limiters before each test
    jest.setTimeout(30000); // 30 second timeout for network requests
  });

  describe('URL Validation', () => {
    it('should validate Printables URLs correctly', () => {
      expect(printablesScraper.validateUrl('https://www.printables.com/model/123456')).toBe(true);
      expect(printablesScraper.validateUrl('https://printables.com/model/123456-test-model')).toBe(true);
      expect(printablesScraper.validateUrl('https://other.com/model/123456')).toBe(false);
      expect(printablesScraper.validateUrl('invalid-url')).toBe(false);
    });

    it('should validate MakerWorld URLs correctly', () => {
      expect(makerWorldScraper.validateUrl('https://makerworld.com/en/models/123456')).toBe(true);
      expect(makerWorldScraper.validateUrl('https://www.makerworld.com/en/models/123456')).toBe(true);
      expect(makerWorldScraper.validateUrl('https://other.com/models/123456')).toBe(false);
    });

    it('should validate Thangs URLs correctly', () => {
      expect(thangsScraper.validateUrl('https://thangs.com/designer/user/model/123456')).toBe(true);
      expect(thangsScraper.validateUrl('https://www.thangs.com/designer/user/model/123456')).toBe(true);
      expect(thangsScraper.validateUrl('https://thangs.com/model/123456')).toBe(false);
    });
  });

  describe('Model ID Extraction', () => {
    it('should extract Printables model IDs', () => {
      expect(printablesScraper.extractModelId('https://www.printables.com/model/123456')).toBe('123456');
      expect(printablesScraper.extractModelId('https://printables.com/model/789012-test-model')).toBe('789012');
      expect(printablesScraper.extractModelId('https://other.com/model/123456')).toBeNull();
    });

    it('should extract MakerWorld model IDs', () => {
      expect(makerWorldScraper.extractModelId('https://makerworld.com/en/models/123456')).toBe('123456');
      expect(makerWorldScraper.extractModelId('https://www.makerworld.com/en/models/789012')).toBe('789012');
      expect(makerWorldScraper.extractModelId('https://other.com/models/123456')).toBeNull();
    });

    it('should extract Thangs model IDs', () => {
      expect(thangsScraper.extractModelId('https://thangs.com/designer/user/model/123456')).toBe('123456');
      expect(thangsScraper.extractModelId('https://www.thangs.com/designer/other/model/789012')).toBe('789012');
      expect(thangsScraper.extractModelId('https://thangs.com/model/123456')).toBeNull();
    });
  });

  describe('Data Normalization', () => {
    const mockScrapedModel = {
      title: 'Test Model',
      description: 'Test description with <script>alert("xss")</script> malicious content',
      summary: 'Test summary',
      images: [
        { id: '1', url: 'https://example.com/image1.jpg' },
        { id: '2', url: 'https://example.com/image1.jpg' }, // Duplicate
        { id: '3', url: 'https://example.com/image2.png' },
      ],
      thumbnail: 'https://example.com/thumb.jpg',
      files: [
        { id: '1', name: 'model.stl', url: 'https://example.com/file.stl', downloadUrl: 'https://example.com/download/file.stl', size: 1024000, format: 'STL' },
      ],
      fileFormats: ['STL', 'OBJ'],
      totalSize: 1024000,
      designer: { id: 'test-designer', name: 'Test Designer' },
      tags: ['test', 'model', 'test'], // Duplicate tag
      category: 'Test Category',
      license: {
        type: 'CC-BY' as const,
        name: 'Creative Commons Attribution 4.0',
        detected: true,
        confidence: 0.9,
        allowCommercialUse: true,
        requireAttribution: true,
        allowDerivatives: true,
      },
      stats: { views: 100, downloads: 50, likes: 25, comments: 10 },
      platform: 'printables' as const,
      originalId: '123456',
      originalUrl: 'https://www.printables.com/model/123456',
      scrapedAt: new Date().toISOString(),
      isFree: true,
    };

    it('should validate scraped model data', () => {
      const validation = DataNormalizer.validateScrapedModel(mockScrapedModel);
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect validation errors', () => {
      const invalidModel = { ...mockScrapedModel, title: '' };
      const validation = DataNormalizer.validateScrapedModel(invalidModel);
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Title is required');
    });

    it('should sanitize scraped model data', () => {
      const sanitized = DataNormalizer.sanitizeScrapedModel(mockScrapedModel);
      expect(sanitized.description).not.toContain('<script>');
      expect(sanitized.description).not.toContain('alert');
    });

    it('should convert to internal model format', () => {
      const internalModel = DataNormalizer.convertToInternalModel(mockScrapedModel);

      expect(internalModel.id).toMatch(/^printables_123456_\d+$/);
      expect(internalModel.title).toBe('Test Model');
      expect(internalModel.source).toBe('printables');
      expect(internalModel.externalSource).toEqual({
        platform: 'printables',
        originalId: '123456',
        originalUrl: 'https://www.printables.com/model/123456',
        importedAt: mockScrapedModel.scrapedAt,
      });
    });

    it('should normalize file size correctly', () => {
      const internalModel = DataNormalizer.convertToInternalModel(mockScrapedModel);
      expect(internalModel.fileSize).toBe('1000.0KB');
    });

    it('should normalize tags correctly', () => {
      const internalModel = DataNormalizer.convertToInternalModel(mockScrapedModel);
      expect(internalModel.tags).toEqual(['test', 'model']); // Duplicates removed
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce platform-specific rate limits', async () => {
      // Test that different platforms have different limits
      const printablesRemaining = await platformRateLimiters.getRemainingPoints('printables');
      const makerworldRemaining = await platformRateLimiters.getRemainingPoints('makerworld');
      const thangsRemaining = await platformRateLimiters.getRemainingPoints('thangs');

      expect(printablesRemaining).toBeLessThanOrEqual(10);
      expect(makerworldRemaining).toBeLessThanOrEqual(15);
      expect(thangsRemaining).toBeLessThanOrEqual(12);
    });

    it('should handle rate limit consumption', async () => {
      const initialRemaining = await platformRateLimiters.getRemainingPoints('printables', 'test-key');

      await platformRateLimiters.consume('printables', 'test-key');

      const afterConsumption = await platformRateLimiters.getRemainingPoints('printables', 'test-key');
      expect(afterConsumption).toBe(initialRemaining - 1);
    });

    it('should reset rate limits correctly', async () => {
      await platformRateLimiters.consume('printables', 'reset-test');
      await platformRateLimiters.consume('printables', 'reset-test');

      let remaining = await platformRateLimiters.getRemainingPoints('printables', 'reset-test');
      expect(remaining).toBeLessThan(10);

      await platformRateLimiters.reset('printables', 'reset-test');

      remaining = await platformRateLimiters.getRemainingPoints('printables', 'reset-test');
      expect(remaining).toBe(10);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid URLs gracefully', async () => {
      await expect(printablesScraper.scrapeModel('https://invalid.com/model/123')).rejects.toThrow('Invalid Printables URL format');
    });

    it('should handle network errors gracefully', async () => {
      // Mock a network error by using a non-existent domain
      await expect(printablesScraper.scrapeModel('https://nonexistent-domain-12345.com/model/123')).rejects.toThrow();
    });

    it('should handle malformed HTML gracefully', async () => {
      // This would require mocking the HTTP client to return malformed HTML
      // For now, we'll test that the scraper doesn't crash on empty responses
      const originalFetchHtml = printablesScraper['fetchHtml'];
      printablesScraper['fetchHtml'] = jest.fn().mockResolvedValue('');

      await expect(printablesScraper.scrapeModel('https://www.printables.com/model/123')).rejects.toThrow();

      // Restore original method
      printablesScraper['fetchHtml'] = originalFetchHtml;
    });
  });

  describe('License Detection', () => {
    it('should detect common Creative Commons licenses', () => {
      const testCases = [
        { text: 'This work is licensed under Creative Commons Attribution 4.0', expected: 'CC-BY' },
        { text: 'CC BY-SA license applies', expected: 'CC-BY-SA' },
        { text: 'Released under CC BY-NC license', expected: 'CC-BY-NC' },
        { text: 'Public domain CC0', expected: 'CC0' },
        { text: 'GNU General Public License', expected: 'GPL' },
        { text: 'MIT License', expected: 'MIT' },
      ];

      testCases.forEach(({ text, expected }) => {
        const license = printablesScraper['detectLicense'](text);
        expect(license.type).toBe(expected);
        expect(license.detected).toBe(true);
        expect(license.confidence).toBeGreaterThan(0.7);
      });
    });

    it('should handle unknown licenses', () => {
      const license = printablesScraper['detectLicense']('Some random license text without keywords');
      expect(license.type).toBe('Custom');
      expect(license.detected).toBe(false);
      expect(license.confidence).toBeLessThan(0.5);
    });
  });

  describe('Performance', () => {
    it('should complete scraping within reasonable time', async () => {
      const startTime = Date.now();

      try {
        // Use a mock URL to avoid actual network requests in CI
        const mockUrl = 'https://www.printables.com/model/1';
        await printablesScraper.scrapeModel(mockUrl);
      } catch (error) {
        // Expected to fail in test environment, but we're measuring time
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within 30 seconds (including retries)
      expect(duration).toBeLessThan(30000);
    });

    it('should handle concurrent scraping requests', async () => {
      const urls = [
        'https://www.printables.com/model/1',
        'https://www.printables.com/model/2',
        'https://www.printables.com/model/3',
      ];

      const startTime = Date.now();

      const promises = urls.map(url =>
        printablesScraper.scrapeModel(url).catch(error => error)
      );

      const results = await Promise.all(promises);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Concurrent requests should not take significantly longer than sequential
      expect(duration).toBeLessThan(45000); // Allow some extra time for concurrency
      expect(results).toHaveLength(3);
    });
  });
});
