/**
 * Налаштування для тестів
 */

import '@testing-library/jest-dom';
import React from 'react';
import { afterAll, afterEach, vi } from 'vitest';

// Mock глобальних об'єктів
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock crypto.randomUUID
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: vi.fn(() => 'test-uuid-123'),
    getRandomValues: vi.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    })
  }
});

// Mock console methods для чистих тестів
const originalConsole = { ...console };
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  info: vi.fn(),
  debug: vi.fn()
};

// Відновлення console після тестів
afterEach(() => {
  vi.clearAllMocks();
});

afterAll(() => {
  global.console = originalConsole;
});

// Mock для Next.js
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn()
  }),
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams()
}));

vi.mock('next/image', () => ({
  default: ({ src, alt, ...props }: any) => {
    // eslint-disable-next-line @next/next/no-img-element
    return React.createElement('img', { src, alt, ...props });
  }
}));

// Mock для Framer Motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => React.createElement('div', props, children),
    span: ({ children, ...props }: any) => React.createElement('span', props, children),
    button: ({ children, ...props }: any) => React.createElement('button', props, children),
    form: ({ children, ...props }: any) => React.createElement('form', props, children),
    header: ({ children, ...props }: any) => React.createElement('header', props, children),
    nav: ({ children, ...props }: any) => React.createElement('nav', props, children)
  },
  AnimatePresence: ({ children }: any) => children,
  useAnimation: () => ({
    start: vi.fn(),
    stop: vi.fn(),
    set: vi.fn()
  }),
  useInView: () => true
}));

// Mock для Recharts
vi.mock('recharts', () => ({
  LineChart: ({ children }: any) => React.createElement('div', { 'data-testid': 'line-chart' }, children),
  Line: () => React.createElement('div', { 'data-testid': 'line' }),
  XAxis: () => React.createElement('div', { 'data-testid': 'x-axis' }),
  YAxis: () => React.createElement('div', { 'data-testid': 'y-axis' }),
  CartesianGrid: () => React.createElement('div', { 'data-testid': 'cartesian-grid' }),
  Tooltip: () => React.createElement('div', { 'data-testid': 'tooltip' }),
  ResponsiveContainer: ({ children }: any) => React.createElement('div', { 'data-testid': 'responsive-container' }, children),
  BarChart: ({ children }: any) => React.createElement('div', { 'data-testid': 'bar-chart' }, children),
  Bar: () => React.createElement('div', { 'data-testid': 'bar' })
}));

// Mock для Lucide React icons
vi.mock('lucide-react', () => {
  const MockIcon = ({ className, ...props }: any) =>
    React.createElement('svg',
      { className, ...props, 'data-testid': 'mock-icon' },
      React.createElement('rect', { width: '24', height: '24' })
    );

  return new Proxy({}, {
    get: (target, prop) => {
      if (typeof prop === 'string') {
        return MockIcon;
      }
      return target[prop as keyof typeof target];
    }
  });
});

// Mock для environment variables
process.env.NODE_ENV = 'test';
process.env.NEXT_PUBLIC_REALTIME_ENABLED = 'true';
process.env.NEXT_PUBLIC_REFRESH_INTERVAL = '5000';
process.env.NEXT_PUBLIC_ADMIN_ENABLED = 'true';

// Глобальні тестові утиліти
global.testUtils = {
  createMockRequest: (url: string, options: RequestInit = {}) => {
    return new Request(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
  },

  createMockResponse: (data: any, status = 200) => {
    return new Response(JSON.stringify(data), {
      status,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },

  createMockEnv: () => ({
    MODEL_DOWNLOAD_MANAGER: {
      idFromName: vi.fn(),
      get: vi.fn()
    },
    SCRAPING_COORDINATOR: {
      idFromName: vi.fn(),
      get: vi.fn()
    },
    ANALYTICS: {
      writeDataPoint: vi.fn()
    },
    CACHE_KV: {
      put: vi.fn(),
      get: vi.fn(),
      delete: vi.fn(),
      list: vi.fn()
    },
    R2_BUCKET: {
      put: vi.fn(),
      get: vi.fn(),
      delete: vi.fn(),
      list: vi.fn()
    },
    DB: {
      prepare: vi.fn(),
      exec: vi.fn(),
      batch: vi.fn()
    }
  }),

  createMockJob: (overrides = {}) => ({
    id: 'test-job-123',
    modelId: 'test-model-456',
    sourceUrl: 'https://example.com/model.stl',
    fileName: 'test-model.stl',
    fileType: 'stl',
    platform: 'printables',
    status: 'pending',
    progress: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    retryCount: 0,
    maxRetries: 3,
    ...overrides
  }),

  createMockModel: (overrides = {}) => ({
    id: 'test-model-123',
    title: 'Test Model',
    description: 'Test description',
    author: 'Test Author',
    platform: 'printables',
    category: 'test',
    tags: ['test', 'model'],
    downloads: 100,
    views: 500,
    likes: 25,
    rating: 4.5,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    scrapedAt: new Date().toISOString(),
    files: [],
    downloadStatus: 'not_started',
    downloadProgress: 0,
    isPremium: false,
    isVerified: false,
    difficulty: 'beginner',
    ...overrides
  }),

  waitFor: async (callback: () => void, timeout = 5000) => {
    const startTime = Date.now();
    while (Date.now() - startTime < timeout) {
      try {
        callback();
        return;
      } catch (error) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }
    throw new Error(`Timeout after ${timeout}ms`);
  }
};

// Типи для глобальних утиліт
declare global {
  var testUtils: {
    createMockRequest: (url: string, options?: RequestInit) => Request;
    createMockResponse: (data: any, status?: number) => Response;
    createMockEnv: () => any;
    createMockJob: (overrides?: any) => any;
    createMockModel: (overrides?: any) => any;
    waitFor: (callback: () => void, timeout?: number) => Promise<void>;
  };
}
