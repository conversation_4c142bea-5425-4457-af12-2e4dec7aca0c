/**
 * Тести для ModelDownloadManager Durable Object
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ModelDownloadManager, DownloadJob } from '../../durable-objects/model-download-manager';

// Mock Cloudflare environment
const mockEnv = {
  R2_BUCKET: {
    put: vi.fn(),
    get: vi.fn(),
    delete: vi.fn(),
    list: vi.fn()
  },
  MODEL_DOWNLOAD_MANAGER: {
    idFromName: vi.fn(),
    get: vi.fn()
  }
};

// Mock DurableObjectState
const mockCtx = {
  storage: {
    get: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    list: vi.fn(),
    setAlarm: vi.fn()
  },
  waitUntil: vi.fn(),
  passThroughOnException: vi.fn()
};

describe('ModelDownloadManager', () => {
  let downloadManager: ModelDownloadManager;

  beforeEach(() => {
    vi.clearAllMocks();
    downloadManager = new ModelDownloadManager(mockCtx as any, mockEnv as any);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Додавання завдань завантаження', () => {
    it('повинен успішно додати нове завдання', async () => {
      const jobData = {
        modelId: 'test-model-123',
        sourceUrl: 'https://example.com/model.stl',
        fileName: 'test-model.stl',
        fileType: 'stl',
        platform: 'printables'
      };

      const request = new Request('https://fake-host/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(jobData)
      });

      const response = await downloadManager.fetch(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.jobId).toBeDefined();
      expect(result.job.modelId).toBe(jobData.modelId);
      expect(result.job.status).toBe('pending');
    });

    it('повинен відхилити завдання з неповними даними', async () => {
      const incompleteJobData = {
        modelId: 'test-model-123',
        // відсутні обов'язкові поля
      };

      const request = new Request('https://fake-host/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(incompleteJobData)
      });

      const response = await downloadManager.fetch(request);
      
      expect(response.status).toBe(500); // Очікуємо помилку
    });
  });

  describe('Отримання списку завдань', () => {
    it('повинен повернути порожній список для нового менеджера', async () => {
      const request = new Request('https://fake-host/jobs');
      const response = await downloadManager.fetch(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.jobs).toEqual([]);
    });

    it('повинен фільтрувати завдання за статусом', async () => {
      // Спочатку додаємо кілька завдань
      const jobData1 = {
        modelId: 'model-1',
        sourceUrl: 'https://example.com/model1.stl',
        fileName: 'model1.stl',
        fileType: 'stl',
        platform: 'printables'
      };

      const jobData2 = {
        modelId: 'model-2',
        sourceUrl: 'https://example.com/model2.stl',
        fileName: 'model2.stl',
        fileType: 'stl',
        platform: 'makerworld'
      };

      // Додаємо завдання
      await downloadManager.fetch(new Request('https://fake-host/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(jobData1)
      }));

      await downloadManager.fetch(new Request('https://fake-host/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(jobData2)
      }));

      // Отримуємо всі завдання
      const allJobsRequest = new Request('https://fake-host/jobs');
      const allJobsResponse = await downloadManager.fetch(allJobsRequest);
      const allJobsResult = await allJobsResponse.json();

      expect(allJobsResult.jobs).toHaveLength(2);

      // Фільтруємо за статусом
      const pendingJobsRequest = new Request('https://fake-host/jobs?status=pending');
      const pendingJobsResponse = await downloadManager.fetch(pendingJobsRequest);
      const pendingJobsResult = await pendingJobsResponse.json();

      expect(pendingJobsResult.jobs).toHaveLength(2);
      expect(pendingJobsResult.jobs.every((job: DownloadJob) => job.status === 'pending')).toBe(true);
    });

    it('повинен фільтрувати завдання за платформою', async () => {
      const jobData = {
        modelId: 'model-printables',
        sourceUrl: 'https://example.com/model.stl',
        fileName: 'model.stl',
        fileType: 'stl',
        platform: 'printables'
      };

      await downloadManager.fetch(new Request('https://fake-host/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(jobData)
      }));

      const filteredRequest = new Request('https://fake-host/jobs?platform=printables');
      const filteredResponse = await downloadManager.fetch(filteredRequest);
      const filteredResult = await filteredResponse.json();

      expect(filteredResult.jobs).toHaveLength(1);
      expect(filteredResult.jobs[0].platform).toBe('printables');
    });
  });

  describe('Статистика завантажень', () => {
    it('повинен повернути правильну статистику', async () => {
      const request = new Request('https://fake-host/stats');
      const response = await downloadManager.fetch(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.stats).toBeDefined();
      expect(result.stats.totalJobs).toBe(0);
      expect(result.stats.pendingJobs).toBe(0);
      expect(result.stats.completedJobs).toBe(0);
      expect(result.stats.failedJobs).toBe(0);
      expect(result.activeDownloads).toBe(0);
      expect(result.maxConcurrentDownloads).toBe(5);
    });
  });

  describe('Видалення завдань', () => {
    it('повинен успішно видалити існуюче завдання', async () => {
      // Спочатку додаємо завдання
      const jobData = {
        modelId: 'test-model',
        sourceUrl: 'https://example.com/model.stl',
        fileName: 'model.stl',
        fileType: 'stl',
        platform: 'printables'
      };

      const addResponse = await downloadManager.fetch(new Request('https://fake-host/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(jobData)
      }));

      const addResult = await addResponse.json();
      const jobId = addResult.jobId;

      // Тепер видаляємо завдання
      const deleteRequest = new Request(`https://fake-host/jobs/${jobId}`, {
        method: 'DELETE'
      });

      const deleteResponse = await downloadManager.fetch(deleteRequest);
      const deleteResult = await deleteResponse.json();

      expect(deleteResponse.status).toBe(200);
      expect(deleteResult.success).toBe(true);
    });

    it('повинен повернути помилку для неіснуючого завдання', async () => {
      const deleteRequest = new Request('https://fake-host/jobs/non-existent-id', {
        method: 'DELETE'
      });

      const deleteResponse = await downloadManager.fetch(deleteRequest);
      const deleteResult = await deleteResponse.json();

      expect(deleteResponse.status).toBe(404);
      expect(deleteResult.error).toBe('Завдання не знайдено');
    });
  });

  describe('Повторна спроба завантаження', () => {
    it('повинен успішно перезапустити завдання', async () => {
      // Додаємо завдання
      const jobData = {
        modelId: 'test-model',
        sourceUrl: 'https://example.com/model.stl',
        fileName: 'model.stl',
        fileType: 'stl',
        platform: 'printables'
      };

      const addResponse = await downloadManager.fetch(new Request('https://fake-host/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(jobData)
      }));

      const addResult = await addResponse.json();
      const jobId = addResult.jobId;

      // Повторна спроба
      const retryRequest = new Request(`https://fake-host/jobs/${jobId}/retry`, {
        method: 'POST'
      });

      const retryResponse = await downloadManager.fetch(retryRequest);
      const retryResult = await retryResponse.json();

      expect(retryResponse.status).toBe(200);
      expect(retryResult.success).toBe(true);
      expect(retryResult.job.retryCount).toBe(1);
    });
  });

  describe('Обробка черги', () => {
    it('повинен обробити чергу завантажень', async () => {
      const processRequest = new Request('https://fake-host/process', {
        method: 'POST'
      });

      const processResponse = await downloadManager.fetch(processRequest);
      const processResult = await processResponse.json();

      expect(processResponse.status).toBe(200);
      expect(processResult.success).toBe(true);
      expect(processResult.processedJobs).toBeDefined();
    });
  });

  describe('Неправильні запити', () => {
    it('повинен повернути 404 для невідомих шляхів', async () => {
      const request = new Request('https://fake-host/unknown-path');
      const response = await downloadManager.fetch(request);

      expect(response.status).toBe(404);
    });

    it('повинен обробити помилки JSON parsing', async () => {
      const request = new Request('https://fake-host/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json'
      });

      const response = await downloadManager.fetch(request);
      expect(response.status).toBe(500);
    });
  });
});

describe('Допоміжні функції ModelDownloadManager', () => {
  let downloadManager: ModelDownloadManager;

  beforeEach(() => {
    downloadManager = new ModelDownloadManager(mockCtx as any, mockEnv as any);
  });

  describe('Генерація R2 ключів', () => {
    it('повинен генерувати правильну структуру ключів', () => {
      const job: DownloadJob = {
        id: 'test-id',
        modelId: 'model-123',
        sourceUrl: 'https://example.com/model.stl',
        fileName: 'awesome-model.stl',
        fileType: 'stl',
        platform: 'printables',
        status: 'pending',
        progress: 0,
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-15T10:30:00Z',
        retryCount: 0,
        maxRetries: 3
      };

      // Використовуємо приватний метод через any
      const r2Key = (downloadManager as any).generateR2Key(job);
      
      expect(r2Key).toMatch(/^models\/printables\/\d{4}\/\d{2}\/\d{2}\/model-123\/awesome-model\.stl$/);
    });
  });

  describe('Визначення MIME типів', () => {
    it('повинен правильно визначати MIME типи', () => {
      const testCases = [
        { fileType: 'stl', expected: 'application/vnd.ms-pki.stl' },
        { fileType: 'obj', expected: 'application/x-tgif' },
        { fileType: '3mf', expected: 'application/vnd.ms-package.3dmanufacturing-3dmodel+xml' },
        { fileType: 'zip', expected: 'application/zip' },
        { fileType: 'unknown', expected: 'application/octet-stream' }
      ];

      testCases.forEach(({ fileType, expected }) => {
        const mimeType = (downloadManager as any).getContentType(fileType);
        expect(mimeType).toBe(expected);
      });
    });
  });
});
