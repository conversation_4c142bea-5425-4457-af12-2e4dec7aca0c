/**
 * Тести для DownloadManagerDashboard компонента
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import DownloadManagerDashboard from '../../../components/admin/DownloadManagerDashboard';

// Mock fetch
global.fetch = vi.fn();

// Mock компонентів UI
vi.mock('../../../components/ui/card', () => ({
  Card: ({ children, className }: any) => <div className={className}>{children}</div>,
  CardContent: ({ children }: any) => <div>{children}</div>,
  CardDescription: ({ children }: any) => <div>{children}</div>,
  CardHeader: ({ children }: any) => <div>{children}</div>,
  CardTitle: ({ children }: any) => <h3>{children}</h3>
}));

vi.mock('../../../components/ui/button', () => ({
  Button: ({ children, onClick, disabled, ...props }: any) => (
    <button onClick={onClick} disabled={disabled} {...props}>
      {children}
    </button>
  )
}));

vi.mock('../../../components/ui/badge', () => ({
  Badge: ({ children, className }: any) => <span className={className}>{children}</span>
}));

vi.mock('../../../components/ui/progress', () => ({
  Progress: ({ value }: any) => <div data-testid="progress" data-value={value}></div>
}));

vi.mock('../../../components/ui/tabs', () => ({
  Tabs: ({ children, value, onValueChange }: any) => (
    <div data-testid="tabs" data-value={value}>
      {children}
    </div>
  ),
  TabsContent: ({ children, value }: any) => (
    <div data-testid={`tab-content-${value}`}>{children}</div>
  ),
  TabsList: ({ children }: any) => <div data-testid="tabs-list">{children}</div>,
  TabsTrigger: ({ children, value, onClick }: any) => (
    <button data-testid={`tab-trigger-${value}`} onClick={() => onClick?.(value)}>
      {children}
    </button>
  )
}));

describe('DownloadManagerDashboard', () => {
  const mockStats = {
    totalJobs: 100,
    pendingJobs: 15,
    completedJobs: 80,
    failedJobs: 5,
    totalDownloadedSize: 1024000000,
    averageDownloadTime: 2500,
    activeDownloads: 3,
    maxConcurrentDownloads: 5
  };

  const mockJobs = [
    {
      id: 'job-1',
      modelId: 'model-123',
      sourceUrl: 'https://example.com/model1.stl',
      fileName: 'awesome-model.stl',
      fileType: 'stl',
      platform: 'printables',
      status: 'completed' as const,
      progress: 100,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:05:00Z',
      retryCount: 0,
      maxRetries: 3,
      r2Key: 'models/printables/2024/01/15/model-123/awesome-model.stl',
      fileSize: 1024000
    },
    {
      id: 'job-2',
      modelId: 'model-456',
      sourceUrl: 'https://example.com/model2.stl',
      fileName: 'cool-gadget.stl',
      fileType: 'stl',
      platform: 'makerworld',
      status: 'downloading' as const,
      progress: 65,
      createdAt: '2024-01-15T10:10:00Z',
      updatedAt: '2024-01-15T10:12:00Z',
      retryCount: 0,
      maxRetries: 3,
      fileSize: 2048000,
      downloadedSize: 1331200
    },
    {
      id: 'job-3',
      modelId: 'model-789',
      sourceUrl: 'https://example.com/model3.stl',
      fileName: 'failed-model.stl',
      fileType: 'stl',
      platform: 'thangs',
      status: 'failed' as const,
      progress: 0,
      createdAt: '2024-01-15T09:00:00Z',
      updatedAt: '2024-01-15T09:05:00Z',
      retryCount: 2,
      maxRetries: 3,
      error: 'Перевищено час очікування'
    }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock успішних API відповідей
    (global.fetch as any).mockImplementation((url: string) => {
      if (url.includes('action=stats')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: mockStats
          })
        });
      }
      
      if (url.includes('action=jobs')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { jobs: mockJobs }
          })
        });
      }
      
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ success: true })
      });
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Рендеринг компонента', () => {
    it('повинен відобразити компонент з завантаженням', () => {
      render(<DownloadManagerDashboard />);
      
      expect(screen.getByText('Завантаження...')).toBeInTheDocument();
    });

    it('повинен відобразити статистику після завантаження', async () => {
      render(<DownloadManagerDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('Всього завдань')).toBeInTheDocument();
        expect(screen.getByText('100')).toBeInTheDocument();
        expect(screen.getByText('Завершено')).toBeInTheDocument();
        expect(screen.getByText('80')).toBeInTheDocument();
      });
    });

    it('повинен відобразити список завдань', async () => {
      render(<DownloadManagerDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('awesome-model.stl')).toBeInTheDocument();
        expect(screen.getByText('cool-gadget.stl')).toBeInTheDocument();
        expect(screen.getByText('failed-model.stl')).toBeInTheDocument();
      });
    });
  });

  describe('Статистика', () => {
    it('повинен правильно відобразити статистику', async () => {
      render(<DownloadManagerDashboard />);
      
      await waitFor(() => {
        // Перевіряємо основні числа
        expect(screen.getByText('100')).toBeInTheDocument(); // totalJobs
        expect(screen.getByText('80')).toBeInTheDocument(); // completedJobs
        expect(screen.getByText('15')).toBeInTheDocument(); // pendingJobs
        
        // Перевіряємо активні завантаження
        expect(screen.getByText('Активних: 3/5')).toBeInTheDocument();
        
        // Перевіряємо відсоток успішності
        expect(screen.getByText('80% успішність')).toBeInTheDocument();
      });
    });

    it('повинен правильно форматувати розмір файлів', async () => {
      render(<DownloadManagerDashboard />);
      
      await waitFor(() => {
        // 1024000000 bytes = 976.56 MB
        expect(screen.getByText(/976\.56 MB/)).toBeInTheDocument();
      });
    });
  });

  describe('Фільтрація завдань', () => {
    it('повинен показати всі завдання за замовчуванням', async () => {
      render(<DownloadManagerDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('awesome-model.stl')).toBeInTheDocument();
        expect(screen.getByText('cool-gadget.stl')).toBeInTheDocument();
        expect(screen.getByText('failed-model.stl')).toBeInTheDocument();
      });
    });

    it('повинен показати правильну кількість завдань у вкладках', async () => {
      render(<DownloadManagerDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('Всі (3)')).toBeInTheDocument();
        expect(screen.getByText('Завершені (1)')).toBeInTheDocument();
        expect(screen.getByText('Завантажуються (1)')).toBeInTheDocument();
        expect(screen.getByText('Помилки (1)')).toBeInTheDocument();
      });
    });
  });

  describe('Дії з завданнями', () => {
    it('повинен викликати retry для завдання', async () => {
      render(<DownloadManagerDashboard />);
      
      await waitFor(() => {
        const retryButton = screen.getByText('Повторити');
        expect(retryButton).toBeInTheDocument();
      });

      const retryButton = screen.getByText('Повторити');
      fireEvent.click(retryButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('action=retry'),
          expect.objectContaining({
            method: 'PATCH'
          })
        );
      });
    });

    it('повинен викликати видалення завдання', async () => {
      render(<DownloadManagerDashboard />);
      
      await waitFor(() => {
        const deleteButtons = screen.getAllByText('×');
        expect(deleteButtons.length).toBeGreaterThan(0);
      });

      const deleteButton = screen.getAllByText('×')[0];
      fireEvent.click(deleteButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('jobId='),
          expect.objectContaining({
            method: 'DELETE'
          })
        );
      });
    });

    it('повинен оновити дані при натисканні кнопки оновлення', async () => {
      render(<DownloadManagerDashboard />);
      
      await waitFor(() => {
        const refreshButton = screen.getByText('Оновити');
        expect(refreshButton).toBeInTheDocument();
      });

      const refreshButton = screen.getByText('Оновити');
      fireEvent.click(refreshButton);

      // Повинен викликати API знову
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledTimes(4); // 2 початкові + 2 після оновлення
      });
    });
  });

  describe('Відображення прогресу', () => {
    it('повинен показати прогрес для завантажуваних файлів', async () => {
      render(<DownloadManagerDashboard />);
      
      await waitFor(() => {
        const progressBar = screen.getByTestId('progress');
        expect(progressBar).toHaveAttribute('data-value', '65');
      });
    });

    it('повинен показати статус завдань', async () => {
      render(<DownloadManagerDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('completed')).toBeInTheDocument();
        expect(screen.getByText('downloading')).toBeInTheDocument();
        expect(screen.getByText('failed')).toBeInTheDocument();
      });
    });
  });

  describe('Обробка помилок', () => {
    it('повинен обробити помилку API', async () => {
      (global.fetch as any).mockRejectedValue(new Error('API Error'));
      
      render(<DownloadManagerDashboard />);
      
      // Компонент не повинен крашитися
      await waitFor(() => {
        expect(screen.queryByText('Завантаження...')).not.toBeInTheDocument();
      });
    });

    it('повинен показати повідомлення про помилку завдання', async () => {
      render(<DownloadManagerDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('Перевищено час очікування')).toBeInTheDocument();
        expect(screen.getByText('Спроба 2/3')).toBeInTheDocument();
      });
    });
  });

  describe('Форматування даних', () => {
    it('повинен правильно форматувати розміри файлів', async () => {
      render(<DownloadManagerDashboard />);
      
      await waitFor(() => {
        // 1024000 bytes = 1000 KB
        expect(screen.getByText('1000 KB')).toBeInTheDocument();
        // 2048000 bytes = 1.95 MB
        expect(screen.getByText('1.95 MB')).toBeInTheDocument();
      });
    });

    it('повинен правильно форматувати дати', async () => {
      render(<DownloadManagerDashboard />);
      
      await waitFor(() => {
        // Перевіряємо, що дати відображаються
        expect(screen.getByText(/Створено:/)).toBeInTheDocument();
        expect(screen.getByText(/Оновлено:/)).toBeInTheDocument();
      });
    });
  });

  describe('Автоматичне оновлення', () => {
    it('повинен налаштувати інтервал оновлення', async () => {
      const setIntervalSpy = vi.spyOn(global, 'setInterval');
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');
      
      const { unmount } = render(<DownloadManagerDashboard />);
      
      expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 5000);
      
      unmount();
      
      expect(clearIntervalSpy).toHaveBeenCalled();
      
      setIntervalSpy.mockRestore();
      clearIntervalSpy.mockRestore();
    });
  });
});
