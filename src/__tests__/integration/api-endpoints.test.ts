/**
 * Інтеграційні тести для API endpoints
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NextRequest } from 'next/server';

// Mock Cloudflare environment
const mockEnv = {
  MODEL_DOWNLOAD_MANAGER: {
    idFromName: vi.fn(),
    get: vi.fn()
  },
  ANALYTICS: {
    writeDataPoint: vi.fn()
  },
  CACHE_KV: {
    put: vi.fn(),
    get: vi.fn()
  },
  R2_BUCKET: {},
  DB: {}
};

// Mock Durable Object stub
const mockStub = {
  fetch: vi.fn()
};

describe('API Endpoints Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockEnv.MODEL_DOWNLOAD_MANAGER.idFromName.mockReturnValue('test-id');
    mockEnv.MODEL_DOWNLOAD_MANAGER.get.mockReturnValue(mockStub);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('/api/models/download-manager', () => {
    // Імпортуємо обробники динамічно
    let GET: any, POST: any, DELETE: any, PATCH: any;

    beforeEach(async () => {
      const module = await import('../../app/api/models/download-manager/route');
      GET = module.GET;
      POST = module.POST;
      DELETE = module.DELETE;
      PATCH = module.PATCH;
    });

    describe('GET requests', () => {
      it('повинен отримати статистику завантажень', async () => {
        const mockStats = {
          totalJobs: 100,
          pendingJobs: 15,
          completedJobs: 80,
          failedJobs: 5,
          activeDownloads: 3,
          maxConcurrentDownloads: 5
        };

        mockStub.fetch.mockResolvedValue({
          json: () => Promise.resolve({
            success: true,
            data: mockStats
          })
        });

        const request = new NextRequest('http://localhost/api/models/download-manager?action=stats');
        (request as any).env = mockEnv;

        const response = await GET(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.data.totalJobs).toBe(100);
        expect(mockStub.fetch).toHaveBeenCalledWith('https://fake-host/stats');
      });

      it('повинен отримати список завдань', async () => {
        const mockJobs = [
          {
            id: 'job-1',
            modelId: 'model-123',
            status: 'completed',
            fileName: 'test.stl'
          }
        ];

        mockStub.fetch.mockResolvedValue({
          json: () => Promise.resolve({
            success: true,
            data: { jobs: mockJobs }
          })
        });

        const request = new NextRequest('http://localhost/api/models/download-manager?action=jobs');
        (request as any).env = mockEnv;

        const response = await GET(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.data.jobs).toHaveLength(1);
      });

      it('повинен фільтрувати завдання за статусом', async () => {
        mockStub.fetch.mockResolvedValue({
          json: () => Promise.resolve({
            success: true,
            data: { jobs: [] }
          })
        });

        const request = new NextRequest('http://localhost/api/models/download-manager?action=jobs&status=pending');
        (request as any).env = mockEnv;

        await GET(request);

        expect(mockStub.fetch).toHaveBeenCalledWith(
          expect.stringContaining('status=pending')
        );
      });

      it('повинен повернути помилку без MODEL_DOWNLOAD_MANAGER', async () => {
        const request = new NextRequest('http://localhost/api/models/download-manager?action=stats');
        (request as any).env = {};

        const response = await GET(request);
        const data = await response.json();

        expect(response.status).toBe(500);
        expect(data.error).toContain('MODEL_DOWNLOAD_MANAGER не налаштований');
      });
    });

    describe('POST requests', () => {
      it('повинен додати нове завдання завантаження', async () => {
        const jobData = {
          modelId: 'model-123',
          sourceUrl: 'https://example.com/model.stl',
          fileName: 'test-model.stl',
          fileType: 'stl',
          platform: 'printables'
        };

        mockStub.fetch.mockResolvedValue({
          json: () => Promise.resolve({
            success: true,
            jobId: 'new-job-id',
            job: { ...jobData, id: 'new-job-id', status: 'pending' }
          })
        });

        const request = new NextRequest('http://localhost/api/models/download-manager', {
          method: 'POST',
          body: JSON.stringify(jobData),
          headers: { 'Content-Type': 'application/json' }
        });
        (request as any).env = mockEnv;

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.jobId).toBe('new-job-id');
        expect(mockStub.fetch).toHaveBeenCalledWith(
          'https://fake-host/download',
          expect.objectContaining({
            method: 'POST',
            body: JSON.stringify(jobData)
          })
        );
      });

      it('повинен валідувати обов\'язкові поля', async () => {
        const incompleteData = {
          modelId: 'model-123'
          // відсутні інші обов'язкові поля
        };

        const request = new NextRequest('http://localhost/api/models/download-manager', {
          method: 'POST',
          body: JSON.stringify(incompleteData),
          headers: { 'Content-Type': 'application/json' }
        });
        (request as any).env = mockEnv;

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(400);
        expect(data.error).toContain('є обов\'язковим');
      });

      it('повинен обробити чергу завантажень', async () => {
        mockStub.fetch.mockResolvedValue({
          json: () => Promise.resolve({
            success: true,
            processedJobs: 5
          })
        });

        const request = new NextRequest('http://localhost/api/models/download-manager?action=process', {
          method: 'POST'
        });
        (request as any).env = mockEnv;

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.processedJobs).toBe(5);
      });
    });

    describe('DELETE requests', () => {
      it('повинен видалити завдання', async () => {
        mockStub.fetch.mockResolvedValue({
          json: () => Promise.resolve({
            success: true
          })
        });

        const request = new NextRequest('http://localhost/api/models/download-manager?jobId=job-123', {
          method: 'DELETE'
        });
        (request as any).env = mockEnv;

        const response = await DELETE(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(mockStub.fetch).toHaveBeenCalledWith(
          'https://fake-host/jobs/job-123',
          { method: 'DELETE' }
        );
      });

      it('повинен вимагати jobId', async () => {
        const request = new NextRequest('http://localhost/api/models/download-manager', {
          method: 'DELETE'
        });
        (request as any).env = mockEnv;

        const response = await DELETE(request);
        const data = await response.json();

        expect(response.status).toBe(400);
        expect(data.error).toContain('jobId є обов\'язковим');
      });
    });

    describe('PATCH requests', () => {
      it('повинен повторити завдання', async () => {
        mockStub.fetch.mockResolvedValue({
          json: () => Promise.resolve({
            success: true,
            job: { id: 'job-123', retryCount: 1 }
          })
        });

        const request = new NextRequest('http://localhost/api/models/download-manager?jobId=job-123&action=retry', {
          method: 'PATCH'
        });
        (request as any).env = mockEnv;

        const response = await PATCH(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(mockStub.fetch).toHaveBeenCalledWith(
          'https://fake-host/jobs/job-123/retry',
          { method: 'POST' }
        );
      });

      it('повинен вимагати action=retry', async () => {
        const request = new NextRequest('http://localhost/api/models/download-manager?jobId=job-123&action=invalid', {
          method: 'PATCH'
        });
        (request as any).env = mockEnv;

        const response = await PATCH(request);
        const data = await response.json();

        expect(response.status).toBe(400);
        expect(data.error).toContain('Підтримується тільки action=retry');
      });
    });
  });

  describe('/api/cloudflare/observability', () => {
    let GET: any, POST: any, PATCH: any, DELETE: any;

    beforeEach(async () => {
      const module = await import('../../app/api/cloudflare/observability/route');
      GET = module.GET;
      POST = module.POST;
      PATCH = module.PATCH;
      DELETE = module.DELETE;
    });

    describe('GET requests', () => {
      it('повинен отримати метрики', async () => {
        const request = new NextRequest('http://localhost/api/cloudflare/observability?type=metrics');
        (request as any).env = mockEnv;

        const response = await GET(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.data).toBeDefined();
      });

      it('повинен отримати логи', async () => {
        const request = new NextRequest('http://localhost/api/cloudflare/observability?type=logs');
        (request as any).env = mockEnv;

        const response = await GET(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.data).toBeDefined();
      });

      it('повинен отримати статус здоров\'я', async () => {
        const request = new NextRequest('http://localhost/api/cloudflare/observability?type=health');
        (request as any).env = mockEnv;

        const response = await GET(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.data.status).toBe('healthy');
        expect(data.data.services).toBeDefined();
      });
    });

    describe('POST requests', () => {
      it('повинен записати метрику', async () => {
        const metricData = {
          name: 'test_metric',
          value: 42,
          labels: { platform: 'test' }
        };

        const request = new NextRequest('http://localhost/api/cloudflare/observability?type=metric', {
          method: 'POST',
          body: JSON.stringify(metricData),
          headers: { 'Content-Type': 'application/json' }
        });
        (request as any).env = mockEnv;

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.message).toBe('Метрика записана');
      });

      it('повинен записати лог', async () => {
        const logData = {
          level: 'info',
          message: 'Test log message',
          context: { test: true }
        };

        const request = new NextRequest('http://localhost/api/cloudflare/observability?type=log', {
          method: 'POST',
          body: JSON.stringify(logData),
          headers: { 'Content-Type': 'application/json' }
        });
        (request as any).env = mockEnv;

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.message).toBe('Лог записаний');
      });

      it('повинен валідувати дані метрики', async () => {
        const invalidData = {
          name: 'test_metric'
          // відсутнє value
        };

        const request = new NextRequest('http://localhost/api/cloudflare/observability?type=metric', {
          method: 'POST',
          body: JSON.stringify(invalidData),
          headers: { 'Content-Type': 'application/json' }
        });
        (request as any).env = mockEnv;

        const response = await POST(request);
        const data = await response.json();

        expect(response.status).toBe(400);
        expect(data.error).toContain('є обов\'язковими');
      });
    });
  });

  describe('Error Handling', () => {
    it('повинен обробити помилки Durable Object', async () => {
      mockStub.fetch.mockRejectedValue(new Error('Durable Object error'));

      const module = await import('../../app/api/models/download-manager/route');
      const GET = module.GET;

      const request = new NextRequest('http://localhost/api/models/download-manager?action=stats');
      (request as any).env = mockEnv;

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBeDefined();
    });

    it('повинен обробити неправильний JSON', async () => {
      const module = await import('../../app/api/models/download-manager/route');
      const POST = module.POST;

      const request = new NextRequest('http://localhost/api/models/download-manager', {
        method: 'POST',
        body: 'invalid json',
        headers: { 'Content-Type': 'application/json' }
      });
      (request as any).env = mockEnv;

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBeDefined();
    });
  });
});
