/**
 * Simple test to verify Jest setup
 */

describe('Jest Setup', () => {
  it('should run basic tests', () => {
    expect(1 + 1).toBe(2)
  })

  it('should have access to test utilities', () => {
    expect(global.testUtils).toBeDefined()
    expect(global.testUtils.createMockScrapedModel).toBeDefined()
  })

  it('should create mock scraped model', () => {
    const mockModel = global.testUtils.createMockScrapedModel()
    expect(mockModel.title).toBe('Test Model')
    expect(mockModel.platform).toBe('printables')
  })
})
