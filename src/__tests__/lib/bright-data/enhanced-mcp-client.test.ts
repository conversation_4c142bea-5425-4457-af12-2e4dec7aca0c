/**
 * Тести для Enhanced Bright Data MCP Client
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { EnhancedBrightDataMCPClient } from '../../../lib/bright-data/enhanced-mcp-client';

// Mock fetch
global.fetch = vi.fn();

describe('EnhancedBrightDataMCPClient', () => {
  let client: EnhancedBrightDataMCPClient;
  const mockConfig = {
    mcpApiToken: 'test-token-123',
    enableFallback: true,
    rateLimitMs: 1000,
    maxRetries: 3
  };

  beforeEach(() => {
    vi.clearAllMocks();
    client = new EnhancedBrightDataMCPClient(mockConfig);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Ініціалізація клієнта', () => {
    it('повинен створити клієнта з правильною конфігурацією', () => {
      expect(client).toBeDefined();
      expect((client as any).config.mcpApiToken).toBe(mockConfig.mcpApiToken);
      expect((client as any).config.enableFallback).toBe(mockConfig.enableFallback);
    });

    it('повинен використовувати значення за замовчуванням', () => {
      const defaultClient = new EnhancedBrightDataMCPClient({});
      
      expect((defaultClient as any).config.enableFallback).toBe(true);
      expect((defaultClient as any).config.rateLimitMs).toBe(2000);
      expect((defaultClient as any).config.maxRetries).toBe(3);
    });
  });

  describe('Скрапінг сторінок', () => {
    it('повинен успішно скрапити сторінку в markdown форматі', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: {
            content: '# Test Page\nThis is test content',
            metadata: {
              title: 'Test Page',
              url: 'https://example.com'
            }
          }
        })
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await client.scrapePage('https://example.com', {
        format: 'markdown'
      });

      expect(result.success).toBe(true);
      expect(result.data.content).toContain('# Test Page');
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/scrape/markdown'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-token-123'
          })
        })
      );
    });

    it('повинен успішно скрапити сторінку в HTML форматі', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: {
            content: '<html><body><h1>Test</h1></body></html>',
            metadata: {
              title: 'Test Page'
            }
          }
        })
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await client.scrapePage('https://example.com', {
        format: 'html'
      });

      expect(result.success).toBe(true);
      expect(result.data.content).toContain('<h1>Test</h1>');
    });

    it('повинен обробити помилку HTTP', async () => {
      const mockResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found'
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await client.scrapePage('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('HTTP 404');
    });

    it('повинен використати fallback при помилці', async () => {
      (global.fetch as any).mockRejectedValue(new Error('Network error'));

      const result = await client.scrapePage('https://example.com');

      // Повинен повернути fallback результат
      expect(result.success).toBe(true);
      expect(result.data.content).toContain('Симуляція');
    });
  });

  describe('Пошук в інтернеті', () => {
    it('повинен виконати пошук через Google', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: {
            results: [
              {
                title: 'Test Result',
                url: 'https://example.com',
                description: 'Test description'
              }
            ]
          }
        })
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await client.searchWeb('test query', 'google');

      expect(result.success).toBe(true);
      expect(result.data.results).toHaveLength(1);
      expect(result.data.results[0].title).toBe('Test Result');
    });

    it('повинен використати Google за замовчуванням', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { results: [] }
        })
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      await client.searchWeb('test query');

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/search'),
        expect.objectContaining({
          body: expect.stringContaining('"engine":"google"')
        })
      );
    });
  });

  describe('Скрапінг популярних моделей', () => {
    it('повинен скрапити популярні моделі з Printables', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: {
            models: [
              {
                id: 'model-1',
                title: 'Awesome Model',
                author: 'Test Author',
                platform: 'printables',
                downloadUrl: 'https://example.com/model.stl'
              }
            ]
          }
        })
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await client.scrapePopularModels('printables', 10);

      expect(result.success).toBe(true);
      expect(result.data.models).toHaveLength(1);
      expect(result.data.models[0].platform).toBe('printables');
    });

    it('повинен обробити невідому платформу', async () => {
      const result = await client.scrapePopularModels('unknown-platform' as any, 10);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Непідтримувана платформа');
    });
  });

  describe('Rate limiting', () => {
    it('повинен дотримуватися rate limit', async () => {
      const fastClient = new EnhancedBrightDataMCPClient({
        ...mockConfig,
        rateLimitMs: 100
      });

      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({ success: true, data: {} })
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const startTime = Date.now();
      
      await fastClient.scrapePage('https://example1.com');
      await fastClient.scrapePage('https://example2.com');
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Повинно пройти принаймні 100ms між запитами
      expect(duration).toBeGreaterThanOrEqual(100);
    });
  });

  describe('Retry логіка', () => {
    it('повинен повторити запит при тимчасовій помилці', async () => {
      const retryClient = new EnhancedBrightDataMCPClient({
        ...mockConfig,
        maxRetries: 2,
        rateLimitMs: 10 // Швидкі retry для тестів
      });

      // Перший виклик - помилка, другий - успіх
      (global.fetch as any)
        .mockRejectedValueOnce(new Error('Temporary error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: {} })
        });

      const result = await retryClient.scrapePage('https://example.com');

      expect(result.success).toBe(true);
      expect(global.fetch).toHaveBeenCalledTimes(2);
    });

    it('повинен припинити після максимальної кількості спроб', async () => {
      const retryClient = new EnhancedBrightDataMCPClient({
        ...mockConfig,
        maxRetries: 2,
        rateLimitMs: 10,
        enableFallback: false // Вимикаємо fallback для цього тесту
      });

      (global.fetch as any).mockRejectedValue(new Error('Persistent error'));

      const result = await retryClient.scrapePage('https://example.com');

      expect(result.success).toBe(false);
      expect(global.fetch).toHaveBeenCalledTimes(3); // Початковий + 2 retry
    });
  });

  describe('Статистика сесії', () => {
    it('повинен отримати статистику сесії', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: {
            requestsCount: 42,
            successRate: 0.95,
            averageResponseTime: 1500
          }
        })
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await client.getSessionStats();

      expect(result.success).toBe(true);
      expect(result.data.requestsCount).toBe(42);
      expect(result.data.successRate).toBe(0.95);
    });
  });

  describe('Обробка помилок', () => {
    it('повинен обробити помилку мережі', async () => {
      (global.fetch as any).mockRejectedValue(new Error('Network error'));

      const result = await client.scrapePage('https://example.com');

      // З fallback повинен повернути успіх
      expect(result.success).toBe(true);
    });

    it('повинен обробити неправильний JSON', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.reject(new Error('Invalid JSON'))
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await client.scrapePage('https://example.com');

      // З fallback повинен повернути успіх
      expect(result.success).toBe(true);
    });

    it('повинен обробити відсутність токена', async () => {
      const noTokenClient = new EnhancedBrightDataMCPClient({
        enableFallback: false
      });

      const result = await noTokenClient.scrapePage('https://example.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('MCP API токен не налаштований');
    });
  });

  describe('Валідація URL', () => {
    it('повинен відхилити неправильний URL', async () => {
      const result = await client.scrapePage('not-a-url');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Неправильний URL');
    });

    it('повинен прийняти правильний URL', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({ success: true, data: {} })
      };

      (global.fetch as any).mockResolvedValue(mockResponse);

      const result = await client.scrapePage('https://example.com');

      expect(result.success).toBe(true);
    });
  });

  describe('Конфігурація API URL', () => {
    it('повинен генерувати правильні URL для різних tools', () => {
      const testCases = [
        { tool: 'scrape_as_markdown_Bright_Data', expected: '/scrape/markdown' },
        { tool: 'scrape_as_html_Bright_Data', expected: '/scrape/html' },
        { tool: 'search_engine_Bright_Data', expected: '/search' },
        { tool: 'session_stats_Bright_Data', expected: '/stats' }
      ];

      testCases.forEach(({ tool, expected }) => {
        const url = (client as any).getBrightDataAPIUrl(tool);
        expect(url).toContain(expected);
      });
    });
  });
});
