/**
 * Тести для Cloudflare Observability системи
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { CloudflareObservability, MetricData, LogEntry, TraceSpan } from '../../../lib/cloudflare/observability';

// Mock Cloudflare environment
const mockEnv = {
  ANALYTICS: {
    writeDataPoint: vi.fn()
  },
  CACHE_KV: {
    put: vi.fn(),
    get: vi.fn(),
    delete: vi.fn(),
    list: vi.fn()
  },
  R2_BUCKET: {},
  DB: {}
};

describe('CloudflareObservability', () => {
  let observability: CloudflareObservability;

  beforeEach(() => {
    vi.clearAllMocks();
    observability = new CloudflareObservability(mockEnv as any);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Запис метрик', () => {
    it('повинен успішно записати метрику', async () => {
      const metricName = 'test_metric';
      const metricValue = 42;
      const labels = { platform: 'printables', status: 'success' };

      await observability.recordMetric(metricName, metricValue, labels, 'count');

      expect(mockEnv.ANALYTICS.writeDataPoint).toHaveBeenCalledWith({
        blobs: [metricName, JSON.stringify(labels)],
        doubles: [metricValue],
        indexes: [metricName]
      });
    });

    it('повинен записати метрику без labels', async () => {
      const metricName = 'simple_metric';
      const metricValue = 100;

      await observability.recordMetric(metricName, metricValue);

      expect(mockEnv.ANALYTICS.writeDataPoint).toHaveBeenCalledWith({
        blobs: [metricName, JSON.stringify({})],
        doubles: [metricValue],
        indexes: [metricName]
      });
    });

    it('повинен обробити помилку Analytics Engine', async () => {
      mockEnv.ANALYTICS.writeDataPoint.mockRejectedValue(new Error('Analytics error'));

      // Не повинно кидати помилку
      await expect(observability.recordMetric('test', 1)).resolves.not.toThrow();
    });
  });

  describe('Логування', () => {
    it('повинен записати лог з усіма параметрами', async () => {
      const level = 'info';
      const message = 'Test log message';
      const context = { userId: '123', action: 'download' };
      const traceId = 'trace-123';
      const spanId = 'span-456';

      await observability.log(level, message, context, traceId, spanId);

      expect(mockEnv.CACHE_KV.put).toHaveBeenCalled();
      
      const putCall = mockEnv.CACHE_KV.put.mock.calls[0];
      expect(putCall[0]).toMatch(/^log:/); // Ключ починається з 'log:'
      
      const logData = JSON.parse(putCall[1]);
      expect(logData.level).toBe(level);
      expect(logData.message).toBe(message);
      expect(logData.context).toEqual(context);
      expect(logData.traceId).toBe(traceId);
      expect(logData.spanId).toBe(spanId);
    });

    it('повинен записати простий лог', async () => {
      await observability.log('error', 'Simple error message');

      expect(mockEnv.CACHE_KV.put).toHaveBeenCalled();
    });

    it('повинен обробити помилку KV storage', async () => {
      mockEnv.CACHE_KV.put.mockRejectedValue(new Error('KV error'));

      await expect(observability.log('info', 'test')).resolves.not.toThrow();
    });
  });

  describe('Трейсинг', () => {
    it('повинен створити новий span', () => {
      const operationName = 'test_operation';
      const tags = { component: 'download-manager' };

      const span = observability.startSpan(operationName, undefined, tags);

      expect(span.operationName).toBe(operationName);
      expect(span.tags).toEqual(tags);
      expect(span.traceId).toBeDefined();
      expect(span.spanId).toBeDefined();
      expect(span.startTime).toBeDefined();
      expect(span.endTime).toBeUndefined();
    });

    it('повинен створити дочірній span', () => {
      const parentSpan = observability.startSpan('parent_operation');
      const childSpan = observability.startSpan('child_operation', parentSpan.spanId);

      expect(childSpan.parentSpanId).toBe(parentSpan.spanId);
      expect(childSpan.traceId).toBe(parentSpan.traceId);
    });

    it('повинен завершити span', () => {
      const span = observability.startSpan('test_operation');
      
      observability.finishSpan(span.spanId, 'ok');

      // Span повинен бути видалений з активних
      const metrics = observability.getMetrics();
      const spanDurationMetrics = metrics.filter(m => m.name === 'span_duration_ms');
      expect(spanDurationMetrics.length).toBeGreaterThan(0);
    });

    it('повинен додати лог до span', () => {
      const span = observability.startSpan('test_operation');
      
      observability.addSpanLog(span.spanId, 'info', 'Test span log', { key: 'value' });

      expect(mockEnv.CACHE_KV.put).toHaveBeenCalled();
    });
  });

  describe('Спеціалізовані метрики', () => {
    it('повинен записати метрики скрапінгу', async () => {
      const platform = 'printables';
      const success = true;
      const duration = 2500;
      const modelsFound = 25;

      await observability.recordScrapingMetrics(platform, success, duration, modelsFound);

      expect(mockEnv.ANALYTICS.writeDataPoint).toHaveBeenCalledTimes(3);
    });

    it('повинен записати метрики завантаження', async () => {
      const platform = 'makerworld';
      const success = true;
      const fileSize = 1024000;
      const duration = 3000;

      await observability.recordDownloadMetrics(platform, success, fileSize, duration);

      expect(mockEnv.ANALYTICS.writeDataPoint).toHaveBeenCalledTimes(3);
    });

    it('повинен записати метрики API', async () => {
      const endpoint = '/api/models/download';
      const method = 'POST';
      const statusCode = 200;
      const duration = 150;

      await observability.recordAPIMetrics(endpoint, method, statusCode, duration);

      expect(mockEnv.ANALYTICS.writeDataPoint).toHaveBeenCalledTimes(2);
    });
  });

  describe('Запис помилок', () => {
    it('повинен записати помилку з контекстом', async () => {
      const error = new Error('Test error');
      const context = { operation: 'download', modelId: '123' };
      const spanId = 'span-123';

      await observability.recordError(error, context, spanId);

      expect(mockEnv.CACHE_KV.put).toHaveBeenCalled();
      expect(mockEnv.ANALYTICS.writeDataPoint).toHaveBeenCalled();
    });

    it('повинен записати просту помилку', async () => {
      const error = new Error('Simple error');

      await observability.recordError(error);

      expect(mockEnv.CACHE_KV.put).toHaveBeenCalled();
      expect(mockEnv.ANALYTICS.writeDataPoint).toHaveBeenCalled();
    });
  });

  describe('Отримання даних', () => {
    beforeEach(async () => {
      // Додаємо тестові дані
      await observability.recordMetric('test_metric_1', 10, { type: 'A' });
      await observability.recordMetric('test_metric_2', 20, { type: 'B' });
      await observability.log('info', 'Test info log');
      await observability.log('error', 'Test error log');
    });

    it('повинен отримати всі метрики', () => {
      const metrics = observability.getMetrics();
      
      expect(metrics.length).toBeGreaterThanOrEqual(2);
      expect(metrics.some(m => m.name === 'test_metric_1')).toBe(true);
      expect(metrics.some(m => m.name === 'test_metric_2')).toBe(true);
    });

    it('повинен фільтрувати метрики за назвою', () => {
      const filteredMetrics = observability.getMetrics(undefined, undefined, 'test_metric_1');
      
      expect(filteredMetrics.every(m => m.name.includes('test_metric_1'))).toBe(true);
    });

    it('повинен фільтрувати метрики за часом', () => {
      const now = Date.now();
      const oneHourAgo = now - 60 * 60 * 1000;
      
      const recentMetrics = observability.getMetrics(oneHourAgo, now);
      
      expect(recentMetrics.every(m => m.timestamp >= oneHourAgo && m.timestamp <= now)).toBe(true);
    });

    it('повинен отримати всі логи', () => {
      const logs = observability.getLogs();
      
      expect(logs.length).toBeGreaterThanOrEqual(2);
    });

    it('повинен фільтрувати логи за рівнем', () => {
      const errorLogs = observability.getLogs(undefined, undefined, 'error');
      
      expect(errorLogs.every(l => l.level === 'error')).toBe(true);
    });

    it('повинен експортувати всі дані', () => {
      const exportData = observability.exportData();
      
      expect(exportData.metrics).toBeDefined();
      expect(exportData.logs).toBeDefined();
      expect(exportData.activeSpans).toBeDefined();
      expect(exportData.timestamp).toBeDefined();
    });
  });

  describe('Очистка даних', () => {
    beforeEach(async () => {
      // Додаємо тестові дані
      await observability.recordMetric('old_metric', 1);
      await observability.log('info', 'Old log');
    });

    it('повинен очистити старі дані', () => {
      const initialMetrics = observability.getMetrics();
      const initialLogs = observability.getLogs();
      
      expect(initialMetrics.length).toBeGreaterThan(0);
      expect(initialLogs.length).toBeGreaterThan(0);

      // Очищуємо дані старше 0 мс (все)
      observability.cleanup(0);

      const afterMetrics = observability.getMetrics();
      const afterLogs = observability.getLogs();
      
      expect(afterMetrics.length).toBe(0);
      expect(afterLogs.length).toBe(0);
    });

    it('повинен зберегти нові дані при очистці', () => {
      // Очищуємо дані старше 1 години
      observability.cleanup(60 * 60 * 1000);

      const metrics = observability.getMetrics();
      const logs = observability.getLogs();
      
      // Нові дані повинні залишитися
      expect(metrics.length).toBeGreaterThan(0);
      expect(logs.length).toBeGreaterThan(0);
    });
  });

  describe('Допоміжні методи', () => {
    it('повинен правильно форматувати emoji для логів', () => {
      const testCases = [
        { level: 'debug', expected: '🐛' },
        { level: 'info', expected: 'ℹ️' },
        { level: 'warn', expected: '⚠️' },
        { level: 'error', expected: '❌' }
      ];

      testCases.forEach(({ level, expected }) => {
        const emoji = (observability as any).getLogEmoji(level);
        expect(emoji).toBe(expected);
      });
    });
  });
});
