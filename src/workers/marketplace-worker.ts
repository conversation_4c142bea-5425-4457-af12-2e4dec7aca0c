/**
 * Marketplace Worker для 3D Marketplace
 * Обробляє API запити для marketplace функціональності
 */

export interface Env {
  // D1 Database
  DB: D1Database;
  // KV Storage
  KV: KVNamespace;
  // R2 Storage
  STORAGE: R2Bucket;
  // Environment variables
  ENVIRONMENT: string;
  API_VERSION: string;
  ENABLE_CORS: string;
  ALLOWED_ORIGINS: string;
}

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

/**
 * Обробка CORS preflight запитів
 */
function handleCORS(request: Request): Response | null {
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }
  return null;
}

/**
 * Обробка помилок
 */
function handleError(error: Error, status: number = 500): Response {
  console.error('Worker Error:', error);
  return new Response(
    JSON.stringify({
      error: error.message,
      status,
      timestamp: new Date().toISOString(),
    }),
    {
      status,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
      },
    }
  );
}

/**
 * Успішна відповідь
 */
function successResponse(data: any, status: number = 200): Response {
  return new Response(
    JSON.stringify({
      success: true,
      data,
      timestamp: new Date().toISOString(),
    }),
    {
      status,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
      },
    }
  );
}

/**
 * Обробка запитів до моделей
 */
async function handleModels(request: Request, env: Env): Promise<Response> {
  const url = new URL(request.url);
  const method = request.method;

  try {
    const pathSegments = url.pathname.split('/');
    const modelId = pathSegments[3]; // /api/models/{id}

    if (modelId) {
      // Обробка запиту для конкретної моделі за ID
      if (method === 'GET') {
        const model = await env.DB.prepare(`
          SELECT
            id, name, description, price, category,
            user_id, created_at, updated_at,
            download_count, view_count, like_count,
            is_featured, is_free, model_url, thumbnail_url, tags
          FROM models
          WHERE id = ?
        `).bind(modelId).first();

        if (!model) {
          return handleError(new Error('Model not found'), 404);
        }
        return successResponse(model);
      } else if (method === 'PUT') {
        // Оновлення моделі
        const modelData = await request.json() as any;
        const updateResult = await env.DB.prepare(`
          UPDATE models SET
            name = ?, description = ?, price = ?, category = ?,
            tags = ?, model_url = ?, thumbnail_url = ?,
            is_free = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).bind(
          modelData.name,
          modelData.description,
          modelData.price,
          modelData.category,
          modelData.tags,
          modelData.model_url,
          modelData.thumbnail_url,
          modelData.is_free,
          modelId
        ).run();

        if (updateResult.meta.changes === 0) {
          return handleError(new Error('Model not found or no changes made'), 404);
        }
        return successResponse({ message: 'Model updated successfully' });
      } else if (method === 'DELETE') {
        // Видалення моделі
        const deleteResult = await env.DB.prepare(`
          DELETE FROM models WHERE id = ?
        `).bind(modelId).run();

        if (deleteResult.meta.changes === 0) {
          return handleError(new Error('Model not found'), 404);
        }
        return successResponse({ message: 'Model deleted successfully' });
      } else {
        return handleError(new Error('Method not allowed for specific model'), 405);
      }
    }

    // Обробка запитів до колекції моделей
    switch (method) {
      case 'GET':
        // Отримання списку моделей
        const models = await env.DB.prepare(`
          SELECT
            id, name, description, price, category,
            user_id, created_at, updated_at,
            download_count, view_count, like_count,
            is_featured, is_free, model_url, thumbnail_url, tags
          FROM models
          ORDER BY created_at DESC
          LIMIT 50
        `).all();

        return successResponse({
          models: models.results,
          count: models.results?.length || 0,
        });

      case 'POST':
        // Створення нової моделі
        const modelData = await request.json() as any;

        const newModel = await env.DB.prepare(`
          INSERT INTO models (
            id, user_id, name, description, price, category,
            tags, model_url, thumbnail_url,
            is_free, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `).bind(
          crypto.randomUUID(),
          modelData.user_id || 'anonymous',
          modelData.name,
          modelData.description,
          modelData.price || 0,
          modelData.category,
          modelData.tags,
          modelData.model_url,
          modelData.thumbnail_url,
          modelData.is_free || false
        ).run();

        return successResponse({
          id: newModel.meta?.last_row_id,
          message: 'Model created successfully',
        }, 201);

      default:
        return handleError(new Error('Method not allowed'), 405);
    }
  } catch (error) {
    return handleError(error as Error);
  }
}

/**
 * Обробка запитів до користувачів
 */
async function handleUsers(request: Request, env: Env): Promise<Response> {
  const url = new URL(request.url);
  const method = request.method;

  try {
    switch (method) {
      case 'GET':
        // Отримання списку користувачів
        const users = await env.DB.prepare(`
          SELECT id, email, name, avatar_url, created_at, updated_at
          FROM users
          ORDER BY created_at DESC
          LIMIT 50
        `).all();

        return successResponse({
          users: users.results,
          count: users.results?.length || 0,
        });

      case 'POST':
        // Створення нового користувача
        const userData = await request.json() as any;

        const newUser = await env.DB.prepare(`
          INSERT INTO users (
            id, email, name, avatar_url, created_at, updated_at
          ) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `).bind(
          crypto.randomUUID(),
          userData.email,
          userData.name,
          userData.avatar_url || null
        ).run();

        return successResponse({
          id: newUser.meta?.last_row_id,
          message: 'User created successfully',
        }, 201);

      default:
        return handleError(new Error('Method not allowed'), 405);
    }
  } catch (error) {
    return handleError(error as Error);
  }
}

/**
 * Обробка запитів до статистики
 */
async function handleStats(request: Request, env: Env): Promise<Response> {
  try {
    // Отримання статистики з D1
    const [modelsCount, usersCount, downloadsCount] = await Promise.all([
      env.DB.prepare('SELECT COUNT(*) as count FROM models').first(),
      env.DB.prepare('SELECT COUNT(*) as count FROM users').first(),
      env.DB.prepare('SELECT SUM(download_count) as count FROM models').first(),
    ]);

    // Отримання статистики з KV
    const kvStats = await env.KV.get('marketplace_stats', { type: 'json' }) || {};

    return successResponse({
      models: modelsCount?.count || 0,
      users: usersCount?.count || 0,
      downloads: downloadsCount?.count || 0,
      kv_stats: kvStats,
    });
  } catch (error) {
    return handleError(error as Error);
  }
}

/**
 * Головна функція обробки запитів
 */
export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    // Обробка CORS
    const corsResponse = handleCORS(request);
    if (corsResponse) return corsResponse;

    const url = new URL(request.url);
    const pathname = url.pathname;

    try {
      // Маршрутизація
      if (pathname.startsWith('/api/models')) {
        return handleModels(request, env);
      } else if (pathname.startsWith('/api/users')) {
        return handleUsers(request, env);
      } else if (pathname.startsWith('/api/stats')) {
        return handleStats(request, env);
      } else if (pathname === '/api/health') {
        return successResponse({
          status: 'healthy',
          version: env.API_VERSION || '2.0.0',
          environment: env.ENVIRONMENT || 'development',
        });
      } else {
        return handleError(new Error('Not found'), 404);
      }
    } catch (error) {
      return handleError(error as Error);
    }
  },
};
