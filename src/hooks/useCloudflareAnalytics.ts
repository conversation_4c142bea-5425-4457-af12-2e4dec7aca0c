/**
 * React Hook для роботи з Cloudflare Analytics
 * Інтеграція з cloudflare-observability MCP
 */

import { useCallback } from 'react';

interface AnalyticsEvent {
  event: string;
  userId?: string;
  data?: Record<string, any>;
}

interface PerformanceMetric {
  metric: string;
  value: number;
  unit?: string;
}

interface ErrorEvent {
  error: Error;
  context?: string;
  userId?: string;
}

/**
 * Hook для трекінгу подій в Cloudflare Analytics
 */
export function useCloudflareAnalytics() {
  const trackEvent = useCallback(async ({ event, userId, data }: AnalyticsEvent) => {
    try {
      await fetch('/api/analytics/track', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event,
          userId,
          data: {
            ...data,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href,
            referrer: document.referrer
          }
        })
      });
    } catch (error) {
      console.error('Failed to track event:', error);
    }
  }, []);

  const trackPerformance = useCallback(async ({ metric, value, unit = 'ms' }: PerformanceMetric) => {
    try {
      await fetch('/api/analytics/performance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          metric,
          value,
          unit,
          timestamp: new Date().toISOString(),
          url: window.location.href
        })
      });
    } catch (error) {
      console.error('Failed to track performance:', error);
    }
  }, []);

  const trackError = useCallback(async ({ error, context, userId }: ErrorEvent) => {
    try {
      await fetch('/api/analytics/error', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: error.message,
          stack: error.stack,
          context,
          userId,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent
        })
      });
    } catch (trackingError) {
      console.error('Failed to track error:', trackingError);
    }
  }, []);

  // Специфічні функції для 3D маркетплейсу
  const trackModelView = useCallback(async (modelId: string, userId?: string) => {
    await trackEvent({
      event: 'model_view',
      userId,
      data: { modelId }
    });
  }, [trackEvent]);

  const trackModelDownload = useCallback(async (modelId: string, userId: string, downloadType: 'free' | 'paid') => {
    await trackEvent({
      event: 'model_download',
      userId,
      data: { modelId, downloadType }
    });
  }, [trackEvent]);

  const trackSearch = useCallback(async (query: string, resultsCount: number, userId?: string) => {
    await trackEvent({
      event: 'search',
      userId,
      data: { query, resultsCount }
    });
  }, [trackEvent]);

  const trackPageView = useCallback(async (page: string, userId?: string) => {
    await trackEvent({
      event: 'page_view',
      userId,
      data: { page }
    });
  }, [trackEvent]);

  const trackUserAction = useCallback(async (action: string, target: string, userId?: string) => {
    await trackEvent({
      event: 'user_action',
      userId,
      data: { action, target }
    });
  }, [trackEvent]);

  const trackConversion = useCallback(async (type: string, value: number, userId?: string) => {
    await trackEvent({
      event: 'conversion',
      userId,
      data: { type, value }
    });
  }, [trackEvent]);

  return {
    // Базові функції
    trackEvent,
    trackPerformance,
    trackError,
    
    // Специфічні для маркетплейсу
    trackModelView,
    trackModelDownload,
    trackSearch,
    trackPageView,
    trackUserAction,
    trackConversion
  };
}

/**
 * Hook для автоматичного трекінгу продуктивності
 */
export function usePerformanceTracking() {
  const { trackPerformance } = useCloudflareAnalytics();

  const measurePerformance = useCallback(async <T>(
    operation: () => Promise<T>,
    metricName: string
  ): Promise<T> => {
    const startTime = performance.now();
    
    try {
      const result = await operation();
      const duration = performance.now() - startTime;
      
      await trackPerformance({
        metric: metricName,
        value: Math.round(duration),
        unit: 'ms'
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      await trackPerformance({
        metric: `${metricName}_error`,
        value: Math.round(duration),
        unit: 'ms'
      });
      
      throw error;
    }
  }, [trackPerformance]);

  return { measurePerformance };
}

/**
 * Hook для автоматичного трекінгу помилок
 */
export function useErrorTracking() {
  const { trackError } = useCloudflareAnalytics();

  const withErrorTracking = useCallback(<T extends any[], R>(
    fn: (...args: T) => R,
    context: string
  ) => {
    return (...args: T): R => {
      try {
        const result = fn(...args);
        
        // Якщо результат - Promise, обробляємо помилки асинхронно
        if (result instanceof Promise) {
          return result.catch((error) => {
            trackError({ error, context });
            throw error;
          }) as R;
        }
        
        return result;
      } catch (error) {
        trackError({ error: error as Error, context });
        throw error;
      }
    };
  }, [trackError]);

  return { withErrorTracking };
}

/**
 * Hook для трекінгу користувацьких дій
 */
export function useUserActionTracking() {
  const { trackUserAction } = useCloudflareAnalytics();

  const trackClick = useCallback((target: string, userId?: string) => {
    trackUserAction('click', target, userId);
  }, [trackUserAction]);

  const trackFormSubmit = useCallback((formName: string, userId?: string) => {
    trackUserAction('form_submit', formName, userId);
  }, [trackUserAction]);

  const trackNavigation = useCallback((destination: string, userId?: string) => {
    trackUserAction('navigation', destination, userId);
  }, [trackUserAction]);

  return {
    trackClick,
    trackFormSubmit,
    trackNavigation
  };
}
