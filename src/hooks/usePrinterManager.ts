import { useState, useEffect, useCallback, useRef } from 'react';

interface Printer {
  id: string;
  name: string;
  type: string;
  status: 'idle' | 'printing' | 'paused' | 'error' | 'maintenance' | 'offline';
  location: string;
  capabilities: {
    maxBuildVolume: { x: number; y: number; z: number };
    supportedMaterials: string[];
    layerHeight: { min: number; max: number };
    nozzleDiameter: number[];
  };
  currentJob?: PrintJob;
  queue: PrintJob[];
  sensors: PrinterSensors;
  lastUpdate: number;
  totalPrintTime: number;
  totalFilamentUsed: number;
}

interface PrintJob {
  id: string;
  modelId: string;
  modelName: string;
  userId: string;
  status: 'queued' | 'printing' | 'paused' | 'completed' | 'failed' | 'cancelled';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  estimatedTime: number;
  actualTime?: number;
  progress: number;
  material: string;
  settings: PrintSettings;
  createdAt: number;
  startedAt?: number;
  completedAt?: number;
  errorMessage?: string;
}

interface PrintSettings {
  layerHeight: number;
  infill: number;
  printSpeed: number;
  temperature: {
    nozzle: number;
    bed: number;
  };
  supports: boolean;
  raft: boolean;
}

interface PrinterSensors {
  temperature: {
    nozzle: number;
    bed: number;
    chamber?: number;
  };
  position: {
    x: number;
    y: number;
    z: number;
  };
  filament: {
    remaining: number;
    type: string;
  };
  vibration?: number;
  humidity?: number;
}

interface PrinterCommand {
  type: 'start' | 'pause' | 'resume' | 'cancel' | 'home' | 'move' | 'heat' | 'cool';
  parameters?: any;
  timestamp: number;
  userId: string;
}

interface PrinterManagerState {
  printers: Printer[];
  isConnected: boolean;
  connectionError: string | null;
  loading: boolean;
}

export function usePrinterManager() {
  const [state, setState] = useState<PrinterManagerState>({
    printers: [],
    isConnected: false,
    connectionError: null,
    loading: true,
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/api/printers/manager/websocket`;
      
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('Connected to printer manager');
        setState(prev => ({
          ...prev,
          isConnected: true,
          connectionError: null,
          loading: false,
        }));
        reconnectAttempts.current = 0;
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          handleMessage(message);
        } catch (error) {
          console.error('Error parsing printer WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('Disconnected from printer manager:', event.code, event.reason);
        setState(prev => ({
          ...prev,
          isConnected: false,
        }));

        // Attempt to reconnect
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            connect();
          }, delay);
        } else {
          setState(prev => ({
            ...prev,
            connectionError: 'Failed to connect after multiple attempts',
          }));
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('Printer WebSocket error:', error);
        setState(prev => ({
          ...prev,
          connectionError: 'Connection error',
        }));
      };

    } catch (error) {
      console.error('Error creating printer WebSocket connection:', error);
      setState(prev => ({
        ...prev,
        connectionError: 'Failed to create connection',
        loading: false,
      }));
    }
  }, []);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    setState(prev => ({
      ...prev,
      isConnected: false,
    }));
  }, []);

  const handleMessage = useCallback((message: any) => {
    switch (message.type) {
      case 'printer-status':
        setState(prev => ({
          ...prev,
          printers: message.data,
        }));
        break;

      case 'printer-registered':
        setState(prev => ({
          ...prev,
          printers: [...prev.printers.filter(p => p.id !== message.data.id), message.data],
        }));
        break;

      case 'printer-updated':
        setState(prev => ({
          ...prev,
          printers: prev.printers.map(printer =>
            printer.id === message.data.id ? message.data : printer
          ),
        }));
        break;

      case 'sensor-update':
        setState(prev => ({
          ...prev,
          printers: prev.printers.map(printer =>
            printer.id === message.data.printerId
              ? { ...printer, sensors: message.data.sensors, lastUpdate: Date.now() }
              : printer
          ),
        }));
        break;

      case 'job-progress':
        setState(prev => ({
          ...prev,
          printers: prev.printers.map(printer =>
            printer.id === message.data.printerId
              ? { ...printer, currentJob: message.data.job }
              : printer
          ),
        }));
        break;

      case 'job-added':
        setState(prev => ({
          ...prev,
          printers: prev.printers.map(printer =>
            printer.id === message.data.printerId
              ? { ...printer, queue: [...printer.queue, message.data.job] }
              : printer
          ),
        }));
        break;

      case 'printer-alerts':
        // Handle printer alerts (could show notifications)
        console.warn('Printer alerts:', message.data);
        break;
    }
  }, []);

  // Printer management functions
  const registerPrinter = useCallback(async (printer: Omit<Printer, 'lastUpdate' | 'queue' | 'totalPrintTime' | 'totalFilamentUsed'>) => {
    try {
      const response = await fetch(`/api/printers/${printer.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(printer),
      });

      if (!response.ok) {
        throw new Error('Failed to register printer');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error registering printer:', error);
      throw error;
    }
  }, []);

  const sendCommand = useCallback(async (printerId: string, command: Omit<PrinterCommand, 'timestamp' | 'userId'>) => {
    try {
      const response = await fetch(`/api/printers/${printerId}?action=command`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          printerId,
          command: {
            ...command,
            timestamp: Date.now(),
            userId: 'current-user', // Replace with actual user ID
          },
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send command');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error sending printer command:', error);
      throw error;
    }
  }, []);

  const addPrintJob = useCallback(async (printerId: string, job: Omit<PrintJob, 'id' | 'status' | 'createdAt' | 'progress'>) => {
    try {
      const response = await fetch(`/api/printers/${printerId}?action=job`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          printerId,
          job,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to add print job');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error adding print job:', error);
      throw error;
    }
  }, []);

  const updateSensors = useCallback(async (printerId: string, sensors: PrinterSensors) => {
    try {
      const response = await fetch(`/api/printers/${printerId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sensors),
      });

      if (!response.ok) {
        throw new Error('Failed to update sensors');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error updating sensors:', error);
      throw error;
    }
  }, []);

  const getPrinterStatus = useCallback(async (printerId: string) => {
    try {
      const response = await fetch(`/api/printers/${printerId}`);
      
      if (!response.ok) {
        throw new Error('Failed to get printer status');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error getting printer status:', error);
      throw error;
    }
  }, []);

  // Connect on mount
  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Helper functions
  const getPrinterById = useCallback((printerId: string) => {
    return state.printers.find(p => p.id === printerId);
  }, [state.printers]);

  const getActivePrinters = useCallback(() => {
    return state.printers.filter(p => p.status !== 'offline');
  }, [state.printers]);

  const getPrintingPrinters = useCallback(() => {
    return state.printers.filter(p => p.status === 'printing');
  }, [state.printers]);

  const getIdlePrinters = useCallback(() => {
    return state.printers.filter(p => p.status === 'idle');
  }, [state.printers]);

  const getTotalQueueLength = useCallback(() => {
    return state.printers.reduce((total, printer) => total + printer.queue.length, 0);
  }, [state.printers]);

  return {
    ...state,
    connect,
    disconnect,
    registerPrinter,
    sendCommand,
    addPrintJob,
    updateSensors,
    getPrinterStatus,
    getPrinterById,
    getActivePrinters,
    getPrintingPrinters,
    getIdlePrinters,
    getTotalQueueLength,
  };
}
