import { useState } from 'react';

interface ToastOptions {
  title: string;
  description?: string;
  duration?: number;
  type?: 'default' | 'success' | 'error' | 'warning' | 'info';
  variant?: 'default' | 'destructive';
}

interface Toast extends ToastOptions {
  id: string;
}

export function useToast() {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const toast = (options: ToastOptions) => {
    const id = Math.random().toString(36).substring(2, 9);
    const newToast: Toast = {
      id,
      title: options.title,
      description: options.description,
      duration: options.duration || 3000,
      type: options.type || 'default',
      variant: options.variant || 'default',
    };

    setToasts((prevToasts) => [...prevToasts, newToast]);

    // Автоматично видаляємо сповіщення після закінчення часу
    setTimeout(() => {
      setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
    }, newToast.duration);

    return id;
  };

  const dismissToast = (id: string) => {
    setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
  };

  return {
    toast,
    dismissToast,
    toasts,
  };
}
