'use client';

import { MakerWorldModel } from '@/lib/bright-data/enhanced-makerworld-scraper';
import { useCallback, useEffect, useState } from 'react';

interface UseEnhancedMakerWorldModelsOptions {
  limit?: number;
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
  enableCache?: boolean;
}

interface UseEnhancedMakerWorldModelsReturn {
  models: MakerWorldModel[];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  clearCache: () => Promise<void>;
  cacheStatus: {
    hasCachedData: boolean;
    cacheAge: number;
    modelsCount: number;
  } | null;
  lastUpdated: string | null;
  processingTime: number;
}

export function useEnhancedMakerWorldModels(
  options: UseEnhancedMakerWorldModelsOptions = {}
): UseEnhancedMakerWorldModelsReturn {
  const {
    limit = 20,
    autoRefresh = false,
    refreshInterval = 5 * 60 * 1000, // 5 minutes
    enableCache = true
  } = options;

  const [models, setModels] = useState<MakerWorldModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cacheStatus, setCacheStatus] = useState<{
    hasCachedData: boolean;
    cacheAge: number;
    modelsCount: number;
  } | null>(null);
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);
  const [processingTime, setProcessingTime] = useState(0);

  const fetchModels = useCallback(async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);

      console.log(`🔍 Fetching MakerWorld models (limit: ${limit}, forceRefresh: ${forceRefresh})`);

      const url = new URL('/api/models/makerworld-trending', window.location.origin);
      url.searchParams.set('limit', limit.toString());
      if (forceRefresh) {
        url.searchParams.set('refresh', 'true');
      }

      const response = await fetch(url.toString());
      const data = await response.json() as any;

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}`);
      }

      if (data.success && data.data) {
        setModels(data.data.models);
        setLastUpdated(data.data.scrapedAt);
        setProcessingTime(data.data.processingTime);
        console.log(`✅ Loaded ${data.data.models.length} MakerWorld models`);
      } else {
        throw new Error(data.error || 'Failed to fetch models');
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('❌ Error fetching MakerWorld models:', errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  const refresh = useCallback(async () => {
    await fetchModels(true);
  }, [fetchModels]);

  const clearCache = useCallback(async () => {
    try {
      const response = await fetch('/api/models/makerworld-trending', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'clear-cache' })
      });

      const data = await response.json() as any;

      if (data.success) {
        console.log('✅ Cache cleared successfully');
        await fetchModels(true);
      } else {
        throw new Error(data.error || 'Failed to clear cache');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('❌ Error clearing cache:', errorMessage);
      setError(errorMessage);
    }
  }, [fetchModels]);

  const getCacheStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/models/makerworld-trending', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'cache-status' })
      });

      const data = await response.json() as any;

      if (data.success && data.data) {
        setCacheStatus(data.data);
      }
    } catch (err) {
      console.error('❌ Error getting cache status:', err);
    }
  }, []);

  // Initial load
  useEffect(() => {
    fetchModels();
    if (enableCache) {
      getCacheStatus();
    }
  }, [fetchModels, enableCache, getCacheStatus]);

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh || refreshInterval <= 0) return;

    const interval = setInterval(() => {
      console.log('🔄 Auto-refreshing MakerWorld models...');
      fetchModels();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchModels]);

  // Update cache status periodically
  useEffect(() => {
    if (!enableCache) return;

    const interval = setInterval(getCacheStatus, 30000); // Every 30 seconds
    return () => clearInterval(interval);
  }, [enableCache, getCacheStatus]);

  return {
    models,
    loading,
    error,
    refresh,
    clearCache,
    cacheStatus,
    lastUpdated,
    processingTime
  };
}
