import { useState, useEffect, useCallback, useRef } from 'react';

interface User {
  id: string;
  name: string;
  avatar?: string;
  cursor?: { x: number; y: number; z: number };
  camera?: { position: [number, number, number]; target: [number, number, number] };
  isActive: boolean;
  joinedAt: number;
}

interface ModelState {
  id: string;
  version: number;
  lastModified: number;
  modifiedBy: string;
  changes: ModelChange[];
}

interface ModelChange {
  id: string;
  type: 'transform' | 'material' | 'geometry' | 'annotation';
  timestamp: number;
  userId: string;
  data: any;
  applied: boolean;
}

interface Message {
  type: 'user-join' | 'user-leave' | 'cursor-move' | 'camera-sync' | 'model-change' | 'chat' | 'annotation';
  userId: string;
  timestamp: number;
  data: any;
}

interface CollaborationState {
  users: User[];
  modelState: ModelState | null;
  roomId: string;
  isConnected: boolean;
  connectionError: string | null;
}

export function useCollaboration(roomId: string, userId: string, userName: string) {
  const [state, setState] = useState<CollaborationState>({
    users: [],
    modelState: null,
    roomId,
    isConnected: false,
    connectionError: null,
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/api/collaboration/${roomId}/websocket?userId=${userId}&userName=${encodeURIComponent(userName)}`;
      
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('Connected to collaboration room:', roomId);
        setState(prev => ({
          ...prev,
          isConnected: true,
          connectionError: null,
        }));
        reconnectAttempts.current = 0;
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          handleMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('Disconnected from collaboration room:', event.code, event.reason);
        setState(prev => ({
          ...prev,
          isConnected: false,
        }));

        // Attempt to reconnect
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            connect();
          }, delay);
        } else {
          setState(prev => ({
            ...prev,
            connectionError: 'Failed to connect after multiple attempts',
          }));
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setState(prev => ({
          ...prev,
          connectionError: 'Connection error',
        }));
      };

    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      setState(prev => ({
        ...prev,
        connectionError: 'Failed to create connection',
      }));
    }
  }, [roomId, userId, userName]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    setState(prev => ({
      ...prev,
      isConnected: false,
      users: [],
    }));
  }, []);

  const sendMessage = useCallback((message: Omit<Message, 'userId' | 'timestamp'>) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      const fullMessage: Message = {
        ...message,
        userId,
        timestamp: Date.now(),
      };
      wsRef.current.send(JSON.stringify(fullMessage));
    }
  }, [userId]);

  const handleMessage = useCallback((message: any) => {
    switch (message.type) {
      case 'room-state':
        setState(prev => ({
          ...prev,
          users: message.data.users || [],
          modelState: message.data.modelState,
          roomId: message.data.roomId,
        }));
        break;

      case 'user-join':
        setState(prev => ({
          ...prev,
          users: [...prev.users.filter(u => u.id !== message.data.user.id), message.data.user],
        }));
        break;

      case 'user-leave':
        setState(prev => ({
          ...prev,
          users: prev.users.filter(u => u.id !== message.userId),
        }));
        break;

      case 'cursor-move':
        setState(prev => ({
          ...prev,
          users: prev.users.map(user =>
            user.id === message.userId
              ? { ...user, cursor: message.data.cursor }
              : user
          ),
        }));
        break;

      case 'camera-sync':
        setState(prev => ({
          ...prev,
          users: prev.users.map(user =>
            user.id === message.userId
              ? { ...user, camera: message.data.camera }
              : user
          ),
        }));
        break;

      case 'model-change':
        setState(prev => ({
          ...prev,
          modelState: message.data.modelState,
        }));
        break;
    }
  }, []);

  // Collaboration actions
  const updateCursor = useCallback((cursor: { x: number; y: number; z: number }) => {
    sendMessage({
      type: 'cursor-move',
      data: { cursor },
    });
  }, [sendMessage]);

  const syncCamera = useCallback((camera: { position: [number, number, number]; target: [number, number, number] }) => {
    sendMessage({
      type: 'camera-sync',
      data: { camera },
    });
  }, [sendMessage]);

  const updateModel = useCallback((changeType: string, changeData: any, modelId: string) => {
    sendMessage({
      type: 'model-change',
      data: {
        modelId,
        changeType,
        changeData,
      },
    });
  }, [sendMessage]);

  const sendChatMessage = useCallback((content: string) => {
    sendMessage({
      type: 'chat',
      data: { content },
    });
  }, [sendMessage]);

  const addAnnotation = useCallback((annotation: any) => {
    sendMessage({
      type: 'annotation',
      data: annotation,
    });
  }, [sendMessage]);

  // Join room on mount
  useEffect(() => {
    const joinRoom = async () => {
      try {
        const response = await fetch(`/api/collaboration/${roomId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId,
            userName,
          }),
        });

        if (response.ok) {
          connect();
        } else {
          throw new Error('Failed to join room');
        }
      } catch (error) {
        console.error('Error joining room:', error);
        setState(prev => ({
          ...prev,
          connectionError: 'Failed to join room',
        }));
      }
    };

    joinRoom();

    return () => {
      disconnect();
    };
  }, [roomId, userId, userName, connect, disconnect]);

  // Leave room on unmount
  useEffect(() => {
    return () => {
      if (state.isConnected) {
        fetch(`/api/collaboration/${roomId}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId,
          }),
        }).catch(console.error);
      }
    };
  }, [roomId, userId, state.isConnected]);

  return {
    ...state,
    connect,
    disconnect,
    updateCursor,
    syncCamera,
    updateModel,
    sendChatMessage,
    addAnnotation,
  };
}
