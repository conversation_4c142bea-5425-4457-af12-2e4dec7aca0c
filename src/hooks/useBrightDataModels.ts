import { useState, useEffect, useCallback } from 'react';
import { ModelSource } from '@/types/models';

interface TrendingModel {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  designer: {
    name: string;
    avatar?: string;
  };
  stats: {
    views: number;
    downloads: number;
    likes: number;
    trending_score: number;
  };
  platform: ModelSource;
  originalUrl: string;
  tags: string[];
  category: string;
  isFree: boolean;
  price?: number;
}

interface PopularModel {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  designer: {
    name: string;
    avatar?: string;
    verified?: boolean;
  };
  stats: {
    views: number;
    downloads: number;
    likes: number;
    rating: number;
    reviews: number;
  };
  platform: ModelSource;
  originalUrl: string;
  tags: string[];
  category: string;
  isFree: boolean;
  price?: number;
  featured: boolean;
  publishedAt: string;
}

interface RecentModel {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  designer: {
    name: string;
    avatar?: string;
    isNew?: boolean;
  };
  stats: {
    views: number;
    downloads: number;
    likes: number;
    rating?: number;
    reviews: number;
  };
  platform: ModelSource;
  originalUrl: string;
  tags: string[];
  category: string;
  isFree: boolean;
  price?: number;
  isNew: boolean;
  publishedAt: string;
  hoursAgo: number;
}

interface UseBrightDataModelsOptions {
  platform?: ModelSource;
  limit?: number;
  category?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseBrightDataModelsReturn {
  // Trending models
  trendingModels: TrendingModel[];
  trendingLoading: boolean;
  trendingError: string | null;
  
  // Popular models
  popularModels: PopularModel[];
  popularLoading: boolean;
  popularError: string | null;
  
  // Recent models
  recentModels: RecentModel[];
  recentLoading: boolean;
  recentError: string | null;
  
  // Actions
  refreshTrending: () => Promise<void>;
  refreshPopular: () => Promise<void>;
  refreshRecent: () => Promise<void>;
  refreshAll: () => Promise<void>;
  
  // Status
  isLoading: boolean;
  hasError: boolean;
  lastUpdated: Date | null;
}

export function useBrightDataModels(options: UseBrightDataModelsOptions = {}): UseBrightDataModelsReturn {
  const {
    platform = 'printables',
    limit = 12,
    category,
    autoRefresh = false,
    refreshInterval = 300000 // 5 minutes
  } = options;

  // Trending models state
  const [trendingModels, setTrendingModels] = useState<TrendingModel[]>([]);
  const [trendingLoading, setTrendingLoading] = useState(false);
  const [trendingError, setTrendingError] = useState<string | null>(null);

  // Popular models state
  const [popularModels, setPopularModels] = useState<PopularModel[]>([]);
  const [popularLoading, setPopularLoading] = useState(false);
  const [popularError, setPopularError] = useState<string | null>(null);

  // Recent models state
  const [recentModels, setRecentModels] = useState<RecentModel[]>([]);
  const [recentLoading, setRecentLoading] = useState(false);
  const [recentError, setRecentError] = useState<string | null>(null);

  // General state
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Fetch trending models
  const fetchTrendingModels = useCallback(async () => {
    setTrendingLoading(true);
    setTrendingError(null);

    try {
      const params = new URLSearchParams({
        platform,
        limit: limit.toString(),
        ...(category && { category })
      });

      const response = await fetch(`/api/bright-data/trending?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setTrendingModels(data.data.models);
        setLastUpdated(new Date());
      } else {
        throw new Error(data.error || 'Failed to fetch trending models');
      }
    } catch (error) {
      console.error('❌ Error fetching trending models:', error);
      setTrendingError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setTrendingLoading(false);
    }
  }, [platform, limit, category]);

  // Fetch popular models
  const fetchPopularModels = useCallback(async () => {
    setPopularLoading(true);
    setPopularError(null);

    try {
      const params = new URLSearchParams({
        platform,
        limit: limit.toString(),
        ...(category && { category })
      });

      const response = await fetch(`/api/bright-data/popular?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setPopularModels(data.data.models);
        setLastUpdated(new Date());
      } else {
        throw new Error(data.error || 'Failed to fetch popular models');
      }
    } catch (error) {
      console.error('❌ Error fetching popular models:', error);
      setPopularError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setPopularLoading(false);
    }
  }, [platform, limit, category]);

  // Fetch recent models
  const fetchRecentModels = useCallback(async () => {
    setRecentLoading(true);
    setRecentError(null);

    try {
      const params = new URLSearchParams({
        platform,
        limit: limit.toString(),
        ...(category && { category })
      });

      const response = await fetch(`/api/bright-data/recent?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setRecentModels(data.data.models);
        setLastUpdated(new Date());
      } else {
        throw new Error(data.error || 'Failed to fetch recent models');
      }
    } catch (error) {
      console.error('❌ Error fetching recent models:', error);
      setRecentError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setRecentLoading(false);
    }
  }, [platform, limit, category]);

  // Refresh functions
  const refreshTrending = useCallback(async () => {
    await fetchTrendingModels();
  }, [fetchTrendingModels]);

  const refreshPopular = useCallback(async () => {
    await fetchPopularModels();
  }, [fetchPopularModels]);

  const refreshRecent = useCallback(async () => {
    await fetchRecentModels();
  }, [fetchRecentModels]);

  const refreshAll = useCallback(async () => {
    await Promise.all([
      fetchTrendingModels(),
      fetchPopularModels(),
      fetchRecentModels()
    ]);
  }, [fetchTrendingModels, fetchPopularModels, fetchRecentModels]);

  // Initial load
  useEffect(() => {
    refreshAll();
  }, [platform, limit, category]); // Refresh when options change

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      refreshAll();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refreshAll]);

  // Computed values
  const isLoading = trendingLoading || popularLoading || recentLoading;
  const hasError = Boolean(trendingError || popularError || recentError);

  return {
    // Trending models
    trendingModels,
    trendingLoading,
    trendingError,
    
    // Popular models
    popularModels,
    popularLoading,
    popularError,
    
    // Recent models
    recentModels,
    recentLoading,
    recentError,
    
    // Actions
    refreshTrending,
    refreshPopular,
    refreshRecent,
    refreshAll,
    
    // Status
    isLoading,
    hasError,
    lastUpdated
  };
}

// Hook for specific model type
export function useTrendingModels(options: UseBrightDataModelsOptions = {}) {
  const { trendingModels, trendingLoading, trendingError, refreshTrending } = useBrightDataModels(options);
  
  return {
    models: trendingModels,
    loading: trendingLoading,
    error: trendingError,
    refresh: refreshTrending
  };
}

export function usePopularModels(options: UseBrightDataModelsOptions = {}) {
  const { popularModels, popularLoading, popularError, refreshPopular } = useBrightDataModels(options);
  
  return {
    models: popularModels,
    loading: popularLoading,
    error: popularError,
    refresh: refreshPopular
  };
}

export function useRecentModels(options: UseBrightDataModelsOptions = {}) {
  const { recentModels, recentLoading, recentError, refreshRecent } = useBrightDataModels(options);
  
  return {
    models: recentModels,
    loading: recentLoading,
    error: recentError,
    refresh: refreshRecent
  };
}

// Hook for multiple platforms
export function useMultiPlatformModels(platforms: ModelSource[], options: Omit<UseBrightDataModelsOptions, 'platform'> = {}) {
  const [allModels, setAllModels] = useState<{
    trending: TrendingModel[];
    popular: PopularModel[];
    recent: RecentModel[];
  }>({
    trending: [],
    popular: [],
    recent: []
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAllPlatforms = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const promises = platforms.map(async (platform) => {
        const [trendingRes, popularRes, recentRes] = await Promise.all([
          fetch(`/api/bright-data/trending?platform=${platform}&limit=${options.limit || 6}`),
          fetch(`/api/bright-data/popular?platform=${platform}&limit=${options.limit || 6}`),
          fetch(`/api/bright-data/recent?platform=${platform}&limit=${options.limit || 6}`)
        ]);

        const [trending, popular, recent] = await Promise.all([
          trendingRes.json(),
          popularRes.json(),
          recentRes.json()
        ]);

        return {
          platform,
          trending: trending.success ? trending.data.models : [],
          popular: popular.success ? popular.data.models : [],
          recent: recent.success ? recent.data.models : []
        };
      });

      const results = await Promise.all(promises);
      
      // Combine all results
      const combined = {
        trending: results.flatMap(r => r.trending),
        popular: results.flatMap(r => r.popular),
        recent: results.flatMap(r => r.recent)
      };

      setAllModels(combined);
    } catch (error) {
      console.error('❌ Error fetching multi-platform models:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [platforms, options.limit]);

  useEffect(() => {
    if (platforms.length > 0) {
      fetchAllPlatforms();
    }
  }, [platforms, fetchAllPlatforms]);

  return {
    models: allModels,
    loading,
    error,
    refresh: fetchAllPlatforms
  };
}
