'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useState } from 'react';
// import { ScrollArea } from '@/components/ui/scroll-area';
import { usePrinterManager } from '@/hooks/usePrinterManager';
import {
    Activity,
    Clock,
    Droplets,
    Pause,
    Play,
    Printer,
    Settings,
    Square,
    Thermometer,
    Wifi,
    WifiOff
} from 'lucide-react';

interface RealTimePrinterMonitorProps {
  onPrinterSelect?: (printerId: string) => void;
  selectedPrinterId?: string;
}

export default function RealTimePrinterMonitor({
  onPrinterSelect,
  selectedPrinterId
}: RealTimePrinterMonitorProps) {
  const printerManager = usePrinterManager();
  const [selectedPrinter, setSelectedPrinter] = useState<string | null>(selectedPrinterId || null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'printing':
        return 'bg-blue-500';
      case 'idle':
        return 'bg-green-500';
      case 'paused':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      case 'maintenance':
        return 'bg-purple-500';
      case 'offline':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'printing':
        return 'Друкує';
      case 'idle':
        return 'Готовий';
      case 'paused':
        return 'Пауза';
      case 'error':
        return 'Помилка';
      case 'maintenance':
        return 'Обслуговування';
      case 'offline':
        return 'Офлайн';
      default:
        return 'Невідомо';
    }
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFilament = (grams: number) => {
    if (grams >= 1000) {
      return `${(grams / 1000).toFixed(1)} кг`;
    }
    return `${grams} г`;
  };

  const handlePrinterClick = (printerId: string) => {
    setSelectedPrinter(printerId);
    onPrinterSelect?.(printerId);
  };

  const handleCommand = async (printerId: string, command: string) => {
    try {
      await printerManager.sendCommand(printerId, {
        type: command as any,
      });
    } catch (error) {
      console.error('Error sending command:', error);
    }
  };

  const selectedPrinterData = selectedPrinter 
    ? printerManager.getPrinterById(selectedPrinter)
    : null;

  return (
    <div className="flex h-full">
      {/* Printer List */}
      <div className="w-80 border-r bg-background">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold flex items-center gap-2">
              <Printer className="w-4 h-4" />
              Принтери
            </h3>
            <Badge variant={printerManager.isConnected ? "default" : "destructive"} className="text-xs">
              {printerManager.isConnected ? (
                <><Wifi className="w-3 h-3 mr-1" />Онлайн</>
              ) : (
                <><WifiOff className="w-3 h-3 mr-1" />Офлайн</>
              )}
            </Badge>
          </div>
          
          {printerManager.connectionError && (
            <div className="text-xs text-red-500 mb-2">
              {printerManager.connectionError}
            </div>
          )}

          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="text-center">
              <div className="font-semibold text-blue-600">{printerManager.getPrintingPrinters().length}</div>
              <div className="text-muted-foreground">Друкують</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-green-600">{printerManager.getIdlePrinters().length}</div>
              <div className="text-muted-foreground">Готові</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-purple-600">{printerManager.getTotalQueueLength()}</div>
              <div className="text-muted-foreground">В черзі</div>
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-auto">
          <div className="p-2 space-y-2">
            {printerManager.loading ? (
              <div className="text-center py-8 text-muted-foreground">
                Завантаження принтерів...
              </div>
            ) : printerManager.printers.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                Принтери не знайдені
              </div>
            ) : (
              printerManager.printers.map((printer) => (
                <Card 
                  key={printer.id}
                  className={`p-3 cursor-pointer transition-colors hover:bg-accent/50 ${
                    selectedPrinter === printer.id ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => handlePrinterClick(printer.id)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${getStatusColor(printer.status)}`} />
                      <span className="font-medium text-sm">{printer.name}</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {getStatusText(printer.status)}
                    </Badge>
                  </div>

                  <div className="text-xs text-muted-foreground mb-2">
                    {printer.location} • {printer.type}
                  </div>

                  {printer.currentJob && (
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span className="truncate">{printer.currentJob.modelName}</span>
                        <span>{printer.currentJob.progress}%</span>
                      </div>
                      <Progress value={printer.currentJob.progress} className="h-1" />
                    </div>
                  )}

                  <div className="flex items-center justify-between mt-2 text-xs">
                    <div className="flex items-center gap-1">
                      <Thermometer className="w-3 h-3" />
                      <span>{printer.sensors.temperature.nozzle}°C</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Droplets className="w-3 h-3" />
                      <span>{formatFilament(printer.sensors.filament.remaining)}</span>
                    </div>
                    {printer.queue.length > 0 && (
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span>{printer.queue.length}</span>
                      </div>
                    )}
                  </div>
                </Card>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Printer Details */}
      <div className="flex-1 flex flex-col">
        {selectedPrinterData ? (
          <>
            {/* Header */}
            <div className="p-6 border-b">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-2xl font-bold">{selectedPrinterData.name}</h2>
                  <p className="text-muted-foreground">
                    {selectedPrinterData.location} • {selectedPrinterData.type}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge 
                    variant={selectedPrinterData.status === 'printing' ? 'default' : 'outline'}
                    className={`${getStatusColor(selectedPrinterData.status)} text-white`}
                  >
                    {getStatusText(selectedPrinterData.status)}
                  </Badge>
                  <Button size="sm" variant="outline">
                    <Settings className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center gap-2">
                {selectedPrinterData.status === 'idle' && (
                  <Button 
                    size="sm" 
                    onClick={() => handleCommand(selectedPrinterData.id, 'start')}
                    disabled={selectedPrinterData.queue.length === 0}
                  >
                    <Play className="w-4 h-4 mr-1" />
                    Почати друк
                  </Button>
                )}
                {selectedPrinterData.status === 'printing' && (
                  <>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleCommand(selectedPrinterData.id, 'pause')}
                    >
                      <Pause className="w-4 h-4 mr-1" />
                      Пауза
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive"
                      onClick={() => handleCommand(selectedPrinterData.id, 'cancel')}
                    >
                      <Square className="w-4 h-4 mr-1" />
                      Зупинити
                    </Button>
                  </>
                )}
                {selectedPrinterData.status === 'paused' && (
                  <Button 
                    size="sm"
                    onClick={() => handleCommand(selectedPrinterData.id, 'resume')}
                  >
                    <Play className="w-4 h-4 mr-1" />
                    Продовжити
                  </Button>
                )}
              </div>
            </div>

            {/* Current Job */}
            {selectedPrinterData.currentJob && (
              <div className="p-6 border-b">
                <h3 className="font-semibold mb-3">Поточне завдання</h3>
                <Card className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="font-medium">{selectedPrinterData.currentJob.modelName}</h4>
                      <p className="text-sm text-muted-foreground">
                        Матеріал: {selectedPrinterData.currentJob.material}
                      </p>
                    </div>
                    <Badge variant="outline">
                      {selectedPrinterData.currentJob.priority}
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Прогрес</span>
                      <span>{selectedPrinterData.currentJob.progress}%</span>
                    </div>
                    <Progress value={selectedPrinterData.currentJob.progress} />
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Час друку:</span>
                        <div className="font-medium">
                          {selectedPrinterData.currentJob.actualTime 
                            ? formatTime(selectedPrinterData.currentJob.actualTime / 1000)
                            : formatTime(selectedPrinterData.currentJob.estimatedTime / 1000)
                          }
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Залишилось:</span>
                        <div className="font-medium">
                          {formatTime((selectedPrinterData.currentJob.estimatedTime * (100 - selectedPrinterData.currentJob.progress) / 100) / 1000)}
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            )}

            {/* Sensors */}
            <div className="p-6 border-b">
              <h3 className="font-semibold mb-3">Датчики</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card className="p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Thermometer className="w-4 h-4 text-red-500" />
                    <span className="text-sm font-medium">Сопло</span>
                  </div>
                  <div className="text-lg font-bold">
                    {selectedPrinterData.sensors.temperature.nozzle}°C
                  </div>
                </Card>

                <Card className="p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Thermometer className="w-4 h-4 text-orange-500" />
                    <span className="text-sm font-medium">Стіл</span>
                  </div>
                  <div className="text-lg font-bold">
                    {selectedPrinterData.sensors.temperature.bed}°C
                  </div>
                </Card>

                <Card className="p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Droplets className="w-4 h-4 text-blue-500" />
                    <span className="text-sm font-medium">Філамент</span>
                  </div>
                  <div className="text-lg font-bold">
                    {formatFilament(selectedPrinterData.sensors.filament.remaining)}
                  </div>
                </Card>

                <Card className="p-3">
                  <div className="flex items-center gap-2 mb-1">
                    <Activity className="w-4 h-4 text-green-500" />
                    <span className="text-sm font-medium">Позиція</span>
                  </div>
                  <div className="text-sm font-bold">
                    X:{selectedPrinterData.sensors.position.x} Y:{selectedPrinterData.sensors.position.y} Z:{selectedPrinterData.sensors.position.z}
                  </div>
                </Card>
              </div>
            </div>

            {/* Queue */}
            <div className="flex-1 p-6">
              <h3 className="font-semibold mb-3">
                Черга завдань ({selectedPrinterData.queue.length})
              </h3>
              
              {selectedPrinterData.queue.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  Черга порожня
                </div>
              ) : (
                <div className="h-64 overflow-auto">
                  <div className="space-y-2">
                    {selectedPrinterData.queue.map((job, index) => (
                      <Card key={job.id} className="p-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-sm">{job.modelName}</div>
                            <div className="text-xs text-muted-foreground">
                              {job.material} • {formatTime(job.estimatedTime / 1000)}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              #{index + 1}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {job.priority}
                            </Badge>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <Printer className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p>Оберіть принтер для перегляду деталей</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
