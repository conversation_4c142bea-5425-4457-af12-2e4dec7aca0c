'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Activity,
  AlertTriangle,
  Battery,
  CheckCircle,
  Clock,
  Download,
  Eye,
  Layers,
  Pause,
  Play,
  Printer,
  RotateCw,
  Settings,
  Square,
  Thermometer,
  Wifi,
  WifiOff,
  Zap
} from 'lucide-react';
import { useState } from 'react';

interface PrinterStatus {
  id: string;
  name: string;
  model: string;
  status: 'idle' | 'printing' | 'paused' | 'error' | 'offline';
  connected: boolean;
  currentJob?: {
    fileName: string;
    progress: number;
    timeRemaining: number;
    timeElapsed: number;
    layerCurrent: number;
    layerTotal: number;
  };
  temperatures: {
    hotend: { current: number; target: number };
    bed: { current: number; target: number };
    chamber?: { current: number; target: number };
  };
  position: {
    x: number;
    y: number;
    z: number;
  };
  filament: {
    material: string;
    color: string;
    remaining: number; // percentage
  };
  lastUpdate: string;
}

interface PrinterManagerProps {
  printers?: PrinterStatus[];
  onPrinterSelect?: (printerId: string) => void;
  onStartPrint?: (printerId: string, modelId: string) => void;
  onPausePrint?: (printerId: string) => void;
  onStopPrint?: (printerId: string) => void;
  className?: string;
}

const MOCK_PRINTERS: PrinterStatus[] = [
  {
    id: 'printer_1',
    name: 'Ender 3 Pro',
    model: 'Creality Ender 3 Pro',
    status: 'printing',
    connected: true,
    currentJob: {
      fileName: 'dragon_articulated.gcode',
      progress: 67,
      timeRemaining: 2.5 * 60 * 60, // 2.5 hours in seconds
      timeElapsed: 5 * 60 * 60, // 5 hours
      layerCurrent: 134,
      layerTotal: 200
    },
    temperatures: {
      hotend: { current: 210, target: 210 },
      bed: { current: 60, target: 60 }
    },
    position: { x: 125.5, y: 89.2, z: 15.8 },
    filament: {
      material: 'PLA',
      color: 'Red',
      remaining: 78
    },
    lastUpdate: new Date().toISOString()
  },
  {
    id: 'printer_2',
    name: 'Prusa i3 MK3S+',
    model: 'Prusa i3 MK3S+',
    status: 'idle',
    connected: true,
    temperatures: {
      hotend: { current: 25, target: 0 },
      bed: { current: 23, target: 0 }
    },
    position: { x: 0, y: 0, z: 0 },
    filament: {
      material: 'PETG',
      color: 'Clear',
      remaining: 45
    },
    lastUpdate: new Date().toISOString()
  },
  {
    id: 'printer_3',
    name: 'Bambu X1 Carbon',
    model: 'Bambu Lab X1 Carbon',
    status: 'error',
    connected: false,
    temperatures: {
      hotend: { current: 0, target: 0 },
      bed: { current: 0, target: 0 },
      chamber: { current: 0, target: 0 }
    },
    position: { x: 0, y: 0, z: 0 },
    filament: {
      material: 'ABS',
      color: 'Black',
      remaining: 12
    },
    lastUpdate: new Date(Date.now() - 10 * 60 * 1000).toISOString() // 10 minutes ago
  }
];

function PrinterCard({ 
  printer, 
  onSelect, 
  onStart, 
  onPause, 
  onStop 
}: { 
  printer: PrinterStatus;
  onSelect?: (id: string) => void;
  onStart?: (id: string) => void;
  onPause?: (id: string) => void;
  onStop?: (id: string) => void;
}) {
  const getStatusColor = () => {
    switch (printer.status) {
      case 'printing':
        return 'bg-green-500';
      case 'paused':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      case 'offline':
        return 'bg-gray-500';
      default:
        return 'bg-blue-500';
    }
  };

  const getStatusIcon = () => {
    switch (printer.status) {
      case 'printing':
        return <Play className="w-4 h-4" />;
      case 'paused':
        return <Pause className="w-4 h-4" />;
      case 'error':
        return <AlertTriangle className="w-4 h-4" />;
      case 'offline':
        return <WifiOff className="w-4 h-4" />;
      default:
        return <CheckCircle className="w-4 h-4" />;
    }
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  return (
    <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={() => onSelect?.(printer.id)}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${getStatusColor()}`} />
          <h3 className="font-semibold">{printer.name}</h3>
          {printer.connected ? (
            <Wifi className="w-4 h-4 text-green-500" />
          ) : (
            <WifiOff className="w-4 h-4 text-red-500" />
          )}
        </div>
        <Badge variant="outline" className="flex items-center gap-1">
          {getStatusIcon()}
          {printer.status}
        </Badge>
      </div>

      <p className="text-sm text-muted-foreground mb-3">{printer.model}</p>

      {/* Current Job */}
      {printer.currentJob && (
        <div className="mb-4 p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">{printer.currentJob.fileName}</span>
            <span className="text-sm text-muted-foreground">
              {printer.currentJob.progress}%
            </span>
          </div>
          <Progress value={printer.currentJob.progress} className="mb-2" />
          <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              <span>{formatTime(printer.currentJob.timeRemaining)} left</span>
            </div>
            <div className="flex items-center gap-1">
              <Layers className="w-3 h-3" />
              <span>{printer.currentJob.layerCurrent}/{printer.currentJob.layerTotal}</span>
            </div>
          </div>
        </div>
      )}

      {/* Temperatures */}
      <div className="grid grid-cols-2 gap-2 mb-3">
        <div className="flex items-center gap-2 text-sm">
          <Thermometer className="w-4 h-4 text-red-500" />
          <span>Hotend: {printer.temperatures.hotend.current}°C</span>
        </div>
        <div className="flex items-center gap-2 text-sm">
          <Thermometer className="w-4 h-4 text-blue-500" />
          <span>Bed: {printer.temperatures.bed.current}°C</span>
        </div>
      </div>

      {/* Filament */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2 text-sm">
          <div className="w-3 h-3 rounded-full bg-red-500" />
          <span>{printer.filament.material} - {printer.filament.color}</span>
        </div>
        <div className="flex items-center gap-1 text-sm">
          <Battery className="w-4 h-4" />
          <span>{printer.filament.remaining}%</span>
        </div>
      </div>

      {/* Controls */}
      <div className="flex gap-2">
        {printer.status === 'printing' && (
          <>
            <Button size="sm" variant="outline" onClick={(e: React.MouseEvent) => { e.stopPropagation(); onPause?.(printer.id); }}>
              <Pause className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="outline" onClick={(e: React.MouseEvent) => { e.stopPropagation(); onStop?.(printer.id); }}>
              <Square className="w-4 h-4" />
            </Button>
          </>
        )}
        {printer.status === 'paused' && (
          <>
            <Button size="sm" variant="outline" onClick={(e: React.MouseEvent) => { e.stopPropagation(); onStart?.(printer.id); }}>
              <Play className="w-4 h-4" />
            </Button>
            <Button size="sm" variant="outline" onClick={(e: React.MouseEvent) => { e.stopPropagation(); onStop?.(printer.id); }}>
              <Square className="w-4 h-4" />
            </Button>
          </>
        )}
        {printer.status === 'idle' && (
          <Button size="sm" variant="outline" onClick={(e: React.MouseEvent) => { e.stopPropagation(); onStart?.(printer.id); }}>
            <Play className="w-4 h-4" />
            Start Print
          </Button>
        )}
        <Button size="sm" variant="outline" onClick={(e: React.MouseEvent) => { e.stopPropagation(); }}>
          <Settings className="w-4 h-4" />
        </Button>
      </div>
    </Card>
  );
}

function PrinterDetails({ printer }: { printer: PrinterStatus }) {
  return (
    <div className="space-y-6">
      {/* Status Overview */}
      <Card className="p-4">
        <h3 className="font-semibold mb-4">Статус принтера</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold">{printer.temperatures.hotend.current}°C</div>
            <div className="text-sm text-muted-foreground">Hotend</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{printer.temperatures.bed.current}°C</div>
            <div className="text-sm text-muted-foreground">Bed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{printer.position.z.toFixed(1)}mm</div>
            <div className="text-sm text-muted-foreground">Z Position</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{printer.filament.remaining}%</div>
            <div className="text-sm text-muted-foreground">Filament</div>
          </div>
        </div>
      </Card>

      {/* Current Job Details */}
      {printer.currentJob && (
        <Card className="p-4">
          <h3 className="font-semibold mb-4">Поточне завдання</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-2">
                <span className="font-medium">{printer.currentJob.fileName}</span>
                <span className="text-muted-foreground">{printer.currentJob.progress}%</span>
              </div>
              <Progress value={printer.currentJob.progress} className="h-3" />
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Залишилось:</span>
                <p className="font-medium">{Math.floor(printer.currentJob.timeRemaining / 3600)}h {Math.floor((printer.currentJob.timeRemaining % 3600) / 60)}m</p>
              </div>
              <div>
                <span className="text-muted-foreground">Пройшло:</span>
                <p className="font-medium">{Math.floor(printer.currentJob.timeElapsed / 3600)}h {Math.floor((printer.currentJob.timeElapsed % 3600) / 60)}m</p>
              </div>
              <div>
                <span className="text-muted-foreground">Шар:</span>
                <p className="font-medium">{printer.currentJob.layerCurrent} / {printer.currentJob.layerTotal}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Швидкість:</span>
                <p className="font-medium">100%</p>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Position and Controls */}
      <Card className="p-4">
        <h3 className="font-semibold mb-4">Позиція та керування</h3>
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="text-lg font-bold">X: {printer.position.x}</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold">Y: {printer.position.y}</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold">Z: {printer.position.z}</div>
          </div>
        </div>
        
        <div className="flex gap-2 justify-center">
          <Button size="sm" variant="outline">
            <RotateCw className="w-4 h-4" />
            Home All
          </Button>
          <Button size="sm" variant="outline">
            <Zap className="w-4 h-4" />
            Disable Motors
          </Button>
          <Button size="sm" variant="outline">
            <Eye className="w-4 h-4" />
            Camera
          </Button>
        </div>
      </Card>
    </div>
  );
}

export default function PrinterManager({
  printers = MOCK_PRINTERS,
  onPrinterSelect,
  onStartPrint,
  onPausePrint,
  onStopPrint,
  className = ""
}: PrinterManagerProps) {
  const [selectedPrinter, setSelectedPrinter] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  const handlePrinterSelect = (printerId: string) => {
    setSelectedPrinter(printerId);
    onPrinterSelect?.(printerId);
  };

  const selectedPrinterData = printers.find(p => p.id === selectedPrinter);

  const activePrinters = printers.filter(p => p.status === 'printing').length;
  const idlePrinters = printers.filter(p => p.status === 'idle').length;
  const errorPrinters = printers.filter(p => p.status === 'error').length;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Управління принтерами</h2>
          <p className="text-muted-foreground">
            {activePrinters} активних • {idlePrinters} вільних • {errorPrinters} з помилками
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline">
            <Download className="w-4 h-4" />
            Завантажити G-code
          </Button>
          <Button size="sm" variant="outline">
            <Settings className="w-4 h-4" />
            Налаштування
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Огляд</TabsTrigger>
          <TabsTrigger value="details">Деталі</TabsTrigger>
          <TabsTrigger value="queue">Черга друку</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {printers.map((printer) => (
              <PrinterCard
                key={printer.id}
                printer={printer}
                onSelect={handlePrinterSelect}
                onStart={(id) => onStartPrint?.(id, 'default-model')}
                onPause={onPausePrint}
                onStop={onStopPrint}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="details" className="space-y-6">
          {selectedPrinterData ? (
            <PrinterDetails printer={selectedPrinterData} />
          ) : (
            <Card className="p-8 text-center">
              <Printer className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">Виберіть принтер</h3>
              <p className="text-muted-foreground">
                Оберіть принтер з списку для перегляду детальної інформації
              </p>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="queue" className="space-y-6">
          <Card className="p-8 text-center">
            <Activity className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">Черга друку</h3>
            <p className="text-muted-foreground">
              Управління чергою завдань для друку
            </p>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
