'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Heart, Download } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface Model {
  id: string;
  title: string;
  thumbnail: string;
  designer: string;
  price: number | 'Free';
  likes: number;
  downloads: number;
}

interface RecommendedModelsProps {
  models: Model[];
  title?: string;
  className?: string;
}

const RecommendedModels: React.FC<RecommendedModelsProps> = ({ 
  models, 
  title = "Рекомендовані моделі", 
  className = "" 
}) => {
  return (
    <div className={className}>
      {title && <h2 className="text-2xl font-bold mb-6">{title}</h2>}
      
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {models.map((model) => (
          <Link key={model.id} href={`/models/${model.id}`}>
            <Card className="overflow-hidden h-full transition-all duration-200 hover:shadow-md">
              <div className="relative aspect-square">
                <Image 
                  src={model.thumbnail}
                  alt={model.title}
                  fill
                  className="object-cover"
                />
                <div className="absolute top-2 right-2 bg-background/80 backdrop-blur-sm rounded-full px-2 py-1 text-xs font-medium">
                  {model.price === 0 || model.price === 'Free' ? 'Безкоштовно' : `$${typeof model.price === 'number' ? model.price.toFixed(2) : model.price}`}
                </div>
              </div>
              <CardContent className="p-4">
                <h3 className="font-medium text-lg mb-1 line-clamp-1">{model.title}</h3>
                <p className="text-sm text-muted-foreground mb-3">by {model.designer}</p>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <div className="flex items-center">
                    <Heart className="h-4 w-4 mr-1 text-red-500" />
                    {model.likes}
                  </div>
                  <div className="flex items-center">
                    <Download className="h-4 w-4 mr-1" />
                    {model.downloads}
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default RecommendedModels;
