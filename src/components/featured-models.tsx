'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { apiClient } from '@/lib/api/config';
import { AnimatePresence, motion } from 'framer-motion';
import {
    AlertCircle,
    ChevronRight,
    Download,
    ExternalLink,
    Eye,
    Heart,
    RefreshCw,
    Star,
    TrendingUp,
    User
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';

// Інтерфейс для популярної моделі
interface PopularModel {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  designer: {
    name: string;
    avatar?: string;
    verified?: boolean;
  };
  stats: {
    views: number;
    downloads: number;
    likes: number;
    rating: number;
    reviews: number;
  };
  platform: 'printables' | 'makerworld' | 'thangs' | 'thingiverse' | 'myminifactory' | 'local';
  originalUrl: string;
  tags: string[];
  category: string;
  isFree: boolean;
  price?: number;
  featured: boolean;
  publishedAt: string;
}

// Компонент для відображення справжніх популярних моделей
const FeaturedModels = () => {
  const [models, setModels] = useState<PopularModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all');
  const [refreshing, setRefreshing] = useState(false);

  // Функція для отримання популярних моделей
  const fetchPopularModels = async (_platform: string = 'all') => {
    try {
      setLoading(true);
      setError(null);

      // Use the API client to get popular models
      const response = await apiClient.getPopularModels() as any;

      if (response.success && response.data && response.data.models) {
        // Map the API response to our interface
        const mappedModels: PopularModel[] = response.data.models.map((model: any) => ({
          ...model,
          stats: {
            views: model.stats.views || 0,
            downloads: model.stats.downloads || 0,
            likes: model.stats.likes || 0,
            rating: model.stats.rating || (model.stats.popularity_score ? model.stats.popularity_score / 20 : 4.5),
            reviews: model.stats.reviews || Math.floor((model.stats.downloads || 100) / 10)
          },
          featured: (model.stats.popularity_score && model.stats.popularity_score > 80) || false,
          publishedAt: model.publishedAt || new Date().toISOString()
        }));

        setModels(mappedModels);
      } else {
        throw new Error('Не вдалося завантажити моделі з API');
      }
    } catch (err) {
      console.error('Помилка завантаження популярних моделей:', err);
      setError(err instanceof Error ? err.message : 'Невідома помилка');

      // Fallback to empty list on error
      setModels([]);
    } finally {
      setLoading(false);
    }
  };

  // Функція для оновлення даних
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchPopularModels(selectedPlatform);
    setRefreshing(false);
  };

  // Завантаження даних при монтуванні компонента
  useEffect(() => {
    fetchPopularModels(selectedPlatform);
  }, [selectedPlatform]);



  return (
    <section className="w-full py-20 px-4 bg-gradient-to-br from-slate-50/80 via-blue-50/80 to-indigo-50/80 dark:from-gray-900/80 dark:via-blue-900/20 dark:to-indigo-900/20">
      <div className="container mx-auto">
        {/* Header Section */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-500/10 to-red-500/10 backdrop-blur-md rounded-full text-orange-600 dark:text-orange-400 text-sm font-medium mb-6 border border-orange-200/50 dark:border-orange-700/50">
            <span className="w-2 h-2 bg-orange-500 rounded-full mr-3 animate-pulse"></span>
            🔥 Популярні моделі
          </div>

          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-orange-600 via-red-600 to-pink-600 bg-clip-text text-transparent">
            Найпопулярніші 3D моделі
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Відкрийте для себе найпопулярніші та найзавантажуваніші 3D моделі з провідних платформ світу
          </p>
        </motion.div>

        {/* Platform Filter */}
        <motion.div
          className="flex justify-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="flex gap-2 p-2 bg-white/60 dark:bg-gray-800/60 backdrop-blur-md rounded-2xl border border-white/20">
            {['all', 'printables', 'makerworld', 'thangs', 'thingiverse'].map((platform) => (
              <Button
                key={platform}
                variant={selectedPlatform === platform ? "default" : "ghost"}
                size="sm"
                onClick={() => setSelectedPlatform(platform)}
                className={`transition-all duration-300 ${
                  selectedPlatform === platform
                    ? 'bg-gradient-to-r from-orange-500 to-red-600 text-white shadow-lg'
                    : 'hover:bg-white/20'
                }`}
              >
                {platform === 'all' ? 'Всі' : platform.charAt(0).toUpperCase() + platform.slice(1)}
              </Button>
            ))}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="hover:bg-white/20"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </motion.div>

        {/* Models Grid */}
        <AnimatePresence mode="wait">
          {loading ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
            >
              {Array.from({ length: 8 }).map((_, index) => (
                <Card key={index} className="overflow-hidden">
                  <Skeleton className="aspect-square w-full" />
                  <CardContent className="p-4">
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-3 w-1/2 mb-3" />
                    <div className="flex justify-between">
                      <Skeleton className="h-3 w-12" />
                      <Skeleton className="h-3 w-12" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </motion.div>
          ) : error ? (
            <motion.div
              key="error"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center py-12"
            >
              <AlertCircle className="w-16 h-16 mx-auto text-red-500 mb-4" />
              <h3 className="text-xl font-semibold mb-2">Помилка завантаження</h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">{error}</p>
              <Button onClick={handleRefresh} className="bg-gradient-to-r from-orange-500 to-red-600">
                <RefreshCw className="h-4 w-4 mr-2" />
                Спробувати знову
              </Button>
            </motion.div>
          ) : (
            <motion.div
              key="models"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
            >
              {models.map((model, index) => (
                <ModelCard key={model.id} model={model} index={index} />
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        {/* View More Button */}
        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <Button
            asChild
            size="lg"
            className="bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white shadow-2xl shadow-orange-500/25 transform hover:scale-105 transition-all duration-300"
          >
            <Link href="/marketplace" className="flex items-center gap-2">
              Переглянути всі моделі
              <ChevronRight className="h-5 w-5" />
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  );
};

// Компонент для відображення окремої моделі
const ModelCard = ({ model, index }: { model: PopularModel; index: number }) => {
  const getPlatformInfo = (platform: string) => {
    switch (platform) {
      case 'printables':
        return { name: 'Printables', icon: '🖨️', color: 'from-green-500 to-emerald-500' };
      case 'makerworld':
        return { name: 'MakerWorld', icon: '🏭', color: 'from-blue-500 to-cyan-500' };
      case 'thangs':
        return { name: 'Thangs', icon: '💎', color: 'from-purple-500 to-pink-500' };
      case 'thingiverse':
        return { name: 'Thingiverse', icon: '🔧', color: 'from-orange-500 to-red-500' };
      case 'myminifactory':
        return { name: 'MyMiniFactory', icon: '🏭', color: 'from-indigo-500 to-purple-500' };
      default:
        return { name: 'Local', icon: '🏠', color: 'from-gray-500 to-slate-500' };
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const platformInfo = getPlatformInfo(model.platform);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group"
    >
      <Card className="overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm transform hover:scale-105">
        <div className="relative">
          {/* Image Container */}
          <div className="relative aspect-square overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800">
            <Image
              src={model.thumbnail}
              alt={model.title}
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-110"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />

            {/* Platform badge */}
            <div className="absolute top-3 left-3">
              <Badge className={`bg-gradient-to-r ${platformInfo.color} text-white border-0 shadow-lg`}>
                <span className="mr-1">{platformInfo.icon}</span>
                {platformInfo.name}
              </Badge>
            </div>

            {/* Price badge */}
            <div className="absolute top-3 right-3">
              <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-full px-3 py-1 border border-white/20 shadow-lg">
                <span className="font-bold text-sm">
                  {model.isFree ? 'Free' : `$${model.price}`}
                </span>
              </div>
            </div>

            {/* Trending indicator */}
            {model.featured && (
              <div className="absolute bottom-3 left-3">
                <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white border-0 shadow-lg">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  Trending
                </Badge>
              </div>
            )}
          </div>

          {/* Content */}
          <CardContent className="p-6">
            {/* Title and Category */}
            <div className="mb-4">
              <Link href={`/marketplace/model/${model.id}`}>
                <h3 className="font-bold text-lg mb-2 line-clamp-2 hover:text-orange-600 dark:hover:text-orange-400 transition-colors">
                  {model.title}
                </h3>
              </Link>
              <Badge variant="secondary" className="text-xs">
                {model.category}
              </Badge>
            </div>

            {/* Designer info */}
            <div className="flex items-center gap-2 mb-4">
              <div className="w-6 h-6 rounded-full bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center overflow-hidden">
                <User className="h-3 w-3 text-white" />
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {model.designer.name}
              </span>
              {model.designer.verified && (
                <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs">✓</span>
                </div>
              )}
            </div>

            {/* Stats */}
            <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <Download className="h-4 w-4" />
                  <span>{formatNumber(model.stats.downloads)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="h-4 w-4" />
                  <span>{formatNumber(model.stats.views)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Heart className="h-4 w-4" />
                  <span>{formatNumber(model.stats.likes)}</span>
                </div>
              </div>

              {model.stats.rating && (
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-medium">{model.stats.rating.toFixed(1)}</span>
                </div>
              )}
            </div>

            {/* Action buttons */}
            <div className="flex gap-2">
              <Button
                asChild
                className="flex-1 bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 shadow-lg"
              >
                <Link href={`/marketplace/model/${model.id}`}>
                  {model.isFree ? 'Download' : 'View Details'}
                </Link>
              </Button>

              <Button
                variant="outline"
                size="icon"
                onClick={() => window.open(model.originalUrl, '_blank')}
                className="border-orange-200 hover:bg-orange-50 dark:border-orange-700 dark:hover:bg-orange-900/20"
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </div>
      </Card>
    </motion.div>
  );
};

export default FeaturedModels;
