'use client';

/**
 * Advanced Image Gallery - Покращена галерея зображень
 * Підтримує lightbox, zoom, navigation та thumbnails
 */

import { AnimatePresence, motion } from 'framer-motion';
import { 
  ChevronLeft, 
  ChevronRight, 
  Download, 
  Maximize2, 
  Minimize2, 
  RotateCw, 
  X, 
  ZoomIn, 
  ZoomOut 
} from 'lucide-react';
import React, { useCallback, useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import OptimizedImage from '@/components/ui/optimized-image';
import { cn } from '@/lib/utils';

export interface GalleryImage {
  id: string;
  src: string;
  alt: string;
  title?: string;
  description?: string;
  width?: number;
  height?: number;
  thumbnail?: string;
  downloadUrl?: string;
}

interface ImageGalleryProps {
  images: GalleryImage[];
  className?: string;
  thumbnailClassName?: string;
  showThumbnails?: boolean;
  showDownload?: boolean;
  showFullscreen?: boolean;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  onImageClick?: (image: GalleryImage, index: number) => void;
  onImageChange?: (image: GalleryImage, index: number) => void;
}

export default function ImageGallery({
  images,
  className,
  thumbnailClassName,
  showThumbnails = true,
  showDownload = true,
  showFullscreen = true,
  autoPlay = false,
  autoPlayInterval = 5000,
  onImageClick,
  onImageChange,
}: ImageGalleryProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay);

  const currentImage = images[currentIndex];

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || isLightboxOpen || images.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % images.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [isAutoPlaying, isLightboxOpen, images.length, autoPlayInterval]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isLightboxOpen) return;

      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          goToPrevious();
          break;
        case 'ArrowRight':
          e.preventDefault();
          goToNext();
          break;
        case 'Escape':
          e.preventDefault();
          closeLightbox();
          break;
        case '+':
        case '=':
          e.preventDefault();
          zoomIn();
          break;
        case '-':
          e.preventDefault();
          zoomOut();
          break;
        case 'r':
          e.preventDefault();
          rotate();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isLightboxOpen]);

  const goToNext = useCallback(() => {
    const newIndex = (currentIndex + 1) % images.length;
    setCurrentIndex(newIndex);
    onImageChange?.(images[newIndex], newIndex);
    resetZoomAndRotation();
  }, [currentIndex, images, onImageChange]);

  const goToPrevious = useCallback(() => {
    const newIndex = currentIndex === 0 ? images.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
    onImageChange?.(images[newIndex], newIndex);
    resetZoomAndRotation();
  }, [currentIndex, images, onImageChange]);

  const goToImage = useCallback((index: number) => {
    setCurrentIndex(index);
    onImageChange?.(images[index], index);
    resetZoomAndRotation();
  }, [images, onImageChange]);

  const openLightbox = useCallback((index?: number) => {
    if (index !== undefined) {
      setCurrentIndex(index);
    }
    setIsLightboxOpen(true);
    setIsAutoPlaying(false);
    document.body.style.overflow = 'hidden';
  }, []);

  const closeLightbox = useCallback(() => {
    setIsLightboxOpen(false);
    resetZoomAndRotation();
    document.body.style.overflow = 'unset';
  }, []);

  const zoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(prev + 0.5, 3));
    setIsZoomed(true);
  }, []);

  const zoomOut = useCallback(() => {
    setZoomLevel(prev => {
      const newLevel = Math.max(prev - 0.5, 1);
      if (newLevel === 1) setIsZoomed(false);
      return newLevel;
    });
  }, []);

  const rotate = useCallback(() => {
    setRotation(prev => (prev + 90) % 360);
  }, []);

  const resetZoomAndRotation = useCallback(() => {
    setZoomLevel(1);
    setRotation(0);
    setIsZoomed(false);
  }, []);

  const downloadImage = useCallback(async (image: GalleryImage) => {
    try {
      const response = await fetch(image.downloadUrl || image.src);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `${image.title || 'image'}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Помилка завантаження зображення:', error);
    }
  }, []);

  const handleImageClick = useCallback((image: GalleryImage, index: number) => {
    onImageClick?.(image, index);
    openLightbox(index);
  }, [onImageClick, openLightbox]);

  if (!images.length) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-100 dark:bg-gray-800 rounded-lg">
        <p className="text-gray-500 dark:text-gray-400">Немає зображень для відображення</p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Main Image */}
      <div className="relative group">
        <div className="relative aspect-video overflow-hidden rounded-lg bg-gray-100 dark:bg-gray-800">
          <OptimizedImage
            src={currentImage.src}
            alt={currentImage.alt}
            fill
            objectFit="contain"
            className="cursor-pointer transition-transform duration-300 group-hover:scale-105"
            onClick={() => handleImageClick(currentImage, currentIndex)}
            priority
            quality={95}
          />
          
          {/* Navigation Arrows */}
          {images.length > 1 && (
            <>
              <Button
                variant="secondary"
                size="icon"
                className="absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={goToPrevious}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <Button
                variant="secondary"
                size="icon"
                className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={goToNext}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </>
          )}

          {/* Image Counter */}
          {images.length > 1 && (
            <div className="absolute bottom-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-sm">
              {currentIndex + 1} / {images.length}
            </div>
          )}

          {/* Auto-play Toggle */}
          {images.length > 1 && (
            <Button
              variant="secondary"
              size="sm"
              className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={() => setIsAutoPlaying(!isAutoPlaying)}
            >
              {isAutoPlaying ? 'Pause' : 'Play'}
            </Button>
          )}
        </div>

        {/* Image Info */}
        {(currentImage.title || currentImage.description) && (
          <div className="mt-2">
            {currentImage.title && (
              <h3 className="font-medium text-gray-900 dark:text-gray-100">
                {currentImage.title}
              </h3>
            )}
            {currentImage.description && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {currentImage.description}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Thumbnails */}
      {showThumbnails && images.length > 1 && (
        <div className="flex gap-2 overflow-x-auto pb-2">
          {images.map((image, index) => (
            <button
              key={image.id}
              className={cn(
                'relative w-16 h-16 rounded-md overflow-hidden flex-shrink-0 border-2 transition-all duration-200',
                currentIndex === index
                  ? 'border-blue-500 ring-2 ring-blue-200'
                  : 'border-gray-200 hover:border-gray-300',
                thumbnailClassName
              )}
              onClick={() => goToImage(index)}
            >
              <OptimizedImage
                src={image.thumbnail || image.src}
                alt={image.alt}
                fill
                objectFit="cover"
                sizes="64px"
                showSkeleton={false}
              />
            </button>
          ))}
        </div>
      )}

      {/* Lightbox */}
      <AnimatePresence>
        {isLightboxOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center"
            onClick={closeLightbox}
          >
            {/* Lightbox Content */}
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="relative max-w-[90vw] max-h-[90vh] flex items-center justify-center"
              onClick={(e) => e.stopPropagation()}
            >
              <div
                className="relative transition-transform duration-300"
                style={{
                  transform: `scale(${zoomLevel}) rotate(${rotation}deg)`,
                }}
              >
                <OptimizedImage
                  src={currentImage.src}
                  alt={currentImage.alt}
                  width={currentImage.width || 1200}
                  height={currentImage.height || 800}
                  className="max-w-full max-h-full object-contain"
                  priority
                  quality={100}
                />
              </div>

              {/* Lightbox Controls */}
              <div className="absolute top-4 right-4 flex gap-2">
                {showDownload && (
                  <Button
                    variant="secondary"
                    size="icon"
                    onClick={() => downloadImage(currentImage)}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                )}
                
                <Button
                  variant="secondary"
                  size="icon"
                  onClick={zoomIn}
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
                
                <Button
                  variant="secondary"
                  size="icon"
                  onClick={zoomOut}
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                
                <Button
                  variant="secondary"
                  size="icon"
                  onClick={rotate}
                >
                  <RotateCw className="h-4 w-4" />
                </Button>
                
                <Button
                  variant="secondary"
                  size="icon"
                  onClick={closeLightbox}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* Navigation in Lightbox */}
              {images.length > 1 && (
                <>
                  <Button
                    variant="secondary"
                    size="icon"
                    className="absolute left-4 top-1/2 -translate-y-1/2"
                    onClick={goToPrevious}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="secondary"
                    size="icon"
                    className="absolute right-4 top-1/2 -translate-y-1/2"
                    onClick={goToNext}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </>
              )}

              {/* Image Info in Lightbox */}
              {(currentImage.title || currentImage.description) && (
                <div className="absolute bottom-4 left-4 right-4 bg-black/50 text-white p-4 rounded">
                  {currentImage.title && (
                    <h3 className="font-medium mb-1">{currentImage.title}</h3>
                  )}
                  {currentImage.description && (
                    <p className="text-sm opacity-90">{currentImage.description}</p>
                  )}
                </div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
