'use client'

import { Suspense, lazy, useCallback, useState, useEffect } from 'react'
import { cn } from '@/lib/utils'
const Spline = lazy(() => import('@splinetool/react-spline'))

// Preset configurations
export const SCENE_PRESETS = {
  PRODUCT_VIEWER: {
    backgroundColor: 'transparent',
    autoRotate: true,
    enableZoom: true,
    enablePan: false,
  },
  INTERACTIVE_SHOWCASE: {
    backgroundColor: '#000000',
    autoRotate: false,
    enableZoom: true,
    enablePan: true,
  },
  GALLERY_ITEM: {
    backgroundColor: 'transparent',
    autoRotate: true,
    enableZoom: false,
    enablePan: false,
  },
  PRINT_SIMULATION: {
    backgroundColor: '#1a1a1a',
    autoRotate: false,
    enableZoom: true,
    enablePan: true,
  }
};

interface SplineSceneProps {
  scene: string
  className?: string
  preset?: keyof typeof SCENE_PRESETS
  config?: {
    backgroundColor?: string
    autoRotate?: boolean
    enableZoom?: boolean
    enablePan?: boolean
  }
  loadingText?: string
  customLoader?: React.ReactNode
  onLoad?: (app: any) => void
  onProgress?: (progress: any) => void
  onMouseDown?: (e: any) => void
  onMouseHover?: (e: any) => void
  onMouseUp?: (e: any) => void
  onKeyDown?: (e: any) => void
  onKeyUp?: (e: any) => void
}

export function SplineScene({
  scene,
  className,
  preset,
  config = {},
  loadingText = "Loading 3D scene...",
  customLoader,
  onLoad,
  onProgress,
  onMouseDown,
  onMouseHover,
  onMouseUp,
  onKeyDown,
  onKeyUp
}: SplineSceneProps) {
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [splineApp, setSplineApp] = useState<any>(null);

  // Merge preset with custom config
  const finalConfig = preset
    ? { ...SCENE_PRESETS[preset], ...config }
    : config;

  const handleProgress = useCallback((event: any) => {
    // Check if event is a number (from Spline) or a React event
    const progress = typeof event === 'number' ? event : 0;
    const progressPercent = Math.round(progress * 100);
    setLoadingProgress(progressPercent);
    onProgress?.(event);
  }, [onProgress]);

  const handleLoad = useCallback((splineApp: any) => {
    console.log('Scene loaded successfully');
    setSplineApp(splineApp);
    onLoad?.(splineApp);
  }, [onLoad]);

  const handleMouseDown = useCallback((e: any) => {
    if (e.target) {
      console.log('Mouse down on:', e.target.name);
      onMouseDown?.(e);
    }
  }, [onMouseDown]);

  const handleMouseHover = useCallback((e: any) => {
    if (e.target) {
      console.log('Mouse hover on:', e.target.name);
      onMouseHover?.(e);
    }
  }, [onMouseHover]);

  const handleMouseUp = useCallback((e: any) => {
    if (e.target) {
      console.log('Mouse up on:', e.target.name);
      onMouseUp?.(e);
    }
  }, [onMouseUp]);

  const handleKeyDown = useCallback((e: any) => {
    console.log('Key down:', e.key);
    onKeyDown?.(e);
  }, [onKeyDown]);

  const handleKeyUp = useCallback((e: any) => {
    console.log('Key up:', e.key);
    onKeyUp?.(e);
  }, [onKeyUp]);

  // Expose methods to manipulate the scene
  useEffect(() => {
    if (!splineApp) return;

    // You could add more methods here to control the scene
    // For example, to zoom to a specific object:
    // splineApp.zoomTo(objectName);

    // Or to animate a specific object:
    // splineApp.emitEvent('mouseDown', objectName);

  }, [splineApp]);

  const DefaultLoader = () => (
    <div className="w-full h-full flex flex-col items-center justify-center"
         style={{ backgroundColor: finalConfig.backgroundColor || 'transparent' }}>
      <span className="loader mb-4"></span>
      <p className="text-white text-sm">{loadingText}</p>
      <div className="w-48 h-2 bg-gray-700 rounded-full mt-2">
        <div
          className="h-full bg-primary rounded-full transition-all duration-300"
          style={{ width: `${loadingProgress}%` }}
        ></div>
      </div>
      <p className="text-white text-xs mt-1">{loadingProgress}%</p>
    </div>
  );

  return (
    <div className={cn("relative w-full h-full", className)}>
      <Suspense fallback={customLoader || <DefaultLoader />}>
        <Spline
          scene={scene}
          onLoad={handleLoad}
          onProgress={handleProgress}
          onMouseDown={handleMouseDown}
          onMouseOver={handleMouseHover}
          onMouseUp={handleMouseUp}
          onKeyDown={handleKeyDown}
          onKeyUp={handleKeyUp}
          style={{
            width: '100%',
            height: '100%',
            backgroundColor: finalConfig.backgroundColor || 'transparent'
          }}
        />
      </Suspense>
    </div>
  )
}