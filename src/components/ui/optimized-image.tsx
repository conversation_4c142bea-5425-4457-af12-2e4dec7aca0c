'use client';

/**
 * Optimized Image Component - Оптимізований компонент зображення
 * Підтримує lazy loading, WebP/AVIF формати, responsive images
 */

import { cn } from '@/lib/utils';
import Image from 'next/image';
import React, { useState, useRef, useEffect } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  sizes?: string;
  fill?: boolean;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  objectPosition?: string;
  onLoad?: () => void;
  onError?: () => void;
  fallbackSrc?: string;
  showSkeleton?: boolean;
  skeletonClassName?: string;
}

export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  quality = 85,
  placeholder = 'empty',
  blurDataURL,
  sizes,
  fill = false,
  objectFit = 'cover',
  objectPosition = 'center',
  onLoad,
  onError,
  fallbackSrc,
  showSkeleton = true,
  skeletonClassName,
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(src);
  const [isInView, setIsInView] = useState(priority);
  const imgRef = useRef<HTMLDivElement>(null);

  // Intersection Observer для lazy loading
  useEffect(() => {
    if (priority || isInView) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '50px',
        threshold: 0.1,
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority, isInView]);

  // Обробка завантаження
  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  // Обробка помилки
  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
    
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc);
      setHasError(false);
      setIsLoading(true);
    } else {
      onError?.();
    }
  };

  // Генерація placeholder зображення
  const generatePlaceholder = (w: number, h: number) => {
    const canvas = document.createElement('canvas');
    canvas.width = w;
    canvas.height = h;
    const ctx = canvas.getContext('2d');
    
    if (ctx) {
      // Створюємо градієнт
      const gradient = ctx.createLinearGradient(0, 0, w, h);
      gradient.addColorStop(0, '#f3f4f6');
      gradient.addColorStop(1, '#e5e7eb');
      
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, w, h);
      
      // Додаємо іконку
      ctx.fillStyle = '#9ca3af';
      ctx.font = `${Math.min(w, h) / 4}px Arial`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('🖼️', w / 2, h / 2);
    }
    
    return canvas.toDataURL();
  };

  // Skeleton компонент
  const Skeleton = () => (
    <div
      className={cn(
        'animate-pulse bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800',
        fill ? 'absolute inset-0' : '',
        skeletonClassName
      )}
      style={
        !fill && width && height
          ? { width: `${width}px`, height: `${height}px` }
          : undefined
      }
    >
      <div className="flex items-center justify-center h-full">
        <div className="text-gray-400 dark:text-gray-500 text-2xl">
          🖼️
        </div>
      </div>
    </div>
  );

  // Error компонент
  const ErrorFallback = () => (
    <div
      className={cn(
        'bg-gray-100 dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center',
        fill ? 'absolute inset-0' : '',
        className
      )}
      style={
        !fill && width && height
          ? { width: `${width}px`, height: `${height}px` }
          : undefined
      }
    >
      <div className="text-center text-gray-500 dark:text-gray-400">
        <div className="text-3xl mb-2">❌</div>
        <div className="text-sm">Зображення недоступне</div>
      </div>
    </div>
  );

  // Якщо є помилка і немає fallback
  if (hasError && (!fallbackSrc || currentSrc === fallbackSrc)) {
    return <ErrorFallback />;
  }

  return (
    <div
      ref={imgRef}
      className={cn('relative overflow-hidden', fill ? 'w-full h-full' : '')}
      style={
        !fill && width && height
          ? { width: `${width}px`, height: `${height}px` }
          : undefined
      }
    >
      {/* Skeleton під час завантаження */}
      {isLoading && showSkeleton && <Skeleton />}

      {/* Основне зображення */}
      {(isInView || priority) && (
        <Image
          src={currentSrc}
          alt={alt}
          width={fill ? undefined : width}
          height={fill ? undefined : height}
          fill={fill}
          priority={priority}
          quality={quality}
          placeholder={placeholder}
          blurDataURL={
            blurDataURL ||
            (placeholder === 'blur' && width && height
              ? generatePlaceholder(width, height)
              : undefined)
          }
          sizes={sizes}
          className={cn(
            'transition-opacity duration-300',
            isLoading ? 'opacity-0' : 'opacity-100',
            fill ? `object-${objectFit}` : '',
            className
          )}
          style={
            fill
              ? {
                  objectFit,
                  objectPosition,
                }
              : undefined
          }
          onLoad={handleLoad}
          onError={handleError}
        />
      )}

      {/* Overlay для додаткових ефектів */}
      {isLoading && (
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/10 to-transparent animate-shimmer" />
      )}
    </div>
  );
}

// Компонент для галереї зображень
export function ImageGallery({
  images,
  className,
  imageClassName,
  onImageClick,
}: {
  images: Array<{
    src: string;
    alt: string;
    width?: number;
    height?: number;
  }>;
  className?: string;
  imageClassName?: string;
  onImageClick?: (index: number) => void;
}) {
  return (
    <div className={cn('grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4', className)}>
      {images.map((image, index) => (
        <div
          key={index}
          className="relative aspect-square cursor-pointer group"
          onClick={() => onImageClick?.(index)}
        >
          <OptimizedImage
            src={image.src}
            alt={image.alt}
            fill
            objectFit="cover"
            className={cn(
              'rounded-lg transition-transform duration-200 group-hover:scale-105',
              imageClassName
            )}
            sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
          />
          
          {/* Hover overlay */}
          <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg" />
        </div>
      ))}
    </div>
  );
}

// Компонент для мініатюр
export function ThumbnailGrid({
  images,
  activeIndex,
  onThumbnailClick,
  className,
}: {
  images: Array<{
    src: string;
    alt: string;
  }>;
  activeIndex?: number;
  onThumbnailClick?: (index: number) => void;
  className?: string;
}) {
  return (
    <div className={cn('flex gap-2 overflow-x-auto pb-2', className)}>
      {images.map((image, index) => (
        <button
          key={index}
          className={cn(
            'relative w-16 h-16 rounded-md overflow-hidden flex-shrink-0 border-2 transition-all duration-200',
            activeIndex === index
              ? 'border-blue-500 ring-2 ring-blue-200'
              : 'border-gray-200 hover:border-gray-300'
          )}
          onClick={() => onThumbnailClick?.(index)}
        >
          <OptimizedImage
            src={image.src}
            alt={image.alt}
            fill
            objectFit="cover"
            sizes="64px"
            showSkeleton={false}
          />
        </button>
      ))}
    </div>
  );
}
