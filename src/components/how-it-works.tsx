'use client';

import React from 'react';
import { Search, Download, Printer, ShoppingCart } from 'lucide-react';

interface Step {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const HowItWorks = () => {
  const steps: Step[] = [
    {
      icon: <Search className="h-10 w-10 text-primary" />,
      title: "Знайдіть модель",
      description: "Перегляньте нашу велику колекцію 3D-моделей або скористайтеся пошуком, щоб знайти ідеальну модель для вашого проекту."
    },
    {
      icon: <Download className="h-10 w-10 text-primary" />,
      title: "Завантажте файли",
      description: "Придбайте та завантажте файли моделі у форматах STL, OBJ або GLTF для використання у вашому 3D-принтері або програмному забезпеченні."
    },
    {
      icon: <Printer className="h-10 w-10 text-primary" />,
      title: "Надрукуйте самостійно",
      description: "Використовуйте власний 3D-принтер для друку моделі з бажаними налаштуваннями та матеріалами."
    },
    {
      icon: <ShoppingCart className="h-10 w-10 text-primary" />,
      title: "Або замовте друк",
      description: "Не маєте принтера? Замовте друк моделі через нашу мережу партнерів і отримайте готовий виріб поштою."
    }
  ];

  return (
    <section className="py-16 bg-muted/50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Як це працює</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Наш 3D-маркетплейс робить процес пошуку, завантаження та друку 3D-моделей простим та зручним.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center text-center">
              <div className="bg-background rounded-full p-6 mb-4 shadow-sm">
                {step.icon}
              </div>
              <h3 className="text-xl font-semibold mb-2">{step.title}</h3>
              <p className="text-muted-foreground">{step.description}</p>

              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-1/2">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 5L16 12L9 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <a
            href="/marketplace"
            className="inline-flex items-center justify-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors font-medium"
          >
            Перейти до маркетплейсу
            <svg className="ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </a>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
