'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Users, FileText, Download } from 'lucide-react';
import Link from 'next/link';

interface Stat {
  icon: React.ReactNode;
  value: string;
  label: string;
}

const JoinCommunity = () => {
  const stats: Stat[] = [
    {
      icon: <FileText className="h-8 w-8 text-primary" />,
      value: "10,000+",
      label: "3D-моделей"
    },
    {
      icon: <Users className="h-8 w-8 text-primary" />,
      value: "5,000+",
      label: "Дизайнерів"
    },
    {
      icon: <Download className="h-8 w-8 text-primary" />,
      value: "100,000+",
      label: "Завантажень"
    }
  ];

  return (
    <section className="py-16 bg-gradient-to-r from-primary/10 to-primary/5">
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-12">
          <div className="lg:w-1/2">
            <h2 className="text-3xl font-bold mb-4">Приєднуйтесь до нашої спільноти</h2>
            <p className="text-muted-foreground mb-6">
              Станьте частиною зростаючої спільноти 3D-дизайнерів та ентузіастів. Діліться своїми моделями, отримуйте відгуки та заробляйте на своїх творіннях.
            </p>
            <div className="flex flex-wrap gap-4">
              <Button asChild size="lg">
                <Link href="/marketplace">Перейти до маркетплейсу</Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/auth/signup">Зареєструватися</Link>
              </Button>
            </div>
          </div>

          <div className="lg:w-1/2 grid grid-cols-1 md:grid-cols-3 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="bg-background rounded-lg p-6 shadow-sm text-center">
                <div className="mx-auto bg-primary/10 rounded-full p-4 w-16 h-16 flex items-center justify-center mb-4">
                  {stat.icon}
                </div>
                <div className="text-3xl font-bold mb-1">{stat.value}</div>
                <div className="text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default JoinCommunity;
