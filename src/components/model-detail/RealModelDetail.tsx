'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Download, 
  Heart, 
  Share2, 
  Eye, 
  MessageCircle,
  Star,
  ExternalLink,
  FileText,
  Package,
  Clock,
  User,
  Shield,
  Zap,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import RealModelViewer from '@/components/3d-viewer/RealModelViewer';
import { ModelFile, ScrapedModelWithFiles } from '@/lib/bright-data/model-file-scraper';

interface RealModelDetailProps {
  model: ScrapedModelWithFiles;
  onDownload?: (files: ModelFile[]) => void;
  onLike?: () => void;
  onShare?: () => void;
}

interface DownloadProgress {
  isDownloading: boolean;
  progress: number;
  currentFile?: string;
}

export default function RealModelDetail({
  model,
  onDownload,
  onLike,
  onShare
}: RealModelDetailProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [isLiked, setIsLiked] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState<DownloadProgress>({
    isDownloading: false,
    progress: 0
  });

  // Handle download
  const handleDownload = async () => {
    if (downloadProgress.isDownloading) return;

    setDownloadProgress({ isDownloading: true, progress: 0 });

    try {
      // Simulate download progress
      for (let i = 0; i <= 100; i += 10) {
        setDownloadProgress({
          isDownloading: true,
          progress: i,
          currentFile: i < 50 ? 'model.stl' : 'textures.zip'
        });
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // Call the download handler
      onDownload?.(model.originalFiles);

      // Reset progress
      setDownloadProgress({ isDownloading: false, progress: 100 });

    } catch (error) {
      console.error('Download failed:', error);
      setDownloadProgress({ isDownloading: false, progress: 0 });
    }
  };

  // Handle like
  const handleLike = () => {
    setIsLiked(!isLiked);
    onLike?.();
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get platform info
  const getPlatformInfo = (platform: string) => {
    const platforms = {
      printables: { name: 'Printables', color: 'bg-green-500', icon: '🖨️' },
      makerworld: { name: 'MakerWorld', color: 'bg-blue-500', icon: '🏭' },
      thangs: { name: 'Thangs', color: 'bg-purple-500', icon: '💎' },
      thingiverse: { name: 'Thingiverse', color: 'bg-orange-500', icon: '🔧' },
      myminifactory: { name: 'MyMiniFactory', color: 'bg-indigo-500', icon: '🏭' }
    };
    return platforms[platform as keyof typeof platforms] || { name: platform, color: 'bg-gray-500', icon: '📦' };
  };

  const platformInfo = getPlatformInfo(model.platform);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-gray-900 dark:to-blue-900">
      <div className="container mx-auto px-4 py-8">
        {/* Navigation */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <Link 
            href="/marketplace" 
            className="inline-flex items-center text-gray-600 dark:text-gray-300 hover:text-orange-600 dark:hover:text-orange-400 transition-colors"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Marketplace
          </Link>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - 3D Viewer */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-2"
          >
            <RealModelViewer
              modelFiles={model.modelFiles}
              previewFile={model.previewFile}
              title={model.title}
              className="h-96 lg:h-[500px]"
              showControls={true}
              showStats={true}
            />

            {/* Model Information Tabs */}
            <Card className="mt-6">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <CardHeader className="pb-3">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="files">Files</TabsTrigger>
                    <TabsTrigger value="details">Details</TabsTrigger>
                    <TabsTrigger value="license">License</TabsTrigger>
                  </TabsList>
                </CardHeader>

                <CardContent>
                  <TabsContent value="overview" className="space-y-4">
                    <div>
                      <h3 className="font-semibold mb-2">Description</h3>
                      <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                        {model.description || 'No description available.'}
                      </p>
                    </div>

                    {model.tags && model.tags.length > 0 && (
                      <div>
                        <h3 className="font-semibold mb-2">Tags</h3>
                        <div className="flex flex-wrap gap-2">
                          {model.tags.map((tag, index) => (
                            <Badge key={index} variant="secondary">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    <div>
                      <h3 className="font-semibold mb-2">Statistics</h3>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <Eye className="h-5 w-5 mx-auto mb-1 text-blue-500" />
                          <div className="font-semibold">{model.stats.views.toLocaleString()}</div>
                          <div className="text-xs text-gray-500">Views</div>
                        </div>
                        <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <Download className="h-5 w-5 mx-auto mb-1 text-green-500" />
                          <div className="font-semibold">{model.stats.downloads.toLocaleString()}</div>
                          <div className="text-xs text-gray-500">Downloads</div>
                        </div>
                        <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <Heart className="h-5 w-5 mx-auto mb-1 text-red-500" />
                          <div className="font-semibold">{model.stats.likes.toLocaleString()}</div>
                          <div className="text-xs text-gray-500">Likes</div>
                        </div>
                        <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <MessageCircle className="h-5 w-5 mx-auto mb-1 text-purple-500" />
                          <div className="font-semibold">{model.stats.comments.toLocaleString()}</div>
                          <div className="text-xs text-gray-500">Comments</div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="files" className="space-y-4">
                    <div>
                      <h3 className="font-semibold mb-3">Available Files</h3>
                      <div className="space-y-2">
                        {model.originalFiles.map((file, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div className="flex items-center gap-3">
                              <FileText className="h-5 w-5 text-gray-500" />
                              <div>
                                <div className="font-medium">{file.name}</div>
                                <div className="text-sm text-gray-500">
                                  {file.format.toUpperCase()} • {formatFileSize(file.size)}
                                </div>
                              </div>
                            </div>
                            <Button variant="outline" size="sm">
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>

                    {model.processedFiles.length > 0 && (
                      <div>
                        <h3 className="font-semibold mb-3">Processed Files</h3>
                        <div className="space-y-2">
                          {model.processedFiles.map((file, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                              <div className="flex items-center gap-3">
                                <Zap className="h-5 w-5 text-blue-500" />
                                <div>
                                  <div className="font-medium">{file.name}</div>
                                  <div className="text-sm text-blue-600 dark:text-blue-400">
                                    {file.format.toUpperCase()} • Optimized for web viewing
                                  </div>
                                </div>
                              </div>
                              <Badge variant="secondary">Web Ready</Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="details" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="font-semibold mb-2">Model Information</h3>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-500">Category:</span>
                            <span>{model.category}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-500">Platform:</span>
                            <Badge className={platformInfo.color}>
                              {platformInfo.icon} {platformInfo.name}
                            </Badge>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-500">File Formats:</span>
                            <span>{model.fileFormats.join(', ').toUpperCase()}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-500">Total Size:</span>
                            <span>{formatFileSize(model.totalSize)}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h3 className="font-semibold mb-2">Designer</h3>
                        <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                            <User className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <div className="font-medium">{model.designer.name}</div>
                            <div className="text-sm text-gray-500">Designer</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="license" className="space-y-4">
                    <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
                      <div className="flex items-center gap-2 mb-2">
                        <Shield className="h-5 w-5 text-green-600" />
                        <h3 className="font-semibold text-green-800 dark:text-green-300">
                          {model.license.name}
                        </h3>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          {model.license.allowCommercialUse ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-red-500" />
                          )}
                          <span>Commercial use {model.license.allowCommercialUse ? 'allowed' : 'not allowed'}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          {model.license.requireAttribution ? (
                            <AlertCircle className="h-4 w-4 text-yellow-500" />
                          ) : (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          )}
                          <span>Attribution {model.license.requireAttribution ? 'required' : 'not required'}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          {model.license.allowDerivatives ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-red-500" />
                          )}
                          <span>Derivatives {model.license.allowDerivatives ? 'allowed' : 'not allowed'}</span>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </CardContent>
              </Tabs>
            </Card>
          </motion.div>

          {/* Right Column - Actions & Info */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-6"
          >
            {/* Action Buttons */}
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <Button
                    onClick={handleDownload}
                    disabled={downloadProgress.isDownloading}
                    className="w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700"
                    size="lg"
                  >
                    {downloadProgress.isDownloading ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        Downloading... {downloadProgress.progress}%
                      </div>
                    ) : (
                      <>
                        <Download className="h-5 w-5 mr-2" />
                        Download {model.isFree ? 'Free' : `$${model.price}`}
                      </>
                    )}
                  </Button>

                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      variant="outline"
                      onClick={handleLike}
                      className={isLiked ? 'text-red-500 border-red-200' : ''}
                    >
                      <Heart className={`h-4 w-4 mr-2 ${isLiked ? 'fill-current' : ''}`} />
                      Like
                    </Button>
                    <Button variant="outline" onClick={onShare}>
                      <Share2 className="h-4 w-4 mr-2" />
                      Share
                    </Button>
                  </div>

                  <Button variant="outline" className="w-full" asChild>
                    <Link href={model.originalUrl} target="_blank">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View Original
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Quick Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quick Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-500">Files:</span>
                  <span className="font-medium">{model.originalFiles.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-500">Formats:</span>
                  <span className="font-medium">{model.fileFormats.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-500">Size:</span>
                  <span className="font-medium">{formatFileSize(model.totalSize)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-500">Added:</span>
                  <span className="font-medium">
                    {new Date(model.scrapedAt).toLocaleDateString()}
                  </span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
