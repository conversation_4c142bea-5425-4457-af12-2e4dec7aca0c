'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import Image from 'next/image';
import {
  Edit,
  Trash2,
  Eye,
  Download,
  Heart,
  DollarSign,
  Plus,
  MoreHorizontal,
  TrendingUp,
  Calendar
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface UserModel {
  id: string;
  name: string;
  description: string;
  thumbnail_url: string;
  price: number;
  category: string;
  is_free: boolean;
  download_count: number;
  view_count: number;
  like_count: number;
  created_at: string;
  updated_at: string;
}

interface UserModelsManagerProps {
  userId?: string;
}

export default function UserModelsManager({ userId }: UserModelsManagerProps) {
  const { data: session } = useSession();
  const [models, setModels] = useState<UserModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('all');

  // Завантаження моделей користувача
  useEffect(() => {
    const fetchUserModels = async () => {
      try {
        const targetUserId = userId || session?.user?.id;
        if (!targetUserId) return;

        const response = await fetch(`/api/users/${targetUserId}/models`);
        if (!response.ok) {
          throw new Error('Failed to fetch user models');
        }

        const data = await response.json();
        setModels(data.data || []);
      } catch (error) {
        console.error('Error fetching user models:', error);
        setError('Failed to load models');
      } finally {
        setLoading(false);
      }
    };

    fetchUserModels();
  }, [userId, session?.user?.id]);

  // Видалення моделі
  const handleDeleteModel = async (modelId: string) => {
    if (!confirm('Ви впевнені, що хочете видалити цю модель?')) {
      return;
    }

    try {
      const response = await fetch(`/api/models/${modelId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete model');
      }

      setModels(models.filter(model => model.id !== modelId));
    } catch (error) {
      console.error('Error deleting model:', error);
      alert('Помилка при видаленні моделі');
    }
  };

  // Фільтрація моделей
  const filteredModels = models.filter(model => {
    switch (activeTab) {
      case 'free':
        return model.is_free;
      case 'paid':
        return !model.is_free;
      case 'popular':
        return model.download_count > 10;
      default:
        return true;
    }
  });

  // Статистика
  const stats = {
    total: models.length,
    free: models.filter(m => m.is_free).length,
    paid: models.filter(m => !m.is_free).length,
    totalDownloads: models.reduce((sum, m) => sum + m.download_count, 0),
    totalViews: models.reduce((sum, m) => sum + m.view_count, 0),
    totalRevenue: models.reduce((sum, m) => sum + (m.price * m.download_count), 0),
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Статистика */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Всього моделей</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Download className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Завантажень</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalDownloads}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Eye className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Переглядів</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalViews}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Дохід</p>
                <p className="text-2xl font-bold text-gray-900">${stats.totalRevenue.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Заголовок та кнопка додавання */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Мої моделі</h2>
        {session?.user?.id === userId && (
          <Link href="/models/upload">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Додати модель
            </Button>
          </Link>
        )}
      </div>

      {/* Фільтри */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">Всі ({stats.total})</TabsTrigger>
          <TabsTrigger value="free">Безкоштовні ({stats.free})</TabsTrigger>
          <TabsTrigger value="paid">Платні ({stats.paid})</TabsTrigger>
          <TabsTrigger value="popular">Популярні</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          {filteredModels.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">Моделі не знайдено</p>
              {session?.user?.id === userId && (
                <Link href="/models/upload" className="mt-4 inline-block">
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    Завантажити першу модель
                  </Button>
                </Link>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredModels.map((model) => (
                <ModelCard
                  key={model.id}
                  model={model}
                  onDelete={handleDeleteModel}
                  canEdit={session?.user?.id === userId}
                />
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Компонент картки моделі
interface ModelCardProps {
  model: UserModel;
  onDelete: (id: string) => void;
  canEdit: boolean;
}

function ModelCard({ model, onDelete, canEdit }: ModelCardProps) {
  return (
    <Card className="group hover:shadow-lg transition-shadow">
      <div className="relative">
        <Image
          src={model.thumbnail_url || '/placeholder-model.jpg'}
          alt={model.name}
          width={300}
          height={200}
          className="w-full h-48 object-cover rounded-t-lg"
        />
        {canEdit && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="secondary" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link href={`/models/${model.id}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    Редагувати
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onDelete(model.id)}
                  className="text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Видалити
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      <CardHeader className="pb-2">
        <CardTitle className="text-lg line-clamp-1">{model.name}</CardTitle>
        <CardDescription className="line-clamp-2">
          {model.description}
        </CardDescription>
      </CardHeader>

      <CardContent className="pb-2">
        <div className="flex items-center justify-between mb-2">
          <Badge variant="secondary">{model.category}</Badge>
          <span className="font-bold text-lg">
            {model.is_free ? 'Безкоштовно' : `$${model.price}`}
          </span>
        </div>

        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center space-x-4">
            <span className="flex items-center">
              <Download className="mr-1 h-3 w-3" />
              {model.download_count}
            </span>
            <span className="flex items-center">
              <Eye className="mr-1 h-3 w-3" />
              {model.view_count}
            </span>
            <span className="flex items-center">
              <Heart className="mr-1 h-3 w-3" />
              {model.like_count}
            </span>
          </div>
        </div>
      </CardContent>

      <CardFooter className="pt-2">
        <Link href={`/models/${model.id}`} className="w-full">
          <Button variant="outline" className="w-full">
            Переглянути
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
