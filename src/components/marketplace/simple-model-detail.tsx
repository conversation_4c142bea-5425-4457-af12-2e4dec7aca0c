'use client';

import Link from 'next/link';
import { useState } from 'react';
import DownloadButton from './download-button';
import ModelViewer from './model-viewer';
import { ModelSourceBadge } from './model-source-badge';
import { Model } from '@/types/models';

interface SimpleModelDetailProps {
  model: Model;
}

export default function SimpleModelDetail({ model }: SimpleModelDetailProps) {
  const [activeTab, setActiveTab] = useState('3d');

  return (
    <div className="bg-white min-h-screen">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main content */}
          <div className="lg:col-span-2 space-y-4">
            <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden">
              {/* Tabs for switching between image and 3D model */}
              <div className="w-full h-full">
                {activeTab === 'image' ? (
                  <img
                    src={model.images?.[0] || model.thumbnail}
                    alt={model.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <ModelViewer
                    sceneUrl="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
                    height="100%"
                  />
                )}
              </div>

              {/* Tab buttons */}
              <div className="absolute top-2 right-2 z-10 bg-white/80 backdrop-blur-sm rounded-lg">
                <div className="flex">
                  <button
                    onClick={() => setActiveTab('image')}
                    className={`px-3 py-1 text-sm ${activeTab === 'image' ? 'bg-blue-600 text-white' : 'bg-transparent text-gray-700'} rounded-l-lg`}
                  >
                    Image
                  </button>
                  <button
                    onClick={() => setActiveTab('3d')}
                    className={`px-3 py-1 text-sm ${activeTab === '3d' ? 'bg-blue-600 text-white' : 'bg-transparent text-gray-700'} rounded-r-lg`}
                  >
                    3D View
                  </button>
                </div>
              </div>
            </div>

            {/* Thumbnails */}
            <div className="flex space-x-2 overflow-x-auto pb-2">
              {model.images && model.images.map((image: string, index: number) => (
                <img
                  key={index}
                  src={image}
                  alt={`${model.title} - view ${index + 1}`}
                  className="h-16 w-16 object-cover rounded-md cursor-pointer border-2 border-transparent hover:border-blue-600"
                  onClick={() => setActiveTab('image')}
                />
              ))}
            </div>

            {/* Description */}
            <div className="mt-8">
              <h3 className="text-xl font-semibold mb-2">Description</h3>
              <p className="text-gray-600">
                {model.description || "Dummy 1:3 Terminator T800 Armor - A highly detailed 3D printable model of the iconic T-800 Terminator endoskeleton armor. Perfect for collectors and fans of the Terminator franchise. This model features intricate details and is designed for easy printing and assembly."}
              </p>
              {model.price === 0 && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                  <h4 className="font-medium text-green-800 mb-1">Free Download from Printables</h4>
                  <p className="text-sm text-green-700">
                    This model is available for free download from Printables. Click the Download button to access all files.
                  </p>
                </div>
              )}
            </div>

            {/* Tags */}
            <div>
              <h3 className="text-xl font-semibold mb-2">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {model.tags && model.tags.map((tag: string) => (
                  <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-800 text-sm rounded-md">{tag}</span>
                ))}
                {(!model.tags || model.tags.length === 0) && (
                  <>
                    <span className="px-2 py-1 bg-gray-100 text-gray-800 text-sm rounded-md">terminator</span>
                    <span className="px-2 py-1 bg-gray-100 text-gray-800 text-sm rounded-md">t800</span>
                    <span className="px-2 py-1 bg-gray-100 text-gray-800 text-sm rounded-md">armor</span>
                    <span className="px-2 py-1 bg-gray-100 text-gray-800 text-sm rounded-md">sci-fi</span>
                    <span className="px-2 py-1 bg-gray-100 text-gray-800 text-sm rounded-md">movie</span>
                    <span className="px-2 py-1 bg-gray-100 text-gray-800 text-sm rounded-md">figurine</span>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-4">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <h1 className="text-2xl font-bold mb-2">{model.title}</h1>

              {/* Model Source and License Badges */}
              <div className="mb-4">
                <ModelSourceBadge
                  model={model}
                  showLicense={true}
                  showExternalLink={true}
                  size="default"
                />
              </div>

              <div className="flex items-center mb-4">
                <div className="h-6 w-6 mr-2 bg-gray-200 rounded-full overflow-hidden">
                  {model.designer.avatar ? (
                    <img src={model.designer.avatar} alt={model.designer.name} className="h-full w-full object-cover" />
                  ) : (
                    <div className="h-full w-full flex items-center justify-center text-xs font-medium">
                      {model.designer.name.substring(0, 2)}
                    </div>
                  )}
                </div>
                <Link href={`/marketplace/designer/${model.designer.id || model.designer.name}`} className="text-sm text-gray-600 hover:text-blue-600">
                  by {model.designer.name}
                </Link>
              </div>

              <div className="flex items-center justify-between mb-6">
                <div className="text-3xl font-bold">
                  {model.price === 0 ? 'Free' : `$${model.price.toFixed(2)}`}
                </div>
                <div className="flex items-center">
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <svg
                        key={star}
                        className={`w-5 h-5 ${star <= 4 ? 'text-yellow-400' : 'text-gray-300'}`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                      </svg>
                    ))}
                  </div>
                  <span className="text-sm text-gray-600 ml-1">(4.0)</span>
                </div>
              </div>

              <div className="space-y-3">
                {model.price === 0 ? (
                  <DownloadButton
                    url="https://www.printables.com/model/1286204-dummy-13-terminator-t800-armor/files"
                    className="w-full py-3 text-lg"
                  />
                ) : (
                  <>
                    <button className="w-full py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                      Buy Now
                    </button>
                    <button className="w-full py-3 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                      Add to Cart
                    </button>
                  </>
                )}
                <button className="w-full py-3 text-gray-700 hover:bg-gray-50 transition-colors border border-gray-300 rounded-md">
                  Add to Favorites
                </button>
              </div>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <h2 className="text-lg font-semibold mb-2">Print on Demand</h2>
              <p className="text-sm text-gray-600 mb-4">
                Don't have a 3D printer? Order a print of this model directly from our partners.
              </p>
              <button className="w-full py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                Order Print
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
