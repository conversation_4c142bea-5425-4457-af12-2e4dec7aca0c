'use client';

/**
 * Market Intelligence Dashboard - Панель ринкової аналітики з Bright Data
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  DollarSign, 
  Users, 
  Target,
  RefreshCw,
  ExternalLink,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface MarketTrend {
  category: string;
  growthRate: number;
  demandLevel: 'low' | 'medium' | 'high';
  averagePrice: number;
  competitionLevel: number;
}

interface CompetitorData {
  platform: string;
  url: string;
  modelCount: number;
  averagePrice: number;
  topCategories: string[];
}

interface PricingInsight {
  category: string;
  recommendedPriceRange: {
    min: number;
    max: number;
    optimal: number;
  };
  marketPosition: 'budget' | 'mid-range' | 'premium';
}

export default function MarketIntelligenceDashboard() {
  const [trends, setTrends] = useState<MarketTrend[]>([]);
  const [competitors, setCompetitors] = useState<CompetitorData[]>([]);
  const [pricingInsights, setPricingInsights] = useState<PricingInsight[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Демо дані
  useEffect(() => {
    const demoTrends: MarketTrend[] = [
      {
        category: 'Miniatures',
        growthRate: 25.3,
        demandLevel: 'high',
        averagePrice: 12.50,
        competitionLevel: 0.7
      },
      {
        category: 'Architecture',
        growthRate: 18.7,
        demandLevel: 'high',
        averagePrice: 65.00,
        competitionLevel: 0.6
      },
      {
        category: 'Vehicles',
        growthRate: -5.2,
        demandLevel: 'medium',
        averagePrice: 35.75,
        competitionLevel: 0.8
      },
      {
        category: 'Home & Garden',
        growthRate: 22.1,
        demandLevel: 'high',
        averagePrice: 8.25,
        competitionLevel: 0.5
      }
    ];

    const demoCompetitors: CompetitorData[] = [
      {
        platform: 'Thingiverse',
        url: 'https://www.thingiverse.com',
        modelCount: 2500000,
        averagePrice: 0,
        topCategories: ['Hobby', 'Household', 'Toys & Games']
      },
      {
        platform: 'MyMiniFactory',
        url: 'https://www.myminifactory.com',
        modelCount: 150000,
        averagePrice: 8.50,
        topCategories: ['Miniatures', 'Tabletop', 'Art']
      },
      {
        platform: 'CGTrader',
        url: 'https://www.cgtrader.com',
        modelCount: 1200000,
        averagePrice: 45.75,
        topCategories: ['Architecture', 'Vehicles', 'Characters']
      }
    ];

    const demoPricing: PricingInsight[] = [
      {
        category: 'Miniatures',
        recommendedPriceRange: { min: 5, max: 25, optimal: 12.50 },
        marketPosition: 'mid-range'
      },
      {
        category: 'Architecture',
        recommendedPriceRange: { min: 30, max: 120, optimal: 65 },
        marketPosition: 'premium'
      }
    ];

    setTrends(demoTrends);
    setCompetitors(demoCompetitors);
    setPricingInsights(demoPricing);
    setLastUpdated(new Date());
  }, []);

  const handleRefresh = async () => {
    setIsLoading(true);
    // Тут би був виклик API для отримання свіжих даних
    setTimeout(() => {
      setIsLoading(false);
      setLastUpdated(new Date());
    }, 2000);
  };

  const getDemandColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'low': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  const getGrowthIcon = (rate: number) => {
    return rate > 0 ? (
      <TrendingUp className="h-4 w-4 text-green-600" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-600" />
    );
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Ринкова аналітика
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Дані зібрані з провідних платформ за допомогою Bright Data
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          {lastUpdated && (
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Оновлено: {lastUpdated.toLocaleTimeString()}
            </div>
          )}
          <Button 
            onClick={handleRefresh}
            disabled={isLoading}
            variant="outline"
            className="border-blue-200 hover:bg-blue-50 dark:border-blue-700 dark:hover:bg-blue-900/20"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Оновлення...' : 'Оновити'}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="trends" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="trends">Тренди ринку</TabsTrigger>
          <TabsTrigger value="competitors">Конкуренти</TabsTrigger>
          <TabsTrigger value="pricing">Ціноутворення</TabsTrigger>
        </TabsList>

        {/* Market Trends */}
        <TabsContent value="trends" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {trends.map((trend, index) => (
              <motion.div
                key={trend.category}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{trend.category}</CardTitle>
                      <div className="flex items-center gap-2">
                        {getGrowthIcon(trend.growthRate)}
                        <span className={`font-bold ${trend.growthRate > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {trend.growthRate > 0 ? '+' : ''}{trend.growthRate.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Попит</span>
                        <Badge className={getDemandColor(trend.demandLevel)}>
                          {trend.demandLevel === 'high' ? 'Високий' : 
                           trend.demandLevel === 'medium' ? 'Середній' : 'Низький'}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Середня ціна</span>
                        <span className="font-bold">${trend.averagePrice.toFixed(2)}</span>
                      </div>
                      
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Конкуренція</span>
                          <span className="text-sm font-medium">{(trend.competitionLevel * 100).toFixed(0)}%</span>
                        </div>
                        <Progress value={trend.competitionLevel * 100} className="h-2" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Competitors */}
        <TabsContent value="competitors" className="space-y-6">
          <div className="grid gap-6">
            {competitors.map((competitor, index) => (
              <motion.div
                key={competitor.platform}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-xl">{competitor.platform}</CardTitle>
                        <CardDescription className="flex items-center gap-2 mt-1">
                          <ExternalLink className="h-4 w-4" />
                          {competitor.url}
                        </CardDescription>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold">{(competitor.modelCount / 1000000).toFixed(1)}M</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">моделей</div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <DollarSign className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                        <div className="text-lg font-bold">
                          {competitor.averagePrice === 0 ? 'Free' : `$${competitor.averagePrice.toFixed(2)}`}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">Середня ціна</div>
                      </div>
                      
                      <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <BarChart3 className="h-8 w-8 text-green-600 mx-auto mb-2" />
                        <div className="text-lg font-bold">{competitor.topCategories.length}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">Топ категорій</div>
                      </div>
                      
                      <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                        <Target className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                        <div className="text-lg font-bold">
                          {competitor.averagePrice === 0 ? 'Free' : 'Paid'}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">Модель</div>
                      </div>
                    </div>
                    
                    <div className="mt-4">
                      <h4 className="font-medium mb-2">Популярні категорії:</h4>
                      <div className="flex flex-wrap gap-2">
                        {competitor.topCategories.map((category, idx) => (
                          <Badge key={idx} variant="secondary">
                            {category}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        {/* Pricing Insights */}
        <TabsContent value="pricing" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {pricingInsights.map((insight, index) => (
              <motion.div
                key={insight.category}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="text-lg">{insight.category}</CardTitle>
                    <CardDescription>Рекомендації по ціноутворенню</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                          <div className="text-lg font-bold text-red-600">
                            ${insight.recommendedPriceRange.min}
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-400">Мінімум</div>
                        </div>
                        
                        <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                          <div className="text-lg font-bold text-green-600">
                            ${insight.recommendedPriceRange.optimal}
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-400">Оптимум</div>
                        </div>
                        
                        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                          <div className="text-lg font-bold text-blue-600">
                            ${insight.recommendedPriceRange.max}
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-400">Максимум</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 dark:text-gray-400">Позиція на ринку</span>
                        <Badge variant={
                          insight.marketPosition === 'premium' ? 'default' :
                          insight.marketPosition === 'mid-range' ? 'secondary' : 'outline'
                        }>
                          {insight.marketPosition === 'premium' ? 'Преміум' :
                           insight.marketPosition === 'mid-range' ? 'Середній' : 'Бюджет'}
                        </Badge>
                      </div>
                      
                      <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700">
                        <div className="flex items-start gap-2">
                          <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
                          <div className="text-sm">
                            <div className="font-medium text-yellow-800 dark:text-yellow-200">Рекомендація</div>
                            <div className="text-yellow-700 dark:text-yellow-300">
                              Встановіть ціну близько до ${insight.recommendedPriceRange.optimal} для оптимального балансу між прибутком та конкурентоспроможністю.
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
