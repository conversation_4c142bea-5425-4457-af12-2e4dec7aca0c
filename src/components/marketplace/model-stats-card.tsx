'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  Download, 
  Heart, 
  Eye, 
  Star, 
  Users,
  Calendar,
  Award,
  Zap,
  BarChart3
} from 'lucide-react';

interface ModelStats {
  views: number;
  downloads: number;
  likes: number;
  rating?: number;
  reviews?: number;
  trending_score?: number;
}

interface ModelStatsCardProps {
  title: string;
  stats: ModelStats;
  platform: string;
  category: string;
  publishedAt?: string;
  featured?: boolean;
  isNew?: boolean;
  className?: string;
  variant?: 'default' | 'compact' | 'detailed';
}

const ModelStatsCard: React.FC<ModelStatsCardProps> = ({
  title,
  stats,
  platform,
  category,
  publishedAt,
  featured = false,
  isNew = false,
  className = "",
  variant = 'default'
}) => {
  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const formatDate = (dateString?: string): string => {
    if (!dateString) return 'Невідомо';
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Вчора';
    if (diffDays < 7) return `${diffDays} днів тому`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} тижнів тому`;
    if (diffDays < 365) return `${Math.ceil(diffDays / 30)} місяців тому`;
    return `${Math.ceil(diffDays / 365)} років тому`;
  };

  const getTrendingLevel = (score?: number): { level: string; color: string; progress: number } => {
    if (!score) return { level: 'Низький', color: 'text-gray-500', progress: 0 };
    
    if (score >= 90) return { level: 'Вірусний', color: 'text-red-500', progress: 100 };
    if (score >= 80) return { level: 'Дуже популярний', color: 'text-orange-500', progress: 85 };
    if (score >= 70) return { level: 'Популярний', color: 'text-yellow-500', progress: 70 };
    if (score >= 50) return { level: 'Помірний', color: 'text-blue-500', progress: 50 };
    return { level: 'Низький', color: 'text-gray-500', progress: 25 };
  };

  const getEngagementRate = (): number => {
    if (stats.views === 0) return 0;
    return ((stats.likes + stats.downloads) / stats.views) * 100;
  };

  const getPopularityScore = (): number => {
    // Простий алгоритм для розрахунку популярності
    const viewsScore = Math.min(stats.views / 10000, 1) * 30;
    const downloadsScore = Math.min(stats.downloads / 5000, 1) * 40;
    const likesScore = Math.min(stats.likes / 2000, 1) * 20;
    const ratingScore = stats.rating ? (stats.rating / 5) * 10 : 0;
    
    return Math.round(viewsScore + downloadsScore + likesScore + ratingScore);
  };

  const trending = getTrendingLevel(stats.trending_score);
  const engagementRate = getEngagementRate();
  const popularityScore = getPopularityScore();

  if (variant === 'compact') {
    return (
      <Card className={`${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-sm truncate">{title}</h3>
            <div className="flex gap-1">
              {featured && <Badge className="bg-purple-500 text-white text-xs">Featured</Badge>}
              {isNew && <Badge className="bg-green-500 text-white text-xs">New</Badge>}
            </div>
          </div>
          
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-muted-foreground">
                <Eye className="w-3 h-3" />
              </div>
              <div className="font-medium">{formatNumber(stats.views)}</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-muted-foreground">
                <Download className="w-3 h-3" />
              </div>
              <div className="font-medium">{formatNumber(stats.downloads)}</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-muted-foreground">
                <Heart className="w-3 h-3" />
              </div>
              <div className="font-medium">{formatNumber(stats.likes)}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (variant === 'detailed') {
    return (
      <Card className={`${className}`}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">{title}</CardTitle>
            <div className="flex gap-2">
              {featured && (
                <Badge className="bg-purple-500 text-white flex items-center gap-1">
                  <Award className="w-3 h-3" />
                  Featured
                </Badge>
              )}
              {isNew && (
                <Badge className="bg-green-500 text-white flex items-center gap-1">
                  <Zap className="w-3 h-3" />
                  New
                </Badge>
              )}
            </div>
          </div>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>{platform.toUpperCase()}</span>
            <span>•</span>
            <span>{category}</span>
            <span>•</span>
            <span>{formatDate(publishedAt)}</span>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Main Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <motion.div 
              className="text-center p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20"
              whileHover={{ scale: 1.05 }}
            >
              <Eye className="w-6 h-6 mx-auto mb-2 text-blue-500" />
              <div className="text-2xl font-bold text-blue-600">{formatNumber(stats.views)}</div>
              <div className="text-xs text-muted-foreground">Перегляди</div>
            </motion.div>

            <motion.div 
              className="text-center p-3 rounded-lg bg-green-50 dark:bg-green-950/20"
              whileHover={{ scale: 1.05 }}
            >
              <Download className="w-6 h-6 mx-auto mb-2 text-green-500" />
              <div className="text-2xl font-bold text-green-600">{formatNumber(stats.downloads)}</div>
              <div className="text-xs text-muted-foreground">Завантаження</div>
            </motion.div>

            <motion.div 
              className="text-center p-3 rounded-lg bg-red-50 dark:bg-red-950/20"
              whileHover={{ scale: 1.05 }}
            >
              <Heart className="w-6 h-6 mx-auto mb-2 text-red-500" />
              <div className="text-2xl font-bold text-red-600">{formatNumber(stats.likes)}</div>
              <div className="text-xs text-muted-foreground">Лайки</div>
            </motion.div>

            {stats.rating && (
              <motion.div 
                className="text-center p-3 rounded-lg bg-yellow-50 dark:bg-yellow-950/20"
                whileHover={{ scale: 1.05 }}
              >
                <Star className="w-6 h-6 mx-auto mb-2 text-yellow-500" />
                <div className="text-2xl font-bold text-yellow-600">{stats.rating}</div>
                <div className="text-xs text-muted-foreground">
                  {stats.reviews ? `${stats.reviews} відгуків` : 'Рейтинг'}
                </div>
              </motion.div>
            )}
          </div>

          {/* Trending Score */}
          {stats.trending_score && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  <span className="font-medium">Трендовість</span>
                </div>
                <span className={`font-semibold ${trending.color}`}>
                  {trending.level}
                </span>
              </div>
              <Progress value={trending.progress} className="h-2" />
              <div className="text-xs text-muted-foreground text-right">
                Оцінка: {Math.round(stats.trending_score)}/100
              </div>
            </div>
          )}

          {/* Engagement Rate */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                <span className="font-medium">Залученість</span>
              </div>
              <span className="font-semibold">
                {engagementRate.toFixed(1)}%
              </span>
            </div>
            <Progress value={Math.min(engagementRate, 100)} className="h-2" />
            <div className="text-xs text-muted-foreground">
              Відношення взаємодій до переглядів
            </div>
          </div>

          {/* Popularity Score */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <BarChart3 className="w-4 h-4" />
                <span className="font-medium">Популярність</span>
              </div>
              <span className="font-semibold">
                {popularityScore}/100
              </span>
            </div>
            <Progress value={popularityScore} className="h-2" />
            <div className="text-xs text-muted-foreground">
              Загальна оцінка популярності моделі
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Default variant
  return (
    <Card className={`${className}`}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold truncate">{title}</h3>
          <div className="flex gap-1">
            {featured && (
              <Badge className="bg-purple-500 text-white text-xs">
                <Award className="w-3 h-3 mr-1" />
                Featured
              </Badge>
            )}
            {isNew && (
              <Badge className="bg-green-500 text-white text-xs">
                <Zap className="w-3 h-3 mr-1" />
                New
              </Badge>
            )}
          </div>
        </div>

        <div className="space-y-3">
          {/* Platform and Category */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>{platform.toUpperCase()}</span>
            <span>•</span>
            <span>{category}</span>
          </div>

          {/* Main Stats */}
          <div className="grid grid-cols-3 gap-3">
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                <Eye className="w-4 h-4" />
                <span className="text-xs">Перегляди</span>
              </div>
              <div className="font-semibold">{formatNumber(stats.views)}</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                <Download className="w-4 h-4" />
                <span className="text-xs">Завантаження</span>
              </div>
              <div className="font-semibold">{formatNumber(stats.downloads)}</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-muted-foreground mb-1">
                <Heart className="w-4 h-4" />
                <span className="text-xs">Лайки</span>
              </div>
              <div className="font-semibold">{formatNumber(stats.likes)}</div>
            </div>
          </div>

          {/* Rating */}
          {stats.rating && (
            <div className="flex items-center justify-center gap-2 pt-2 border-t">
              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
              <span className="font-medium">{stats.rating}</span>
              {stats.reviews && (
                <span className="text-xs text-muted-foreground">
                  ({stats.reviews} відгуків)
                </span>
              )}
            </div>
          )}

          {/* Trending Score */}
          {stats.trending_score && (
            <div className="pt-2 border-t">
              <div className="flex items-center justify-between mb-1">
                <div className="flex items-center gap-1">
                  <TrendingUp className="w-3 h-3" />
                  <span className="text-xs">Трендовість</span>
                </div>
                <span className={`text-xs font-medium ${trending.color}`}>
                  {trending.level}
                </span>
              </div>
              <Progress value={trending.progress} className="h-1" />
            </div>
          )}

          {/* Published Date */}
          {publishedAt && (
            <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground pt-2 border-t">
              <Calendar className="w-3 h-3" />
              {formatDate(publishedAt)}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ModelStatsCard;
