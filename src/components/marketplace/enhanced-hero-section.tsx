'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  Download, 
  TrendingUp, 
  Database, 
  Zap, 
  Globe,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import Client3DScene from './client-3d-scene';

interface PlatformStats {
  name: string;
  url: string;
  models: number;
  lastUpdate: string;
  status: 'active' | 'scraping' | 'error';
  icon: string;
  color: string;
}

interface ScrapingProgress {
  platform: string;
  progress: number;
  status: string;
}

export const EnhancedHeroSection: React.FC = () => {
  const [platformStats, setPlatformStats] = useState<PlatformStats[]>([
    {
      name: 'MakerWorld',
      url: 'makerworld.com',
      models: 0,
      lastUpdate: 'Ніколи',
      status: 'active',
      icon: '🏭',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      name: 'Printables',
      url: 'printables.com',
      models: 0,
      lastUpdate: 'Ніколи',
      status: 'active',
      icon: '🖨️',
      color: 'from-green-500 to-emerald-500'
    },
    {
      name: 'Thangs',
      url: 'thangs.com',
      models: 0,
      lastUpdate: 'Ніколи',
      status: 'active',
      icon: '💎',
      color: 'from-purple-500 to-pink-500'
    }
  ]);

  const [scrapingProgress, setScrapingProgress] = useState<ScrapingProgress[]>([]);
  const [isScrapingActive, setIsScrapingActive] = useState(false);
  const [totalModels, setTotalModels] = useState(0);

  // Завантаження статистики при ініціалізації
  useEffect(() => {
    loadPlatformStats();
  }, []);

  const loadPlatformStats = async () => {
    try {
      const response = await fetch('/api/scraped-models?stats=true');
      if (response.ok) {
        const data = await response.json();
        if (data.stats) {
          setTotalModels(data.stats.total || 0);
          // Оновлюємо статистику платформ
          setPlatformStats(prev => prev.map(platform => ({
            ...platform,
            models: data.stats.byPlatform?.[platform.name.toLowerCase()] || 0,
            lastUpdate: data.stats.lastUpdate || 'Ніколи'
          })));
        }
      }
    } catch (error) {
      console.error('Помилка завантаження статистики:', error);
    }
  };

  const startScraping = async (platform?: string) => {
    setIsScrapingActive(true);
    
    try {
      const action = platform ? `scrape-${platform.toLowerCase()}` : 'bright-data-scrape';
      const response = await fetch('/api/scrape', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, count: 20 })
      });

      const data = await response.json();
      
      if (data.success) {
        // Оновлюємо статистику після успішного скрапінгу
        await loadPlatformStats();
      }
    } catch (error) {
      console.error('Помилка скрапінгу:', error);
    } finally {
      setIsScrapingActive(false);
    }
  };

  const startEnhancedScraping = async () => {
    setIsScrapingActive(true);
    setScrapingProgress([
      { platform: 'MakerWorld', progress: 0, status: 'Початок...' },
      { platform: 'Printables', progress: 0, status: 'Очікування...' },
      { platform: 'Thangs', progress: 0, status: 'Очікування...' }
    ]);

    try {
      // Симулюємо прогрес скрапінгу
      for (let i = 0; i < 3; i++) {
        const platforms = ['MakerWorld', 'Printables', 'Thangs'];
        const platform = platforms[i];
        
        setScrapingProgress(prev => prev.map(p => 
          p.platform === platform 
            ? { ...p, status: 'Скрапінг...', progress: 25 }
            : p
        ));

        const response = await fetch('/api/scraping/enhanced', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'scrape-popular',
            platforms: [platform.toLowerCase()],
            count: 10
          })
        });

        setScrapingProgress(prev => prev.map(p => 
          p.platform === platform 
            ? { ...p, status: 'Завершено', progress: 100 }
            : p
        ));

        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      await loadPlatformStats();
    } catch (error) {
      console.error('Помилка enhanced скрапінгу:', error);
    } finally {
      setIsScrapingActive(false);
      setScrapingProgress([]);
    }
  };

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20">
      {/* Animated background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/30 to-purple-400/30 rounded-full blur-3xl animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-indigo-400/30 to-blue-400/30 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse delay-500" />
      </div>

      <div className="relative container mx-auto px-4 py-16">
        {/* Hero Content */}
        <div className="text-center mb-16">
          <motion.h1 
            className="text-6xl md:text-8xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            3D Маркетплейс
          </motion.h1>
          
          <motion.p 
            className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Використовуємо <span className="font-bold text-blue-600">Bright Data</span> для збору найкращих 3D моделей 
            з провідних платформ світу. AI-powered пошук та розумне ціноутворення.
          </motion.p>

          {/* Stats */}
          <motion.div 
            className="flex flex-wrap justify-center gap-8 mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">{totalModels.toLocaleString()}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Моделей в базі</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">3</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Платформи</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">24/7</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Оновлення</div>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div 
            className="flex flex-wrap justify-center gap-4 mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <Button
              onClick={startEnhancedScraping}
              disabled={isScrapingActive}
              size="lg"
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg"
            >
              {isScrapingActive ? (
                <>
                  <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                  Скрапінг...
                </>
              ) : (
                <>
                  <Zap className="h-5 w-5 mr-2" />
                  Запустити скрапінг
                </>
              )}
            </Button>
            
            <Button
              variant="outline"
              size="lg"
              className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-4 text-lg"
            >
              <Search className="h-5 w-5 mr-2" />
              Розпочати пошук
            </Button>
          </motion.div>
        </div>

        {/* Platform Cards */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16"
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          {platformStats.map((platform, index) => (
            <Card key={platform.name} className="relative overflow-hidden border-0 shadow-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
              <div className={`absolute inset-0 bg-gradient-to-br ${platform.color} opacity-10`} />
              <CardHeader className="relative">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="text-3xl">{platform.icon}</div>
                    <div>
                      <CardTitle className="text-lg">{platform.name}</CardTitle>
                      <CardDescription>{platform.url}</CardDescription>
                    </div>
                  </div>
                  <Badge variant={platform.status === 'active' ? 'default' : 'secondary'}>
                    {platform.status === 'active' ? 'Активна' : 'Скрапінг'}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="relative">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Моделей:</span>
                    <span className="font-bold text-lg">{platform.models.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Оновлено:</span>
                    <span className="text-sm">{platform.lastUpdate}</span>
                  </div>
                  <Button
                    onClick={() => startScraping(platform.name)}
                    disabled={isScrapingActive}
                    className="w-full"
                    variant="outline"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Скрапити
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </motion.div>

        {/* Scraping Progress */}
        <AnimatePresence>
          {scrapingProgress.length > 0 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mb-16"
            >
              <Card className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Прогрес скрапінгу
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {scrapingProgress.map((progress) => (
                      <div key={progress.platform} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">{progress.platform}</span>
                          <span className="text-sm text-gray-600">{progress.status}</span>
                        </div>
                        <Progress value={progress.progress} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 3D Scene */}
        <motion.div 
          className="relative"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, delay: 1 }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-3xl blur-xl" />
          <div className="relative bg-white/10 dark:bg-gray-800/10 backdrop-blur-sm rounded-3xl border border-white/20 dark:border-gray-700/20 overflow-hidden">
            <Client3DScene
              sceneUrl="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
              height="500px"
              className="rounded-3xl"
            />
          </div>
        </motion.div>
      </div>
    </section>
  );
};
