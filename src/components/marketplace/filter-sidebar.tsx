'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';

// Categories based on thangs.com
const CATEGORIES = [
  'All',
  'Toys & Games',
  'Functional',
  'Home',
  'Miniatures',
  'Costumes & Cosplay',
  'Art',
  'Articulated',
  'Gridfinity',
  'Print In Place',
];

// Popular tags
const POPULAR_TAGS = [
  'dragon', 'articulated', 'vase', 'organizer', 'cosplay',
  'miniature', 'functional', 'decor', 'toy', 'fantasy'
];

export default function FilterSidebar() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get current filters from URL
  const currentCategory = searchParams.get('category') || 'All';
  const currentSearch = searchParams.get('search') || '';
  
  // Local state for filters
  const [selectedCategory, setSelectedCategory] = useState(currentCategory);
  const [priceFilter, setPriceFilter] = useState('all');
  const [priceRange, setPriceRange] = useState([0, 50]);

  // Handle category selection
  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
    
    // Build query parameters
    const params = new URLSearchParams(searchParams.toString());
    if (category !== 'All') {
      params.set('category', category);
    } else {
      params.delete('category');
    }
    
    // Navigate to the new URL
    router.push(`/marketplace?${params.toString()}`);
  };

  // Handle price filter selection
  const handlePriceFilterSelect = (filter: string) => {
    setPriceFilter(filter);
    
    // Build query parameters
    const params = new URLSearchParams(searchParams.toString());
    
    if (filter === 'free') {
      params.set('maxPrice', '0');
      params.set('minPrice', '0');
    } else if (filter === 'paid') {
      params.set('minPrice', '0.01');
      params.delete('maxPrice');
    } else {
      params.delete('minPrice');
      params.delete('maxPrice');
    }
    
    // Navigate to the new URL
    router.push(`/marketplace?${params.toString()}`);
  };

  // Handle price range change
  const handlePriceRangeChange = (values: number[]) => {
    setPriceRange(values);
  };

  // Apply price range filter
  const applyPriceRange = () => {
    // Build query parameters
    const params = new URLSearchParams(searchParams.toString());
    params.set('minPrice', priceRange[0].toString());
    params.set('maxPrice', priceRange[1].toString());
    
    // Navigate to the new URL
    router.push(`/marketplace?${params.toString()}`);
  };

  // Handle tag selection
  const handleTagSelect = (tag: string) => {
    // Build query parameters
    const params = new URLSearchParams(searchParams.toString());
    params.set('search', tag);
    
    // Navigate to the new URL
    router.push(`/marketplace?${params.toString()}`);
  };

  return (
    <div className="space-y-6">
      <div className="bg-card rounded-lg p-6 shadow-sm">
        <h2 className="text-xl font-semibold mb-4">Categories</h2>
        <div className="space-y-2">
          {CATEGORIES.map((category) => (
            <button
              key={category}
              className={`block w-full text-left px-3 py-2 rounded-md transition-colors ${
                selectedCategory === category
                  ? 'bg-primary text-primary-foreground'
                  : 'hover:bg-muted'
              }`}
              onClick={() => handleCategorySelect(category)}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      <div className="bg-card rounded-lg p-6 shadow-sm">
        <h2 className="text-xl font-semibold mb-4">Price</h2>
        <div className="space-y-4">
          <div className="flex space-x-2">
            <Button
              variant={priceFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handlePriceFilterSelect('all')}
              className="flex-1"
            >
              All
            </Button>
            <Button
              variant={priceFilter === 'free' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handlePriceFilterSelect('free')}
              className="flex-1"
            >
              Free
            </Button>
            <Button
              variant={priceFilter === 'paid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handlePriceFilterSelect('paid')}
              className="flex-1"
            >
              Paid
            </Button>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span>${priceRange[0]}</span>
              <span>${priceRange[1]}</span>
            </div>
            <Slider
              defaultValue={[0, 50]}
              max={50}
              step={1}
              value={priceRange}
              onValueChange={handlePriceRangeChange}
            />
            <Button 
              size="sm" 
              className="w-full mt-2"
              onClick={applyPriceRange}
            >
              Apply
            </Button>
          </div>
        </div>
      </div>

      <div className="bg-card rounded-lg p-6 shadow-sm">
        <h2 className="text-xl font-semibold mb-4">Popular Tags</h2>
        <div className="flex flex-wrap gap-2">
          {POPULAR_TAGS.map((tag) => (
            <Badge
              key={tag}
              variant="outline"
              className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
              onClick={() => handleTagSelect(tag)}
            >
              {tag}
            </Badge>
          ))}
        </div>
      </div>
    </div>
  );
}
