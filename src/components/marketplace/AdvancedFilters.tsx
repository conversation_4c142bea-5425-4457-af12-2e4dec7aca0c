'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { 
  Filter, 
  X, 
  ChevronDown, 
  ChevronUp,
  Search,
  SlidersHorizontal,
  Tag,
  DollarSign,
  Calendar,
  Download,
  Star
} from 'lucide-react';
import { ModelsQueryParams, ModelSource } from '@/types/models';

interface AdvancedFiltersProps {
  filters: ModelsQueryParams;
  onFiltersChange: (filters: ModelsQueryParams) => void;
  onReset: () => void;
  availableCategories?: string[];
  availableTags?: string[];
  availableFileFormats?: string[];
  isLoading?: boolean;
}

interface FilterSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  isOpen: boolean;
}

export default function AdvancedFilters({
  filters,
  onFiltersChange,
  onReset,
  availableCategories = [],
  availableTags = [],
  availableFileFormats = [],
  isLoading = false
}: AdvancedFiltersProps) {
  const [searchTerm, setSearchTerm] = useState(filters.search || '');
  const [selectedTags, setSelectedTags] = useState<string[]>(filters.tags || []);
  const [priceRange, setPriceRange] = useState<[number, number]>([
    filters.minPrice || 0,
    filters.maxPrice || 1000
  ]);
  const [sections, setSections] = useState<FilterSection[]>([
    { id: 'search', title: 'Search', icon: <Search className="w-4 h-4" />, isOpen: true },
    { id: 'category', title: 'Category', icon: <Filter className="w-4 h-4" />, isOpen: true },
    { id: 'price', title: 'Price', icon: <DollarSign className="w-4 h-4" />, isOpen: true },
    { id: 'tags', title: 'Tags', icon: <Tag className="w-4 h-4" />, isOpen: false },
    { id: 'platform', title: 'Platform', icon: <SlidersHorizontal className="w-4 h-4" />, isOpen: false },
    { id: 'date', title: 'Date', icon: <Calendar className="w-4 h-4" />, isOpen: false },
    { id: 'stats', title: 'Statistics', icon: <Star className="w-4 h-4" />, isOpen: false },
    { id: 'files', title: 'Files', icon: <Download className="w-4 h-4" />, isOpen: false },
  ]);

  const platforms: { value: ModelSource; label: string }[] = [
    { value: 'local', label: 'Local' },
    { value: 'printables', label: 'Printables' },
    { value: 'thingiverse', label: 'Thingiverse' },
    { value: 'myminifactory', label: 'MyMiniFactory' },
    { value: 'thangs', label: 'Thangs' },
    { value: 'makerworld', label: 'MakerWorld' },
  ];

  const sortOptions = [
    { value: 'newest', label: 'Newest' },
    { value: 'popular', label: 'Popular' },
    { value: 'downloads', label: 'Most Downloaded' },
    { value: 'price_asc', label: 'Price: Low to High' },
    { value: 'price_desc', label: 'Price: High to Low' },
    { value: 'oldest', label: 'Oldest' },
    { value: 'name', label: 'By Name' },
  ];

  const dateRanges = [
    { value: 'all', label: 'All Time' },
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'year', label: 'This Year' },
  ];

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchTerm !== filters.search) {
        onFiltersChange({ ...filters, search: searchTerm });
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm, filters, onFiltersChange]);

  const toggleSection = (sectionId: string) => {
    setSections(prev => prev.map(section => 
      section.id === sectionId 
        ? { ...section, isOpen: !section.isOpen }
        : section
    ));
  };

  const handleTagToggle = (tag: string) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag];
    
    setSelectedTags(newTags);
    onFiltersChange({ ...filters, tags: newTags });
  };

  const handlePriceChange = (values: number[]) => {
    const [min, max] = values;
    setPriceRange([min, max]);
    onFiltersChange({ 
      ...filters, 
      minPrice: min > 0 ? min : undefined,
      maxPrice: max < 1000 ? max : undefined
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.category) count++;
    if (filters.minPrice || filters.maxPrice) count++;
    if (filters.tags?.length) count += filters.tags.length;
    if (filters.source) count++;
    if (filters.dateRange && filters.dateRange !== 'all') count++;
    if (filters.isFree !== undefined) count++;
    if (filters.minDownloads) count++;
    if (filters.fileFormats?.length) count += filters.fileFormats.length;
    return count;
  };

  const clearAllFilters = () => {
    setSearchTerm('');
    setSelectedTags([]);
    setPriceRange([0, 1000]);
    onReset();
  };

  // Sync local state with filters prop changes
  useEffect(() => {
    setSearchTerm(filters.search || '');
    setSelectedTags(filters.tags || []);
    setPriceRange([
      filters.minPrice || 0,
      filters.maxPrice || 1000
    ]);
  }, [filters.search, filters.tags, filters.minPrice, filters.maxPrice]);

  return (
    <Card className="p-4 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="w-5 h-5" />
          <h3 className="font-semibold">Filters</h3>
          {getActiveFiltersCount() > 0 && (
            <Badge variant="secondary" className="ml-2">
              {getActiveFiltersCount()}
            </Badge>
          )}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={clearAllFilters}
          disabled={getActiveFiltersCount() === 0}
        >
          <X className="w-4 h-4 mr-1" />
          Clear
        </Button>
      </div>

      {/* Search Section */}
      <Collapsible 
        open={sections.find(s => s.id === 'search')?.isOpen}
        onOpenChange={() => toggleSection('search')}
      >
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-between p-2">
            <div className="flex items-center gap-2">
              <Search className="w-4 h-4" />
              <span>Search</span>
            </div>
            {sections.find(s => s.id === 'search')?.isOpen ?
              <ChevronUp className="w-4 h-4" /> :
              <ChevronDown className="w-4 h-4" />
            }
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-2 pt-2">
          <Input
            placeholder="Search models..."
            value={searchTerm}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
            className="w-full"
          />
        </CollapsibleContent>
      </Collapsible>

      {/* Category Section */}
      <Collapsible 
        open={sections.find(s => s.id === 'category')?.isOpen}
        onOpenChange={() => toggleSection('category')}
      >
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-between p-2">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4" />
              <span>Category</span>
            </div>
            {sections.find(s => s.id === 'category')?.isOpen ?
              <ChevronUp className="w-4 h-4" /> :
              <ChevronDown className="w-4 h-4" />
            }
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-2 pt-2">
          <Select
            value={filters.category || ''}
            onValueChange={(value: string) =>
              onFiltersChange({
                ...filters,
                category: value === 'all' ? undefined : value
              })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {availableCategories.map(category => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CollapsibleContent>
      </Collapsible>

      {/* Price Section */}
      <Collapsible 
        open={sections.find(s => s.id === 'price')?.isOpen}
        onOpenChange={() => toggleSection('price')}
      >
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-between p-2">
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4" />
              <span>Price</span>
            </div>
            {sections.find(s => s.id === 'price')?.isOpen ?
              <ChevronUp className="w-4 h-4" /> :
              <ChevronDown className="w-4 h-4" />
            }
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 pt-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="free-only"
              checked={filters.isFree === true}
              onCheckedChange={(checked: boolean) =>
                onFiltersChange({
                  ...filters,
                  isFree: checked ? true : undefined
                })
              }
            />
            <Label htmlFor="free-only" className="text-sm">
              Free Only
            </Label>
          </div>

          {filters.isFree !== true && (
            <div className="space-y-2">
              <Label className="text-sm">
                Price Range: ${priceRange[0]} - ${priceRange[1]}
              </Label>
              <Slider
                value={priceRange}
                onValueChange={handlePriceChange}
                max={1000}
                min={0}
                step={5}
                className="w-full"
              />
            </div>
          )}
        </CollapsibleContent>
      </Collapsible>

      {/* Tags Section */}
      <Collapsible 
        open={sections.find(s => s.id === 'tags')?.isOpen}
        onOpenChange={() => toggleSection('tags')}
      >
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-between p-2">
            <div className="flex items-center gap-2">
              <Tag className="w-4 h-4" />
              <span>Tags</span>
              {selectedTags.length > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {selectedTags.length}
                </Badge>
              )}
            </div>
            {sections.find(s => s.id === 'tags')?.isOpen ?
              <ChevronUp className="w-4 h-4" /> :
              <ChevronDown className="w-4 h-4" />
            }
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-2 pt-2">
          <div className="flex flex-wrap gap-1 max-h-32 overflow-y-auto">
            {availableTags.map(tag => (
              <Badge
                key={tag}
                variant={selectedTags.includes(tag) ? "default" : "outline"}
                className="cursor-pointer text-xs"
                onClick={() => handleTagToggle(tag)}
              >
                {tag}
              </Badge>
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Platform Section */}
      <Collapsible 
        open={sections.find(s => s.id === 'platform')?.isOpen}
        onOpenChange={() => toggleSection('platform')}
      >
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-between p-2">
            <div className="flex items-center gap-2">
              <SlidersHorizontal className="w-4 h-4" />
              <span>Platform</span>
            </div>
            {sections.find(s => s.id === 'platform')?.isOpen ?
              <ChevronUp className="w-4 h-4" /> :
              <ChevronDown className="w-4 h-4" />
            }
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-2 pt-2">
          <Select
            value={filters.source || ''}
            onValueChange={(value: string) =>
              onFiltersChange({
                ...filters,
                source: value === 'all' ? undefined : value as ModelSource
              })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select platform" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Platforms</SelectItem>
              {platforms.map(platform => (
                <SelectItem key={platform.value} value={platform.value}>
                  {platform.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CollapsibleContent>
      </Collapsible>

      {/* Date Section */}
      <Collapsible
        open={sections.find(s => s.id === 'date')?.isOpen}
        onOpenChange={() => toggleSection('date')}
      >
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-between p-2">
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              <span>Date</span>
            </div>
            {sections.find(s => s.id === 'date')?.isOpen ?
              <ChevronUp className="w-4 h-4" /> :
              <ChevronDown className="w-4 h-4" />
            }
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-2 pt-2">
          <Select
            value={filters.dateRange || 'all'}
            onValueChange={(value: string) =>
              onFiltersChange({
                ...filters,
                dateRange: value === 'all' ? undefined : value
              })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select date range" />
            </SelectTrigger>
            <SelectContent>
              {dateRanges.map(range => (
                <SelectItem key={range.value} value={range.value}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CollapsibleContent>
      </Collapsible>

      {/* Statistics Section */}
      <Collapsible
        open={sections.find(s => s.id === 'stats')?.isOpen}
        onOpenChange={() => toggleSection('stats')}
      >
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-between p-2">
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4" />
              <span>Statistics</span>
            </div>
            {sections.find(s => s.id === 'stats')?.isOpen ?
              <ChevronUp className="w-4 h-4" /> :
              <ChevronDown className="w-4 h-4" />
            }
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 pt-2">
          <div className="space-y-2">
            <Label className="text-sm">Minimum Downloads</Label>
            <Input
              type="number"
              placeholder="e.g. 100"
              value={filters.minDownloads || ''}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                onFiltersChange({
                  ...filters,
                  minDownloads: e.target.value ? parseInt(e.target.value) : undefined
                })
              }
              className="w-full"
            />
          </div>
          <div className="space-y-2">
            <Label className="text-sm">Minimum Rating</Label>
            <Select
              value={filters.minRating?.toString() || ''}
              onValueChange={(value: string) =>
                onFiltersChange({
                  ...filters,
                  minRating: value ? parseFloat(value) : undefined
                })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select minimum rating" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Any Rating</SelectItem>
                <SelectItem value="1">1+ Stars</SelectItem>
                <SelectItem value="2">2+ Stars</SelectItem>
                <SelectItem value="3">3+ Stars</SelectItem>
                <SelectItem value="4">4+ Stars</SelectItem>
                <SelectItem value="4.5">4.5+ Stars</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Files Section */}
      <Collapsible
        open={sections.find(s => s.id === 'files')?.isOpen}
        onOpenChange={() => toggleSection('files')}
      >
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-between p-2">
            <div className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              <span>File Formats</span>
            </div>
            {sections.find(s => s.id === 'files')?.isOpen ?
              <ChevronUp className="w-4 h-4" /> :
              <ChevronDown className="w-4 h-4" />
            }
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-2 pt-2">
          <div className="flex flex-wrap gap-1 max-h-32 overflow-y-auto">
            {availableFileFormats.length > 0 ? (
              availableFileFormats.map(format => (
                <Badge
                  key={format}
                  variant={filters.fileFormats?.includes(format) ? "default" : "outline"}
                  className="cursor-pointer text-xs"
                  onClick={() => {
                    const currentFormats = filters.fileFormats || [];
                    const newFormats = currentFormats.includes(format)
                      ? currentFormats.filter((f: string) => f !== format)
                      : [...currentFormats, format];
                    onFiltersChange({ ...filters, fileFormats: newFormats });
                  }}
                >
                  {format.toUpperCase()}
                </Badge>
              ))
            ) : (
              ['STL', 'OBJ', 'GLTF', 'PLY', '3MF', 'AMF'].map(format => (
                <Badge
                  key={format}
                  variant={filters.fileFormats?.includes(format.toLowerCase()) ? "default" : "outline"}
                  className="cursor-pointer text-xs"
                  onClick={() => {
                    const currentFormats = filters.fileFormats || [];
                    const formatLower = format.toLowerCase();
                    const newFormats = currentFormats.includes(formatLower)
                      ? currentFormats.filter((f: string) => f !== formatLower)
                      : [...currentFormats, formatLower];
                    onFiltersChange({ ...filters, fileFormats: newFormats });
                  }}
                >
                  {format}
                </Badge>
              ))
            )}
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Sort Section */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">Sort By</Label>
        <Select
          value={filters.sortBy || 'newest'}
          onValueChange={(value: string) =>
            onFiltersChange({
              ...filters,
              sortBy: value as ModelsQueryParams['sortBy']
            })
          }
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {sortOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </Card>
  );
}
