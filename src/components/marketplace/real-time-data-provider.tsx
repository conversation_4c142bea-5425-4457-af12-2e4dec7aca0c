'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { BrightDataMCPClient } from '@/lib/bright-data/mcp-client';
import { ModelSource } from '@/types/models';

interface RealTimeData {
  trending: any[];
  popular: any[];
  recent: any[];
  lastUpdated: Date | null;
  isLoading: boolean;
  error: string | null;
}

interface RealTimeDataContextType {
  data: RealTimeData;
  refreshData: (platform?: ModelSource) => Promise<void>;
  subscribeToUpdates: (callback: (data: RealTimeData) => void) => () => void;
  getModelsByPlatform: (platform: ModelSource) => any[];
  getModelsByCategory: (category: string) => any[];
  searchModels: (query: string) => any[];
}

const RealTimeDataContext = createContext<RealTimeDataContextType | null>(null);

interface RealTimeDataProviderProps {
  children: ReactNode;
  autoRefresh?: boolean;
  refreshInterval?: number;
  platforms?: ModelSource[];
}

export const RealTimeDataProvider: React.FC<RealTimeDataProviderProps> = ({
  children,
  autoRefresh = true,
  refreshInterval = 300000, // 5 minutes
  platforms = ['printables', 'makerworld', 'thangs']
}) => {
  const [data, setData] = useState<RealTimeData>({
    trending: [],
    popular: [],
    recent: [],
    lastUpdated: null,
    isLoading: false,
    error: null
  });

  const [subscribers, setSubscribers] = useState<((data: RealTimeData) => void)[]>([]);
  const [mcpClient] = useState(() => new BrightDataMCPClient());

  // Notify all subscribers when data changes
  const notifySubscribers = (newData: RealTimeData) => {
    subscribers.forEach(callback => callback(newData));
  };

  // Fetch data from all platforms
  const fetchAllData = async (platform?: ModelSource) => {
    setData(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const targetPlatforms = platform ? [platform] : platforms;
      const results = await Promise.allSettled(
        targetPlatforms.map(async (p) => {
          const [trending, popular, recent] = await Promise.all([
            fetchTrendingModels(p),
            fetchPopularModels(p),
            fetchRecentModels(p)
          ]);
          
          return { platform: p, trending, popular, recent };
        })
      );

      // Combine results from all platforms
      const combinedData = {
        trending: [],
        popular: [],
        recent: []
      } as any;

      results.forEach((result) => {
        if (result.status === 'fulfilled') {
          combinedData.trending.push(...result.value.trending);
          combinedData.popular.push(...result.value.popular);
          combinedData.recent.push(...result.value.recent);
        }
      });

      // Sort by relevance/score
      combinedData.trending.sort((a: any, b: any) => 
        (b.stats?.trending_score || 0) - (a.stats?.trending_score || 0)
      );
      combinedData.popular.sort((a: any, b: any) => 
        (b.stats?.downloads || 0) - (a.stats?.downloads || 0)
      );
      combinedData.recent.sort((a: any, b: any) => 
        (a.hoursAgo || 0) - (b.hoursAgo || 0)
      );

      const newData = {
        ...combinedData,
        lastUpdated: new Date(),
        isLoading: false,
        error: null
      };

      setData(newData);
      notifySubscribers(newData);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const newData = {
        ...data,
        isLoading: false,
        error: errorMessage
      };
      
      setData(newData);
      notifySubscribers(newData);
    }
  };

  // Fetch trending models from a platform
  const fetchTrendingModels = async (platform: ModelSource) => {
    try {
      const response = await fetch(`/api/bright-data/trending?platform=${platform}&limit=8`);
      const result = await response.json();
      return result.success ? result.data.models : [];
    } catch (error) {
      console.error(`Error fetching trending models from ${platform}:`, error);
      return [];
    }
  };

  // Fetch popular models from a platform
  const fetchPopularModels = async (platform: ModelSource) => {
    try {
      const response = await fetch(`/api/bright-data/popular?platform=${platform}&limit=8`);
      const result = await response.json();
      return result.success ? result.data.models : [];
    } catch (error) {
      console.error(`Error fetching popular models from ${platform}:`, error);
      return [];
    }
  };

  // Fetch recent models from a platform
  const fetchRecentModels = async (platform: ModelSource) => {
    try {
      const response = await fetch(`/api/bright-data/recent?platform=${platform}&limit=8`);
      const result = await response.json();
      return result.success ? result.data.models : [];
    } catch (error) {
      console.error(`Error fetching recent models from ${platform}:`, error);
      return [];
    }
  };

  // Subscribe to data updates
  const subscribeToUpdates = (callback: (data: RealTimeData) => void) => {
    setSubscribers(prev => [...prev, callback]);
    
    // Return unsubscribe function
    return () => {
      setSubscribers(prev => prev.filter(cb => cb !== callback));
    };
  };

  // Filter models by platform
  const getModelsByPlatform = (platform: ModelSource) => {
    const allModels = [...data.trending, ...data.popular, ...data.recent];
    return allModels.filter(model => model.platform === platform);
  };

  // Filter models by category
  const getModelsByCategory = (category: string) => {
    const allModels = [...data.trending, ...data.popular, ...data.recent];
    return allModels.filter(model => 
      model.category?.toLowerCase() === category.toLowerCase()
    );
  };

  // Search models
  const searchModels = (query: string) => {
    const allModels = [...data.trending, ...data.popular, ...data.recent];
    const searchTerm = query.toLowerCase();
    
    return allModels.filter(model => 
      model.title?.toLowerCase().includes(searchTerm) ||
      model.description?.toLowerCase().includes(searchTerm) ||
      model.tags?.some((tag: string) => tag.toLowerCase().includes(searchTerm)) ||
      model.designer?.name?.toLowerCase().includes(searchTerm)
    );
  };

  // Initial data fetch
  useEffect(() => {
    fetchAllData();
  }, []);

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchAllData();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]);

  // Context value
  const contextValue: RealTimeDataContextType = {
    data,
    refreshData: fetchAllData,
    subscribeToUpdates,
    getModelsByPlatform,
    getModelsByCategory,
    searchModels
  };

  return (
    <RealTimeDataContext.Provider value={contextValue}>
      {children}
    </RealTimeDataContext.Provider>
  );
};

// Hook to use the real-time data context
export const useRealTimeData = () => {
  const context = useContext(RealTimeDataContext);
  if (!context) {
    throw new Error('useRealTimeData must be used within a RealTimeDataProvider');
  }
  return context;
};

// Hook for specific data types
export const useTrendingData = () => {
  const { data, refreshData } = useRealTimeData();
  return {
    models: data.trending,
    isLoading: data.isLoading,
    error: data.error,
    lastUpdated: data.lastUpdated,
    refresh: () => refreshData()
  };
};

export const usePopularData = () => {
  const { data, refreshData } = useRealTimeData();
  return {
    models: data.popular,
    isLoading: data.isLoading,
    error: data.error,
    lastUpdated: data.lastUpdated,
    refresh: () => refreshData()
  };
};

export const useRecentData = () => {
  const { data, refreshData } = useRealTimeData();
  return {
    models: data.recent,
    isLoading: data.isLoading,
    error: data.error,
    lastUpdated: data.lastUpdated,
    refresh: () => refreshData()
  };
};

// Hook for platform-specific data
export const usePlatformData = (platform: ModelSource) => {
  const { getModelsByPlatform, refreshData, data } = useRealTimeData();
  
  return {
    models: getModelsByPlatform(platform),
    isLoading: data.isLoading,
    error: data.error,
    lastUpdated: data.lastUpdated,
    refresh: () => refreshData(platform)
  };
};

// Hook for category-specific data
export const useCategoryData = (category: string) => {
  const { getModelsByCategory, data } = useRealTimeData();
  
  return {
    models: getModelsByCategory(category),
    isLoading: data.isLoading,
    error: data.error,
    lastUpdated: data.lastUpdated
  };
};

// Hook for search functionality
export const useSearchData = (query: string) => {
  const { searchModels, data } = useRealTimeData();
  
  return {
    models: query ? searchModels(query) : [],
    isLoading: data.isLoading,
    error: data.error,
    lastUpdated: data.lastUpdated
  };
};

// Performance monitoring hook
export const useDataMetrics = () => {
  const { data } = useRealTimeData();
  
  const metrics = {
    totalModels: data.trending.length + data.popular.length + data.recent.length,
    trendingCount: data.trending.length,
    popularCount: data.popular.length,
    recentCount: data.recent.length,
    lastUpdated: data.lastUpdated,
    isStale: data.lastUpdated ? 
      (Date.now() - data.lastUpdated.getTime()) > 600000 : true, // 10 minutes
    platforms: Array.from(new Set([
      ...data.trending.map(m => m.platform),
      ...data.popular.map(m => m.platform),
      ...data.recent.map(m => m.platform)
    ])),
    categories: Array.from(new Set([
      ...data.trending.map(m => m.category),
      ...data.popular.map(m => m.category),
      ...data.recent.map(m => m.category)
    ]))
  };

  return metrics;
};

export default RealTimeDataProvider;
