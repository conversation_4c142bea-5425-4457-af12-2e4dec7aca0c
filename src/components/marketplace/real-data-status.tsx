'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { motion } from 'framer-motion';
import { 
  Activity, 
  Database, 
  Globe, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle,
  TrendingUp,
  Download,
  Eye
} from 'lucide-react';
import { useEffect, useState } from 'react';

interface ScrapingStats {
  totalModels: number;
  successfulScrapes: number;
  failedScrapes: number;
  platforms: Record<string, number>;
  lastUpdate: string;
}

interface MCPHealth {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  lastCheck: string;
  error?: string;
}

interface RealDataStatus {
  scraping: {
    enabled: boolean;
    stats: ScrapingStats;
    lastRun: string;
  };
  mcp: {
    enabled: boolean;
    health: MCPHealth[];
  };
  models: {
    total: number;
    realData: number;
    fallback: number;
  };
}

export default function RealDataStatus() {
  const [status, setStatus] = useState<RealDataStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchStatus = async () => {
    try {
      setRefreshing(true);
      
      // Fetch scraping stats
      const scrapingResponse = await fetch('/api/scraping/popular-models?action=stats');
      const scrapingData = scrapingResponse.ok ? await scrapingResponse.json() : null;
      
      // Fetch MCP health
      const mcpResponse = await fetch('/api/mcp/bright-data?action=health');
      const mcpData = mcpResponse.ok ? await mcpResponse.json() : null;
      
      // Test real scraping
      const testResponse = await fetch('/api/scraping/popular-models?limit=3');
      const testData = testResponse.ok ? await testResponse.json() : null;
      
      setStatus({
        scraping: {
          enabled: true,
          stats: scrapingData?.data || {
            totalModels: 0,
            successfulScrapes: 0,
            failedScrapes: 0,
            platforms: {},
            lastUpdate: new Date().toISOString()
          },
          lastRun: new Date().toISOString()
        },
        mcp: {
          enabled: true,
          health: mcpData?.data || []
        },
        models: {
          total: testData?.data?.models?.length || 0,
          realData: testData?.success ? testData.data.models.length : 0,
          fallback: testData?.success ? 0 : 3
        }
      });
      
    } catch (error) {
      console.error('Failed to fetch real data status:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchStatus();
  }, []);

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className="h-24 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-50 border-green-200';
      case 'degraded': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'unhealthy': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-4 w-4" />;
      case 'degraded': return <AlertCircle className="h-4 w-4" />;
      case 'unhealthy': return <AlertCircle className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="w-full border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5 text-blue-600" />
                Real Data Integration Status
              </CardTitle>
              <CardDescription>
                Live status of Bright Data MCP scraping and real 3D model data
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchStatus}
              disabled={refreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <motion.div
              className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <Database className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Real Models</p>
                  <p className="text-2xl font-bold text-green-600">{status?.models.realData || 0}</p>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Success Rate</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {status?.scraping.stats.successfulScrapes && status?.scraping.stats.totalModels 
                      ? Math.round((status.scraping.stats.successfulScrapes / (status.scraping.stats.successfulScrapes + status.scraping.stats.failedScrapes)) * 100)
                      : 0}%
                  </p>
                </div>
              </div>
            </motion.div>

            <motion.div
              className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
              whileHover={{ scale: 1.02 }}
            >
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                  <Globe className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Platforms</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {Object.keys(status?.scraping.stats.platforms || {}).length}
                  </p>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Platform Breakdown */}
          {status?.scraping.stats.platforms && Object.keys(status.scraping.stats.platforms).length > 0 && (
            <div>
              <h4 className="text-lg font-semibold mb-3">Platform Breakdown</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {Object.entries(status.scraping.stats.platforms).map(([platform, count]) => (
                  <div key={platform} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg border">
                    <span className="capitalize font-medium">{platform}</span>
                    <Badge variant="secondary">{count} models</Badge>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* MCP Health Status */}
          {status?.mcp.health && status.mcp.health.length > 0 && (
            <div>
              <h4 className="text-lg font-semibold mb-3">MCP Services Health</h4>
              <div className="space-y-2">
                {status.mcp.health.map((service, index) => (
                  <div key={index} className={`flex items-center justify-between p-3 rounded-lg border ${getStatusColor(service.status)}`}>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(service.status)}
                      <span className="font-medium">{service.service}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <span>{service.responseTime}ms</span>
                      <Badge variant={service.status === 'healthy' ? 'default' : 'destructive'}>
                        {service.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Last Update */}
          <div className="text-center text-sm text-gray-500 dark:text-gray-400">
            Last updated: {status?.scraping.lastRun ? new Date(status.scraping.lastRun).toLocaleString() : 'Never'}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
