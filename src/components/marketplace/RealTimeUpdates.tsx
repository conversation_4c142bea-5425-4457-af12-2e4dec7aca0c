'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Bell, 
  X, 
  Download, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Zap,
  Activity,
  RefreshCw
} from 'lucide-react';

interface RealTimeUpdate {
  id: string;
  type: 'download_started' | 'download_completed' | 'download_failed' | 'scraping_started' | 'scraping_completed' | 'system_alert';
  title: string;
  message: string;
  timestamp: string;
  data?: any;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
}

interface RealTimeUpdatesProps {
  onUpdateReceived?: (update: RealTimeUpdate) => void;
}

export default function RealTimeUpdates({ onUpdateReceived }: RealTimeUpdatesProps) {
  const [updates, setUpdates] = useState<RealTimeUpdate[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const wsRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    // Симуляція WebSocket з'єднання для real-time оновлень
    // В реальному додатку це буде справжнє WebSocket з'єднання
    simulateRealTimeUpdates();
    
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  const simulateRealTimeUpdates = () => {
    // Симуляція підключення
    setIsConnected(true);
    
    // Симуляція отримання оновлень
    const interval = setInterval(() => {
      if (Math.random() > 0.7) { // 30% шанс отримати оновлення
        const mockUpdate = generateMockUpdate();
        addUpdate(mockUpdate);
      }
    }, 5000); // Кожні 5 секунд

    return () => {
      clearInterval(interval);
      setIsConnected(false);
    };
  };

  const generateMockUpdate = (): RealTimeUpdate => {
    const updateTypes = [
      {
        type: 'download_completed' as const,
        title: 'Завантаження завершено',
        message: 'Модель "Awesome Dragon" успішно завантажена на R2',
        priority: 'medium' as const
      },
      {
        type: 'download_started' as const,
        title: 'Початок завантаження',
        message: 'Розпочато завантаження моделі "Cool Gadget" з Printables',
        priority: 'low' as const
      },
      {
        type: 'scraping_completed' as const,
        title: 'Скрапінг завершено',
        message: 'Знайдено 25 нових моделей на MakerWorld',
        priority: 'medium' as const
      },
      {
        type: 'download_failed' as const,
        title: 'Помилка завантаження',
        message: 'Не вдалося завантажити модель "Failed Model" - перевищено час очікування',
        priority: 'high' as const
      },
      {
        type: 'system_alert' as const,
        title: 'Системне сповіщення',
        message: 'Досягнуто ліміт одночасних завантажень (5/5)',
        priority: 'medium' as const
      }
    ];

    const randomUpdate = updateTypes[Math.floor(Math.random() * updateTypes.length)];
    
    return {
      id: crypto.randomUUID(),
      ...randomUpdate,
      timestamp: new Date().toISOString(),
      read: false,
      data: {
        platform: ['printables', 'makerworld', 'thangs'][Math.floor(Math.random() * 3)],
        modelId: `model_${Math.random().toString(36).substr(2, 9)}`
      }
    };
  };

  const addUpdate = (update: RealTimeUpdate) => {
    setUpdates(prev => [update, ...prev.slice(0, 49)]); // Зберігаємо останні 50 оновлень
    setUnreadCount(prev => prev + 1);
    
    if (onUpdateReceived) {
      onUpdateReceived(update);
    }

    // Показуємо toast notification для важливих оновлень
    if (update.priority === 'high') {
      showToastNotification(update);
    }
  };

  const showToastNotification = (update: RealTimeUpdate) => {
    // В реальному додатку тут буде toast notification
    console.log('Toast notification:', update);
  };

  const markAsRead = (updateId: string) => {
    setUpdates(prev => 
      prev.map(update => 
        update.id === updateId ? { ...update, read: true } : update
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setUpdates(prev => prev.map(update => ({ ...update, read: true })));
    setUnreadCount(0);
  };

  const removeUpdate = (updateId: string) => {
    setUpdates(prev => prev.filter(update => update.id !== updateId));
    const update = updates.find(u => u.id === updateId);
    if (update && !update.read) {
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
  };

  const getUpdateIcon = (type: string) => {
    switch (type) {
      case 'download_completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'download_failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'download_started':
        return <Download className="h-4 w-4 text-blue-500" />;
      case 'scraping_completed':
        return <Zap className="h-4 w-4 text-purple-500" />;
      case 'scraping_started':
        return <RefreshCw className="h-4 w-4 text-blue-500" />;
      case 'system_alert':
        return <Activity className="h-4 w-4 text-orange-500" />;
      default:
        return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = Date.now();
    const time = new Date(timestamp).getTime();
    const diff = now - time;
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (hours > 0) return `${hours}г тому`;
    if (minutes > 0) return `${minutes}хв тому`;
    return 'щойно';
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Кнопка сповіщень */}
      <div className="relative">
        <Button
          onClick={() => setIsVisible(!isVisible)}
          className="rounded-full w-12 h-12 shadow-lg"
          variant={unreadCount > 0 ? "default" : "outline"}
        >
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs min-w-[20px] h-5 flex items-center justify-center rounded-full">
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>

        {/* Індикатор підключення */}
        <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
      </div>

      {/* Панель оновлень */}
      {isVisible && (
        <Card className="absolute bottom-16 right-0 w-96 max-h-96 shadow-xl">
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center space-x-2">
              <Bell className="h-4 w-4" />
              <span className="font-medium">Real-time оновлення</span>
              <Badge variant="outline" className={isConnected ? 'text-green-600' : 'text-red-600'}>
                {isConnected ? 'Підключено' : 'Відключено'}
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              {unreadCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={markAllAsRead}
                  className="text-xs"
                >
                  Прочитати всі
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsVisible(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <CardContent className="p-0 max-h-80 overflow-y-auto">
            {updates.length === 0 ? (
              <div className="p-4 text-center text-muted-foreground">
                <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>Немає оновлень</p>
              </div>
            ) : (
              <div className="space-y-1">
                {updates.map((update) => (
                  <div
                    key={update.id}
                    className={`p-3 border-b last:border-b-0 hover:bg-gray-50 transition-colors ${
                      !update.read ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3 flex-1">
                        {getUpdateIcon(update.type)}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="text-sm font-medium truncate">
                              {update.title}
                            </h4>
                            <Badge className={getPriorityColor(update.priority)} variant="outline">
                              {update.priority}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground line-clamp-2">
                            {update.message}
                          </p>
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-muted-foreground">
                              {formatTimeAgo(update.timestamp)}
                            </span>
                            {update.data?.platform && (
                              <Badge variant="secondary" className="text-xs">
                                {update.data.platform}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1 ml-2">
                        {!update.read && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => markAsRead(update.id)}
                            className="h-6 w-6 p-0"
                          >
                            <CheckCircle className="h-3 w-3" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeUpdate(update.id)}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
