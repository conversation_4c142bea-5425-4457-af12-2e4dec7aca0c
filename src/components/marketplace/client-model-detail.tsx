'use client';

declare global {
  interface Window {
    open: (url?: string | URL | undefined, target?: string | undefined, features?: string | undefined) => Window | null;
  }
}

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { SplineScene } from '@/components/ui/splite';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useCart } from '@/context/cart-context';
import { useToast } from '@/hooks/use-toast';
import { Download } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

// Export the client component
const ClientModelDetail = ({ model }: { model: any }) => {
  const { addToCart, isInCart } = useCart();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');
  const [activeImage, setActiveImage] = useState(0);

  // Function to add model to cart
  const handleAddToCart = () => {
    addToCart({
      id: model.id,
      title: model.title,
      price: model.price,
      thumbnail: model.thumbnail,
    });

    toast({
      title: "Added to Cart",
      description: `${model.title} has been added to your cart.`,
      duration: 3000,
    });
  };

  // Function to download free model
  const handleDownload = () => {
    // URL to the Printables model
    if (model.downloadUrl) {
      if (typeof window !== 'undefined') {
        window.open(model.downloadUrl, '_blank');
      }
      toast({
        title: "Download Initiated",
        description: "Your download should begin shortly. If it doesn't, please click the link again.",
        duration: 5000,
        type: "success"
      });
    } else {
      toast({
        title: "Download Not Available",
        description: "No download URL is provided for this model.",
        duration: 3000,
        type: "error"
      });
    }
  };

  return (
    <main className="bg-background min-h-screen">
      <div className="container mx-auto px-4 py-8">
        {/* Navigation breadcrumbs */}
        <div className="mb-6 text-sm text-muted-foreground">
          <Link href="/marketplace" className="hover:text-primary">Marketplace</Link>
          <span className="mx-2">/</span>
          <Link href={`/marketplace/categories/${model.category}`} className="hover:text-primary">{model.category}</Link>
          <span className="mx-2">/</span>
          <span className="text-foreground">{model.title}</span>
        </div>

        {/* Main model information */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Image gallery */}
          <div className="lg:col-span-2 space-y-4">
            <div className="relative aspect-video bg-muted rounded-lg overflow-hidden">
              {/* Tabs for switching between image and 3D model */}
              <Tabs defaultValue="image" className="w-full h-full">
                <div className="absolute top-2 right-2 z-10 bg-background/80 backdrop-blur-sm rounded-lg">
                  <TabsList>
                    <TabsTrigger value="image">Image</TabsTrigger>
                    <TabsTrigger value="3d">3D View</TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="image" className="m-0 h-full">
                  <img
                    src={model.images[activeImage] || model.thumbnail}
                    alt={model.title}
                    className="w-full h-full object-cover"
                  />
                </TabsContent>

                <TabsContent value="3d" className="m-0 h-full">
                  {/* Using SplineScene for 3D model display */}
                  <div className="h-full">
                    <SplineScene
                      scene="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
                      preset="PRODUCT_VIEWER"
                      className="w-full h-full"
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </div>
            <div className="flex space-x-2 overflow-x-auto pb-2">
              {model.images && model.images.map((image: string, index: number) => (
                <button
                  key={index}
                  className={`relative w-20 h-20 rounded-md overflow-hidden ${
                    activeImage === index ? 'ring-2 ring-primary' : 'opacity-70'
                  }`}
                  onClick={() => setActiveImage(index)}
                >
                  <img
                    src={image}
                    alt={`${model.title} preview ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>

            {/* Information tabs */}
            <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="mt-8">
              <TabsList className="grid grid-cols-4 mb-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="print-settings">Print Settings</TabsTrigger>
                <TabsTrigger value="reviews">Reviews ({model.reviewCount || 0})</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div>
                  <h3 className="text-xl font-semibold mb-2">Description</h3>
                  <p className="text-muted-foreground">
                    {model.description || "Dummy 1:3 Terminator T800 Armor - A highly detailed 3D printable model of the iconic T-800 Terminator endoskeleton armor. Perfect for collectors and fans of the Terminator franchise. This model features intricate details and is designed for easy printing and assembly."}
                  </p>
                  {model.price === 0 && (
                    <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                      <h4 className="font-medium text-green-800 mb-1">Free Download from Printables</h4>
                      <p className="text-sm text-green-700">
                        This model is available for free download from Printables. Click the "Download Now" button to access all files.
                      </p>
                    </div>
                  )}
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {model.tags && model.tags.map((tag: string) => (
                      <Badge key={tag} variant="secondary">{tag}</Badge>
                    ))}
                    {(!model.tags || model.tags.length === 0) && (
                      <>
                        <Badge variant="secondary">terminator</Badge>
                        <Badge variant="secondary">t800</Badge>
                        <Badge variant="secondary">armor</Badge>
                        <Badge variant="secondary">sci-fi</Badge>
                        <Badge variant="secondary">movie</Badge>
                        <Badge variant="secondary">figurine</Badge>
                      </>
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="details" className="space-y-4">
                {/* Details content */}
                {/* ... (rest of the details tab content) */}
              </TabsContent>

              <TabsContent value="print-settings" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-xl font-semibold mb-2">Recommended Print Settings</h3>
                      <div className="bg-muted p-4 rounded-md space-y-3">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Layer Height:</span>
                          <span className="font-medium">0.2mm</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Infill:</span>
                          <span className="font-medium">15-20%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Supports:</span>
                          <span className="font-medium">Yes (Tree supports recommended)</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Rafts:</span>
                          <span className="font-medium">No</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Material:</span>
                          <span className="font-medium">PLA, PETG</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Nozzle Temperature:</span>
                          <span className="font-medium">200-215°C (PLA), 230-245°C (PETG)</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Bed Temperature:</span>
                          <span className="font-medium">60°C (PLA), 70-80°C (PETG)</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h3 className="text-xl font-semibold mb-2">Assembly Instructions</h3>
                      <p className="text-muted-foreground mb-4">
                        This model consists of multiple parts that need to be assembled after printing.
                      </p>
                      <ol className="list-decimal pl-5 space-y-2 text-muted-foreground">
                        <li>Print all parts with the recommended settings</li>
                        <li>Clean up any support material</li>
                        <li>Sand the parts if necessary for a smoother finish</li>
                        <li>Assemble the parts using super glue or epoxy</li>
                        <li>Paint the model if desired (metallic silver recommended)</li>
                      </ol>
                    </div>

                    <div className="mt-6">
                      <h3 className="text-xl font-semibold mb-2">Printing Time</h3>
                      <p className="text-muted-foreground">
                        Estimated printing time: 15-20 hours (depending on printer speed and settings)
                      </p>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="reviews" className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold">Customer Reviews</h3>
                  <div className="flex items-center">
                    <div className="flex items-center mr-2">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <svg
                          key={star}
                          className={`w-5 h-5 ${star <= (model.averageRating || 4.5) ? 'text-yellow-400' : 'text-gray-300'}`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                      ))}
                    </div>
                    <span className="text-lg font-medium">{model.averageRating || '4.5'} out of 5</span>
                  </div>
                </div>

                <div className="border-t border-b py-4">
                  <div className="flex justify-between mb-1">
                    <span>5 stars</span>
                    <div className="w-2/3 bg-gray-200 rounded-full h-2.5">
                      <div className="bg-yellow-400 h-2.5 rounded-full" style={{ width: `${model.ratingDistribution?.fiveStar || 70}%` }}></div>
                    </div>
                    <span>{model.ratingDistribution?.fiveStar || 70}%</span>
                  </div>
                  <div className="flex justify-between mb-1">
                    <span>4 stars</span>
                    <div className="w-2/3 bg-gray-200 rounded-full h-2.5">
                      <div className="bg-yellow-400 h-2.5 rounded-full" style={{ width: `${model.ratingDistribution?.fourStar || 20}%` }}></div>
                    </div>
                    <span>{model.ratingDistribution?.fourStar || 20}%</span>
                  </div>
                  <div className="flex justify-between mb-1">
                    <span>3 stars</span>
                    <div className="w-2/3 bg-gray-200 rounded-full h-2.5">
                      <div className="bg-yellow-400 h-2.5 rounded-full" style={{ width: `${model.ratingDistribution?.threeStar || 5}%` }}></div>
                    </div>
                    <span>{model.ratingDistribution?.threeStar || 5}%</span>
                  </div>
                  <div className="flex justify-between mb-1">
                    <span>2 stars</span>
                    <div className="w-2/3 bg-gray-200 rounded-full h-2.5">
                      <div className="bg-yellow-400 h-2.5 rounded-full" style={{ width: `${model.ratingDistribution?.twoStar || 3}%` }}></div>
                    </div>
                    <span>{model.ratingDistribution?.twoStar || 3}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>1 star</span>
                    <div className="w-2/3 bg-gray-200 rounded-full h-2.5">
                      <div className="bg-yellow-400 h-2.5 rounded-full" style={{ width: `${model.ratingDistribution?.oneStar || 2}%` }}></div>
                    </div>
                    <span>{model.ratingDistribution?.oneStar || 2}%</span>
                  </div>
                </div>

                {/* Individual reviews */}
                <div className="space-y-6">
                  <div className="border-b pb-6">
                    <div className="flex items-center mb-2">
                      <div className="flex items-center mr-2">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <svg
                            key={star}
                            className={`w-4 h-4 ${star <= (model.reviews?.[0]?.rating || 5) ? 'text-yellow-400' : 'text-gray-300'}`}
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                          </svg>
                        ))}
                      </div>
                      <h4 className="font-medium">{model.reviews?.[0]?.title || 'Amazing detail!'}</h4>
                    </div>
                    <p className="text-muted-foreground mb-2">
                      {model.reviews?.[0]?.comment || 'The level of detail on this T800 model is incredible. Printed perfectly on my Prusa i3 MK3S with the recommended settings. Assembly was straightforward and the final result looks fantastic on my shelf.'}
                    </p>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <span className="font-medium">{model.reviews?.[0]?.author || 'John D.'}</span>
                      <span className="mx-2">•</span>
                      <span>{model.reviews?.[0]?.date || '2 weeks ago'}</span>
                    </div>
                  </div>

                  <div className="border-b pb-6">
                    <div className="flex items-center mb-2">
                      <div className="flex items-center mr-2">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <svg
                            key={star}
                            className={`w-4 h-4 ${star <= (model.reviews?.[1]?.rating || 4) ? 'text-yellow-400' : 'text-gray-300'}`}
                            fill="currentColor"
                            viewBox="0 0 20 20"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                          </svg>
                        ))}
                      </div>
                      <h4 className="font-medium">{model.reviews?.[1]?.title || 'Great model, some assembly challenges'}</h4>
                    </div>
                    <p className="text-muted-foreground mb-2">
                      {model.reviews?.[1]?.comment || 'The model itself is excellent and very detailed. I had some difficulty with the assembly of smaller parts, but overall it\'s a great representation of the T800. Printed in silver PLA and it looks just like the movie version.'}
                    </p>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <span className="font-medium">{model.reviews?.[1]?.author || 'Sarah K.'}</span>
                      <span className="mx-2">•</span>
                      <span>{model.reviews?.[1]?.date || '1 month ago'}</span>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Sidebar with information and action buttons */}
          <div className="space-y-6">
            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h1 className="text-2xl font-bold mb-2">{model.title}</h1>
              <div className="flex items-center mb-4">
                <Avatar className="h-6 w-6 mr-2">
                  <AvatarImage src={model.designerAvatar} alt={model.designer} />
                  <AvatarFallback>{model.designer.substring(0, 2)}</AvatarFallback>
                </Avatar>
                <Link href={`/marketplace/designer/${model.designer}`} className="text-sm text-muted-foreground hover:text-primary">
                  by {model.designer}
                </Link>
              </div>

              <div className="mb-6">
                <div className="text-3xl font-bold mb-2">
                  {model.price === 0 ? 'Free' : `$${model.price.toFixed(2)}`}
                </div>
                <div className="text-sm text-muted-foreground">
                  License: {model.license || 'Creative Commons'}
                </div>
              </div>

              <div className="space-y-3">
                {model.price === 0 ? (
                  <Button
                    className="w-full flex items-center justify-center gap-2"
                    size="lg"
                    onClick={handleDownload}
                  >
                    <Download className="h-5 w-5" />
                    Завантажити зараз
                  </Button>
                ) : (
                  <>
                    <Button className="w-full" size="lg">
                      Buy Now
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full"
                      size="lg"
                      onClick={handleAddToCart}
                      disabled={isInCart(model.id)}
                    >
                      {isInCart(model.id) ? 'Added to Cart' : 'Add to Cart'}
                    </Button>
                  </>
                )}
                <Button variant="ghost" className="w-full" size="lg">
                  Add to Favorites
                </Button>
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold mb-4">Print on Demand</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Don't have a 3D printer? Order a print of this model directly from our partners.
              </p>
              <Button variant="outline" className="w-full">
                Order Print
              </Button>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default ClientModelDetail;
