'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Database, 
  Zap, 
  Globe, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Activity,
  Clock,
  TrendingUp,
  Download,
  Eye,
  Wifi,
  WifiOff
} from 'lucide-react';

interface BrightDataStatus {
  isConnected: boolean;
  tokenStatus: 'valid' | 'expired' | 'invalid';
  lastActivity: string;
  requestsCount: number;
  successRate: number;
  remainingCredits: number;
  rateLimitRemaining: number;
}

interface PlatformStatus {
  platform: string;
  status: 'operational' | 'degraded' | 'down';
  responseTime: number;
  lastCheck: string;
  modelsScraped: number;
}

interface BrightDataIntegrationProps {
  className?: string;
  showDetails?: boolean;
  autoRefresh?: boolean;
}

const BrightDataIntegration: React.FC<BrightDataIntegrationProps> = ({
  className = "",
  showDetails = true,
  autoRefresh = true
}) => {
  const [status, setStatus] = useState<BrightDataStatus | null>(null);
  const [platforms, setPlatforms] = useState<PlatformStatus[]>([]);
  const [loading, setLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchStatus = async () => {
    setLoading(true);
    try {
      // Симуляція отримання статусу Bright Data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockStatus: BrightDataStatus = {
        isConnected: true,
        tokenStatus: 'expired', // Відповідає реальному стану
        lastActivity: new Date().toISOString(),
        requestsCount: Math.floor(Math.random() * 100) + 50,
        successRate: 0.85 + Math.random() * 0.15,
        remainingCredits: Math.floor(Math.random() * 5000) + 1000,
        rateLimitRemaining: Math.floor(Math.random() * 100) + 50
      };

      const mockPlatforms: PlatformStatus[] = [
        {
          platform: 'Printables',
          status: 'operational',
          responseTime: 800 + Math.random() * 400,
          lastCheck: new Date().toISOString(),
          modelsScraped: Math.floor(Math.random() * 1000) + 500
        },
        {
          platform: 'MakerWorld',
          status: 'operational',
          responseTime: 1200 + Math.random() * 600,
          lastCheck: new Date().toISOString(),
          modelsScraped: Math.floor(Math.random() * 800) + 300
        },
        {
          platform: 'Thangs',
          status: 'degraded',
          responseTime: 2000 + Math.random() * 1000,
          lastCheck: new Date().toISOString(),
          modelsScraped: Math.floor(Math.random() * 600) + 200
        },
        {
          platform: 'Thingiverse',
          status: 'operational',
          responseTime: 1500 + Math.random() * 500,
          lastCheck: new Date().toISOString(),
          modelsScraped: Math.floor(Math.random() * 1200) + 800
        }
      ];

      setStatus(mockStatus);
      setPlatforms(mockPlatforms);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching Bright Data status:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchStatus, 30000); // Оновлення кожні 30 секунд
    return () => clearInterval(interval);
  }, [autoRefresh]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational': return 'text-green-500';
      case 'degraded': return 'text-yellow-500';
      case 'down': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'operational': return CheckCircle;
      case 'degraded': return AlertTriangle;
      case 'down': return XCircle;
      default: return Activity;
    }
  };

  const getTokenStatusBadge = (tokenStatus: string) => {
    switch (tokenStatus) {
      case 'valid':
        return <Badge className="bg-green-500 text-white">Активний</Badge>;
      case 'expired':
        return <Badge className="bg-yellow-500 text-white">Застарілий</Badge>;
      case 'invalid':
        return <Badge className="bg-red-500 text-white">Недійсний</Badge>;
      default:
        return <Badge variant="outline">Невідомо</Badge>;
    }
  };

  if (!status) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="w-6 h-6 animate-spin mr-2" />
            <span>Завантаження статусу Bright Data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Status Card */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Database className="w-5 h-5" />
              Bright Data Integration
            </CardTitle>
            <div className="flex items-center gap-2">
              {status.isConnected ? (
                <Wifi className="w-4 h-4 text-green-500" />
              ) : (
                <WifiOff className="w-4 h-4 text-red-500" />
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={fetchStatus}
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Connection Status */}
          <div className="flex items-center justify-between">
            <span className="font-medium">Статус з'єднання:</span>
            <div className="flex items-center gap-2">
              {status.isConnected ? (
                <>
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-green-600">Підключено</span>
                </>
              ) : (
                <>
                  <XCircle className="w-4 h-4 text-red-500" />
                  <span className="text-red-600">Відключено</span>
                </>
              )}
            </div>
          </div>

          {/* Token Status */}
          <div className="flex items-center justify-between">
            <span className="font-medium">Статус токену:</span>
            {getTokenStatusBadge(status.tokenStatus)}
          </div>

          {/* Token Warning */}
          {status.tokenStatus === 'expired' && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Токен Bright Data застарів. Використовується симуляція даних. 
                Для отримання реальних даних оновіть токен у налаштуваннях.
              </AlertDescription>
            </Alert>
          )}

          {showDetails && (
            <>
              {/* Statistics */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {status.requestsCount}
                  </div>
                  <div className="text-xs text-muted-foreground">Запитів</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {(status.successRate * 100).toFixed(1)}%
                  </div>
                  <div className="text-xs text-muted-foreground">Успішність</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {status.remainingCredits.toLocaleString()}
                  </div>
                  <div className="text-xs text-muted-foreground">Кредитів</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {status.rateLimitRemaining}
                  </div>
                  <div className="text-xs text-muted-foreground">Ліміт</div>
                </div>
              </div>

              {/* Success Rate Progress */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Успішність запитів</span>
                  <span className="text-sm text-muted-foreground">
                    {(status.successRate * 100).toFixed(1)}%
                  </span>
                </div>
                <Progress value={status.successRate * 100} className="h-2" />
              </div>
            </>
          )}

          {/* Last Updated */}
          {lastUpdated && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground pt-2 border-t">
              <Clock className="w-3 h-3" />
              Оновлено: {lastUpdated.toLocaleTimeString()}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Platform Status */}
      {showDetails && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="w-5 h-5" />
              Статус платформ
            </CardTitle>
          </CardHeader>

          <CardContent>
            <div className="space-y-3">
              {platforms.map((platform) => {
                const StatusIcon = getStatusIcon(platform.status);
                return (
                  <motion.div
                    key={platform.platform}
                    className="flex items-center justify-between p-3 rounded-lg border"
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="flex items-center gap-3">
                      <StatusIcon className={`w-4 h-4 ${getStatusColor(platform.status)}`} />
                      <div>
                        <div className="font-medium">{platform.platform}</div>
                        <div className="text-xs text-muted-foreground">
                          {platform.modelsScraped.toLocaleString()} моделей
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="text-sm font-medium capitalize">
                        {platform.status === 'operational' && 'Працює'}
                        {platform.status === 'degraded' && 'Повільно'}
                        {platform.status === 'down' && 'Недоступно'}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {Math.round(platform.responseTime)}ms
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Трендові моделі
            </Button>
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              Популярні моделі
            </Button>
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Нові моделі
            </Button>
            <Button variant="outline" size="sm" className="flex items-center gap-2">
              <Zap className="w-4 h-4" />
              Оновити дані
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BrightDataIntegration;
