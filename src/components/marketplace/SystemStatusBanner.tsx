'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Activity, 
  Download, 
  Zap, 
  Cloud, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle,
  Clock,
  TrendingUp,
  Database
} from 'lucide-react';

interface SystemStatus {
  brightDataMCP: {
    status: 'active' | 'inactive' | 'error';
    lastUpdate: string;
    requestsToday: number;
  };
  modelDownloads: {
    status: 'active' | 'inactive' | 'error';
    activeJobs: number;
    completedToday: number;
    queueSize: number;
  };
  cloudflareServices: {
    r2: boolean;
    d1: boolean;
    kv: boolean;
    analytics: boolean;
  };
  observability: {
    status: 'active' | 'inactive' | 'error';
    metricsCount: number;
    logsCount: number;
  };
  lastScraping: {
    timestamp: string;
    platform: string;
    modelsFound: number;
    success: boolean;
  };
}

export default function SystemStatusBanner() {
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    fetchSystemStatus();
    const interval = setInterval(fetchSystemStatus, 30000); // Оновлення кожні 30 секунд
    return () => clearInterval(interval);
  }, []);

  const fetchSystemStatus = async () => {
    try {
      // Симуляція отримання статусу системи
      // В реальному додатку це буде API виклик
      const mockStatus: SystemStatus = {
        brightDataMCP: {
          status: 'active',
          lastUpdate: new Date().toISOString(),
          requestsToday: 247
        },
        modelDownloads: {
          status: 'active',
          activeJobs: 3,
          completedToday: 156,
          queueSize: 12
        },
        cloudflareServices: {
          r2: true,
          d1: true,
          kv: true,
          analytics: true
        },
        observability: {
          status: 'active',
          metricsCount: 1247,
          logsCount: 89
        },
        lastScraping: {
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15 хвилин тому
          platform: 'Printables',
          modelsFound: 25,
          success: true
        }
      };

      setStatus(mockStatus);
    } catch (error) {
      console.error('Помилка отримання статусу системи:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = Date.now();
    const time = new Date(timestamp).getTime();
    const diff = now - time;
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days > 0) return `${days}д тому`;
    if (hours > 0) return `${hours}г тому`;
    if (minutes > 0) return `${minutes}хв тому`;
    return 'щойно';
  };

  if (loading) {
    return (
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex items-center space-x-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span className="text-sm">Завантаження статусу системи...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!status) {
    return null;
  }

  const allServicesHealthy = 
    status.brightDataMCP.status === 'active' &&
    status.modelDownloads.status === 'active' &&
    status.observability.status === 'active' &&
    Object.values(status.cloudflareServices).every(Boolean);

  return (
    <Card className={`mb-6 border-l-4 ${allServicesHealthy ? 'border-l-green-500' : 'border-l-yellow-500'}`}>
      <CardContent className="p-4">
        {/* Компактний вигляд */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-blue-500" />
              <span className="font-medium">Система активна</span>
              <Badge className={allServicesHealthy ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                {allServicesHealthy ? 'Всі сервіси працюють' : 'Потребує уваги'}
              </Badge>
            </div>
            
            <div className="hidden md:flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Zap className="h-3 w-3" />
                <span>{status.brightDataMCP.requestsToday} запитів</span>
              </div>
              <div className="flex items-center space-x-1">
                <Download className="h-3 w-3" />
                <span>{status.modelDownloads.completedToday} завантажень</span>
              </div>
              <div className="flex items-center space-x-1">
                <TrendingUp className="h-3 w-3" />
                <span>Останній скрапінг: {formatTimeAgo(status.lastScraping.timestamp)}</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? 'Згорнути' : 'Детальніше'}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={fetchSystemStatus}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Розгорнутий вигляд */}
        {expanded && (
          <div className="mt-4 pt-4 border-t space-y-4">
            {/* Bright Data MCP */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(status.brightDataMCP.status)}
                  <span className="font-medium">Bright Data MCP</span>
                  <Badge className={getStatusColor(status.brightDataMCP.status)}>
                    {status.brightDataMCP.status}
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground space-y-1">
                  <div>Запитів сьогодні: {status.brightDataMCP.requestsToday}</div>
                  <div>Останнє оновлення: {formatTimeAgo(status.brightDataMCP.lastUpdate)}</div>
                </div>
              </div>

              {/* Model Downloads */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(status.modelDownloads.status)}
                  <span className="font-medium">Завантаження моделей</span>
                  <Badge className={getStatusColor(status.modelDownloads.status)}>
                    {status.modelDownloads.status}
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground space-y-1">
                  <div>Активних завдань: {status.modelDownloads.activeJobs}</div>
                  <div>Завершено сьогодні: {status.modelDownloads.completedToday}</div>
                  <div>В черзі: {status.modelDownloads.queueSize}</div>
                </div>
              </div>

              {/* Cloudflare Services */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Cloud className="h-4 w-4 text-orange-500" />
                  <span className="font-medium">Cloudflare</span>
                </div>
                <div className="text-sm space-y-1">
                  <div className="flex items-center justify-between">
                    <span>R2 Storage:</span>
                    <span className={status.cloudflareServices.r2 ? 'text-green-600' : 'text-red-600'}>
                      {status.cloudflareServices.r2 ? '✅' : '❌'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>D1 Database:</span>
                    <span className={status.cloudflareServices.d1 ? 'text-green-600' : 'text-red-600'}>
                      {status.cloudflareServices.d1 ? '✅' : '❌'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>KV Storage:</span>
                    <span className={status.cloudflareServices.kv ? 'text-green-600' : 'text-red-600'}>
                      {status.cloudflareServices.kv ? '✅' : '❌'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Analytics:</span>
                    <span className={status.cloudflareServices.analytics ? 'text-green-600' : 'text-red-600'}>
                      {status.cloudflareServices.analytics ? '✅' : '❌'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Observability */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(status.observability.status)}
                  <span className="font-medium">Моніторинг</span>
                  <Badge className={getStatusColor(status.observability.status)}>
                    {status.observability.status}
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground space-y-1">
                  <div>Метрик: {status.observability.metricsCount}</div>
                  <div>Логів: {status.observability.logsCount}</div>
                </div>
              </div>
            </div>

            {/* Останній скрапінг */}
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Database className="h-4 w-4 text-blue-500" />
                  <span className="font-medium">Останній скрапінг</span>
                  <Badge className={status.lastScraping.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                    {status.lastScraping.success ? 'Успішно' : 'Помилка'}
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground">
                  {formatTimeAgo(status.lastScraping.timestamp)}
                </div>
              </div>
              <div className="mt-2 text-sm text-muted-foreground">
                Платформа: {status.lastScraping.platform} • 
                Знайдено моделей: {status.lastScraping.modelsFound}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
