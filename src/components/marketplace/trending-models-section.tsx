'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useTrendingModels } from '@/hooks/useBrightDataModels';
import { ModelSource } from '@/types/models';
import { 
  TrendingUp, 
  Download, 
  Heart, 
  Eye, 
  Star, 
  RefreshCw,
  ExternalLink,
  Filter,
  Grid3X3,
  List
} from 'lucide-react';

interface TrendingModelsSectionProps {
  title?: string;
  subtitle?: string;
  platform?: ModelSource;
  limit?: number;
  category?: string;
  showPlatformFilter?: boolean;
  showViewToggle?: boolean;
  className?: string;
}

const TrendingModelsSection: React.FC<TrendingModelsSectionProps> = ({
  title = "🔥 Трендові моделі",
  subtitle = "Найпопулярніші моделі цього тижня",
  platform = 'printables',
  limit = 12,
  category,
  showPlatformFilter = true,
  showViewToggle = true,
  className = ""
}) => {
  const [selectedPlatform, setSelectedPlatform] = useState<ModelSource>(platform);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const { models, loading, error, refresh } = useTrendingModels({
    platform: selectedPlatform,
    limit,
    category,
    autoRefresh: true,
    refreshInterval: 300000 // 5 minutes
  });

  const platforms: { value: ModelSource; label: string; color: string }[] = [
    { value: 'printables', label: 'Printables', color: 'bg-orange-500' },
    { value: 'makerworld', label: 'MakerWorld', color: 'bg-blue-500' },
    { value: 'thangs', label: 'Thangs', color: 'bg-purple-500' },
    { value: 'thingiverse', label: 'Thingiverse', color: 'bg-green-500' },
    { value: 'myminifactory', label: 'MyMiniFactory', color: 'bg-red-500' }
  ];

  const handlePlatformChange = (newPlatform: ModelSource) => {
    setSelectedPlatform(newPlatform);
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getTrendingBadgeColor = (score: number): string => {
    if (score >= 90) return 'bg-red-500 text-white';
    if (score >= 80) return 'bg-orange-500 text-white';
    if (score >= 70) return 'bg-yellow-500 text-black';
    return 'bg-gray-500 text-white';
  };

  const ModelCard = ({ model, index }: { model: any; index: number }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="group"
    >
      <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800">
        <div className="relative">
          {/* Thumbnail */}
          <div className="aspect-[4/3] overflow-hidden">
            <img
              src={model.thumbnail}
              alt={model.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              loading="lazy"
            />
            
            {/* Trending Badge */}
            <div className="absolute top-2 left-2">
              <Badge className={`${getTrendingBadgeColor(model.stats.trending_score)} flex items-center gap-1`}>
                <TrendingUp className="w-3 h-3" />
                {Math.round(model.stats.trending_score)}
              </Badge>
            </div>

            {/* Platform Badge */}
            <div className="absolute top-2 right-2">
              <Badge variant="secondary" className="bg-black/70 text-white">
                {model.platform.toUpperCase()}
              </Badge>
            </div>

            {/* Price Badge */}
            {model.price && (
              <div className="absolute bottom-2 right-2">
                <Badge className="bg-green-500 text-white">
                  ${model.price}
                </Badge>
              </div>
            )}
            {model.isFree && (
              <div className="absolute bottom-2 right-2">
                <Badge className="bg-blue-500 text-white">
                  FREE
                </Badge>
              </div>
            )}
          </div>

          <CardContent className="p-4">
            {/* Title */}
            <h3 className="font-semibold text-lg mb-2 line-clamp-2 group-hover:text-primary transition-colors">
              {model.title}
            </h3>

            {/* Designer */}
            <div className="flex items-center gap-2 mb-3">
              <img
                src={model.designer.avatar}
                alt={model.designer.name}
                className="w-6 h-6 rounded-full"
              />
              <span className="text-sm text-muted-foreground">
                {model.designer.name}
              </span>
            </div>

            {/* Stats */}
            <div className="flex items-center justify-between text-sm text-muted-foreground mb-3">
              <div className="flex items-center gap-1">
                <Eye className="w-4 h-4" />
                {formatNumber(model.stats.views)}
              </div>
              <div className="flex items-center gap-1">
                <Download className="w-4 h-4" />
                {formatNumber(model.stats.downloads)}
              </div>
              <div className="flex items-center gap-1">
                <Heart className="w-4 h-4" />
                {formatNumber(model.stats.likes)}
              </div>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-1 mb-3">
              {model.tags.slice(0, 3).map((tag: string) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>

            {/* Actions */}
            <div className="flex gap-2">
              <Button 
                size="sm" 
                className="flex-1"
                onClick={() => window.open(model.originalUrl, '_blank')}
              >
                <ExternalLink className="w-4 h-4 mr-1" />
                Переглянути
              </Button>
            </div>
          </CardContent>
        </div>
      </Card>
    </motion.div>
  );

  const ModelListItem = ({ model, index }: { model: any; index: number }) => (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: index * 0.05 }}
    >
      <Card className="p-4 hover:shadow-md transition-all duration-300">
        <div className="flex gap-4">
          {/* Thumbnail */}
          <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
            <img
              src={model.thumbnail}
              alt={model.title}
              className="w-full h-full object-cover"
              loading="lazy"
            />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-lg mb-1 truncate">
                  {model.title}
                </h3>
                <p className="text-sm text-muted-foreground mb-2">
                  by {model.designer.name}
                </p>
              </div>
              
              <div className="flex items-center gap-2 ml-4">
                <Badge className={getTrendingBadgeColor(model.stats.trending_score)}>
                  <TrendingUp className="w-3 h-3 mr-1" />
                  {Math.round(model.stats.trending_score)}
                </Badge>
                {model.isFree ? (
                  <Badge className="bg-blue-500 text-white">FREE</Badge>
                ) : (
                  <Badge className="bg-green-500 text-white">${model.price}</Badge>
                )}
              </div>
            </div>

            {/* Stats */}
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Eye className="w-4 h-4" />
                {formatNumber(model.stats.views)}
              </div>
              <div className="flex items-center gap-1">
                <Download className="w-4 h-4" />
                {formatNumber(model.stats.downloads)}
              </div>
              <div className="flex items-center gap-1">
                <Heart className="w-4 h-4" />
                {formatNumber(model.stats.likes)}
              </div>
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  );

  return (
    <section className={`py-16 ${className}`}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <motion.h2 
            className="text-4xl font-bold mb-4 bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            {title}
          </motion.h2>
          <motion.p 
            className="text-xl text-muted-foreground max-w-2xl mx-auto"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            {subtitle}
          </motion.p>
        </div>

        {/* Controls */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mb-8">
          {/* Platform Filter */}
          {showPlatformFilter && (
            <div className="flex flex-wrap gap-2">
              {platforms.map((p) => (
                <Button
                  key={p.value}
                  variant={selectedPlatform === p.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePlatformChange(p.value)}
                  className="flex items-center gap-2"
                >
                  <div className={`w-3 h-3 rounded-full ${p.color}`} />
                  {p.label}
                </Button>
              ))}
            </div>
          )}

          {/* View Controls */}
          <div className="flex items-center gap-2">
            {showViewToggle && (
              <div className="flex border rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            )}
            
            <Button
              variant="outline"
              size="sm"
              onClick={refresh}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {/* Content */}
        <AnimatePresence mode="wait">
          {loading ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {Array.from({ length: limit }).map((_, i) => (
                    <Card key={i} className="overflow-hidden">
                      <Skeleton className="aspect-[4/3] w-full" />
                      <CardContent className="p-4">
                        <Skeleton className="h-6 w-3/4 mb-2" />
                        <Skeleton className="h-4 w-1/2 mb-3" />
                        <div className="flex gap-2 mb-3">
                          <Skeleton className="h-6 w-12" />
                          <Skeleton className="h-6 w-12" />
                          <Skeleton className="h-6 w-12" />
                        </div>
                        <Skeleton className="h-8 w-full" />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {Array.from({ length: limit }).map((_, i) => (
                    <Card key={i} className="p-4">
                      <div className="flex gap-4">
                        <Skeleton className="w-20 h-20 rounded-lg" />
                        <div className="flex-1">
                          <Skeleton className="h-6 w-3/4 mb-2" />
                          <Skeleton className="h-4 w-1/2 mb-2" />
                          <div className="flex gap-4">
                            <Skeleton className="h-4 w-16" />
                            <Skeleton className="h-4 w-16" />
                            <Skeleton className="h-4 w-16" />
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </motion.div>
          ) : error ? (
            <motion.div
              key="error"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center py-12"
            >
              <p className="text-red-500 mb-4">Помилка завантаження: {error}</p>
              <Button onClick={refresh} variant="outline">
                <RefreshCw className="w-4 h-4 mr-2" />
                Спробувати знову
              </Button>
            </motion.div>
          ) : (
            <motion.div
              key="content"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {models.map((model, index) => (
                    <ModelCard key={model.id} model={model} index={index} />
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {models.map((model, index) => (
                    <ModelListItem key={model.id} model={model} index={index} />
                  ))}
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Empty State */}
        {!loading && !error && models.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <TrendingUp className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold mb-2">Немає трендових моделей</h3>
            <p className="text-muted-foreground">
              Спробуйте вибрати іншу платформу або категорію
            </p>
          </motion.div>
        )}
      </div>
    </section>
  );
};

export default TrendingModelsSection;
