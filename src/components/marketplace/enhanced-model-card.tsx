'use client';

/**
 * Enhanced Model Card - Покращена картка моделі з красивим дизайном
 */

import { motion } from 'framer-motion';
import {
  Award,
  Clock,
  Download,
  Eye,
  Globe,
  Heart,
  ShoppingCart,
  Star,
  TrendingUp,
  User,
  Zap
} from 'lucide-react';
import Link from 'next/link';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import OptimizedImage from '@/components/ui/optimized-image';
import { cn } from '@/lib/utils';

interface EnhancedModelCardProps {
  model: {
    id: string;
    name: string;
    description?: string;
    price: number;
    thumbnailUrl?: string;
    category: string;
    tags?: string[];
    downloadCount: number;
    viewCount: number;
    likeCount: number;
    rating?: number;
    isFeatured?: boolean;
    isFree?: boolean;
    isNew?: boolean;
    isTrending?: boolean;
    creator?: {
      name: string;
      avatar?: string;
      verified?: boolean;
    };
    createdAt?: string;
    platform?: 'makerworld' | 'printables' | 'thangs' | 'local';
    originalUrl?: string;
    scrapedAt?: string;
  };
  className?: string;
  showQuickActions?: boolean;
}

export default function EnhancedModelCard({ 
  model, 
  className,
  showQuickActions = true 
}: EnhancedModelCardProps) {
  const [isLiked, setIsLiked] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleLike = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsLiked(!isLiked);
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Add to cart logic
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getPlatformInfo = (platform?: string) => {
    switch (platform) {
      case 'makerworld':
        return { name: 'MakerWorld', icon: '🏭', color: 'from-blue-500 to-cyan-500' };
      case 'printables':
        return { name: 'Printables', icon: '🖨️', color: 'from-green-500 to-emerald-500' };
      case 'thangs':
        return { name: 'Thangs', icon: '💎', color: 'from-purple-500 to-pink-500' };
      default:
        return { name: 'Local', icon: '🏠', color: 'from-gray-500 to-slate-500' };
    }
  };

  return (
    <motion.div
      className={cn("group", className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className="overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
        <div className="relative">
          {/* Image Container */}
          <div className="relative aspect-square overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800">
            {model.thumbnailUrl ? (
              <OptimizedImage
                src={model.thumbnailUrl}
                alt={model.name}
                fill
                objectFit="cover"
                className="transition-transform duration-500 group-hover:scale-110"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                priority={false}
                quality={90}
                placeholder="blur"
                fallbackSrc="/images/model-placeholder.jpg"
                showSkeleton={true}
                skeletonClassName="bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800"
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-400 dark:text-gray-500">
                <div className="text-6xl">🎯</div>
              </div>
            )}

            {/* Overlay with quick actions */}
            <motion.div
              className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              initial={false}
              animate={{ opacity: isHovered ? 1 : 0 }}
            >
              {showQuickActions && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <motion.div
                    className="flex gap-2"
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ 
                      scale: isHovered ? 1 : 0.8, 
                      opacity: isHovered ? 1 : 0 
                    }}
                    transition={{ delay: 0.1 }}
                  >
                    <Button
                      size="sm"
                      variant="secondary"
                      className="bg-white/90 hover:bg-white text-gray-900"
                      onClick={handleLike}
                    >
                      <Heart className={cn("h-4 w-4", isLiked && "fill-red-500 text-red-500")} />
                    </Button>
                    <Button
                      size="sm"
                      variant="secondary"
                      className="bg-white/90 hover:bg-white text-gray-900"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    {!model.isFree && (
                      <Button
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700"
                        onClick={handleAddToCart}
                      >
                        <ShoppingCart className="h-4 w-4" />
                      </Button>
                    )}
                  </motion.div>
                </div>
              )}
            </motion.div>

            {/* Status badges */}
            <div className="absolute top-3 left-3 flex flex-col gap-2">
              {/* Platform badge */}
              {model.platform && (
                <Badge className={`bg-gradient-to-r ${getPlatformInfo(model.platform).color} text-white border-0`}>
                  <span className="mr-1">{getPlatformInfo(model.platform).icon}</span>
                  {getPlatformInfo(model.platform).name}
                </Badge>
              )}

              {model.isFeatured && (
                <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-0">
                  <Award className="h-3 w-3 mr-1" />
                  Featured
                </Badge>
              )}
              {model.isNew && (
                <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0">
                  <Zap className="h-3 w-3 mr-1" />
                  New
                </Badge>
              )}
              {model.isTrending && (
                <Badge className="bg-gradient-to-r from-pink-500 to-purple-500 text-white border-0">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  Trending
                </Badge>
              )}
              {model.isFree && (
                <Badge className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white border-0">
                  Free
                </Badge>
              )}
            </div>

            {/* Price badge */}
            <div className="absolute top-3 right-3">
              <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-full px-3 py-1 border border-white/20">
                <span className="font-bold text-lg">
                  {model.isFree ? 'Free' : `$${model.price}`}
                </span>
              </div>
            </div>
          </div>

          {/* Content */}
          <CardContent className="p-6">
            {/* Title and Category */}
            <div className="mb-4">
              <Link href={`/marketplace/model/${model.id}`}>
                <h3 className="font-bold text-lg mb-2 line-clamp-2 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                  {model.name}
                </h3>
              </Link>
              <Badge variant="secondary" className="text-xs">
                {model.category}
              </Badge>
            </div>

            {/* Creator info */}
            {model.creator && (
              <div className="flex items-center gap-2 mb-4">
                <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center overflow-hidden">
                  {model.creator.avatar ? (
                    <OptimizedImage
                      src={model.creator.avatar}
                      alt={model.creator.name}
                      width={24}
                      height={24}
                      className="rounded-full"
                      sizes="24px"
                      showSkeleton={false}
                      fallbackSrc="/images/default-avatar.png"
                    />
                  ) : (
                    <User className="h-3 w-3 text-white" />
                  )}
                </div>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {model.creator.name}
                </span>
                {model.creator.verified && (
                  <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">✓</span>
                  </div>
                )}
              </div>
            )}

            {/* Stats */}
            <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <Download className="h-4 w-4" />
                  <span>{formatNumber(model.downloadCount)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="h-4 w-4" />
                  <span>{formatNumber(model.viewCount)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Heart className="h-4 w-4" />
                  <span>{formatNumber(model.likeCount)}</span>
                </div>
              </div>
              
              {model.rating && (
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-medium">{model.rating.toFixed(1)}</span>
                </div>
              )}
            </div>

            {/* Tags */}
            {model.tags && model.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-4">
                {model.tags.slice(0, 3).map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {model.tags.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{model.tags.length - 3}
                  </Badge>
                )}
              </div>
            )}

            {/* Action buttons */}
            <div className="flex gap-2">
              <Button 
                asChild 
                className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <Link href={`/marketplace/model/${model.id}`}>
                  {model.isFree ? 'Download' : 'View Details'}
                </Link>
              </Button>
              
              {!model.isFree && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleAddToCart}
                  className="border-blue-200 hover:bg-blue-50 dark:border-blue-700 dark:hover:bg-blue-900/20"
                >
                  <ShoppingCart className="h-4 w-4" />
                </Button>
              )}
            </div>

            {/* Time info and source */}
            <div className="mt-3 space-y-1">
              {model.createdAt && (
                <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                  <Clock className="h-3 w-3" />
                  <span>Added {new Date(model.createdAt).toLocaleDateString()}</span>
                </div>
              )}

              {model.scrapedAt && (
                <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                  <Globe className="h-3 w-3" />
                  <span>Scraped {new Date(model.scrapedAt).toLocaleDateString()}</span>
                </div>
              )}

              {model.originalUrl && (
                <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                  <span>Source: </span>
                  <a
                    href={model.originalUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 underline"
                    onClick={(e) => e.stopPropagation()}
                  >
                    Original
                  </a>
                </div>
              )}
            </div>
          </CardContent>
        </div>
      </Card>
    </motion.div>
  );
}
