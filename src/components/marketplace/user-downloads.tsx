'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import Image from 'next/image';
import {
  Download,
  Eye,
  Calendar,
  ExternalLink,
  Search,
  Filter,
  Grid,
  List
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface UserDownload {
  id: string;
  model_id: string;
  model_name: string;
  model_description: string;
  model_thumbnail: string;
  model_url: string;
  model_price: number;
  model_category: string;
  author_name: string;
  download_date: string;
  additional_files?: string[];
}

export default function UserDownloads() {
  const { data: session } = useSession();
  const [downloads, setDownloads] = useState<UserDownload[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Завантаження списку завантажень
  useEffect(() => {
    const fetchDownloads = async () => {
      if (!session?.user?.id) return;

      try {
        const response = await fetch('/api/users/downloads');
        if (!response.ok) {
          throw new Error('Failed to fetch downloads');
        }

        const data = await response.json();
        setDownloads(data.data || []);
      } catch (error) {
        console.error('Error fetching downloads:', error);
        setError('Failed to load downloads');
      } finally {
        setLoading(false);
      }
    };

    fetchDownloads();
  }, [session?.user?.id]);

  // Фільтрація та сортування
  const filteredDownloads = downloads
    .filter(download => 
      download.model_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      download.author_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      download.model_category.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.download_date).getTime() - new Date(a.download_date).getTime();
        case 'oldest':
          return new Date(a.download_date).getTime() - new Date(b.download_date).getTime();
        case 'name':
          return a.model_name.localeCompare(b.model_name);
        case 'author':
          return a.author_name.localeCompare(b.author_name);
        default:
          return 0;
      }
    });

  // Завантаження файлу
  const handleDownload = async (download: UserDownload) => {
    try {
      // Створюємо посилання для завантаження
      const link = document.createElement('a');
      link.href = download.model_url;
      link.download = `${download.model_name}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Download error:', error);
      alert('Помилка при завантаженні файлу');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Заголовок */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Мої завантаження</h2>
          <p className="text-gray-600">
            {downloads.length} {downloads.length === 1 ? 'модель' : 'моделей'} у вашій бібліотеці
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Фільтри та пошук */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Пошук моделей..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Сортування" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="newest">Найновіші</SelectItem>
            <SelectItem value="oldest">Найстаріші</SelectItem>
            <SelectItem value="name">За назвою</SelectItem>
            <SelectItem value="author">За автором</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Список завантажень */}
      {filteredDownloads.length === 0 ? (
        <div className="text-center py-12">
          <Download className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium mb-2">
            {searchQuery ? 'Нічого не знайдено' : 'Немає завантажень'}
          </h3>
          <p className="text-gray-500 mb-4">
            {searchQuery 
              ? 'Спробуйте змінити пошуковий запит'
              : 'Почніть досліджувати маркетплейс та завантажуйте моделі'
            }
          </p>
          {!searchQuery && (
            <Link href="/marketplace">
              <Button>Переглянути маркетплейс</Button>
            </Link>
          )}
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
        }>
          {filteredDownloads.map((download) => (
            viewMode === 'grid' ? (
              <DownloadCard key={download.id} download={download} onDownload={handleDownload} />
            ) : (
              <DownloadListItem key={download.id} download={download} onDownload={handleDownload} />
            )
          ))}
        </div>
      )}
    </div>
  );
}

// Компонент картки завантаження
interface DownloadItemProps {
  download: UserDownload;
  onDownload: (download: UserDownload) => void;
}

function DownloadCard({ download, onDownload }: DownloadItemProps) {
  return (
    <Card className="hover:shadow-lg transition-shadow">
      <div className="relative">
        <Image
          src={download.model_thumbnail || '/placeholder-model.jpg'}
          alt={download.model_name}
          width={300}
          height={200}
          className="w-full h-48 object-cover rounded-t-lg"
        />
        <Badge className="absolute top-2 left-2" variant="secondary">
          {download.model_category}
        </Badge>
      </div>

      <CardHeader className="pb-2">
        <CardTitle className="text-lg line-clamp-1">{download.model_name}</CardTitle>
        <CardDescription className="line-clamp-2">
          від {download.author_name}
        </CardDescription>
      </CardHeader>

      <CardContent className="pb-2">
        <div className="flex items-center text-sm text-gray-500 mb-2">
          <Calendar className="mr-1 h-3 w-3" />
          Завантажено {new Date(download.download_date).toLocaleDateString('uk-UA')}
        </div>
        {download.model_price > 0 && (
          <div className="text-sm font-medium text-green-600">
            Куплено за ${download.model_price}
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-2 space-x-2">
        <Button
          onClick={() => onDownload(download)}
          size="sm"
          className="flex-1"
        >
          <Download className="mr-2 h-4 w-4" />
          Завантажити
        </Button>
        <Link href={`/models/${download.model_id}`}>
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4" />
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}

function DownloadListItem({ download, onDownload }: DownloadItemProps) {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center space-x-4">
          <Image
            src={download.model_thumbnail || '/placeholder-model.jpg'}
            alt={download.model_name}
            width={80}
            height={80}
            className="w-20 h-20 object-cover rounded-lg"
          />
          
          <div className="flex-1">
            <h3 className="font-medium text-lg">{download.model_name}</h3>
            <p className="text-sm text-gray-600">від {download.author_name}</p>
            <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
              <span className="flex items-center">
                <Calendar className="mr-1 h-3 w-3" />
                {new Date(download.download_date).toLocaleDateString('uk-UA')}
              </span>
              <Badge variant="secondary">{download.model_category}</Badge>
              {download.model_price > 0 && (
                <span className="text-green-600 font-medium">${download.model_price}</span>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Link href={`/models/${download.model_id}`}>
              <Button variant="outline" size="sm">
                <Eye className="mr-2 h-4 w-4" />
                Переглянути
              </Button>
            </Link>
            <Button
              onClick={() => onDownload(download)}
              size="sm"
            >
              <Download className="mr-2 h-4 w-4" />
              Завантажити
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
