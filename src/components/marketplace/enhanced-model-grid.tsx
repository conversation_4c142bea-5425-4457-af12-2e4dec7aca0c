'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Search, 
  Filter, 
  Grid3X3, 
  List, 
  SortAsc, 
  SortDesc,
  Download, 
  Heart, 
  Eye, 
  Star,
  ExternalLink,
  TrendingUp,
  Clock,
  Award
} from 'lucide-react';

interface Model {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  designer: {
    name: string;
    avatar?: string;
    verified?: boolean;
  };
  stats: {
    views: number;
    downloads: number;
    likes: number;
    rating?: number;
    reviews?: number;
  };
  platform: string;
  originalUrl: string;
  tags: string[];
  category: string;
  isFree: boolean;
  price?: number;
  publishedAt?: string;
  featured?: boolean;
  isNew?: boolean;
  trending_score?: number;
}

interface EnhancedModelGridProps {
  models: Model[];
  loading?: boolean;
  title?: string;
  subtitle?: string;
  showSearch?: boolean;
  showFilters?: boolean;
  showViewToggle?: boolean;
  showSorting?: boolean;
  defaultViewMode?: 'grid' | 'list';
  className?: string;
}

const EnhancedModelGrid: React.FC<EnhancedModelGridProps> = ({
  models = [],
  loading = false,
  title,
  subtitle,
  showSearch = true,
  showFilters = true,
  showViewToggle = true,
  showSorting = true,
  defaultViewMode = 'grid',
  className = ""
}) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(defaultViewMode);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('trending');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Extract unique categories and platforms
  const categories = useMemo(() => {
    const cats = Array.from(new Set(models.map(m => m.category)));
    return ['all', ...cats];
  }, [models]);

  const platforms = useMemo(() => {
    const plats = Array.from(new Set(models.map(m => m.platform)));
    return ['all', ...plats];
  }, [models]);

  // Filter and sort models
  const filteredAndSortedModels = useMemo(() => {
    let filtered = models.filter(model => {
      const matchesSearch = searchQuery === '' || 
        model.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        model.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        model.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || model.category === selectedCategory;
      const matchesPlatform = selectedPlatform === 'all' || model.platform === selectedPlatform;
      
      return matchesSearch && matchesCategory && matchesPlatform;
    });

    // Sort models
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case 'trending':
          aValue = a.trending_score || 0;
          bValue = b.trending_score || 0;
          break;
        case 'downloads':
          aValue = a.stats.downloads;
          bValue = b.stats.downloads;
          break;
        case 'likes':
          aValue = a.stats.likes;
          bValue = b.stats.likes;
          break;
        case 'views':
          aValue = a.stats.views;
          bValue = b.stats.views;
          break;
        case 'rating':
          aValue = a.stats.rating || 0;
          bValue = b.stats.rating || 0;
          break;
        case 'date':
          aValue = new Date(a.publishedAt || 0).getTime();
          bValue = new Date(b.publishedAt || 0).getTime();
          break;
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'price':
          aValue = a.price || 0;
          bValue = b.price || 0;
          break;
        default:
          return 0;
      }
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    return filtered;
  }, [models, searchQuery, selectedCategory, selectedPlatform, sortBy, sortOrder]);

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getModelBadges = (model: Model) => {
    const badges = [];
    
    if (model.featured) {
      badges.push({ text: 'Featured', color: 'bg-purple-500 text-white', icon: Award });
    }
    
    if (model.isNew) {
      badges.push({ text: 'New', color: 'bg-green-500 text-white', icon: Clock });
    }
    
    if (model.trending_score && model.trending_score > 80) {
      badges.push({ text: 'Trending', color: 'bg-orange-500 text-white', icon: TrendingUp });
    }
    
    return badges;
  };

  const ModelCard = ({ model, index }: { model: Model; index: number }) => {
    const badges = getModelBadges(model);
    
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
        className="group"
      >
        <Card className="overflow-hidden hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 hover:scale-[1.02]">
          <div className="relative">
            {/* Thumbnail */}
            <div className="aspect-[4/3] overflow-hidden">
              <img
                src={model.thumbnail}
                alt={model.title}
                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                loading="lazy"
              />
              
              {/* Badges */}
              <div className="absolute top-2 left-2 flex flex-col gap-1">
                {badges.map((badge, i) => {
                  const Icon = badge.icon;
                  return (
                    <Badge key={i} className={`${badge.color} flex items-center gap-1`}>
                      <Icon className="w-3 h-3" />
                      {badge.text}
                    </Badge>
                  );
                })}
              </div>

              {/* Platform Badge */}
              <div className="absolute top-2 right-2">
                <Badge variant="secondary" className="bg-black/70 text-white">
                  {model.platform.toUpperCase()}
                </Badge>
              </div>

              {/* Price Badge */}
              <div className="absolute bottom-2 right-2">
                {model.isFree ? (
                  <Badge className="bg-blue-500 text-white font-semibold">
                    FREE
                  </Badge>
                ) : (
                  <Badge className="bg-green-500 text-white font-semibold">
                    ${model.price}
                  </Badge>
                )}
              </div>
            </div>

            <CardContent className="p-4">
              {/* Title */}
              <h3 className="font-bold text-lg mb-2 line-clamp-2 group-hover:text-primary transition-colors">
                {model.title}
              </h3>

              {/* Designer */}
              <div className="flex items-center gap-2 mb-3">
                <img
                  src={model.designer.avatar || `https://picsum.photos/32/32?random=${model.id}`}
                  alt={model.designer.name}
                  className="w-6 h-6 rounded-full"
                />
                <span className="text-sm text-muted-foreground">
                  {model.designer.name}
                </span>
                {model.designer.verified && (
                  <Badge variant="outline" className="text-xs">
                    ✓
                  </Badge>
                )}
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-2 text-sm text-muted-foreground mb-3">
                <div className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  {formatNumber(model.stats.views)}
                </div>
                <div className="flex items-center gap-1">
                  <Download className="w-4 h-4" />
                  {formatNumber(model.stats.downloads)}
                </div>
                <div className="flex items-center gap-1">
                  <Heart className="w-4 h-4" />
                  {formatNumber(model.stats.likes)}
                </div>
              </div>

              {/* Rating */}
              {model.stats.rating && (
                <div className="flex items-center gap-1 mb-3">
                  <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm font-medium">{model.stats.rating}</span>
                  <span className="text-xs text-muted-foreground">
                    ({model.stats.reviews} відгуків)
                  </span>
                </div>
              )}

              {/* Tags */}
              <div className="flex flex-wrap gap-1 mb-4">
                {model.tags.slice(0, 3).map((tag: string) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {model.tags.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{model.tags.length - 3}
                  </Badge>
                )}
              </div>

              {/* Actions */}
              <Button 
                className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors"
                onClick={() => window.open(model.originalUrl, '_blank')}
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Переглянути модель
              </Button>
            </CardContent>
          </div>
        </Card>
      </motion.div>
    );
  };

  const ModelListItem = ({ model, index }: { model: Model; index: number }) => {
    const badges = getModelBadges(model);
    
    return (
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: index * 0.05 }}
      >
        <Card className="p-4 hover:shadow-lg transition-all duration-300 hover:bg-accent/50">
          <div className="flex gap-4">
            {/* Thumbnail */}
            <div className="w-24 h-24 rounded-lg overflow-hidden flex-shrink-0">
              <img
                src={model.thumbnail}
                alt={model.title}
                className="w-full h-full object-cover"
                loading="lazy"
              />
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1 min-w-0">
                  <h3 className="font-bold text-lg mb-1 truncate">
                    {model.title}
                  </h3>
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm text-muted-foreground">
                      by {model.designer.name}
                    </span>
                    {model.designer.verified && (
                      <Badge variant="outline" className="text-xs">✓</Badge>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center gap-2 ml-4">
                  {badges.map((badge, i) => {
                    const Icon = badge.icon;
                    return (
                      <Badge key={i} className={badge.color}>
                        <Icon className="w-3 h-3 mr-1" />
                        {badge.text}
                      </Badge>
                    );
                  })}
                  {model.isFree ? (
                    <Badge className="bg-blue-500 text-white">FREE</Badge>
                  ) : (
                    <Badge className="bg-green-500 text-white">${model.price}</Badge>
                  )}
                </div>
              </div>

              {/* Stats */}
              <div className="flex items-center gap-6 text-sm text-muted-foreground mb-2">
                <div className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  {formatNumber(model.stats.views)}
                </div>
                <div className="flex items-center gap-1">
                  <Download className="w-4 h-4" />
                  {formatNumber(model.stats.downloads)}
                </div>
                <div className="flex items-center gap-1">
                  <Heart className="w-4 h-4" />
                  {formatNumber(model.stats.likes)}
                </div>
                {model.stats.rating && (
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    {model.stats.rating}
                  </div>
                )}
              </div>

              {/* Tags */}
              <div className="flex flex-wrap gap-1">
                {model.tags.slice(0, 5).map((tag: string) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </Card>
      </motion.div>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      {(title || subtitle) && (
        <div className="text-center">
          {title && (
            <h2 className="text-3xl font-bold mb-2">{title}</h2>
          )}
          {subtitle && (
            <p className="text-lg text-muted-foreground">{subtitle}</p>
          )}
        </div>
      )}

      {/* Controls */}
      <div className="flex flex-col lg:flex-row gap-4 justify-between items-center">
        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          {showSearch && (
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Пошук моделей..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          )}

          {showFilters && (
            <div className="flex gap-2">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Категорія" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(cat => (
                    <SelectItem key={cat} value={cat}>
                      {cat === 'all' ? 'Всі категорії' : cat}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Платформа" />
                </SelectTrigger>
                <SelectContent>
                  {platforms.map(platform => (
                    <SelectItem key={platform} value={platform}>
                      {platform === 'all' ? 'Всі платформи' : platform.toUpperCase()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {/* View and Sort Controls */}
        <div className="flex items-center gap-2">
          {showSorting && (
            <div className="flex gap-2">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="trending">Трендові</SelectItem>
                  <SelectItem value="downloads">Завантаження</SelectItem>
                  <SelectItem value="likes">Лайки</SelectItem>
                  <SelectItem value="views">Перегляди</SelectItem>
                  <SelectItem value="rating">Рейтинг</SelectItem>
                  <SelectItem value="date">Дата</SelectItem>
                  <SelectItem value="title">Назва</SelectItem>
                  <SelectItem value="price">Ціна</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              >
                {sortOrder === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
              </Button>
            </div>
          )}

          {showViewToggle && (
            <div className="flex border rounded-lg p-1">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid3X3 className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Results Count */}
      <div className="text-sm text-muted-foreground">
        Знайдено {filteredAndSortedModels.length} з {models.length} моделей
      </div>

      {/* Content */}
      <AnimatePresence mode="wait">
        {loading ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {Array.from({ length: 12 }).map((_, i) => (
                  <Card key={i} className="overflow-hidden">
                    <Skeleton className="aspect-[4/3] w-full" />
                    <CardContent className="p-4">
                      <Skeleton className="h-6 w-3/4 mb-2" />
                      <Skeleton className="h-4 w-1/2 mb-3" />
                      <div className="flex gap-2 mb-3">
                        <Skeleton className="h-6 w-12" />
                        <Skeleton className="h-6 w-12" />
                        <Skeleton className="h-6 w-12" />
                      </div>
                      <Skeleton className="h-8 w-full" />
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {Array.from({ length: 8 }).map((_, i) => (
                  <Card key={i} className="p-4">
                    <div className="flex gap-4">
                      <Skeleton className="w-24 h-24 rounded-lg" />
                      <div className="flex-1">
                        <Skeleton className="h-6 w-3/4 mb-2" />
                        <Skeleton className="h-4 w-1/2 mb-2" />
                        <div className="flex gap-4 mb-2">
                          <Skeleton className="h-4 w-16" />
                          <Skeleton className="h-4 w-16" />
                          <Skeleton className="h-4 w-16" />
                        </div>
                        <div className="flex gap-2">
                          <Skeleton className="h-6 w-16" />
                          <Skeleton className="h-6 w-16" />
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </motion.div>
        ) : (
          <motion.div
            key="content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredAndSortedModels.map((model, index) => (
                  <ModelCard key={model.id} model={model} index={index} />
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredAndSortedModels.map((model, index) => (
                  <ModelListItem key={model.id} model={model} index={index} />
                ))}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Empty State */}
      {!loading && filteredAndSortedModels.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <Search className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-xl font-semibold mb-2">Моделі не знайдено</h3>
          <p className="text-muted-foreground">
            Спробуйте змінити параметри пошуку або фільтри
          </p>
        </motion.div>
      )}
    </div>
  );
};

export default EnhancedModelGrid;
