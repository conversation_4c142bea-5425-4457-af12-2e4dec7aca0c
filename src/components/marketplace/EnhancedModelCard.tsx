'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Download, 
  Eye, 
  Heart, 
  Star, 
  ExternalLink, 
  FileText, 
  Calendar,
  User,
  Package,
  Cloud,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';

interface ModelFile {
  fileName: string;
  fileType: string;
  fileSize?: number;
  downloadUrl?: string;
  r2Key?: string;
  status?: 'pending' | 'downloading' | 'completed' | 'failed';
  progress?: number;
}

interface EnhancedModel {
  id: string;
  title: string;
  description: string;
  author: string;
  platform: string;
  category: string;
  tags: string[];
  imageUrl?: string;
  thumbnailUrl?: string;
  originalUrl: string;
  
  // Статистика
  downloads: number;
  views: number;
  likes: number;
  rating: number;
  
  // Дати
  createdAt: string;
  updatedAt: string;
  scrapedAt: string;
  
  // Файли
  files: ModelFile[];
  
  // Статус завантаження
  downloadStatus: 'not_started' | 'in_progress' | 'completed' | 'failed';
  downloadProgress: number;
  
  // Метадані
  isPremium: boolean;
  isVerified: boolean;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  printTime?: string;
  material?: string;
}

interface EnhancedModelCardProps {
  model: EnhancedModel;
  onDownload?: (modelId: string) => void;
  onView?: (modelId: string) => void;
  onLike?: (modelId: string) => void;
}

export default function EnhancedModelCard({ 
  model, 
  onDownload, 
  onView, 
  onLike 
}: EnhancedModelCardProps) {
  const [isLiked, setIsLiked] = useState(false);
  const [downloadingFiles, setDownloadingFiles] = useState<Set<string>>(new Set());

  const handleDownload = async () => {
    if (onDownload) {
      onDownload(model.id);
    }
  };

  const handleFileDownload = async (file: ModelFile) => {
    if (file.r2Key) {
      // Завантаження з R2
      const downloadUrl = `/api/models/download/${model.id}/${file.fileName}`;
      window.open(downloadUrl, '_blank');
    } else if (file.downloadUrl) {
      // Пряме завантаження
      window.open(file.downloadUrl, '_blank');
    }
  };

  const handleLike = () => {
    setIsLiked(!isLiked);
    if (onLike) {
      onLike(model.id);
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform.toLowerCase()) {
      case 'printables':
        return 'bg-orange-100 text-orange-800';
      case 'makerworld':
        return 'bg-blue-100 text-blue-800';
      case 'thangs':
        return 'bg-purple-100 text-purple-800';
      case 'thingiverse':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-100 text-green-800';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800';
      case 'advanced':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getDownloadStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'in_progress':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Cloud className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'N/A';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  return (
    <Card className="group hover:shadow-lg transition-all duration-200 overflow-hidden">
      {/* Зображення */}
      <div className="relative aspect-video bg-gray-100 overflow-hidden">
        {model.imageUrl ? (
          <img
            src={model.imageUrl}
            alt={model.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
            <Package className="h-12 w-12 text-gray-400" />
          </div>
        )}
        
        {/* Оверлей з статусом */}
        <div className="absolute top-2 left-2 flex space-x-2">
          <Badge className={getPlatformColor(model.platform)}>
            {model.platform}
          </Badge>
          {model.isPremium && (
            <Badge className="bg-yellow-100 text-yellow-800">
              Premium
            </Badge>
          )}
          {model.isVerified && (
            <Badge className="bg-blue-100 text-blue-800">
              ✓ Verified
            </Badge>
          )}
        </div>

        {/* Статус завантаження */}
        <div className="absolute top-2 right-2">
          {getDownloadStatusIcon(model.downloadStatus)}
        </div>

        {/* Прогрес завантаження */}
        {model.downloadStatus === 'in_progress' && (
          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 p-2">
            <div className="flex items-center space-x-2 text-white text-xs">
              <span>Завантаження...</span>
              <Progress value={model.downloadProgress} className="flex-1 h-1" />
              <span>{model.downloadProgress}%</span>
            </div>
          </div>
        )}
      </div>

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg line-clamp-2 group-hover:text-blue-600 transition-colors">
              {model.title}
            </CardTitle>
            <CardDescription className="flex items-center space-x-2 mt-1">
              <User className="h-3 w-3" />
              <span>{model.author}</span>
              <span>•</span>
              <Calendar className="h-3 w-3" />
              <span>{new Date(model.createdAt).toLocaleDateString()}</span>
            </CardDescription>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleLike}
            className={isLiked ? 'text-red-500' : 'text-gray-500'}
          >
            <Heart className={`h-4 w-4 ${isLiked ? 'fill-current' : ''}`} />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Опис */}
        <p className="text-sm text-muted-foreground line-clamp-2">
          {model.description}
        </p>

        {/* Статистика */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <Download className="h-3 w-3" />
              <span>{formatNumber(model.downloads)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Eye className="h-3 w-3" />
              <span>{formatNumber(model.views)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Heart className="h-3 w-3" />
              <span>{formatNumber(model.likes)}</span>
            </div>
          </div>
          <div className="flex items-center space-x-1">
            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
            <span>{model.rating.toFixed(1)}</span>
          </div>
        </div>

        {/* Теги та категорія */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {model.category}
            </Badge>
            <Badge className={getDifficultyColor(model.difficulty)} variant="outline">
              {model.difficulty}
            </Badge>
          </div>
          
          {model.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {model.tags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
              {model.tags.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{model.tags.length - 3}
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* Файли */}
        {model.files.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium flex items-center">
              <FileText className="h-3 w-3 mr-1" />
              Файли ({model.files.length})
            </h4>
            <div className="space-y-1">
              {model.files.slice(0, 3).map((file, index) => (
                <div key={index} className="flex items-center justify-between text-xs">
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    <span className="truncate">{file.fileName}</span>
                    <Badge variant="outline" className="text-xs">
                      {file.fileType.toUpperCase()}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-muted-foreground">
                      {formatFileSize(file.fileSize)}
                    </span>
                    {file.r2Key && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleFileDownload(file)}
                        className="h-6 w-6 p-0"
                      >
                        <Download className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
              {model.files.length > 3 && (
                <div className="text-xs text-muted-foreground">
                  +{model.files.length - 3} більше файлів
                </div>
              )}
            </div>
          </div>
        )}

        {/* Додаткова інформація */}
        {(model.printTime || model.material) && (
          <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
            {model.printTime && (
              <div>
                <span className="font-medium">Час друку:</span> {model.printTime}
              </div>
            )}
            {model.material && (
              <div>
                <span className="font-medium">Матеріал:</span> {model.material}
              </div>
            )}
          </div>
        )}

        {/* Дії */}
        <div className="flex space-x-2 pt-2">
          <Button 
            onClick={handleDownload}
            className="flex-1"
            disabled={model.downloadStatus === 'in_progress'}
          >
            <Download className="h-4 w-4 mr-2" />
            {model.downloadStatus === 'completed' ? 'Завантажено' : 'Завантажити'}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={() => onView && onView(model.id)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          
          <Button 
            variant="outline"
            onClick={() => window.open(model.originalUrl, '_blank')}
          >
            <ExternalLink className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
