'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Database, 
  TrendingUp, 
  RefreshCw,
  Zap,
  Globe,
  Clock
} from 'lucide-react';

interface MarketplaceStatus {
  isInitialized: boolean;
  autoLoading: {
    isRunning: boolean;
    lastUpdate: string | null;
    config: {
      platforms: string[];
      modelsPerPlatform: number;
      updateInterval: number;
    };
  };
  message: string;
}

export default function RealMarketplaceStatus() {
  const [status, setStatus] = useState<MarketplaceStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [initializing, setInitializing] = useState(false);

  useEffect(() => {
    loadStatus();
    
    // Оновлюємо статус кожні 30 секунд
    const interval = setInterval(loadStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadStatus = async () => {
    try {
      const response = await fetch('/api/marketplace/init?action=status');
      const data = await response.json();
      
      if (data.success) {
        setStatus(data.data);
      }
    } catch (error) {
      console.error('Помилка завантаження статусу:', error);
    } finally {
      setLoading(false);
    }
  };

  const initializeMarketplace = async () => {
    setInitializing(true);
    try {
      const response = await fetch('/api/marketplace/init', {
        method: 'POST'
      });
      
      const data = await response.json();
      
      if (data.success) {
        await loadStatus();
      }
    } catch (error) {
      console.error('Помилка ініціалізації:', error);
    } finally {
      setInitializing(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Завантаження статусу маркетплейсу...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!status) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <XCircle className="h-8 w-8 mx-auto mb-2" />
            <p>Не вдалося завантажити статус маркетплейсу</p>
            <Button onClick={loadStatus} variant="outline" size="sm" className="mt-2">
              <RefreshCw className="h-4 w-4 mr-2" />
              Спробувати знову
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-2 border-dashed border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-blue-600" />
          Статус справжнього маркетплейсу
        </CardTitle>
        <CardDescription>
          Автоматичне завантаження трендових моделей з реальних платформ
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Основний статус */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {status.isInitialized ? (
              <>
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-700">Маркетплейс активний</span>
              </>
            ) : (
              <>
                <XCircle className="h-5 w-5 text-orange-600" />
                <span className="font-medium text-orange-700">Потребує ініціалізації</span>
              </>
            )}
          </div>
          
          {!status.isInitialized && (
            <Button 
              onClick={initializeMarketplace} 
              disabled={initializing}
              size="sm"
            >
              {initializing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Ініціалізація...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Ініціалізувати
                </>
              )}
            </Button>
          )}
        </div>

        {/* Статус автоматичного завантаження */}
        {status.isInitialized && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-blue-600" />
              <span className="font-medium">Автоматичне завантаження:</span>
              <Badge variant={status.autoLoading.isRunning ? "default" : "secondary"}>
                {status.autoLoading.isRunning ? "Активне" : "Зупинено"}
              </Badge>
            </div>

            {/* Платформи */}
            <div className="flex items-center gap-2">
              <Globe className="h-4 w-4 text-gray-600" />
              <span className="text-sm text-gray-600">Платформи:</span>
              <div className="flex gap-1">
                {status.autoLoading.config.platforms.map(platform => (
                  <Badge key={platform} variant="outline" className="text-xs">
                    {platform}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Останнє оновлення */}
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-600" />
              <span className="text-sm text-gray-600">Останнє оновлення:</span>
              <span className="text-sm">
                {status.autoLoading.lastUpdate 
                  ? new Date(status.autoLoading.lastUpdate).toLocaleString('uk-UA')
                  : 'Ніколи'
                }
              </span>
            </div>

            {/* Конфігурація */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Database className="h-4 w-4 text-gray-600" />
                <span className="text-gray-600">Моделей з платформи:</span>
                <span className="font-medium">{status.autoLoading.config.modelsPerPlatform}</span>
              </div>
              <div className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4 text-gray-600" />
                <span className="text-gray-600">Інтервал:</span>
                <span className="font-medium">{status.autoLoading.config.updateInterval} хв</span>
              </div>
            </div>
          </div>
        )}

        {/* Повідомлення */}
        <div className="p-3 bg-white/50 rounded-lg border">
          <p className="text-sm text-gray-700">{status.message}</p>
        </div>

        {/* Дії */}
        <div className="flex gap-2">
          <Button onClick={loadStatus} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Оновити статус
          </Button>
          
          {status.isInitialized && (
            <Button asChild variant="outline" size="sm">
              <a href="/admin/trending">
                <TrendingUp className="h-4 w-4 mr-2" />
                Управління
              </a>
            </Button>
          )}
        </div>

        {/* Переваги справжнього маркетплейсу */}
        {status.isInitialized && (
          <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
            <h4 className="font-medium text-green-800 mb-2">🎉 Справжній маркетплейс активний!</h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>✅ Реальні дані з Printables, MakerWorld, Thangs</li>
              <li>✅ Автоматичне оновлення трендових моделей</li>
              <li>✅ Bright Data MCP інтеграція</li>
              <li>✅ Професійна якість скрапінгу</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
