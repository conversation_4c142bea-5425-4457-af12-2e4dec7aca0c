'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { ExternalLink, Info } from 'lucide-react';
import { Model, ModelSource } from '@/types/models';

interface ModelSourceBadgeProps {
  model: Model;
  showLicense?: boolean;
  showExternalLink?: boolean;
  size?: 'sm' | 'default' | 'lg';
}

const sourceLabels: Record<ModelSource, string> = {
  local: 'Локальна',
  printables: 'Printables',
  thingiverse: 'Thingiverse',
  myminifactory: 'MyMiniFactory',
  thangs: 'Thangs'
};

const sourceColors: Record<ModelSource, string> = {
  local: 'bg-blue-100 text-blue-800 border-blue-200',
  printables: 'bg-orange-100 text-orange-800 border-orange-200',
  thingiverse: 'bg-green-100 text-green-800 border-green-200',
  myminifactory: 'bg-purple-100 text-purple-800 border-purple-200',
  thangs: 'bg-red-100 text-red-800 border-red-200'
};

const licenseColors = {
  'CC0': 'bg-green-100 text-green-800 border-green-200',
  'CC-BY': 'bg-blue-100 text-blue-800 border-blue-200',
  'CC-BY-SA': 'bg-blue-100 text-blue-800 border-blue-200',
  'CC-BY-NC': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  'CC-BY-NC-SA': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  'GPL': 'bg-purple-100 text-purple-800 border-purple-200',
  'MIT': 'bg-gray-100 text-gray-800 border-gray-200',
  'Custom': 'bg-gray-100 text-gray-800 border-gray-200',
  'Commercial': 'bg-red-100 text-red-800 border-red-200'
};

export const ModelSourceBadge: React.FC<ModelSourceBadgeProps> = ({
  model,
  showLicense = false,
  showExternalLink = false,
  size = 'default'
}) => {
  const sourceLabel = sourceLabels[model.source];
  const sourceColorClass = sourceColors[model.source];

  const getLicenseTooltip = () => {
    if (!model.license) return '';

    const permissions: string[] = [];
    if (model.license.allowCommercialUse) permissions.push('✓ Комерційне використання');
    else permissions.push('✗ Заборонено комерційне використання');

    if (model.license.requireAttribution) permissions.push('! Потрібна атрибуція');
    else permissions.push('✓ Атрибуція не потрібна');

    if (model.license.allowDerivatives) permissions.push('✓ Дозволені похідні роботи');
    else permissions.push('✗ Заборонені похідні роботи');

    return `${model.license.name}\n\n${permissions.join('\n')}`;
  };

  return (
    <div className="flex items-center gap-2 flex-wrap">
      {/* Source Badge */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant="outline"
              className={`${sourceColorClass} ${size === 'sm' ? 'text-xs px-2 py-0.5' : ''}`}
            >
              {sourceLabel}
              {model.source !== 'local' && (
                <Info className="ml-1 h-3 w-3" />
              )}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-sm">
              <p className="font-medium">Джерело: {sourceLabel}</p>
              {model.externalSource && (
                <>
                  <p className="text-xs text-gray-600 mt-1">
                    Імпортовано: {new Date(model.externalSource.importedAt).toLocaleDateString('uk-UA')}
                  </p>
                  <p className="text-xs text-gray-600">
                    ID: {model.externalSource.originalId}
                  </p>
                </>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* Free Badge */}
      {model.isFree && (
        <Badge
          variant="outline"
          className={`bg-green-100 text-green-800 border-green-200 ${size === 'sm' ? 'text-xs px-2 py-0.5' : ''}`}
        >
          Безкоштовно
        </Badge>
      )}

      {/* License Badge */}
      {showLicense && model.license && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge
                variant="outline"
                className={`${licenseColors[model.license.type]} ${size === 'sm' ? 'text-xs px-2 py-0.5' : ''}`}
              >
                {model.license.type}
                <Info className="ml-1 h-3 w-3" />
              </Badge>
            </TooltipTrigger>
            <TooltipContent className="max-w-xs">
              <div className="text-sm whitespace-pre-line">
                {getLicenseTooltip()}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}

      {/* External Link */}
      {showExternalLink && model.externalSource && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size={size === 'sm' ? 'sm' : 'default'}
                className="h-auto p-1"
                asChild
              >
                <a
                  href={model.externalSource.originalUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1"
                >
                  <ExternalLink className="h-3 w-3" />
                  {size !== 'sm' && <span className="text-xs">Оригінал</span>}
                </a>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Переглянути оригінал на {sourceLabel}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
};
