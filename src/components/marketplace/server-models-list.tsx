import { Model, ModelsQueryParams } from '@/types/models';
import ClientMarketplace from '@/components/marketplace/client-marketplace';

// Function to fetch models from API
export async function getModels(params?: ModelsQueryParams): Promise<{
  models: Model[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}> {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.category) queryParams.append('category', params.category);
    if (params?.search) queryParams.append('search', params.search);
    if (params?.tags) queryParams.append('tags', params.tags.join(','));
    if (params?.minPrice !== undefined) queryParams.append('minPrice', params.minPrice.toString());
    if (params?.maxPrice !== undefined) queryParams.append('maxPrice', params.maxPrice.toString());
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);

    // Make API request
    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || ''}/api/models${queryString}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      cache: 'no-store', // Disable caching to get the latest data
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch models: ${response.statusText}`);
    }

    const data = await response.json() as any;
    // Перевіряємо структуру відповіді та повертаємо правильний формат
    if (data.data && typeof data.data === 'object') {
      return data.data as {
        models: Model[];
        pagination: {
          total: number;
          page: number;
          limit: number;
          pages: number;
        };
      };
    } else if (data.models && data.pagination) {
      return data as {
        models: Model[];
        pagination: {
          total: number;
          page: number;
          limit: number;
          pages: number;
        };
      };
    } else {
      // Якщо структура не відповідає очікуваній, спробуємо адаптувати її
      return {
        models: Array.isArray(data.data) ? data.data : (data.models || []) as Model[],
        pagination: data.pagination || {
          total: Array.isArray(data.data) ? data.data.length : 0,
          page: 1,
          limit: 20,
          pages: 1
        }
      };
    }
  } catch (error) {
    console.error('Error fetching models:', error);
    // Повертаємо порожній результат у випадку помилки
    return {
      models: [],
      pagination: {
        total: 0,
        page: params?.page || 1,
        limit: params?.limit || 20,
        pages: 0
      }
    };
  }
}

// Models list component with server-side data fetching
export async function ServerModelsList({ searchParams }: { searchParams: { [key: string]: string | string[] | undefined } }) {
  // Parse search parameters
  const page = typeof searchParams.page === 'string' ? parseInt(searchParams.page) : 1;
  const category = typeof searchParams.category === 'string' ? searchParams.category : undefined;
  const search = typeof searchParams.search === 'string' ? searchParams.search : undefined;
  const sortBy = typeof searchParams.sortBy === 'string' ? searchParams.sortBy as ModelsQueryParams['sortBy'] : undefined;
  const minPrice = typeof searchParams.minPrice === 'string' ? parseFloat(searchParams.minPrice) : undefined;
  const maxPrice = typeof searchParams.maxPrice === 'string' ? parseFloat(searchParams.maxPrice) : undefined;

  // Fetch models from API
  const { models, pagination } = await getModels({
    page,
    limit: 20,
    category,
    search,
    sortBy,
    minPrice,
    maxPrice
  });

  // Перевіряємо, чи є моделі для відображення
  const hasModels = Array.isArray(models) && models.length > 0;

  return (
    <div className="w-full">
      {/* Pass the fetched data to the client component */}
      <ClientMarketplace
        initialModels={models}
        pagination={pagination}
        initialSearchParams={{
          page,
          category,
          search,
          sortBy,
          minPrice,
          maxPrice
        }}
      />

      {/* Показуємо повідомлення, якщо немає моделей і це не через фільтри */}
      {!hasModels && !search && !category && (
        <div className="mt-8 p-6 bg-primary/5 rounded-lg text-center">
          <h3 className="text-xl font-medium mb-2">Немає доступних моделей</h3>
          <p className="text-muted-foreground">
            Наразі в маркетплейсі немає моделей. Спробуйте повернутися пізніше.
          </p>
        </div>
      )}
    </div>
  );
}
