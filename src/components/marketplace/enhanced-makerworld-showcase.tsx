'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useEnhancedMakerWorldModels } from '@/hooks/useEnhancedMakerWorldModels';
import { 
  TrendingUp, 
  Download, 
  Heart, 
  Eye, 
  Star, 
  RefreshCw,
  ExternalLink,
  Clock,
  Layers,
  Palette,
  Zap,
  Award,
  Users,
  Calendar,
  FileText,
  Settings
} from 'lucide-react';

interface EnhancedMakerWorldShowcaseProps {
  limit?: number;
  autoRefresh?: boolean;
  className?: string;
}

export function EnhancedMakerWorldShowcase({
  limit = 16,
  autoRefresh = true,
  className = ""
}: EnhancedMakerWorldShowcaseProps) {
  const [viewMode, setViewMode] = useState<'featured' | 'grid' | 'list'>('featured');
  
  const { 
    models, 
    loading, 
    error, 
    refresh, 
    clearCache, 
    cacheStatus, 
    lastUpdated, 
    processingTime 
  } = useEnhancedMakerWorldModels({
    limit,
    autoRefresh,
    refreshInterval: 10 * 60 * 1000, // 10 minutes
    enableCache: true
  });

  const formatNumber = (num: number): string => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getTrendingColor = (score: number): string => {
    if (score >= 90) return 'from-red-500 to-pink-500';
    if (score >= 80) return 'from-orange-500 to-red-500';
    if (score >= 70) return 'from-yellow-500 to-orange-500';
    if (score >= 60) return 'from-green-500 to-yellow-500';
    return 'from-blue-500 to-green-500';
  };

  const FeaturedModelCard = ({ model, index }: { model: any; index: number }) => (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ delay: index * 0.1 }}
      className="group relative"
    >
      <Card className="overflow-hidden border-0 bg-gradient-to-br from-white via-gray-50 to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-700 shadow-xl hover:shadow-2xl transition-all duration-500">
        <div className="relative">
          {/* Hero Image */}
          <div className="aspect-[16/10] overflow-hidden relative">
            <img
              src={model.thumbnail}
              alt={model.title}
              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              loading="lazy"
            />
            
            {/* Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
            
            {/* Trending Badge */}
            <div className="absolute top-4 left-4">
              <Badge className={`bg-gradient-to-r ${getTrendingColor(model.stats.trending_score)} text-white border-0 shadow-lg`}>
                <TrendingUp className="w-3 h-3 mr-1" />
                {Math.round(model.stats.trending_score)}
              </Badge>
            </div>

            {/* Platform Badge */}
            <div className="absolute top-4 right-4">
              <Badge className="bg-black/70 text-white border-0 backdrop-blur-sm">
                MakerWorld
              </Badge>
            </div>

            {/* Price Badge */}
            <div className="absolute bottom-4 right-4">
              {model.isFree ? (
                <Badge className="bg-emerald-500 text-white border-0 shadow-lg">
                  <Zap className="w-3 h-3 mr-1" />
                  FREE
                </Badge>
              ) : (
                <Badge className="bg-amber-500 text-white border-0 shadow-lg">
                  ${model.price}
                </Badge>
              )}
            </div>

            {/* Title Overlay */}
            <div className="absolute bottom-4 left-4 right-20">
              <h3 className="text-white font-bold text-lg line-clamp-2 drop-shadow-lg">
                {model.title}
              </h3>
            </div>
          </div>

          <CardContent className="p-6">
            {/* Designer */}
            <div className="flex items-center gap-3 mb-4">
              <img
                src={model.designer.avatar}
                alt={model.designer.name}
                className="w-8 h-8 rounded-full ring-2 ring-primary/20"
              />
              <div>
                <p className="font-medium text-sm">{model.designer.name}</p>
                <p className="text-xs text-muted-foreground">Designer</p>
              </div>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-sm font-semibold">
                  <Eye className="w-4 h-4 text-blue-500" />
                  {formatNumber(model.stats.views)}
                </div>
                <p className="text-xs text-muted-foreground">Views</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-sm font-semibold">
                  <Download className="w-4 h-4 text-green-500" />
                  {formatNumber(model.stats.downloads)}
                </div>
                <p className="text-xs text-muted-foreground">Downloads</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-sm font-semibold">
                  <Heart className="w-4 h-4 text-red-500" />
                  {formatNumber(model.stats.likes)}
                </div>
                <p className="text-xs text-muted-foreground">Likes</p>
              </div>
            </div>

            {/* Print Info */}
            {model.printSettings && (
              <div className="grid grid-cols-2 gap-2 mb-4 text-xs">
                <div className="flex items-center gap-1">
                  <Palette className="w-3 h-3 text-purple-500" />
                  <span>{model.printSettings.material}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3 text-orange-500" />
                  <span>{model.printSettings.printTime}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Award className="w-3 h-3 text-yellow-500" />
                  <span>{model.printSettings.difficulty}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Layers className="w-3 h-3 text-indigo-500" />
                  <span>{model.printSettings.supports ? 'Supports' : 'No Supports'}</span>
                </div>
              </div>
            )}

            {/* Tags */}
            <div className="flex flex-wrap gap-1 mb-4">
              {model.tags.slice(0, 4).map((tag: string) => (
                <Badge key={tag} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>

            {/* Actions */}
            <div className="flex gap-2">
              <Button 
                className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                onClick={() => window.open(model.originalUrl, '_blank')}
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                View on MakerWorld
              </Button>
            </div>
          </CardContent>
        </div>
      </Card>
    </motion.div>
  );

  const CompactModelCard = ({ model, index }: { model: any; index: number }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.05 }}
    >
      <Card className="overflow-hidden hover:shadow-lg transition-all duration-300 group">
        <div className="aspect-square overflow-hidden relative">
          <img
            src={model.thumbnail}
            alt={model.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            loading="lazy"
          />
          
          <div className="absolute top-2 left-2">
            <Badge className={`bg-gradient-to-r ${getTrendingColor(model.stats.trending_score)} text-white text-xs`}>
              {Math.round(model.stats.trending_score)}
            </Badge>
          </div>

          {model.isFree && (
            <div className="absolute top-2 right-2">
              <Badge className="bg-emerald-500 text-white text-xs">FREE</Badge>
            </div>
          )}
        </div>

        <CardContent className="p-3">
          <h4 className="font-semibold text-sm line-clamp-2 mb-2">{model.title}</h4>
          <p className="text-xs text-muted-foreground mb-2">by {model.designer.name}</p>
          
          <div className="flex justify-between text-xs text-muted-foreground">
            <span className="flex items-center gap-1">
              <Download className="w-3 h-3" />
              {formatNumber(model.stats.downloads)}
            </span>
            <span className="flex items-center gap-1">
              <Heart className="w-3 h-3" />
              {formatNumber(model.stats.likes)}
            </span>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <section className={`py-16 ${className}`}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium mb-4"
          >
            <TrendingUp className="w-4 h-4" />
            Live from MakerWorld
          </motion.div>
          
          <motion.h2 
            className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            🔥 Trending 3D Models
          </motion.h2>
          
          <motion.p 
            className="text-xl text-muted-foreground max-w-2xl mx-auto"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            Discover the hottest 3D models from MakerWorld's creative community
          </motion.p>
        </div>

        {/* Stats Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex flex-wrap justify-center gap-6 mb-8 p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-xl"
        >
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{models.length}</div>
            <div className="text-sm text-muted-foreground">Models Loaded</div>
          </div>
          {lastUpdated && (
            <div className="text-center">
              <div className="text-sm font-medium">Last Updated</div>
              <div className="text-xs text-muted-foreground">
                {new Date(lastUpdated).toLocaleTimeString()}
              </div>
            </div>
          )}
          <div className="text-center">
            <div className="text-sm font-medium">{processingTime}ms</div>
            <div className="text-xs text-muted-foreground">Processing Time</div>
          </div>
          {cacheStatus && (
            <div className="text-center">
              <div className="text-sm font-medium">
                {cacheStatus.hasCachedData ? 'Cached' : 'Live'}
              </div>
              <div className="text-xs text-muted-foreground">
                {cacheStatus.modelsCount} models
              </div>
            </div>
          )}
        </motion.div>

        {/* Controls */}
        <div className="flex flex-wrap justify-between items-center mb-8 gap-4">
          <div className="flex gap-2">
            <Button
              variant={viewMode === 'featured' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('featured')}
            >
              <Star className="w-4 h-4 mr-2" />
              Featured
            </Button>
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Layers className="w-4 h-4 mr-2" />
              Grid
            </Button>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refresh}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearCache}
            >
              <Settings className="w-4 h-4 mr-2" />
              Clear Cache
            </Button>
          </div>
        </div>

        {/* Content */}
        <AnimatePresence mode="wait">
          {loading ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            >
              {Array.from({ length: 8 }).map((_, i) => (
                <Card key={i} className="overflow-hidden">
                  <Skeleton className="aspect-square w-full" />
                  <CardContent className="p-4">
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-3 w-1/2 mb-3" />
                    <div className="flex gap-2">
                      <Skeleton className="h-3 w-12" />
                      <Skeleton className="h-3 w-12" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </motion.div>
          ) : error ? (
            <motion.div
              key="error"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center py-12"
            >
              <p className="text-red-500 mb-4">Error: {error}</p>
              <Button onClick={refresh} variant="outline">
                <RefreshCw className="w-4 h-4 mr-2" />
                Try Again
              </Button>
            </motion.div>
          ) : (
            <motion.div
              key="content"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {viewMode === 'featured' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {models.slice(0, 6).map((model, index) => (
                    <FeaturedModelCard key={model.id} model={model} index={index} />
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                  {models.map((model, index) => (
                    <CompactModelCard key={model.id} model={model} index={index} />
                  ))}
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Load More */}
        {!loading && !error && models.length > 0 && viewMode === 'featured' && models.length > 6 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center mt-12"
          >
            <Button 
              size="lg"
              className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
              onClick={() => setViewMode('grid')}
            >
              <Users className="w-4 h-4 mr-2" />
              View All {models.length} Models
            </Button>
          </motion.div>
        )}
      </div>
    </section>
  );
}
