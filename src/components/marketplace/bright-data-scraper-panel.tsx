'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  Download, 
  RefreshCw, 
  CheckCircle, 
  AlertCircle,
  Globe,
  Database,
  Zap,
  TrendingUp
} from 'lucide-react';

interface ScrapingResult {
  success: boolean;
  message: string;
  data?: any[];
  stats?: any;
  error?: string;
}

interface PlatformStatus {
  name: string;
  status: 'idle' | 'scraping' | 'success' | 'error';
  progress: number;
  models: number;
  lastUpdate: string;
  icon: string;
  color: string;
}

export const BrightDataScraperPanel: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<ScrapingResult | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [modelCount, setModelCount] = useState(20);
  
  const [platformStatuses, setPlatformStatuses] = useState<PlatformStatus[]>([
    {
      name: 'MakerWorld',
      status: 'idle',
      progress: 0,
      models: 0,
      lastUpdate: 'Ніколи',
      icon: '🏭',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      name: 'Printables',
      status: 'idle',
      progress: 0,
      models: 0,
      lastUpdate: 'Ніколи',
      icon: '🖨️',
      color: 'from-green-500 to-emerald-500'
    },
    {
      name: 'Thangs',
      status: 'idle',
      progress: 0,
      models: 0,
      lastUpdate: 'Ніколи',
      icon: '💎',
      color: 'from-purple-500 to-pink-500'
    }
  ]);

  const updatePlatformStatus = (name: string, updates: Partial<PlatformStatus>) => {
    setPlatformStatuses(prev => prev.map(platform => 
      platform.name === name ? { ...platform, ...updates } : platform
    ));
  };

  const handleScraping = async (action: string, additionalData: any = {}) => {
    setIsLoading(true);
    setResult(null);

    try {
      // Оновлюємо статуси платформ
      if (action === 'bright-data-scrape') {
        platformStatuses.forEach(platform => {
          updatePlatformStatus(platform.name, { status: 'scraping', progress: 0 });
        });
      }

      const requestData = {
        action,
        count: modelCount,
        ...additionalData
      };

      console.log('🚀 Запуск Bright Data скрапінгу:', requestData);

      // Симулюємо прогрес
      if (action === 'bright-data-scrape') {
        for (let i = 0; i < platformStatuses.length; i++) {
          const platform = platformStatuses[i];
          updatePlatformStatus(platform.name, { progress: 25 });
          
          await new Promise(resolve => setTimeout(resolve, 500));
          
          updatePlatformStatus(platform.name, { progress: 75 });
          
          await new Promise(resolve => setTimeout(resolve, 500));
          
          updatePlatformStatus(platform.name, { 
            progress: 100, 
            status: 'success',
            models: Math.floor(Math.random() * 50) + 10,
            lastUpdate: new Date().toLocaleString()
          });
        }
      }

      const response = await fetch('/api/scrape', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const data = await response.json();
      setResult(data);

      if (data.success) {
        console.log('✅ Скрапінг успішний:', data);
      } else {
        console.error('❌ Помилка скрапінгу:', data);
        // Оновлюємо статуси на помилку
        platformStatuses.forEach(platform => {
          updatePlatformStatus(platform.name, { status: 'error', progress: 0 });
        });
      }
    } catch (error) {
      console.error('❌ Помилка запиту:', error);
      setResult({
        success: false,
        message: 'Помилка мережі',
        error: error instanceof Error ? error.message : 'Невідома помилка'
      });
      
      // Оновлюємо статуси на помилку
      platformStatuses.forEach(platform => {
        updatePlatformStatus(platform.name, { status: 'error', progress: 0 });
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: PlatformStatus['status']) => {
    switch (status) {
      case 'scraping':
        return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Database className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-500" />
            Bright Data Scraper
          </CardTitle>
          <CardDescription>
            Скрапінг 3D моделей з makerworld.com, printables.com та thangs.com
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="quick" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="quick">Швидкий старт</TabsTrigger>
              <TabsTrigger value="search">Пошук</TabsTrigger>
              <TabsTrigger value="advanced">Розширений</TabsTrigger>
            </TabsList>

            <TabsContent value="quick" className="space-y-4">
              <div>
                <Label htmlFor="model-count">Кількість моделей</Label>
                <Input
                  id="model-count"
                  type="number"
                  value={modelCount}
                  onChange={(e) => setModelCount(parseInt(e.target.value) || 20)}
                  min={1}
                  max={100}
                  className="mt-1"
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  onClick={() => handleScraping('bright-data-scrape')}
                  disabled={isLoading}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  {isLoading ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Скрапінг...
                    </>
                  ) : (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Скрапити всі платформи
                    </>
                  )}
                </Button>
                
                <Button
                  onClick={() => handleScraping('fake-data')}
                  disabled={isLoading}
                  variant="outline"
                >
                  <Database className="h-4 w-4 mr-2" />
                  Згенерувати тестові дані
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="search" className="space-y-4">
              <div>
                <Label htmlFor="search-query">Пошуковий запит</Label>
                <Input
                  id="search-query"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="dragon miniature, robot toy, etc."
                  className="mt-1"
                />
              </div>
              
              <Button
                onClick={() => handleScraping('search-models', { query: searchQuery })}
                disabled={isLoading || !searchQuery.trim()}
                className="w-full"
              >
                <Search className="h-4 w-4 mr-2" />
                Пошук моделей
              </Button>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  onClick={() => handleScraping('scrape-printables')}
                  disabled={isLoading}
                  variant="outline"
                >
                  🖨️ Printables
                </Button>
                
                <Button
                  onClick={() => handleScraping('scrape-thangs')}
                  disabled={isLoading}
                  variant="outline"
                >
                  💎 Thangs
                </Button>
                
                <Button
                  onClick={() => handleScraping('populate')}
                  disabled={isLoading}
                  variant="outline"
                >
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Популярні
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Platform Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {platformStatuses.map((platform) => (
          <Card key={platform.name} className="relative overflow-hidden">
            <div className={`absolute inset-0 bg-gradient-to-br ${platform.color} opacity-5`} />
            <CardHeader className="relative pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">{platform.icon}</span>
                  <CardTitle className="text-lg">{platform.name}</CardTitle>
                </div>
                {getStatusIcon(platform.status)}
              </div>
            </CardHeader>
            <CardContent className="relative">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Моделей:</span>
                  <span className="font-medium">{platform.models}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Оновлено:</span>
                  <span className="text-xs text-gray-500">{platform.lastUpdate}</span>
                </div>
                {platform.status === 'scraping' && (
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs">
                      <span>Прогрес:</span>
                      <span>{platform.progress}%</span>
                    </div>
                    <Progress value={platform.progress} className="h-2" />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Results */}
      <AnimatePresence>
        {result && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {result.success ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-500" />
                  )}
                  Результат скрапінгу
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Badge variant={result.success ? 'default' : 'destructive'}>
                      {result.success ? 'Успішно' : 'Помилка'}
                    </Badge>
                    <p className="mt-2">{result.message}</p>
                  </div>

                  {result.error && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <p className="text-red-800 text-sm">{result.error}</p>
                    </div>
                  )}

                  {result.stats && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h4 className="font-medium mb-2">Статистика:</h4>
                      <pre className="text-sm text-blue-800 whitespace-pre-wrap">
                        {JSON.stringify(result.stats, null, 2)}
                      </pre>
                    </div>
                  )}

                  {result.data && result.data.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">
                        Знайдено моделей: {result.data.length}
                      </h4>
                      <div className="max-h-60 overflow-y-auto space-y-2">
                        {result.data.slice(0, 5).map((model: any, index: number) => (
                          <div key={index} className="border rounded-lg p-3">
                            <h5 className="font-medium">{model.title || model.name}</h5>
                            <p className="text-sm text-gray-600 truncate">
                              {model.description}
                            </p>
                            <div className="flex gap-2 mt-2">
                              {model.platform && (
                                <Badge variant="outline">{model.platform}</Badge>
                              )}
                              {model.isFree ? (
                                <Badge variant="secondary">Безкоштовно</Badge>
                              ) : (
                                <Badge variant="default">${model.price}</Badge>
                              )}
                            </div>
                          </div>
                        ))}
                        {result.data.length > 5 && (
                          <p className="text-sm text-gray-500 text-center">
                            ... та ще {result.data.length - 5} моделей
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
