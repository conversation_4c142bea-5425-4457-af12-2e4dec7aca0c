'use client';

import DownloadButton from '@/components/marketplace/download-button';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { SplineScene } from '@/components/ui/splite';
import { LogIn, Pause, Play, RotateCw, Search, ZoomIn, ZoomOut } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

const HeroSection = () => {
  const [isRotating, setIsRotating] = useState(true);
  const router = useRouter();

  const handleToggleRotation = () => {
    setIsRotating(!isRotating);
  };

  return (
    <section className="relative bg-gradient-to-b from-primary/5 to-background pt-20 pb-24">
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center gap-12">
          <div className="lg:w-1/2 text-center lg:text-left">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
              Відкрийте світ 3D-моделей для ваших проектів
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto lg:mx-0">
              Знаходьте, купуйте та продавайте високоякісні 3D-моделі для ігор, архітектури, VR/AR та інших проектів. Наша платформа об'єднує талановитих 3D-художників та покупців з усього світу.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8 relative z-10">
              <Button size="lg">
                <Link href="/marketplace" className="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                  </svg>
                  Перейти до маркетплейсу
                </Link>
              </Button>
              <Button variant="outline" size="lg">
                <Link href="/auth/signin" className="flex items-center gap-2">
                  <LogIn className="h-5 w-5" />
                  Увійти
                </Link>
              </Button>
            </div>

            <form onSubmit={(e) => {
              e.preventDefault();
              const searchTerm = (e.target as HTMLFormElement).search.value;
              window.location.href = `/marketplace${searchTerm ? `?search=${encodeURIComponent(searchTerm)}` : ''}`;
            }} className="relative max-w-md mx-auto lg:mx-0">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-muted-foreground" />
              </div>
              <Input
                type="text"
                name="search"
                placeholder="Пошук 3D-моделей..."
                className="pl-10 py-6 text-base"
              />
              <Button type="submit" className="absolute right-1 top-1 bottom-1" variant="ghost" onClick={() => console.log('Search button clicked')}>
                Пошук
              </Button>
            </form>
          </div>

          <div className="lg:w-1/2 relative">
            <div className="relative w-full aspect-square max-w-lg mx-auto rounded-2xl overflow-hidden pointer-events-none">
              {/* 3D Model - Articulated Dragon */}
              <SplineScene
                scene="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
                preset="PRODUCT_VIEWER"
                config={{
                  backgroundColor: "transparent",
                  autoRotate: isRotating,
                  enableZoom: true,
                  enablePan: true
                }}
              />

              {/* Controls overlay */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center bg-background/80 backdrop-blur-sm rounded-full px-2 py-1 shadow-md pointer-events-auto">
                <Button variant="ghost" size="icon" onClick={handleToggleRotation} aria-label={isRotating ? "Pause rotation" : "Start rotation"}>
                  {isRotating ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
                <Button variant="ghost" size="icon" aria-label="Zoom in">
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" aria-label="Zoom out">
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" aria-label="Rotate">
                  <RotateCw className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Model info */}
            <div className="mt-4 text-center">
              <h3 className="text-lg font-semibold">Terminator T800 Armor</h3>
              <p className="text-sm text-muted-foreground">
                Detailed armor pieces for cosplay and display
              </p>
              <div className="flex items-center justify-center gap-2 mt-2 relative z-10">
                <Button variant="outline" size="sm">
                  <Link href="/models/printables_1286204_terminator_t800">View Details</Link>
                </Button>
                <DownloadButton
                  modelId="printables_1286204_terminator_t800"
                  modelName="Terminator T800 Armor"
                  isFree={true}
                  size="sm"
                />
              </div>
            </div>

            {/* Floating badges */}
            <div className="absolute top-10 -left-4 bg-background rounded-lg p-3 shadow-lg">
              <div className="flex items-center gap-2">
                <div className="bg-primary/10 rounded-full p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line></svg>
                </div>
                <div>
                  <div className="text-sm font-medium">10,000+ моделей</div>
                  <div className="text-xs text-muted-foreground">Висока якість</div>
                </div>
              </div>
            </div>

            <div className="absolute top-10 -right-4 bg-background rounded-lg p-3 shadow-lg">
              <div className="flex items-center gap-2">
                <div className="bg-primary/10 rounded-full p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
                </div>
                <div>
                  <div className="text-sm font-medium">5,000+ дизайнерів</div>
                  <div className="text-xs text-muted-foreground">Талановита спільнота</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Wave decoration */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="w-full h-auto">
          <path fill="currentColor" fillOpacity="0.05" d="M0,288L48,272C96,256,192,224,288,197.3C384,171,480,149,576,165.3C672,181,768,235,864,250.7C960,267,1056,245,1152,224C1248,203,1344,181,1392,170.7L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
        </svg>
      </div>
    </section>
  );
};

export default HeroSection;
