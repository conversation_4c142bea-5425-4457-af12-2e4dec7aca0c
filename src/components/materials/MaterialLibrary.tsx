'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
    AlertTriangle,
    Beaker,
    CheckCircle,
    Edit,
    Info,
    Layers,
    Package,
    Palette,
    Plus,
    Search,
    Shield,
    Star,
    Thermometer,
    Trash2,
    Zap
} from 'lucide-react';
import { useState } from 'react';

interface Material {
  id: string;
  name: string;
  type: 'PLA' | 'ABS' | 'PETG' | 'TPU' | 'ASA' | 'PC' | 'Nylon' | 'Wood' | 'Metal' | 'Resin';
  brand: string;
  color: string;
  hexColor: string;
  properties: {
    printTemp: { min: number; max: number; recommended: number };
    bedTemp: { min: number; max: number; recommended: number };
    speed: { min: number; max: number; recommended: number };
    retraction: { distance: number; speed: number };
    layerHeight: { min: number; max: number; recommended: number };
  };
  characteristics: {
    strength: number; // 1-10
    flexibility: number; // 1-10
    durability: number; // 1-10
    printability: number; // 1-10
    postProcessing: number; // 1-10
  };
  applications: string[];
  pros: string[];
  cons: string[];
  price: number;
  availability: 'in-stock' | 'low-stock' | 'out-of-stock';
  rating: number;
  reviews: number;
  description: string;
  safetyNotes?: string[];
  storageConditions?: string;
}

interface MaterialLibraryProps {
  materials?: Material[];
  onMaterialSelect?: (material: Material) => void;
  onAddMaterial?: (material: Partial<Material>) => void;
  onEditMaterial?: (id: string, material: Partial<Material>) => void;
  onDeleteMaterial?: (id: string) => void;
  className?: string;
}

const MOCK_MATERIALS: Material[] = [
  {
    id: 'pla_red_1',
    name: 'Premium PLA Red',
    type: 'PLA',
    brand: 'Prusament',
    color: 'Red',
    hexColor: '#dc2626',
    properties: {
      printTemp: { min: 190, max: 220, recommended: 210 },
      bedTemp: { min: 0, max: 70, recommended: 60 },
      speed: { min: 30, max: 100, recommended: 60 },
      retraction: { distance: 0.8, speed: 25 },
      layerHeight: { min: 0.1, max: 0.3, recommended: 0.2 }
    },
    characteristics: {
      strength: 6,
      flexibility: 4,
      durability: 5,
      printability: 9,
      postProcessing: 7
    },
    applications: ['Прототипи', 'Декоративні об\'єкти', 'Іграшки', 'Навчальні моделі'],
    pros: ['Легко друкувати', 'Без запаху', 'Біорозкладний', 'Яскраві кольори'],
    cons: ['Низька термостійкість', 'Крихкий при низьких температурах'],
    price: 25.99,
    availability: 'in-stock',
    rating: 4.8,
    reviews: 342,
    description: 'Високоякісний PLA філамент з відмінною якістю друку та яскравими кольорами.',
    storageConditions: 'Зберігати в сухому місці при температурі 15-25°C'
  },
  {
    id: 'abs_black_1',
    name: 'Industrial ABS Black',
    type: 'ABS',
    brand: 'Polymaker',
    color: 'Black',
    hexColor: '#1f2937',
    properties: {
      printTemp: { min: 220, max: 260, recommended: 240 },
      bedTemp: { min: 80, max: 110, recommended: 100 },
      speed: { min: 20, max: 80, recommended: 50 },
      retraction: { distance: 1.0, speed: 30 },
      layerHeight: { min: 0.1, max: 0.4, recommended: 0.25 }
    },
    characteristics: {
      strength: 8,
      flexibility: 6,
      durability: 9,
      printability: 6,
      postProcessing: 8
    },
    applications: ['Функціональні деталі', 'Автомобільні компоненти', 'Корпуси', 'Інструменти'],
    pros: ['Висока міцність', 'Термостійкий', 'Хімічна стійкість', 'Можна обробляти'],
    cons: ['Складний у друці', 'Потребує підігрів столу', 'Неприємний запах'],
    price: 32.50,
    availability: 'in-stock',
    rating: 4.5,
    reviews: 189,
    description: 'Промисловий ABS філамент для створення міцних функціональних деталей.',
    safetyNotes: ['Використовувати в провітрюваному приміщенні', 'Уникати вдихання парів'],
    storageConditions: 'Зберігати в герметичному контейнері з силікагелем'
  },
  {
    id: 'petg_clear_1',
    name: 'Crystal Clear PETG',
    type: 'PETG',
    brand: 'Overture',
    color: 'Clear',
    hexColor: '#f3f4f6',
    properties: {
      printTemp: { min: 220, max: 250, recommended: 235 },
      bedTemp: { min: 70, max: 90, recommended: 80 },
      speed: { min: 25, max: 70, recommended: 45 },
      retraction: { distance: 1.2, speed: 20 },
      layerHeight: { min: 0.1, max: 0.35, recommended: 0.2 }
    },
    characteristics: {
      strength: 7,
      flexibility: 5,
      durability: 8,
      printability: 7,
      postProcessing: 6
    },
    applications: ['Прозорі деталі', 'Контейнери', 'Захисні кожухи', 'Медичні пристрої'],
    pros: ['Прозорий', 'Хімічна стійкість', 'Харчова безпека', 'Легше за ABS'],
    cons: ['Схильний до стрінгінгу', 'Може прилипати до сопла'],
    price: 28.75,
    availability: 'low-stock',
    rating: 4.6,
    reviews: 156,
    description: 'Кристально прозорий PETG філамент для створення прозорих деталей.',
    storageConditions: 'Зберігати в сухому місці, уникати прямих сонячних променів'
  },
  {
    id: 'tpu_flexible_1',
    name: 'Flexible TPU Shore 95A',
    type: 'TPU',
    brand: 'NinjaFlex',
    color: 'Blue',
    hexColor: '#3b82f6',
    properties: {
      printTemp: { min: 210, max: 230, recommended: 220 },
      bedTemp: { min: 20, max: 60, recommended: 40 },
      speed: { min: 10, max: 30, recommended: 20 },
      retraction: { distance: 0.5, speed: 15 },
      layerHeight: { min: 0.1, max: 0.3, recommended: 0.2 }
    },
    characteristics: {
      strength: 5,
      flexibility: 10,
      durability: 7,
      printability: 4,
      postProcessing: 5
    },
    applications: ['Гнучкі деталі', 'Ущільнювачі', 'Іграшки', 'Спортивне спорядження'],
    pros: ['Дуже гнучкий', 'Стійкий до розтягування', 'Хімічна стійкість'],
    cons: ['Складний у друці', 'Повільний друк', 'Схильний до засмічення'],
    price: 45.00,
    availability: 'in-stock',
    rating: 4.2,
    reviews: 98,
    description: 'Гнучкий TPU філамент для створення еластичних деталей.',
    safetyNotes: ['Використовувати низькі швидкості друку'],
    storageConditions: 'Зберігати в сухому місці при кімнатній температурі'
  }
];

function MaterialCard({ 
  material, 
  onSelect, 
  onEdit, 
  onDelete 
}: { 
  material: Material;
  onSelect?: (material: Material) => void;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
}) {
  const getAvailabilityColor = () => {
    switch (material.availability) {
      case 'in-stock':
        return 'text-green-600';
      case 'low-stock':
        return 'text-yellow-600';
      case 'out-of-stock':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getAvailabilityIcon = () => {
    switch (material.availability) {
      case 'in-stock':
        return <CheckCircle className="w-4 h-4" />;
      case 'low-stock':
        return <AlertTriangle className="w-4 h-4" />;
      case 'out-of-stock':
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <Package className="w-4 h-4" />;
    }
  };

  return (
    <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={() => onSelect?.(material)}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div 
            className="w-6 h-6 rounded-full border-2 border-gray-300"
            style={{ backgroundColor: material.hexColor }}
          />
          <div>
            <h3 className="font-semibold">{material.name}</h3>
            <p className="text-sm text-muted-foreground">{material.brand}</p>
          </div>
        </div>
        <Badge variant="outline">{material.type}</Badge>
      </div>

      <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
        {material.description}
      </p>

      {/* Properties */}
      <div className="grid grid-cols-2 gap-2 mb-3 text-xs">
        <div className="flex items-center gap-1">
          <Thermometer className="w-3 h-3 text-red-500" />
          <span>{material.properties.printTemp.recommended}°C</span>
        </div>
        <div className="flex items-center gap-1">
          <Layers className="w-3 h-3 text-blue-500" />
          <span>{material.properties.bedTemp.recommended}°C</span>
        </div>
        <div className="flex items-center gap-1">
          <Zap className="w-3 h-3 text-yellow-500" />
          <span>{material.properties.speed.recommended}mm/s</span>
        </div>
        <div className="flex items-center gap-1">
          <Shield className="w-3 h-3 text-green-500" />
          <span>Міцність: {material.characteristics.strength}/10</span>
        </div>
      </div>

      {/* Rating and Price */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-1">
          <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
          <span className="text-sm font-medium">{material.rating}</span>
          <span className="text-xs text-muted-foreground">({material.reviews})</span>
        </div>
        <span className="font-semibold">${material.price}</span>
      </div>

      {/* Availability */}
      <div className={`flex items-center gap-1 mb-3 text-sm ${getAvailabilityColor()}`}>
        {getAvailabilityIcon()}
        <span className="capitalize">{material.availability.replace('-', ' ')}</span>
      </div>

      {/* Applications */}
      <div className="flex flex-wrap gap-1 mb-3">
        {material.applications.slice(0, 2).map((app, index) => (
          <Badge key={index} variant="secondary" className="text-xs">
            {app}
          </Badge>
        ))}
        {material.applications.length > 2 && (
          <Badge variant="secondary" className="text-xs">
            +{material.applications.length - 2}
          </Badge>
        )}
      </div>

      {/* Actions */}
      <div className="flex gap-2">
        <Button size="sm" variant="outline" className="flex-1">
          <Info className="w-4 h-4" />
          Деталі
        </Button>
        <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); onEdit?.(material.id); }}>
          <Edit className="w-4 h-4" />
        </Button>
        <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); onDelete?.(material.id); }}>
          <Trash2 className="w-4 h-4" />
        </Button>
      </div>
    </Card>
  );
}

function MaterialDetails({ material }: { material: Material }) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start gap-4">
        <div 
          className="w-16 h-16 rounded-lg border-2 border-gray-300"
          style={{ backgroundColor: material.hexColor }}
        />
        <div className="flex-1">
          <h2 className="text-2xl font-bold">{material.name}</h2>
          <p className="text-muted-foreground">{material.brand} • {material.type}</p>
          <div className="flex items-center gap-2 mt-2">
            <div className="flex items-center gap-1">
              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
              <span className="font-medium">{material.rating}</span>
              <span className="text-muted-foreground">({material.reviews} відгуків)</span>
            </div>
            <Badge variant="outline">${material.price}</Badge>
          </div>
        </div>
      </div>

      {/* Properties */}
      <Card className="p-4">
        <h3 className="font-semibold mb-4">Параметри друку</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <label className="text-sm font-medium">Температура сопла</label>
            <p className="text-lg">{material.properties.printTemp.recommended}°C</p>
            <p className="text-xs text-muted-foreground">
              ({material.properties.printTemp.min}-{material.properties.printTemp.max}°C)
            </p>
          </div>
          <div>
            <label className="text-sm font-medium">Температура столу</label>
            <p className="text-lg">{material.properties.bedTemp.recommended}°C</p>
            <p className="text-xs text-muted-foreground">
              ({material.properties.bedTemp.min}-{material.properties.bedTemp.max}°C)
            </p>
          </div>
          <div>
            <label className="text-sm font-medium">Швидкість друку</label>
            <p className="text-lg">{material.properties.speed.recommended} mm/s</p>
            <p className="text-xs text-muted-foreground">
              ({material.properties.speed.min}-{material.properties.speed.max} mm/s)
            </p>
          </div>
          <div>
            <label className="text-sm font-medium">Ретракція</label>
            <p className="text-lg">{material.properties.retraction.distance}mm</p>
            <p className="text-xs text-muted-foreground">
              {material.properties.retraction.speed} mm/s
            </p>
          </div>
          <div>
            <label className="text-sm font-medium">Висота шару</label>
            <p className="text-lg">{material.properties.layerHeight.recommended}mm</p>
            <p className="text-xs text-muted-foreground">
              ({material.properties.layerHeight.min}-{material.properties.layerHeight.max}mm)
            </p>
          </div>
        </div>
      </Card>

      {/* Characteristics */}
      <Card className="p-4">
        <h3 className="font-semibold mb-4">Характеристики</h3>
        <div className="space-y-3">
          {Object.entries(material.characteristics).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between">
              <span className="capitalize">{key}</span>
              <div className="flex items-center gap-2">
                <div className="w-24 h-2 bg-muted rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-primary transition-all"
                    style={{ width: `${value * 10}%` }}
                  />
                </div>
                <span className="text-sm font-medium">{value}/10</span>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Applications */}
      <Card className="p-4">
        <h3 className="font-semibold mb-4">Застосування</h3>
        <div className="flex flex-wrap gap-2">
          {material.applications.map((app, index) => (
            <Badge key={index} variant="secondary">
              {app}
            </Badge>
          ))}
        </div>
      </Card>

      {/* Pros and Cons */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="p-4">
          <h3 className="font-semibold mb-4 text-green-600">Переваги</h3>
          <ul className="space-y-2">
            {material.pros.map((pro, index) => (
              <li key={index} className="flex items-center gap-2 text-sm">
                <CheckCircle className="w-4 h-4 text-green-500" />
                {pro}
              </li>
            ))}
          </ul>
        </Card>

        <Card className="p-4">
          <h3 className="font-semibold mb-4 text-red-600">Недоліки</h3>
          <ul className="space-y-2">
            {material.cons.map((con, index) => (
              <li key={index} className="flex items-center gap-2 text-sm">
                <AlertTriangle className="w-4 h-4 text-red-500" />
                {con}
              </li>
            ))}
          </ul>
        </Card>
      </div>

      {/* Safety and Storage */}
      {(material.safetyNotes || material.storageConditions) && (
        <Card className="p-4">
          <h3 className="font-semibold mb-4">Безпека та зберігання</h3>
          {material.safetyNotes && (
            <div className="mb-4">
              <h4 className="font-medium mb-2 text-orange-600">Заходи безпеки:</h4>
              <ul className="space-y-1">
                {material.safetyNotes.map((note, index) => (
                  <li key={index} className="flex items-center gap-2 text-sm">
                    <AlertTriangle className="w-4 h-4 text-orange-500" />
                    {note}
                  </li>
                ))}
              </ul>
            </div>
          )}
          {material.storageConditions && (
            <div>
              <h4 className="font-medium mb-2 text-blue-600">Умови зберігання:</h4>
              <p className="text-sm flex items-center gap-2">
                <Package className="w-4 h-4 text-blue-500" />
                {material.storageConditions}
              </p>
            </div>
          )}
        </Card>
      )}
    </div>
  );
}

export default function MaterialLibrary({
  materials = MOCK_MATERIALS,
  onMaterialSelect,
  onAddMaterial,
  onEditMaterial,
  onDeleteMaterial,
  className = ""
}: MaterialLibraryProps) {
  const [selectedMaterial, setSelectedMaterial] = useState<Material | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [activeTab, setActiveTab] = useState('library');

  const filteredMaterials = materials.filter(material => {
    const matchesSearch = material.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         material.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         material.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'all' || material.type === filterType;
    return matchesSearch && matchesFilter;
  });

  const materialTypes = Array.from(new Set(materials.map(m => m.type)));

  const handleMaterialSelect = (material: Material) => {
    setSelectedMaterial(material);
    onMaterialSelect?.(material);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Бібліотека матеріалів</h2>
          <p className="text-muted-foreground">
            {materials.length} матеріалів • {filteredMaterials.length} відображено
          </p>
        </div>
        <Button onClick={() => onAddMaterial?.({})}>
          <Plus className="w-4 h-4" />
          Додати матеріал
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="library">Бібліотека</TabsTrigger>
          <TabsTrigger value="details">Деталі</TabsTrigger>
          <TabsTrigger value="compare">Порівняння</TabsTrigger>
        </TabsList>

        <TabsContent value="library" className="space-y-6">
          {/* Search and Filter */}
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Пошук матеріалів..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Всі типи</option>
              {materialTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          {/* Materials Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredMaterials.map((material) => (
              <MaterialCard
                key={material.id}
                material={material}
                onSelect={handleMaterialSelect}
                onEdit={(id) => onEditMaterial?.(id, {})}
                onDelete={onDeleteMaterial}
              />
            ))}
          </div>

          {filteredMaterials.length === 0 && (
            <Card className="p-8 text-center">
              <Beaker className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">Матеріали не знайдено</h3>
              <p className="text-muted-foreground">
                Спробуйте змінити параметри пошуку або додайте новий матеріал
              </p>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="details" className="space-y-6">
          {selectedMaterial ? (
            <MaterialDetails material={selectedMaterial} />
          ) : (
            <Card className="p-8 text-center">
              <Palette className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">Виберіть матеріал</h3>
              <p className="text-muted-foreground">
                Оберіть матеріал з бібліотеки для перегляду детальної інформації
              </p>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="compare" className="space-y-6">
          <Card className="p-8 text-center">
            <Beaker className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">Порівняння матеріалів</h3>
            <p className="text-muted-foreground">
              Функція порівняння матеріалів буде доступна незабаром
            </p>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
