'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
    Activity,
    Calendar,
    Download,
    Edit,
    Eye,
    FolderOpen,
    MessageSquare,
    MoreHorizontal,
    Plus,
    Search,
    Share2,
    Star
} from 'lucide-react';
import { useState } from 'react';

interface Project {
  id: string;
  name: string;
  description: string;
  status: 'planning' | 'in-progress' | 'review' | 'completed' | 'archived';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  progress: number;
  createdAt: string;
  updatedAt: string;
  dueDate?: string;
  owner: {
    id: string;
    name: string;
    avatar?: string;
  };
  collaborators: Array<{
    id: string;
    name: string;
    avatar?: string;
    role: 'owner' | 'editor' | 'viewer';
  }>;
  models: Array<{
    id: string;
    name: string;
    version: string;
    status: 'draft' | 'review' | 'approved';
  }>;
  tags: string[];
  stats: {
    views: number;
    downloads: number;
    comments: number;
    likes: number;
  };
}

interface ProjectManagerProps {
  projects?: Project[];
  onProjectSelect?: (project: Project) => void;
  onCreateProject?: (project: Partial<Project>) => void;
  onUpdateProject?: (id: string, project: Partial<Project>) => void;
  onDeleteProject?: (id: string) => void;
  className?: string;
}

const MOCK_PROJECTS: Project[] = [
  {
    id: 'proj_1',
    name: 'Articulated Dragon Collection',
    description: 'Серія рухомих драконів різних розмірів та стилів для колекціонерів та любителів фентезі.',
    status: 'in-progress',
    priority: 'high',
    progress: 75,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
    dueDate: '2024-02-15T23:59:59Z',
    owner: {
      id: 'user_1',
      name: 'Олександр Петренко',
      avatar: '/avatars/alex.jpg'
    },
    collaborators: [
      { id: 'user_1', name: 'Олександр Петренко', role: 'owner' },
      { id: 'user_2', name: 'Марія Іваненко', role: 'editor' },
      { id: 'user_3', name: 'Дмитро Коваленко', role: 'viewer' }
    ],
    models: [
      { id: 'model_1', name: 'Fire Dragon', version: '2.1', status: 'approved' },
      { id: 'model_2', name: 'Ice Dragon', version: '1.5', status: 'review' },
      { id: 'model_3', name: 'Earth Dragon', version: '1.0', status: 'draft' }
    ],
    tags: ['fantasy', 'articulated', 'collection', 'dragons'],
    stats: {
      views: 1250,
      downloads: 340,
      comments: 45,
      likes: 89
    }
  },
  {
    id: 'proj_2',
    name: 'Functional Phone Accessories',
    description: 'Практичні аксесуари для смартфонів: підставки, тримачі, захисні кейси.',
    status: 'review',
    priority: 'medium',
    progress: 90,
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-22T16:45:00Z',
    dueDate: '2024-01-30T23:59:59Z',
    owner: {
      id: 'user_2',
      name: 'Марія Іваненко',
      avatar: '/avatars/maria.jpg'
    },
    collaborators: [
      { id: 'user_2', name: 'Марія Іваненко', role: 'owner' },
      { id: 'user_4', name: 'Андрій Сидоренко', role: 'editor' }
    ],
    models: [
      { id: 'model_4', name: 'Universal Phone Stand', version: '3.0', status: 'approved' },
      { id: 'model_5', name: 'Car Mount Holder', version: '2.2', status: 'approved' },
      { id: 'model_6', name: 'Wireless Charging Stand', version: '1.8', status: 'review' }
    ],
    tags: ['functional', 'phone', 'accessories', 'practical'],
    stats: {
      views: 890,
      downloads: 234,
      comments: 28,
      likes: 67
    }
  },
  {
    id: 'proj_3',
    name: 'Miniature Terrain Set',
    description: 'Набір мініатюрного ландшафту для настільних ігор та діорам.',
    status: 'planning',
    priority: 'low',
    progress: 25,
    createdAt: '2024-01-20T11:30:00Z',
    updatedAt: '2024-01-22T10:15:00Z',
    owner: {
      id: 'user_3',
      name: 'Дмитро Коваленко',
      avatar: '/avatars/dmitro.jpg'
    },
    collaborators: [
      { id: 'user_3', name: 'Дмитро Коваленко', role: 'owner' }
    ],
    models: [
      { id: 'model_7', name: 'Stone Bridge', version: '1.0', status: 'draft' },
      { id: 'model_8', name: 'Ancient Ruins', version: '0.5', status: 'draft' }
    ],
    tags: ['miniature', 'terrain', 'tabletop', 'gaming'],
    stats: {
      views: 156,
      downloads: 23,
      comments: 8,
      likes: 12
    }
  }
];

function ProjectCard({ 
  project, 
  onSelect, 
  onEdit, 
  onDelete 
}: { 
  project: Project;
  onSelect?: (project: Project) => void;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
}) {
  const getStatusColor = () => {
    switch (project.status) {
      case 'planning':
        return 'bg-gray-500';
      case 'in-progress':
        return 'bg-blue-500';
      case 'review':
        return 'bg-yellow-500';
      case 'completed':
        return 'bg-green-500';
      case 'archived':
        return 'bg-gray-400';
      default:
        return 'bg-gray-500';
    }
  };

  const getPriorityColor = () => {
    switch (project.priority) {
      case 'urgent':
        return 'text-red-600 bg-red-50';
      case 'high':
        return 'text-orange-600 bg-orange-50';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50';
      case 'low':
        return 'text-green-600 bg-green-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('uk-UA', {
      day: 'numeric',
      month: 'short'
    });
  };

  const getDaysUntilDue = () => {
    if (!project.dueDate) return null;
    const now = new Date();
    const due = new Date(project.dueDate);
    const diffTime = due.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysUntilDue = getDaysUntilDue();

  return (
    <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={() => onSelect?.(project)}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${getStatusColor()}`} />
          <h3 className="font-semibold line-clamp-1">{project.name}</h3>
        </div>
        <div className="flex items-center gap-1">
          <Badge variant="outline" className={getPriorityColor()}>
            {project.priority}
          </Badge>
          <Button size="sm" variant="ghost" onClick={(e: React.MouseEvent) => { e.stopPropagation(); }}>
            <MoreHorizontal className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
        {project.description}
      </p>

      {/* Progress */}
      <div className="mb-3">
        <div className="flex justify-between text-sm mb-1">
          <span>Прогрес</span>
          <span>{project.progress}%</span>
        </div>
        <Progress value={project.progress} className="h-2" />
      </div>

      {/* Collaborators */}
      <div className="flex items-center gap-2 mb-3">
        <div className="flex -space-x-2">
          {project.collaborators.slice(0, 3).map((collaborator, index) => (
            <Avatar key={collaborator.id} className="w-6 h-6 border-2 border-background">
              <AvatarImage src={collaborator.avatar} />
              <AvatarFallback className="text-xs">
                {collaborator.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
          ))}
          {project.collaborators.length > 3 && (
            <div className="w-6 h-6 rounded-full bg-muted border-2 border-background flex items-center justify-center text-xs">
              +{project.collaborators.length - 3}
            </div>
          )}
        </div>
        <span className="text-xs text-muted-foreground">
          {project.collaborators.length} учасників
        </span>
      </div>

      {/* Models */}
      <div className="mb-3">
        <div className="flex items-center gap-2 text-sm">
          <FolderOpen className="w-4 h-4" />
          <span>{project.models.length} моделей</span>
          <div className="flex gap-1">
            {project.models.filter(m => m.status === 'approved').length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {project.models.filter(m => m.status === 'approved').length} готових
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-4 gap-2 mb-3 text-xs text-muted-foreground">
        <div className="flex items-center gap-1">
          <Eye className="w-3 h-3" />
          <span>{project.stats.views}</span>
        </div>
        <div className="flex items-center gap-1">
          <Download className="w-3 h-3" />
          <span>{project.stats.downloads}</span>
        </div>
        <div className="flex items-center gap-1">
          <MessageSquare className="w-3 h-3" />
          <span>{project.stats.comments}</span>
        </div>
        <div className="flex items-center gap-1">
          <Star className="w-3 h-3" />
          <span>{project.stats.likes}</span>
        </div>
      </div>

      {/* Due Date */}
      {project.dueDate && (
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-1 text-muted-foreground">
            <Calendar className="w-3 h-3" />
            <span>До {formatDate(project.dueDate)}</span>
          </div>
          {daysUntilDue !== null && (
            <span className={`font-medium ${
              daysUntilDue < 0 ? 'text-red-600' : 
              daysUntilDue < 3 ? 'text-yellow-600' : 
              'text-green-600'
            }`}>
              {daysUntilDue < 0 ? `${Math.abs(daysUntilDue)} днів тому` : 
               daysUntilDue === 0 ? 'Сьогодні' : 
               `${daysUntilDue} днів`}
            </span>
          )}
        </div>
      )}

      {/* Tags */}
      <div className="flex flex-wrap gap-1 mt-3">
        {project.tags.slice(0, 3).map((tag, index) => (
          <Badge key={index} variant="outline" className="text-xs">
            {tag}
          </Badge>
        ))}
        {project.tags.length > 3 && (
          <Badge variant="outline" className="text-xs">
            +{project.tags.length - 3}
          </Badge>
        )}
      </div>
    </Card>
  );
}

function ProjectDetails({ project }: { project: Project }) {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div>
          <h2 className="text-2xl font-bold">{project.name}</h2>
          <p className="text-muted-foreground mt-1">{project.description}</p>
          <div className="flex items-center gap-4 mt-3">
            <Badge variant="outline" className="capitalize">
              {project.status.replace('-', ' ')}
            </Badge>
            <Badge variant="outline" className="capitalize">
              {project.priority} priority
            </Badge>
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Calendar className="w-4 h-4" />
              <span>Створено {new Date(project.createdAt).toLocaleDateString('uk-UA')}</span>
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Button size="sm" variant="outline">
            <Edit className="w-4 h-4" />
            Редагувати
          </Button>
          <Button size="sm" variant="outline">
            <Share2 className="w-4 h-4" />
            Поділитися
          </Button>
        </div>
      </div>

      {/* Progress */}
      <Card className="p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-semibold">Прогрес проекту</h3>
          <span className="text-lg font-bold">{project.progress}%</span>
        </div>
        <Progress value={project.progress} className="h-3" />
      </Card>

      {/* Collaborators */}
      <Card className="p-4">
        <h3 className="font-semibold mb-4">Учасники проекту</h3>
        <div className="space-y-3">
          {project.collaborators.map((collaborator) => (
            <div key={collaborator.id} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarImage src={collaborator.avatar} />
                  <AvatarFallback>
                    {collaborator.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium">{collaborator.name}</p>
                  <p className="text-sm text-muted-foreground capitalize">{collaborator.role}</p>
                </div>
              </div>
              <Button size="sm" variant="outline">
                <MessageSquare className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      </Card>

      {/* Models */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold">Моделі проекту</h3>
          <Button size="sm">
            <Plus className="w-4 h-4" />
            Додати модель
          </Button>
        </div>
        <div className="space-y-3">
          {project.models.map((model) => (
            <div key={model.id} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-3">
                <FolderOpen className="w-5 h-5 text-primary" />
                <div>
                  <p className="font-medium">{model.name}</p>
                  <p className="text-sm text-muted-foreground">Версія {model.version}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="capitalize">
                  {model.status}
                </Badge>
                <Button size="sm" variant="outline">
                  <Eye className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Statistics */}
      <Card className="p-4">
        <h3 className="font-semibold mb-4">Статистика</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold">{project.stats.views}</div>
            <div className="text-sm text-muted-foreground">Переглядів</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{project.stats.downloads}</div>
            <div className="text-sm text-muted-foreground">Завантажень</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{project.stats.comments}</div>
            <div className="text-sm text-muted-foreground">Коментарів</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold">{project.stats.likes}</div>
            <div className="text-sm text-muted-foreground">Лайків</div>
          </div>
        </div>
      </Card>
    </div>
  );
}

export default function ProjectManager({
  projects = MOCK_PROJECTS,
  onProjectSelect,
  onCreateProject,
  onUpdateProject,
  onDeleteProject,
  className = ""
}: ProjectManagerProps) {
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [activeTab, setActiveTab] = useState('projects');

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesFilter = filterStatus === 'all' || project.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const statusOptions = ['all', 'planning', 'in-progress', 'review', 'completed', 'archived'];

  const handleProjectSelect = (project: Project) => {
    setSelectedProject(project);
    onProjectSelect?.(project);
  };

  const projectStats = {
    total: projects.length,
    inProgress: projects.filter(p => p.status === 'in-progress').length,
    completed: projects.filter(p => p.status === 'completed').length,
    overdue: projects.filter(p => {
      if (!p.dueDate) return false;
      return new Date(p.dueDate) < new Date() && p.status !== 'completed';
    }).length
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Управління проектами</h2>
          <p className="text-muted-foreground">
            {projectStats.total} проектів • {projectStats.inProgress} в роботі • {projectStats.completed} завершено
          </p>
        </div>
        <Button onClick={() => onCreateProject?.({})}>
          <Plus className="w-4 h-4" />
          Новий проект
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="projects">Проекти</TabsTrigger>
          <TabsTrigger value="details">Деталі</TabsTrigger>
          <TabsTrigger value="activity">Активність</TabsTrigger>
        </TabsList>

        <TabsContent value="projects" className="space-y-6">
          {/* Search and Filter */}
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Пошук проектів..."
                value={searchTerm}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border rounded-md bg-background"
            >
              <option value="all">Всі статуси</option>
              {statusOptions.slice(1).map(status => (
                <option key={status} value={status}>
                  {status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
                </option>
              ))}
            </select>
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredProjects.map((project) => (
              <ProjectCard
                key={project.id}
                project={project}
                onSelect={handleProjectSelect}
                onEdit={(id) => onUpdateProject?.(id, {})}
                onDelete={onDeleteProject}
              />
            ))}
          </div>

          {filteredProjects.length === 0 && (
            <Card className="p-8 text-center">
              <FolderOpen className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">Проекти не знайдено</h3>
              <p className="text-muted-foreground">
                Спробуйте змінити параметри пошуку або створіть новий проект
              </p>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="details" className="space-y-6">
          {selectedProject ? (
            <ProjectDetails project={selectedProject} />
          ) : (
            <Card className="p-8 text-center">
              <FolderOpen className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">Виберіть проект</h3>
              <p className="text-muted-foreground">
                Оберіть проект зі списку для перегляду детальної інформації
              </p>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          <Card className="p-8 text-center">
            <Activity className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">Активність проектів</h3>
            <p className="text-muted-foreground">
              Тут буде відображатися активність по всіх проектах
            </p>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
