'use client'

import { useState } from 'react'
import { SplineScene, SCENE_PRESETS } from "@/components/ui/splite"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { 
  Download, 
  Printer, 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  Pause, 
  Play,
  Info,
  Layers,
  Share2
} from "lucide-react"
import { cn } from '@/lib/utils'

interface ModelViewerProps {
  model: {
    id: string
    name: string
    description: string
    splineSceneUrl: string
    thumbnailUrl: string
    fileSize: string
    fileFormat: string
    category: string
    tags: string[]
    creator: {
      name: string
      avatarUrl: string
    }
    price: number | 'Free'
    downloadCount: number
    createdAt: string
  }
  className?: string
}

export function ModelViewer({ model, className }: ModelViewerProps) {
  const [isRotating, setIsRotating] = useState(true);
  const [activeTab, setActiveTab] = useState('preview');
  const [selectedPart, setSelectedPart] = useState<string | null>(null);
  
  const handleModelInteraction = (e: any) => {
    if (e.target) {
      setSelectedPart(e.target.name);
      
      // You could show specific information about that part
      if (e.target.name === "print_bed") {
        // Show printing dimensions
        console.log("Print bed selected - showing dimensions");
      } else if (e.target.name.includes("support")) {
        // Show support structure information
        console.log("Support structure selected - showing support info");
      }
    }
  };
  
  const toggleRotation = () => {
    setIsRotating(!isRotating);
  };
  
  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-2xl">{model.name}</CardTitle>
            <CardDescription className="mt-2">
              By <span className="font-medium">{model.creator.name}</span> • {model.downloadCount} downloads
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
            {model.price === 'Free' ? (
              <Button size="sm">
                <Download className="h-4 w-4 mr-2" />
                Download Free
              </Button>
            ) : (
              <Button size="sm">
                ${model.price} - Buy Now
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      
      <Tabs defaultValue="preview" onValueChange={setActiveTab}>
        <div className="px-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="preview">Preview</TabsTrigger>
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="print">Print Settings</TabsTrigger>
          </TabsList>
        </div>
        
        <TabsContent value="preview" className="m-0">
          <CardContent className="p-0 relative">
            <div className="h-[500px] relative">
              <SplineScene 
                scene={model.splineSceneUrl}
                preset="PRODUCT_VIEWER"
                config={{ 
                  backgroundColor: activeTab === 'preview' ? "#f5f5f5" : "transparent",
                  autoRotate: isRotating,
                  enableZoom: true,
                  enablePan: true
                }}
                onMouseDown={handleModelInteraction}
                className="w-full h-full"
              />
              
              {/* Controls overlay */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center bg-background/80 backdrop-blur-sm rounded-full px-2 py-1 shadow-md">
                <Button variant="ghost" size="icon" onClick={toggleRotation}>
                  {isRotating ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
                <Button variant="ghost" size="icon">
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon">
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon">
                  <RotateCw className="h-4 w-4" />
                </Button>
              </div>
              
              {/* Selected part info */}
              {selectedPart && (
                <div className="absolute top-4 right-4 bg-background/80 backdrop-blur-sm rounded-lg p-3 shadow-md max-w-xs">
                  <h4 className="font-medium flex items-center">
                    <Info className="h-4 w-4 mr-2" />
                    {selectedPart}
                  </h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    Click on different parts of the model to see more information.
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </TabsContent>
        
        <TabsContent value="details" className="m-0">
          <CardContent className="space-y-4 pt-6">
            <div>
              <h3 className="font-medium">Description</h3>
              <p className="text-muted-foreground mt-1">{model.description}</p>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium">File Details</h3>
                <ul className="mt-2 space-y-1 text-sm">
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">Format</span>
                    <span>{model.fileFormat}</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">Size</span>
                    <span>{model.fileSize}</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">Uploaded</span>
                    <span>{model.createdAt}</span>
                  </li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-medium">Categories & Tags</h3>
                <div className="mt-2">
                  <span className="inline-block bg-primary/10 text-primary rounded-full px-2.5 py-0.5 text-xs font-medium mr-2">
                    {model.category}
                  </span>
                  {model.tags.map((tag) => (
                    <span key={tag} className="inline-block bg-secondary/20 text-secondary-foreground rounded-full px-2.5 py-0.5 text-xs font-medium mr-2 mb-2">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </TabsContent>
        
        <TabsContent value="print" className="m-0">
          <CardContent className="space-y-4 pt-6">
            <div className="flex items-start space-x-4">
              <Layers className="h-5 w-5 text-primary mt-0.5" />
              <div>
                <h3 className="font-medium">Recommended Print Settings</h3>
                <ul className="mt-2 space-y-1 text-sm">
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">Layer Height</span>
                    <span>0.2mm</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">Infill</span>
                    <span>20%</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">Supports</span>
                    <span>Required</span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">Print Time (est.)</span>
                    <span>4h 30m</span>
                  </li>
                </ul>
              </div>
            </div>
            
            <div className="flex items-start space-x-4">
              <Printer className="h-5 w-5 text-primary mt-0.5" />
              <div>
                <h3 className="font-medium">Order a Print</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Don't have a 3D printer? Order a print from one of our trusted print services.
                </p>
                <Button className="mt-3" variant="outline">
                  Find Print Services
                </Button>
              </div>
            </div>
          </CardContent>
        </TabsContent>
      </Tabs>
      
      <CardFooter className="flex justify-between border-t p-6">
        <Button variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Download STL
        </Button>
        <Button>
          <Printer className="h-4 w-4 mr-2" />
          Order Print
        </Button>
      </CardFooter>
    </Card>
  );
}
