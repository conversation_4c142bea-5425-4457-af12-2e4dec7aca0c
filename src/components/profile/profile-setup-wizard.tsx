'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  User, 
  MapPin, 
  Globe, 
  Camera, 
  Briefcase, 
  Star,
  ChevronRight,
  ChevronLeft,
  Check
} from 'lucide-react';

interface ProfileSetupData {
  // Basic Info
  bio: string;
  location: string;
  website: string;
  
  // Seller Info (if applicable)
  businessName: string;
  businessType: 'individual' | 'company' | 'studio';
  specializations: string[];
  yearsExperience: number;
  portfolioDescription: string;
}

const SPECIALIZATION_OPTIONS = [
  'Miniatures', 'Functional Parts', 'Artistic Models', 'Jewelry', 
  'Architectural Models', 'Educational Models', 'Toys & Games',
  'Home Decor', 'Tools & Gadgets', 'Automotive Parts'
];

export default function ProfileSetupWizard() {
  const { data: session } = useSession();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  
  const [profileData, setProfileData] = useState<ProfileSetupData>({
    bio: '',
    location: '',
    website: '',
    businessName: '',
    businessType: 'individual',
    specializations: [],
    yearsExperience: 0,
    portfolioDescription: '',
  });

  const totalSteps = session?.user?.userType === 'seller' || session?.user?.userType === 'both' ? 3 : 2;
  const progress = (currentStep / totalSteps) * 100;

  const handleInputChange = (field: keyof ProfileSetupData, value: any) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSpecializationToggle = (specialization: string) => {
    setProfileData(prev => ({
      ...prev,
      specializations: prev.specializations.includes(specialization)
        ? prev.specializations.filter(s => s !== specialization)
        : [...prev.specializations, specialization]
    }));
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/users/profile/setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });

      if (response.ok) {
        router.push('/profile');
      } else {
        console.error('Failed to setup profile');
      }
    } catch (error) {
      console.error('Error setting up profile:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep1 = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Основна інформація
        </CardTitle>
        <CardDescription>
          Розкажіть про себе та додайте основну інформацію до профілю
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="bio">Про себе</Label>
          <Textarea
            id="bio"
            placeholder="Розкажіть про свої інтереси в 3D друці та моделюванні..."
            value={profileData.bio}
            onChange={(e) => handleInputChange('bio', e.target.value)}
            className="mt-1"
          />
        </div>
        
        <div>
          <Label htmlFor="location" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Місцезнаходження
          </Label>
          <Input
            id="location"
            placeholder="Київ, Україна"
            value={profileData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            className="mt-1"
          />
        </div>
        
        <div>
          <Label htmlFor="website" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Веб-сайт (необов'язково)
          </Label>
          <Input
            id="website"
            placeholder="https://your-website.com"
            value={profileData.website}
            onChange={(e) => handleInputChange('website', e.target.value)}
            className="mt-1"
          />
        </div>
      </CardContent>
    </Card>
  );

  const renderStep2 = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Camera className="h-5 w-5" />
          Налаштування профілю
        </CardTitle>
        <CardDescription>
          Завершіть налаштування свого профілю
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-center">
          <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
            <Camera className="h-8 w-8 text-gray-400" />
          </div>
          <Button variant="outline" size="sm">
            Завантажити фото профілю
          </Button>
        </div>
        
        <div className="text-sm text-gray-600 text-center">
          Ваш профіль майже готовий! Ви можете додати фото пізніше в налаштуваннях.
        </div>
      </CardContent>
    </Card>
  );

  const renderStep3 = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Briefcase className="h-5 w-5" />
          Інформація про продавця
        </CardTitle>
        <CardDescription>
          Налаштуйте свій профіль продавця для продажу 3D моделей
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="businessName">Назва бізнесу</Label>
          <Input
            id="businessName"
            placeholder="Ваша назва або назва компанії"
            value={profileData.businessName}
            onChange={(e) => handleInputChange('businessName', e.target.value)}
            className="mt-1"
          />
        </div>
        
        <div>
          <Label htmlFor="portfolioDescription">Опис портфоліо</Label>
          <Textarea
            id="portfolioDescription"
            placeholder="Опишіть свій досвід та спеціалізацію в 3D моделюванні..."
            value={profileData.portfolioDescription}
            onChange={(e) => handleInputChange('portfolioDescription', e.target.value)}
            className="mt-1"
          />
        </div>
        
        <div>
          <Label>Спеціалізації</Label>
          <div className="flex flex-wrap gap-2 mt-2">
            {SPECIALIZATION_OPTIONS.map((spec) => (
              <Badge
                key={spec}
                variant={profileData.specializations.includes(spec) ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => handleSpecializationToggle(spec)}
              >
                {spec}
              </Badge>
            ))}
          </div>
        </div>
        
        <div>
          <Label htmlFor="yearsExperience">Роки досвіду</Label>
          <Input
            id="yearsExperience"
            type="number"
            min="0"
            max="50"
            value={profileData.yearsExperience}
            onChange={(e) => handleInputChange('yearsExperience', parseInt(e.target.value) || 0)}
            className="mt-1"
          />
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Налаштування профілю</h1>
          <p className="mt-2 text-gray-600">
            Крок {currentStep} з {totalSteps}
          </p>
          <Progress value={progress} className="mt-4" />
        </div>

        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}

        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Назад
          </Button>
          
          {currentStep < totalSteps ? (
            <Button onClick={handleNext}>
              Далі
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button onClick={handleSubmit} disabled={isLoading}>
              {isLoading ? 'Збереження...' : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Завершити
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
