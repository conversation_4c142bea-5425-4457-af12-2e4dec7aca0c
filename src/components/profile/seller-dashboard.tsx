'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  DollarSign,
  Download,
  Star,
  TrendingUp,
  Package,
  Users,
  Award,
  Eye,
  MessageSquare,
  Calendar,
  BarChart3
} from 'lucide-react';

interface SellerStats {
  totalRevenue: number;
  totalSales: number;
  totalModels: number;
  totalDownloads: number;
  averageRating: number;
  totalReviews: number;
  sellerTier: 'bronze' | 'silver' | 'gold' | 'platinum';
  monthlyRevenue: number;
  monthlyGrowth: number;
}

interface RecentActivity {
  id: string;
  type: 'sale' | 'download' | 'review' | 'follow';
  description: string;
  amount?: number;
  date: string;
  modelName?: string;
  rating?: number;
}

export default function SellerDashboard() {
  const { data: session } = useSession();
  const [stats, setStats] = useState<SellerStats>({
    totalRevenue: 1250.75,
    totalSales: 89,
    totalModels: 24,
    totalDownloads: 1420,
    averageRating: 4.7,
    totalReviews: 67,
    sellerTier: 'silver',
    monthlyRevenue: 320.50,
    monthlyGrowth: 15.3,
  });

  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([
    {
      id: '1',
      type: 'sale',
      description: 'Продано модель "Dragon Miniature"',
      amount: 12.99,
      date: '2024-01-15T10:30:00Z',
      modelName: 'Dragon Miniature'
    },
    {
      id: '2',
      type: 'review',
      description: 'Новий відгук на модель "Functional Bracket"',
      rating: 5,
      date: '2024-01-15T09:15:00Z',
      modelName: 'Functional Bracket'
    },
    {
      id: '3',
      type: 'download',
      description: 'Завантажено безкоштовну модель "Sample Print"',
      date: '2024-01-14T16:45:00Z',
      modelName: 'Sample Print'
    },
  ]);

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'bronze': return 'bg-amber-100 text-amber-800';
      case 'silver': return 'bg-gray-100 text-gray-800';
      case 'gold': return 'bg-yellow-100 text-yellow-800';
      case 'platinum': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTierProgress = (tier: string) => {
    switch (tier) {
      case 'bronze': return 25;
      case 'silver': return 50;
      case 'gold': return 75;
      case 'platinum': return 100;
      default: return 0;
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'sale': return <DollarSign className="h-4 w-4 text-green-600" />;
      case 'download': return <Download className="h-4 w-4 text-blue-600" />;
      case 'review': return <MessageSquare className="h-4 w-4 text-purple-600" />;
      case 'follow': return <Users className="h-4 w-4 text-orange-600" />;
      default: return <Calendar className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Панель продавця</h1>
          <p className="text-gray-600">Керуйте своїми моделями та відстежуйте продажі</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge className={getTierColor(stats.sellerTier)}>
            <Award className="h-3 w-3 mr-1" />
            {stats.sellerTier.toUpperCase()}
          </Badge>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Загальний дохід</p>
                <p className="text-2xl font-bold text-gray-900">${stats.totalRevenue.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Продажі</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalSales}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Download className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Завантаження</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalDownloads}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Рейтинг</p>
                <p className="text-2xl font-bold text-gray-900">{stats.averageRating}</p>
                <p className="text-xs text-gray-500">({stats.totalReviews} відгуків)</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Seller Tier Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Рівень продавця
          </CardTitle>
          <CardDescription>
            Підвищуйте свій рівень, щоб отримати більше переваг
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Поточний рівень: {stats.sellerTier.toUpperCase()}</span>
              <span className="text-sm text-gray-500">Наступний: GOLD</span>
            </div>
            <Progress value={getTierProgress(stats.sellerTier)} className="h-2" />
            <div className="text-sm text-gray-600">
              Продайте ще 50 моделей або заробіть $500, щоб досягти рівня Gold
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for detailed views */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Огляд</TabsTrigger>
          <TabsTrigger value="analytics">Аналітика</TabsTrigger>
          <TabsTrigger value="reviews">Відгуки</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Monthly Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Місячна ефективність
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Дохід цього місяця</span>
                    <span className="font-semibold">${stats.monthlyRevenue.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Зростання</span>
                    <span className="font-semibold text-green-600">+{stats.monthlyGrowth}%</span>
                  </div>
                  <Progress value={65} className="h-2" />
                  <p className="text-xs text-gray-500">
                    65% від місячної цілі ($500)
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Остання активність</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-center gap-3">
                      {getActivityIcon(activity.type)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {activity.description}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(activity.date).toLocaleDateString('uk-UA')}
                        </p>
                      </div>
                      {activity.amount && (
                        <span className="text-sm font-semibold text-green-600">
                          +${activity.amount}
                        </span>
                      )}
                      {activity.rating && (
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 text-yellow-500 fill-current" />
                          <span className="text-xs">{activity.rating}</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Детальна аналітика
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Детальна аналітика продажів та ефективності буде доступна найближчим часом.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reviews">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Відгуки покупців
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Система управління відгуками буде доступна найближчим часом.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
