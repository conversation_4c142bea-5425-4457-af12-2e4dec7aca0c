'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import Image from 'next/image';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Heart,
  Search,
  Filter,
  DollarSign,
  Download,
  Star,
  Trash2,
  ShoppingCart,
  Bell,
  BellOff,
  Grid,
  List,
  SortAsc,
  Calendar
} from 'lucide-react';

interface WishlistItem {
  id: string;
  modelId: string;
  modelName: string;
  modelDescription: string;
  modelThumbnail: string;
  currentPrice: number;
  originalPrice?: number;
  authorName: string;
  authorId: string;
  category: string;
  rating: number;
  downloadCount: number;
  addedDate: string;
  priority: 1 | 2 | 3; // 1=low, 2=medium, 3=high
  priceAlertEnabled: boolean;
  targetPrice?: number;
  notes?: string;
}

export default function WishlistManager() {
  const { data: session } = useSession();
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([
    {
      id: '1',
      modelId: 'model-1',
      modelName: 'Dragon Miniature Set',
      modelDescription: 'Detailed fantasy dragon miniature perfect for tabletop gaming',
      modelThumbnail: '/api/placeholder/300/300',
      currentPrice: 15.99,
      originalPrice: 19.99,
      authorName: 'FantasyCreator',
      authorId: 'user-1',
      category: 'Miniatures',
      rating: 4.8,
      downloadCount: 1250,
      addedDate: '2024-01-10T10:00:00Z',
      priority: 3,
      priceAlertEnabled: true,
      targetPrice: 12.00,
      notes: 'Wait for sale'
    },
    {
      id: '2',
      modelId: 'model-2',
      modelName: 'Functional Phone Stand',
      modelDescription: 'Adjustable phone stand with cable management',
      modelThumbnail: '/api/placeholder/300/300',
      currentPrice: 8.50,
      authorName: 'TechDesigner',
      authorId: 'user-2',
      category: 'Functional',
      rating: 4.6,
      downloadCount: 890,
      addedDate: '2024-01-08T14:30:00Z',
      priority: 2,
      priceAlertEnabled: false,
    },
  ]);

  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('dateAdded');
  const [filterPriority, setFilterPriority] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const filteredItems = wishlistItems.filter(item => {
    const matchesSearch = item.modelName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.authorName.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesPriority = filterPriority === 'all' || item.priority.toString() === filterPriority;
    return matchesSearch && matchesPriority;
  });

  const sortedItems = [...filteredItems].sort((a, b) => {
    switch (sortBy) {
      case 'dateAdded':
        return new Date(b.addedDate).getTime() - new Date(a.addedDate).getTime();
      case 'price':
        return a.currentPrice - b.currentPrice;
      case 'priority':
        return b.priority - a.priority;
      case 'name':
        return a.modelName.localeCompare(b.modelName);
      default:
        return 0;
    }
  });

  const handleRemoveFromWishlist = async (itemId: string) => {
    setWishlistItems(prev => prev.filter(item => item.id !== itemId));
    // In real implementation, make API call to remove from wishlist
  };

  const handleTogglePriceAlert = async (itemId: string) => {
    setWishlistItems(prev => prev.map(item => 
      item.id === itemId 
        ? { ...item, priceAlertEnabled: !item.priceAlertEnabled }
        : item
    ));
    // In real implementation, make API call to update price alert
  };

  const getPriorityColor = (priority: number) => {
    switch (priority) {
      case 1: return 'bg-gray-100 text-gray-800';
      case 2: return 'bg-yellow-100 text-yellow-800';
      case 3: return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityLabel = (priority: number) => {
    switch (priority) {
      case 1: return 'Низький';
      case 2: return 'Середній';
      case 3: return 'Високий';
      default: return 'Невідомо';
    }
  };

  const WishlistCard = ({ item }: { item: WishlistItem }) => (
    <Card className="group hover:shadow-lg transition-shadow">
      <div className="relative">
        <Image
          src={item.modelThumbnail}
          alt={item.modelName}
          width={300}
          height={200}
          className="w-full h-48 object-cover rounded-t-lg"
        />
        <div className="absolute top-2 right-2 flex gap-2">
          <Badge className={getPriorityColor(item.priority)}>
            {getPriorityLabel(item.priority)}
          </Badge>
          {item.originalPrice && item.currentPrice < item.originalPrice && (
            <Badge className="bg-green-100 text-green-800">
              Знижка
            </Badge>
          )}
        </div>
      </div>
      
      <CardContent className="p-4">
        <div className="space-y-2">
          <h3 className="font-semibold text-lg line-clamp-2">{item.modelName}</h3>
          <p className="text-sm text-gray-600 line-clamp-2">{item.modelDescription}</p>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="font-bold text-lg">${item.currentPrice}</span>
              {item.originalPrice && item.currentPrice < item.originalPrice && (
                <span className="text-sm text-gray-500 line-through">
                  ${item.originalPrice}
                </span>
              )}
            </div>
            <div className="flex items-center gap-1">
              <Star className="h-4 w-4 text-yellow-500 fill-current" />
              <span className="text-sm">{item.rating}</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>by {item.authorName}</span>
            <div className="flex items-center gap-1">
              <Download className="h-3 w-3" />
              {item.downloadCount}
            </div>
          </div>
          
          {item.priceAlertEnabled && item.targetPrice && (
            <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded">
              <Bell className="h-3 w-3 inline mr-1" />
              Сповіщення при ціні ${item.targetPrice}
            </div>
          )}
          
          <div className="flex gap-2 pt-2">
            <Button size="sm" className="flex-1">
              <ShoppingCart className="h-4 w-4 mr-2" />
              Купити
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleTogglePriceAlert(item.id)}
            >
              {item.priceAlertEnabled ? (
                <BellOff className="h-4 w-4" />
              ) : (
                <Bell className="h-4 w-4" />
              )}
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleRemoveFromWishlist(item.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const WishlistListItem = ({ item }: { item: WishlistItem }) => (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          <Image
            src={item.modelThumbnail}
            alt={item.modelName}
            width={80}
            height={80}
            className="w-20 h-20 object-cover rounded"
          />
          
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold truncate">{item.modelName}</h3>
            <p className="text-sm text-gray-600 truncate">{item.modelDescription}</p>
            <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
              <span>by {item.authorName}</span>
              <div className="flex items-center gap-1">
                <Star className="h-3 w-3 text-yellow-500 fill-current" />
                {item.rating}
              </div>
              <div className="flex items-center gap-1">
                <Download className="h-3 w-3" />
                {item.downloadCount}
              </div>
            </div>
          </div>
          
          <div className="text-right">
            <div className="font-bold text-lg">${item.currentPrice}</div>
            {item.originalPrice && item.currentPrice < item.originalPrice && (
              <div className="text-sm text-gray-500 line-through">
                ${item.originalPrice}
              </div>
            )}
            <Badge className={getPriorityColor(item.priority)} size="sm">
              {getPriorityLabel(item.priority)}
            </Badge>
          </div>
          
          <div className="flex flex-col gap-2">
            <Button size="sm">
              <ShoppingCart className="h-4 w-4 mr-2" />
              Купити
            </Button>
            <div className="flex gap-1">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleTogglePriceAlert(item.id)}
              >
                {item.priceAlertEnabled ? (
                  <BellOff className="h-4 w-4" />
                ) : (
                  <Bell className="h-4 w-4" />
                )}
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleRemoveFromWishlist(item.id)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Heart className="h-8 w-8 text-red-500" />
            Список бажань
          </h1>
          <p className="text-gray-600">{wishlistItems.length} моделей у вашому списку бажань</p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Пошук моделей..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <select
              value={filterPriority}
              onChange={(e) => setFilterPriority(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="all">Всі пріоритети</option>
              <option value="3">Високий</option>
              <option value="2">Середній</option>
              <option value="1">Низький</option>
            </select>
            
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="dateAdded">За датою додавання</option>
              <option value="price">За ціною</option>
              <option value="priority">За пріоритетом</option>
              <option value="name">За назвою</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Wishlist Items */}
      {sortedItems.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Ваш список бажань порожній
            </h3>
            <p className="text-gray-600 mb-4">
              Додайте моделі до списку бажань, щоб відстежувати їх та отримувати сповіщення про знижки
            </p>
            <Link href="/marketplace">
              <Button>
                Переглянути моделі
              </Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
            : 'space-y-4'
        }>
          {sortedItems.map((item) => (
            viewMode === 'grid' ? (
              <WishlistCard key={item.id} item={item} />
            ) : (
              <WishlistListItem key={item.id} item={item} />
            )
          ))}
        </div>
      )}
    </div>
  );
}
