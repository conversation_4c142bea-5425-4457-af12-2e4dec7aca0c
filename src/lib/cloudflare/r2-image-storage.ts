/**
 * Cloudflare R2 Image Storage - Система зберігання зображень в R2
 * Підтримує завантаження, оптимізацію та CDN доступ
 */

export interface R2ImageUploadOptions {
  bucket?: string;
  folder?: string;
  optimize?: boolean;
  generateThumbnails?: boolean;
  formats?: ('webp' | 'avif' | 'jpeg')[];
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export interface R2ImageMetadata {
  originalUrl: string;
  r2Key: string;
  r2Url: string;
  cdnUrl: string;
  size: number;
  width: number;
  height: number;
  format: string;
  optimized: boolean;
  thumbnails?: {
    small: string;
    medium: string;
    large: string;
  };
  uploadedAt: string;
  expiresAt?: string;
}

export interface R2UploadResult {
  success: boolean;
  metadata?: R2ImageMetadata;
  error?: string;
  processingTime: number;
}

export class CloudflareR2ImageStorage {
  private accountId: string;
  private accessKeyId: string;
  private secretAccessKey: string;
  private bucketName: string;
  private cdnDomain: string;
  private isConfigured: boolean = false;

  constructor() {
    this.accountId = process.env.CLOUDFLARE_ACCOUNT_ID || '';
    this.accessKeyId = process.env.R2_ACCESS_KEY_ID || '';
    this.secretAccessKey = process.env.R2_SECRET_ACCESS_KEY || '';
    this.bucketName = process.env.R2_BUCKET_NAME || 'marketplace-storage';
    this.cdnDomain = process.env.R2_CDN_DOMAIN || '';
    
    this.isConfigured = !!(this.accountId && this.accessKeyId && this.secretAccessKey);
    
    if (!this.isConfigured) {
      console.log('⚠️ R2 не налаштований, використовуємо симуляцію');
    }
  }

  /**
   * Завантаження зображення в R2
   */
  async uploadImage(
    imageUrl: string, 
    options: R2ImageUploadOptions = {}
  ): Promise<R2UploadResult> {
    const startTime = Date.now();
    
    try {
      console.log(`📤 Завантаження зображення в R2: ${imageUrl}`);

      if (!this.isConfigured) {
        return await this.simulateUpload(imageUrl, options, startTime);
      }

      // Завантажуємо оригінальне зображення
      const imageBuffer = await this.downloadImage(imageUrl);
      if (!imageBuffer) {
        throw new Error('Не вдалося завантажити зображення');
      }

      // Генеруємо унікальний ключ
      const r2Key = this.generateR2Key(imageUrl, options.folder);
      
      // Оптимізуємо зображення якщо потрібно
      const optimizedBuffer = options.optimize 
        ? await this.optimizeImage(imageBuffer, options)
        : imageBuffer;

      // Завантажуємо в R2
      const uploadResult = await this.uploadToR2(r2Key, optimizedBuffer);
      
      // Генеруємо мініатюри якщо потрібно
      const thumbnails = options.generateThumbnails
        ? await this.generateThumbnails(imageBuffer, r2Key)
        : undefined;

      const metadata: R2ImageMetadata = {
        originalUrl: imageUrl,
        r2Key,
        r2Url: uploadResult.url,
        cdnUrl: this.getCDNUrl(r2Key),
        size: optimizedBuffer.length,
        width: uploadResult.width || 0,
        height: uploadResult.height || 0,
        format: uploadResult.format || 'jpeg',
        optimized: !!options.optimize,
        thumbnails,
        uploadedAt: new Date().toISOString()
      };

      console.log(`✅ Зображення завантажено в R2: ${metadata.cdnUrl}`);

      return {
        success: true,
        metadata,
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      console.error('❌ Помилка завантаження в R2:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Завантаження зображення з URL
   */
  private async downloadImage(url: string): Promise<Buffer | null> {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const arrayBuffer = await response.arrayBuffer();
      return Buffer.from(arrayBuffer);
    } catch (error) {
      console.error('Помилка завантаження зображення:', error);
      return null;
    }
  }

  /**
   * Генерація унікального ключа для R2
   */
  private generateR2Key(originalUrl: string, folder?: string): string {
    const urlHash = this.hashString(originalUrl);
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    
    const filename = `${urlHash}_${timestamp}_${random}.jpg`;
    
    return folder ? `${folder}/${filename}` : `images/${filename}`;
  }

  /**
   * Хешування рядка
   */
  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Оптимізація зображення
   */
  private async optimizeImage(
    buffer: Buffer, 
    options: R2ImageUploadOptions
  ): Promise<Buffer> {
    try {
      // В реальному проекті тут би була інтеграція з Sharp або подібною бібліотекою
      // Поки що повертаємо оригінальний buffer
      console.log('🔧 Оптимізація зображення...');
      
      // Симуляція оптимізації
      await new Promise(resolve => setTimeout(resolve, 500));
      
      return buffer;
    } catch (error) {
      console.error('Помилка оптимізації:', error);
      return buffer;
    }
  }

  /**
   * Завантаження в R2
   */
  private async uploadToR2(key: string, buffer: Buffer): Promise<{
    url: string;
    width?: number;
    height?: number;
    format?: string;
  }> {
    try {
      // В реальному проекті тут би був виклик R2 API
      console.log(`📤 Завантаження в R2: ${key}`);
      
      // Симуляція завантаження
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      return {
        url: `https://${this.bucketName}.${this.accountId}.r2.cloudflarestorage.com/${key}`,
        width: 800,
        height: 600,
        format: 'jpeg'
      };
    } catch (error) {
      console.error('Помилка завантаження в R2:', error);
      throw error;
    }
  }

  /**
   * Генерація мініатюр
   */
  private async generateThumbnails(
    buffer: Buffer, 
    baseKey: string
  ): Promise<{ small: string; medium: string; large: string }> {
    try {
      console.log('🖼️ Генерація мініатюр...');
      
      // Симуляція генерації мініатюр
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const baseName = baseKey.replace('.jpg', '');
      
      return {
        small: this.getCDNUrl(`${baseName}_thumb_150.jpg`),
        medium: this.getCDNUrl(`${baseName}_thumb_300.jpg`),
        large: this.getCDNUrl(`${baseName}_thumb_600.jpg`)
      };
    } catch (error) {
      console.error('Помилка генерації мініатюр:', error);
      throw error;
    }
  }

  /**
   * Отримання CDN URL
   */
  private getCDNUrl(key: string): string {
    if (this.cdnDomain) {
      return `https://${this.cdnDomain}/${key}`;
    }
    
    return `https://${this.bucketName}.${this.accountId}.r2.cloudflarestorage.com/${key}`;
  }

  /**
   * Симуляція завантаження для розробки
   */
  private async simulateUpload(
    imageUrl: string, 
    options: R2ImageUploadOptions,
    startTime: number
  ): Promise<R2UploadResult> {
    console.log('🎭 Симуляція завантаження в R2...');
    
    // Симуляція затримки
    await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000));
    
    const urlHash = this.hashString(imageUrl);
    const r2Key = `images/sim_${urlHash}_${Date.now()}.jpg`;
    
    const metadata: R2ImageMetadata = {
      originalUrl: imageUrl,
      r2Key,
      r2Url: `https://marketplace-storage.demo.r2.cloudflarestorage.com/${r2Key}`,
      cdnUrl: `https://cdn.marketplace.demo/${r2Key}`,
      size: Math.floor(Math.random() * 500000) + 100000, // 100KB - 600KB
      width: 800,
      height: 600,
      format: 'jpeg',
      optimized: !!options.optimize,
      thumbnails: options.generateThumbnails ? {
        small: `https://cdn.marketplace.demo/${r2Key.replace('.jpg', '_thumb_150.jpg')}`,
        medium: `https://cdn.marketplace.demo/${r2Key.replace('.jpg', '_thumb_300.jpg')}`,
        large: `https://cdn.marketplace.demo/${r2Key.replace('.jpg', '_thumb_600.jpg')}`
      } : undefined,
      uploadedAt: new Date().toISOString()
    };

    return {
      success: true,
      metadata,
      processingTime: Date.now() - startTime
    };
  }

  /**
   * Видалення зображення з R2
   */
  async deleteImage(r2Key: string): Promise<boolean> {
    try {
      if (!this.isConfigured) {
        console.log(`🎭 Симуляція видалення з R2: ${r2Key}`);
        return true;
      }

      // В реальному проекті тут би був виклик R2 API для видалення
      console.log(`🗑️ Видалення з R2: ${r2Key}`);
      
      return true;
    } catch (error) {
      console.error('Помилка видалення з R2:', error);
      return false;
    }
  }

  /**
   * Отримання статистики використання R2
   */
  async getStorageStats(): Promise<{
    totalImages: number;
    totalSize: number;
    bandwidth: number;
    requests: number;
  }> {
    try {
      if (!this.isConfigured) {
        return {
          totalImages: Math.floor(Math.random() * 1000) + 500,
          totalSize: Math.floor(Math.random() * 10000000000) + 1000000000, // 1-10 GB
          bandwidth: Math.floor(Math.random() * 1000000000) + 100000000, // 100MB - 1GB
          requests: Math.floor(Math.random() * 100000) + 10000
        };
      }

      // В реальному проекті тут би був виклик R2 API для статистики
      return {
        totalImages: 0,
        totalSize: 0,
        bandwidth: 0,
        requests: 0
      };
    } catch (error) {
      console.error('Помилка отримання статистики R2:', error);
      throw error;
    }
  }

  /**
   * Перевірка конфігурації
   */
  isR2Configured(): boolean {
    return this.isConfigured;
  }
}

// Експорт екземпляра
export const r2ImageStorage = new CloudflareR2ImageStorage();
