/**
 * Middleware для роботи з Cloudflare в Next.js API routes
 * Інтеграція з cloudflare-bindings MCP та cloudflare-observability MCP
 */

import { getRequestContext } from '@cloudflare/next-on-pages';
import { NextRequest, NextResponse } from 'next/server';
import { trackUserEvent } from './analytics';
import { Hyperdrive } from './hyperdrive';
import { initializeCloudflare } from './index';
import { KVNamespace } from './kv';
import { CloudflareEnv, D1Database, R2Bucket } from './types';
import { Worker } from './workers';

// Інтерфейс для контексту Cloudflare Bindings
export interface CloudflareBindingsContext {
  params: Record<string, string>;
  db: D1Database;
  storage: R2Bucket;
  kv: KVNamespace;
  worker: Worker;
  marketplaceWorker: Worker;
  hyperdrive: Hyperdrive;
  env: CloudflareEnv;
  cf?: any;
  analytics?: any;
}

// Інтерфейс для розширеного контексту з observability
export interface CloudflareObservabilityContext extends CloudflareBindingsContext {
  trackEvent: (event: string, data?: any) => Promise<void>;
  trackError: (error: Error, context?: string) => Promise<void>;
  trackPerformance: (metric: string, value: number, unit?: string) => Promise<void>;
}

/**
 * Middleware для доступу до Cloudflare Bindings в API маршрутах
 * @param handler Обробник запиту
 * @returns Функція-обробник з доступом до Cloudflare Bindings
 */
export function withCloudflareBindings<T>(
  handler: (
    req: NextRequest,
    context: CloudflareBindingsContext
  ) => Promise<NextResponse<T> | NextResponse<any>>
) {
  return async (
    req: NextRequest,
    context: any
  ): Promise<NextResponse<T>> => {
    let env: any = {};
    let cf: any = {};

    try {
      // Спроба отримати контекст Cloudflare
      const { env: cloudflareEnv, cf: cloudflareCf } = getRequestContext();
      env = cloudflareEnv;
      cf = cloudflareCf;

      // Ініціалізуємо Cloudflare сервіси
      initializeCloudflare(env);
    } catch (error) {
      console.warn('⚠️ Running outside Cloudflare environment, using fallback');

      // Fallback для локальної розробки
      env = {
        DB: process.env.DB as unknown as D1Database,
        R2_BUCKET: process.env.R2_BUCKET as unknown as R2Bucket,
        CACHE_KV: process.env.CACHE_KV as unknown as KVNamespace,
        ANALYTICS: process.env.ANALYTICS,
        ENVIRONMENT: process.env.NODE_ENV || 'development'
      };
    }

    // Отримання доступу до сервісів
    const db = env.DB || process.env.DB as unknown as D1Database;
    const storage = env.R2_BUCKET || process.env.R2_BUCKET as unknown as R2Bucket;
    const kv = env.CACHE_KV || process.env.CACHE_KV as unknown as KVNamespace;
    const worker = process.env.WORKER as unknown as Worker;
    const marketplaceWorker = process.env.MARKETPLACE_WORKER as unknown as Worker;
    const hyperdrive = process.env.HYPERDRIVE as unknown as Hyperdrive;

    // Перевірка доступності основних сервісів
    if (!db) {
      return NextResponse.json(
        { error: 'Database not available' },
        { status: 500 }
      ) as NextResponse<T>;
    }

    if (!storage) {
      return NextResponse.json(
        { error: 'Storage not available' },
        { status: 500 }
      ) as NextResponse<T>;
    }

    // Виклик обробника з доступом до Cloudflare Bindings
    return handler(req, {
      ...context,
      db,
      storage,
      kv: kv || {} as KVNamespace,
      worker: worker || {} as Worker,
      marketplaceWorker: marketplaceWorker || {} as Worker,
      hyperdrive: hyperdrive || {} as Hyperdrive,
      env,
      cf,
      analytics: env.ANALYTICS
    });
  };
}

/**
 * Middleware для обробки помилок в API маршрутах
 * @param handler Обробник запиту
 * @returns Функція-обробник з обробкою помилок
 */
export function withErrorHandling<T>(
  handler: (
    req: NextRequest,
    context: any
  ) => Promise<NextResponse<T> | NextResponse<any>>
) {
  return async (
    req: NextRequest,
    context: any
  ): Promise<NextResponse<T>> => {
    try {
      return await handler(req, context);
    } catch (error) {
      console.error('API Error:', error);

      return NextResponse.json(
        {
          error: 'Internal Server Error',
          message: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined,
        },
        { status: 500 }
      ) as NextResponse<T>;
    }
  };
}

/**
 * Middleware з observability функціями
 * @param handler Обробник запиту
 * @returns Функція-обробник з observability
 */
export function withCloudflareObservability<T>(
  handler: (
    req: NextRequest,
    context: CloudflareObservabilityContext
  ) => Promise<NextResponse<T> | NextResponse<any>>
) {
  return withCloudflareBindings(async (req: NextRequest, context: CloudflareBindingsContext) => {
    const startTime = Date.now();

    // Функції для tracking
    const trackEvent = async (event: string, data?: any) => {
      try {
        await trackUserEvent(event, data?.userId, data);
      } catch (error) {
        console.error('Failed to track event:', error);
      }
    };

    const trackError = async (error: Error, errorContext?: string) => {
      try {
        await trackUserEvent('error', undefined, {
          message: error.message,
          stack: error.stack,
          context: errorContext,
          url: req.url,
          method: req.method
        });
      } catch (trackingError) {
        console.error('Failed to track error:', trackingError);
      }
    };

    const trackPerformance = async (metric: string, value: number, unit: string = 'ms') => {
      try {
        await trackUserEvent('performance', undefined, {
          metric,
          value,
          unit,
          url: req.url,
          method: req.method
        });
      } catch (error) {
        console.error('Failed to track performance:', error);
      }
    };

    try {
      // Виклик обробника з observability функціями
      const response = await handler(req, {
        ...context,
        trackEvent,
        trackError,
        trackPerformance
      });

      // Трекінг продуктивності
      const duration = Date.now() - startTime;
      await trackPerformance('api_request_duration', duration);

      return response;
    } catch (error) {
      // Трекінг помилки
      await trackError(error as Error, 'api_handler');
      throw error;
    }
  });
}

/**
 * Комбінує middleware для API маршрутів
 * @param handler Обробник запиту
 * @returns Функція-обробник з усіма middleware
 */
export function withApiMiddleware<T>(
  handler: (
    req: NextRequest,
    context: CloudflareBindingsContext
  ) => Promise<NextResponse<T> | NextResponse<any>>
) {
  return withErrorHandling(withCloudflareBindings(handler));
}

/**
 * Комбінує middleware для API маршрутів з observability
 * @param handler Обробник запиту
 * @returns Функція-обробник з усіма middleware та observability
 */
export function withFullApiMiddleware<T>(
  handler: (
    req: NextRequest,
    context: CloudflareObservabilityContext
  ) => Promise<NextResponse<T> | NextResponse<any>>
) {
  return withErrorHandling(withCloudflareObservability(handler));
}
