import { FilamentProduct } from '../bright-data/filament-scraper';

export interface FilamentAnalyticsEvent {
  eventType: 'view' | 'search' | 'filter' | 'click' | 'purchase' | 'compare';
  filamentId?: string;
  searchQuery?: string;
  filterCriteria?: Record<string, any>;
  userId?: string;
  sessionId: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface FilamentPopularityMetrics {
  filamentId: string;
  views: number;
  clicks: number;
  searches: number;
  purchases: number;
  comparisons: number;
  popularityScore: number;
  trendingScore: number;
  lastUpdated: Date;
}

export interface FilamentSearchMetrics {
  query: string;
  frequency: number;
  resultCount: number;
  clickThroughRate: number;
  conversionRate: number;
  lastSearched: Date;
}

export class FilamentAnalytics {
  private analyticsEngine?: any; // Cloudflare Analytics Engine binding
  private cacheKV?: any; // Cloudflare KV binding
  private readonly CACHE_TTL = 3600; // 1 година

  constructor(bindings?: { ANALYTICS?: any; CACHE_KV?: any }) {
    this.analyticsEngine = bindings?.ANALYTICS;
    this.cacheKV = bindings?.CACHE_KV;
  }

  /**
   * Запис події аналітики
   */
  async trackEvent(event: FilamentAnalyticsEvent): Promise<void> {
    try {
      // Записуємо в Analytics Engine якщо доступний
      if (this.analyticsEngine) {
        await this.analyticsEngine.writeDataPoint({
          blobs: [
            event.eventType,
            event.filamentId || '',
            event.searchQuery || '',
            event.userId || '',
            event.sessionId
          ],
          doubles: [
            Date.now(),
            event.metadata?.price || 0,
            event.metadata?.rating || 0
          ],
          indexes: [
            event.eventType,
            event.filamentId || 'unknown'
          ]
        });
      }

      // Оновлюємо кешовані метрики
      await this.updateCachedMetrics(event);

      console.log(`📊 Записано подію аналітики: ${event.eventType}`, event);
    } catch (error) {
      console.error('❌ Помилка запису події аналітики:', error);
    }
  }

  /**
   * Отримання популярних філаментів
   */
  async getPopularFilaments(limit: number = 10): Promise<FilamentPopularityMetrics[]> {
    try {
      const cacheKey = `popular_filaments_${limit}`;
      
      // Спробуємо отримати з кешу
      if (this.cacheKV) {
        const cached = await this.cacheKV.get(cacheKey, 'json');
        if (cached) {
          console.log('📊 Отримано популярні філаменти з кешу');
          return cached;
        }
      }

      // Якщо немає в кеші, генеруємо дані
      const popularFilaments = await this.calculatePopularFilaments(limit);

      // Зберігаємо в кеш
      if (this.cacheKV) {
        await this.cacheKV.put(cacheKey, JSON.stringify(popularFilaments), {
          expirationTtl: this.CACHE_TTL
        });
      }

      return popularFilaments;
    } catch (error) {
      console.error('❌ Помилка отримання популярних філаментів:', error);
      return [];
    }
  }

  /**
   * Отримання трендових пошукових запитів
   */
  async getTrendingSearches(limit: number = 10): Promise<FilamentSearchMetrics[]> {
    try {
      const cacheKey = `trending_searches_${limit}`;
      
      if (this.cacheKV) {
        const cached = await this.cacheKV.get(cacheKey, 'json');
        if (cached) {
          return cached;
        }
      }

      const trendingSearches = await this.calculateTrendingSearches(limit);

      if (this.cacheKV) {
        await this.cacheKV.put(cacheKey, JSON.stringify(trendingSearches), {
          expirationTtl: this.CACHE_TTL
        });
      }

      return trendingSearches;
    } catch (error) {
      console.error('❌ Помилка отримання трендових пошуків:', error);
      return [];
    }
  }

  /**
   * Отримання рекомендацій для користувача
   */
  async getPersonalizedRecommendations(
    userId: string, 
    sessionId: string, 
    limit: number = 5
  ): Promise<string[]> {
    try {
      const cacheKey = `recommendations_${userId}_${limit}`;
      
      if (this.cacheKV) {
        const cached = await this.cacheKV.get(cacheKey, 'json');
        if (cached) {
          return cached;
        }
      }

      const recommendations = await this.calculatePersonalizedRecommendations(userId, sessionId, limit);

      if (this.cacheKV) {
        await this.cacheKV.put(cacheKey, JSON.stringify(recommendations), {
          expirationTtl: 1800 // 30 хвилин для персоналізованих рекомендацій
        });
      }

      return recommendations;
    } catch (error) {
      console.error('❌ Помилка отримання рекомендацій:', error);
      return [];
    }
  }

  /**
   * Аналіз поведінки користувача
   */
  async analyzeUserBehavior(userId: string, sessionId: string): Promise<{
    interests: string[];
    priceRange: [number, number];
    preferredBrands: string[];
    searchPatterns: string[];
  }> {
    try {
      const cacheKey = `user_behavior_${userId}`;
      
      if (this.cacheKV) {
        const cached = await this.cacheKV.get(cacheKey, 'json');
        if (cached) {
          return cached;
        }
      }

      // Симуляція аналізу поведінки користувача
      const behavior = {
        interests: ['PLA', 'PETG', 'Specialty'],
        priceRange: [15, 35] as [number, number],
        preferredBrands: ['Prusament', 'eSUN', 'Hatchbox'],
        searchPatterns: ['transparent filament', 'flexible TPU', 'wood PLA']
      };

      if (this.cacheKV) {
        await this.cacheKV.put(cacheKey, JSON.stringify(behavior), {
          expirationTtl: 7200 // 2 години
        });
      }

      return behavior;
    } catch (error) {
      console.error('❌ Помилка аналізу поведінки користувача:', error);
      return {
        interests: [],
        priceRange: [0, 100],
        preferredBrands: [],
        searchPatterns: []
      };
    }
  }

  /**
   * Оновлення кешованих метрик
   */
  private async updateCachedMetrics(event: FilamentAnalyticsEvent): Promise<void> {
    if (!this.cacheKV || !event.filamentId) return;

    try {
      const metricsKey = `metrics_${event.filamentId}`;
      const currentMetrics = await this.cacheKV.get(metricsKey, 'json') || {
        views: 0,
        clicks: 0,
        searches: 0,
        purchases: 0,
        comparisons: 0
      };

      // Оновлюємо метрики залежно від типу події
      switch (event.eventType) {
        case 'view':
          currentMetrics.views++;
          break;
        case 'click':
          currentMetrics.clicks++;
          break;
        case 'search':
          currentMetrics.searches++;
          break;
        case 'purchase':
          currentMetrics.purchases++;
          break;
        case 'compare':
          currentMetrics.comparisons++;
          break;
      }

      await this.cacheKV.put(metricsKey, JSON.stringify(currentMetrics), {
        expirationTtl: 86400 // 24 години
      });
    } catch (error) {
      console.error('❌ Помилка оновлення кешованих метрик:', error);
    }
  }

  /**
   * Розрахунок популярних філаментів
   */
  private async calculatePopularFilaments(limit: number): Promise<FilamentPopularityMetrics[]> {
    // Симуляція розрахунку популярності
    const mockPopularFilaments: FilamentPopularityMetrics[] = [];
    
    for (let i = 1; i <= limit; i++) {
      mockPopularFilaments.push({
        filamentId: `filament_${i}`,
        views: Math.floor(Math.random() * 1000) + 100,
        clicks: Math.floor(Math.random() * 200) + 20,
        searches: Math.floor(Math.random() * 50) + 5,
        purchases: Math.floor(Math.random() * 30) + 1,
        comparisons: Math.floor(Math.random() * 15) + 1,
        popularityScore: Math.random() * 100,
        trendingScore: Math.random() * 100,
        lastUpdated: new Date()
      });
    }

    return mockPopularFilaments.sort((a, b) => b.popularityScore - a.popularityScore);
  }

  /**
   * Розрахунок трендових пошуків
   */
  private async calculateTrendingSearches(limit: number): Promise<FilamentSearchMetrics[]> {
    const mockTrendingSearches: FilamentSearchMetrics[] = [
      {
        query: 'transparent PETG',
        frequency: 150,
        resultCount: 25,
        clickThroughRate: 0.65,
        conversionRate: 0.12,
        lastSearched: new Date()
      },
      {
        query: 'flexible TPU filament',
        frequency: 120,
        resultCount: 18,
        clickThroughRate: 0.58,
        conversionRate: 0.08,
        lastSearched: new Date()
      },
      {
        query: 'wood PLA brown',
        frequency: 95,
        resultCount: 12,
        clickThroughRate: 0.72,
        conversionRate: 0.15,
        lastSearched: new Date()
      }
    ];

    return mockTrendingSearches.slice(0, limit);
  }

  /**
   * Розрахунок персоналізованих рекомендацій
   */
  private async calculatePersonalizedRecommendations(
    userId: string, 
    sessionId: string, 
    limit: number
  ): Promise<string[]> {
    // Симуляція персоналізованих рекомендацій
    const allFilaments = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'];
    return allFilaments.slice(0, limit);
  }
}
