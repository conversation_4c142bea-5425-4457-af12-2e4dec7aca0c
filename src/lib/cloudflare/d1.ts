/**
 * Утиліти для роботи з Cloudflare D1 базою даних
 * Інтеграція з cloudflare-bindings MCP
 */

import { CloudflareEnv, D1Database, D1ExecResult, D1PreparedStatement, D1Result, QueryParams, TransactionCallback, TransactionOptions } from './types';

// Глобальна змінна для зберігання екземпляра бази даних
let db: D1Database | null = null;

/**
 * Ініціалізує з'єднання з D1 базою даних
 * @param env Cloudflare Environment з bindings
 */
export function initializeD1(env: CloudflareEnv): void {
  if (env.DB) {
    db = env.DB;
    console.log('✅ D1 Database initialized successfully');
  } else {
    console.warn('⚠️ D1 Database binding not found in environment');
  }
}

/**
 * Отримує екземпляр бази даних D1
 * @returns Екземпляр бази даних D1
 */
export function getDb(): D1Database {
  if (!db) {
    // В середовищі Cloudflare Pages, D1 доступна через env.DB
    if (process.env.DB) {
      db = process.env.DB as unknown as D1Database;
    } else if (globalThis.DB) {
      // Для серверних компонентів Next.js
      db = globalThis.DB as unknown as D1Database;
    } else {
      throw new Error('База даних недоступна. Переконайтеся, що ви налаштували D1 в wrangler.toml і запустили проект з прапорцем --d1=DB');
    }
  }
  return db;
}

/**
 * Виконує SQL-запит до бази даних
 * @param sql SQL-запит
 * @param params Параметри запиту
 * @returns Результат запиту
 */
export async function query<T = unknown>(sql: string, params: QueryParams = []): Promise<T[]> {
  const db = getDb();
  const stmt = db.prepare(sql);
  const result = await stmt.bind(...params).all<T>();
  return result.results;
}

/**
 * Отримує один запис з бази даних
 * @param sql SQL-запит
 * @param params Параметри запиту
 * @returns Перший запис або null
 */
export async function queryOne<T = unknown>(sql: string, params: QueryParams = []): Promise<T | null> {
  const db = getDb();
  const stmt = db.prepare(sql);
  return await stmt.bind(...params).first<T>();
}

/**
 * Виконує SQL-запит без повернення результатів
 * @param sql SQL-запит
 * @param params Параметри запиту
 * @returns Результат виконання
 */
export async function execute(sql: string, params: QueryParams = []): Promise<D1ExecResult> {
  const db = getDb();
  const stmt = db.prepare(sql);
  return await stmt.bind(...params).run();
}

/**
 * Виконує пакет SQL-запитів
 * @param statements Масив підготовлених запитів
 * @returns Результати виконання
 */
export async function batch(statements: D1PreparedStatement[]): Promise<D1Result[]> {
  const db = getDb();
  return await db.batch(statements);
}

/**
 * Виконує транзакцію
 * @param callback Функція, яка виконується в транзакції
 * @param options Опції транзакції
 * @returns Результат виконання функції
 */
export async function transaction<T>(
  callback: TransactionCallback<T>,
  options: TransactionOptions = {}
): Promise<T> {
  const db = getDb();

  // Початок транзакції
  await db.exec('BEGIN TRANSACTION');

  try {
    // Виконання функції в транзакції
    const result = await callback(db);

    // Підтвердження транзакції
    await db.exec('COMMIT');

    return result;
  } catch (error) {
    // Відкат транзакції у випадку помилки
    await db.exec('ROLLBACK');

    if (options.rollbackOnError !== false) {
      throw error;
    }

    return null as unknown as T;
  }
}

/**
 * Створює підготовлений запит
 * @param sql SQL-запит
 * @returns Підготовлений запит
 */
export function prepareStatement(sql: string): D1PreparedStatement {
  const db = getDb();
  return db.prepare(sql);
}

/**
 * Виконує запит з пагінацією
 * @param sql SQL-запит без LIMIT та OFFSET
 * @param params Параметри запиту
 * @param page Номер сторінки (починається з 1)
 * @param limit Кількість записів на сторінці
 * @returns Результат запиту з пагінацією
 */
export async function queryWithPagination<T = unknown>(
  sql: string,
  params: QueryParams = [],
  page: number = 1,
  limit: number = 10
): Promise<{ data: T[]; total: number; page: number; limit: number; totalPages: number }> {
  const db = getDb();

  // Запит для отримання загальної кількості записів
  const countSql = `SELECT COUNT(*) as total FROM (${sql.replace(/SELECT .* FROM/i, 'SELECT 1 FROM')})`;
  const countResult = await db.prepare(countSql).bind(...params).first<{ total: number }>();
  const total = countResult?.total || 0;

  // Запит з пагінацією
  const paginatedSql = `${sql} LIMIT ? OFFSET ?`;
  const offset = (page - 1) * limit;
  const paginatedParams = [...params, limit, offset];

  const result = await db.prepare(paginatedSql).bind(...paginatedParams).all<T>();

  return {
    data: result.results,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
  };
}

/**
 * Генерує унікальний ідентифікатор
 * @returns Унікальний ідентифікатор
 */
export function generateId(): string {
  return crypto.randomUUID();
}
