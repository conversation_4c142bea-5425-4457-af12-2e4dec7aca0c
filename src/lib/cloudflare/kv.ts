/**
 * Утиліти для роботи з Cloudflare KV Storage
 * Інтеграція з cloudflare-bindings MCP
 */

import { KVListOptions, KVListResult, KVNamespace, KVPutOptions } from './types';

// Опції для отримання значення з KV
export interface KVGetOptions {
  type?: 'text' | 'json' | 'arrayBuffer' | 'stream';
  cacheTtl?: number;
}

// Опції для запису значення в KV
export interface KVPutOptions {
  expiration?: number;
  expirationTtl?: number;
  metadata?: any;
}

// Опції для отримання списку ключів з KV
export interface KVListOptions {
  prefix?: string;
  limit?: number;
  cursor?: string;
}

// Результат отримання списку ключів з KV
export interface KVListResult {
  keys: KVListKey[];
  list_complete: boolean;
  cursor?: string;
}

// Ключ у списку ключів KV
export interface KVListKey {
  name: string;
  expiration?: number;
  metadata?: any;
}

// Глобальна змінна для зберігання екземпляра KV namespace
let kv: KVNamespace | null = null;

/**
 * Ініціалізує з'єднання з KV Storage
 * @param env Cloudflare Environment з bindings
 */
export function initializeKV(env: any): void {
  if (env.CACHE_KV) {
    kv = env.CACHE_KV;
    console.log('✅ KV Storage initialized successfully');
  } else {
    console.warn('⚠️ KV Storage binding not found in environment');
  }
}

/**
 * Отримує екземпляр KV namespace
 * @returns Екземпляр KV namespace
 */
export function getKV(): KVNamespace {
  if (!kv) {
    // В середовищі Cloudflare Pages, KV доступний через env.CACHE_KV
    if (process.env.CACHE_KV) {
      kv = process.env.CACHE_KV as unknown as KVNamespace;
    } else if (globalThis.CACHE_KV) {
      // Для серверних компонентів Next.js
      kv = globalThis.CACHE_KV as unknown as KVNamespace;
    } else {
      throw new Error('KV недоступний. Переконайтеся, що ви налаштували KV в wrangler.toml і запустили проект з прапорцем --kv=CACHE_KV');
    }
  }
  return kv;
}

/**
 * Отримує значення з KV
 * @param key Ключ
 * @param options Опції
 * @returns Значення або null, якщо ключ не знайдено
 */
export async function getValue(key: string, options?: KVGetOptions): Promise<string | null> {
  const kv = getKV();
  return await kv.get(key, options);
}

/**
 * Отримує значення з KV як JSON
 * @param key Ключ
 * @returns Значення як об'єкт або null, якщо ключ не знайдено
 */
export async function getJSON<T = any>(key: string): Promise<T | null> {
  const kv = getKV();
  const value = await kv.get(key, { type: 'json' });
  return value as T | null;
}

/**
 * Отримує значення з KV з метаданими
 * @param key Ключ
 * @param options Опції
 * @returns Значення та метадані або null, якщо ключ не знайдено
 */
export async function getWithMetadata<T = any, M = any>(
  key: string,
  options?: KVGetOptions
): Promise<{ value: T | null; metadata: M | null }> {
  const kv = getKV();
  const result = await kv.getWithMetadata<M>(key, options);

  let value: T | null = null;

  if (result.value) {
    if (options?.type === 'json') {
      try {
        value = JSON.parse(result.value) as T;
      } catch (error) {
        console.error(`Error parsing JSON for key ${key}:`, error);
      }
    } else {
      value = result.value as unknown as T;
    }
  }

  return { value, metadata: result.metadata };
}

/**
 * Записує значення в KV
 * @param key Ключ
 * @param value Значення
 * @param options Опції
 */
export async function setValue(
  key: string,
  value: string | ReadableStream | ArrayBuffer | object,
  options?: KVPutOptions
): Promise<void> {
  const kv = getKV();

  // Якщо значення є об'єктом, перетворюємо його в JSON
  const finalValue = typeof value === 'object' && !(value instanceof ReadableStream) && !(value instanceof ArrayBuffer)
    ? JSON.stringify(value)
    : value;

  await kv.put(key, finalValue, options);
}

/**
 * Видаляє значення з KV
 * @param key Ключ
 */
export async function deleteValue(key: string): Promise<void> {
  const kv = getKV();
  await kv.delete(key);
}

/**
 * Отримує список ключів з KV
 * @param options Опції
 * @returns Список ключів
 */
export async function listKeys(options?: KVListOptions): Promise<KVListResult> {
  const kv = getKV();
  return await kv.list(options);
}

/**
 * Отримує всі значення за префіксом
 * @param prefix Префікс ключів
 * @returns Об'єкт, де ключі - це ключі KV, а значення - це значення KV
 */
export async function getAllByPrefix<T = any>(prefix: string): Promise<Record<string, T>> {
  const kv = getKV();
  const result: Record<string, T> = {};

  let listComplete = false;
  let cursor: string | undefined;

  while (!listComplete) {
    const listResult = await kv.list({ prefix, cursor });

    for (const key of listResult.keys) {
      const value = await kv.get(key.name, { type: 'json' });
      if (value !== null) {
        result[key.name] = value as T;
      }
    }

    listComplete = listResult.list_complete;
    cursor = listResult.cursor;
  }

  return result;
}
