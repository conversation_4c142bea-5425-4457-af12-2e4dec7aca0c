/**
 * Типи для роботи з Cloudflare Bindings
 * Інтеграція з cloudflare-bindings MCP та cloudflare-observability MCP
 */

// Типи для D1
export type D1Result<T = unknown> = {
  results: T[];
  success: boolean;
  meta: {
    duration: number;
    rows_read: number;
    rows_written: number;
    size_after?: number;
    changes?: number;
    last_row_id?: number;
  };
};

export type D1ExecResult = {
  success: boolean;
  meta: {
    duration: number;
    rows_written: number;
    size_after?: number;
    changes?: number;
    last_row_id?: number;
  };
};

// Інтерфейс для D1Database
export interface D1Database {
  prepare: (query: string) => D1PreparedStatement;
  dump: () => Promise<ArrayBuffer>;
  batch: (statements: D1PreparedStatement[]) => Promise<D1Result[]>;
  exec: (query: string) => Promise<D1ExecResult>;
}

// Інтерфейс для D1PreparedStatement
export interface D1PreparedStatement {
  bind: (...values: any[]) => D1PreparedStatement;
  first: <T = unknown>(colName?: string) => Promise<T | null>;
  run: () => Promise<D1ExecResult>;
  all: <T = unknown>() => Promise<D1Result<T>>;
  raw: <T = unknown>() => Promise<T[]>;
}

// Типи для R2
export interface R2Bucket {
  head: (key: string) => Promise<R2Object | null>;
  get: (key: string) => Promise<R2ObjectBody | null>;
  put: (key: string, value: ReadableStream | ArrayBuffer | string) => Promise<R2Object>;
  delete: (key: string) => Promise<void>;
  list: (options?: { prefix?: string; limit?: number; cursor?: string }) => Promise<R2Objects>;
}

export interface R2Object {
  key: string;
  version: string;
  size: number;
  etag: string;
  httpEtag: string;
  uploaded: Date;
  httpMetadata?: Record<string, string>;
  customMetadata?: Record<string, string>;
}

export interface R2ObjectBody extends R2Object {
  body: ReadableStream;
  bodyUsed: boolean;
  arrayBuffer: () => Promise<ArrayBuffer>;
  text: () => Promise<string>;
  json: () => Promise<any>;
  blob: () => Promise<Blob>;
}

export interface R2Objects {
  objects: R2Object[];
  truncated: boolean;
  cursor?: string;
  delimitedPrefixes?: string[];
}

// Параметри для запиту до бази даних
export type QueryParams = any[];

// Опції для транзакції
export interface TransactionOptions {
  timeout?: number;
  rollbackOnError?: boolean;
}

// Функція для виконання транзакції
export type TransactionCallback<T> = (tx: D1Database) => Promise<T>;

// Опції для пагінації
export interface PaginationOptions {
  page?: number;
  limit?: number;
}

// Результат з пагінацією
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

// Типи для KV
export interface KVNamespace {
  get: (key: string, options?: { type?: 'text' | 'json' | 'arrayBuffer' | 'stream' }) => Promise<any>;
  put: (key: string, value: string | ArrayBuffer | ReadableStream, options?: KVPutOptions) => Promise<void>;
  delete: (key: string) => Promise<void>;
  list: (options?: KVListOptions) => Promise<KVListResult>;
}

export interface KVPutOptions {
  expiration?: number;
  expirationTtl?: number;
  metadata?: Record<string, any>;
}

export interface KVListOptions {
  prefix?: string;
  limit?: number;
  cursor?: string;
}

export interface KVListResult {
  keys: KVKey[];
  list_complete: boolean;
  cursor?: string;
}

export interface KVKey {
  name: string;
  expiration?: number;
  metadata?: Record<string, any>;
}

// Типи для Analytics Engine
export interface AnalyticsEngineDataset {
  writeDataPoint: (dataPoint: AnalyticsEngineDataPoint) => Promise<void>;
}

export interface AnalyticsEngineDataPoint {
  blobs?: string[];
  doubles?: number[];
  indexes?: string[];
}

// Типи для Queues
export interface Queue {
  send: (message: any, options?: QueueSendOptions) => Promise<void>;
  sendBatch: (messages: QueueMessage[], options?: QueueSendBatchOptions) => Promise<void>;
}

export interface QueueMessage {
  body: any;
  contentType?: string;
  delaySeconds?: number;
}

export interface QueueSendOptions {
  contentType?: string;
  delaySeconds?: number;
}

export interface QueueSendBatchOptions {
  delaySeconds?: number;
}

// Головний інтерфейс для Cloudflare Environment
export interface CloudflareEnv {
  // D1 Database
  DB: D1Database;

  // R2 Storage
  R2_BUCKET: R2Bucket;

  // KV Storage
  CACHE_KV: KVNamespace;

  // Analytics Engine
  ANALYTICS: AnalyticsEngineDataset;

  // Background Queue
  BACKGROUND_QUEUE: Queue;

  // Durable Objects
  DOWNLOAD_MANAGER: DurableObjectNamespace;

  // Environment variables
  ENVIRONMENT: string;
  NEXTAUTH_SECRET: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;

  // OAuth secrets
  GITHUB_CLIENT_ID: string;
  GITHUB_CLIENT_SECRET: string;
  GOOGLE_CLIENT_ID: string;
  GOOGLE_CLIENT_SECRET: string;

  // Email configuration
  EMAIL_SERVER_HOST: string;
  EMAIL_SERVER_PORT: string;
  EMAIL_SERVER_USER: string;
  EMAIL_SERVER_PASSWORD: string;
  EMAIL_FROM: string;
}
