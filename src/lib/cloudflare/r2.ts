/**
 * Утиліти для роботи з Cloudflare R2 Storage
 * Інтеграція з cloudflare-bindings MCP
 */

import { CloudflareEnv, R2Bucket, R2Object, R2ObjectBody, R2Objects } from './types';

// Глобальна змінна для зберігання екземпляра бакета
let bucket: R2Bucket | null = null;

/**
 * Ініціалізує з'єднання з R2 Storage
 * @param env Cloudflare Environment з bindings
 */
export function initializeR2(env: CloudflareEnv): void {
  if (env.R2_BUCKET) {
    bucket = env.R2_BUCKET;
    console.log('✅ R2 Storage initialized successfully');
  } else {
    console.warn('⚠️ R2 Storage binding not found in environment');
  }
}

/**
 * Отримує екземпляр бакета R2
 * @returns Екземпляр бакета R2
 */
export function getStorage(): R2Bucket {
  if (!bucket) {
    // В середовищі Cloudflare Pages, R2 доступний через env.R2_BUCKET
    if (process.env.R2_BUCKET) {
      bucket = process.env.R2_BUCKET as unknown as R2Bucket;
    } else if (globalThis.R2_BUCKET) {
      // Для серверних компонентів Next.js
      bucket = globalThis.R2_BUCKET as unknown as R2Bucket;
    } else {
      throw new Error('Сховище недоступне. Переконайтеся, що ви налаштували R2 в wrangler.toml і запустили проект з прапорцем --r2=R2_BUCKET');
    }
  }
  return bucket;
}

/**
 * Завантажує файл у сховище
 * @param key Ключ файлу
 * @param file Файл для завантаження
 * @param metadata Метадані файлу
 * @returns Об'єкт R2
 */
export async function uploadFile(
  key: string,
  file: File | Blob | ArrayBuffer | ReadableStream | string,
  metadata?: Record<string, string>
): Promise<R2Object> {
  const storage = getStorage();

  const options: any = {};

  if (metadata) {
    options.customMetadata = metadata;
  }

  // Конвертуємо File або Blob в ArrayBuffer для сумісності з R2
  let fileData: ArrayBuffer | ReadableStream | string;

  if (file instanceof File || file instanceof Blob) {
    options.httpMetadata = {
      contentType: file.type || 'application/octet-stream',
    };
    fileData = await file.arrayBuffer();
  } else {
    fileData = file;
  }

  return await storage.put(key, fileData);
}

/**
 * Отримує файл зі сховища
 * @param key Ключ файлу
 * @returns Об'єкт R2 або null, якщо файл не знайдено
 */
export async function getFile(key: string): Promise<R2ObjectBody | null> {
  const storage = getStorage();
  return await storage.get(key);
}

/**
 * Видаляє файл зі сховища
 * @param key Ключ файлу
 */
export async function deleteFile(key: string): Promise<void> {
  const storage = getStorage();
  await storage.delete(key);
}

/**
 * Отримує URL для доступу до файлу
 * @param key Ключ файлу
 * @returns URL файлу
 */
export function getFileUrl(key: string): string {
  // Базовий URL для доступу до файлів R2
  const baseUrl = process.env.NEXT_PUBLIC_R2_URL || 'https://storage.example.com';
  return `${baseUrl}/${key}`;
}

/**
 * Генерує унікальний ключ для файлу
 * @param fileName Ім'я файлу
 * @param prefix Префікс для ключа
 * @returns Унікальний ключ
 */
export function generateFileKey(fileName: string, prefix: string = ''): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 10);
  const extension = fileName.includes('.') ? fileName.split('.').pop() : '';

  return `${prefix ? prefix + '/' : ''}${timestamp}-${randomString}${extension ? '.' + extension : ''}`;
}

/**
 * Отримує список файлів зі сховища
 * @param prefix Префікс для фільтрації файлів
 * @param limit Максимальна кількість файлів
 * @param cursor Курсор для пагінації
 * @returns Список файлів
 */
export async function listFiles(
  prefix: string = '',
  limit: number = 100,
  cursor?: string
): Promise<R2Objects> {
  const storage = getStorage();
  return await storage.list({ prefix, limit, cursor });
}

/**
 * Перевіряє, чи існує файл у сховищі
 * @param key Ключ файлу
 * @returns true, якщо файл існує
 */
export async function fileExists(key: string): Promise<boolean> {
  const storage = getStorage();
  const object = await storage.head(key);
  return object !== null;
}
