/**
 * Cloudflare Observability інтеграція
 * Використовує cloudflare-observability MCP та cloudflare-bindings MCP
 */

import { CloudflareEnv } from './types';

export interface MetricData {
  name: string;
  value: number;
  timestamp: number;
  labels?: Record<string, string>;
  unit?: string;
}

export interface LogEntry {
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  timestamp: number;
  context?: Record<string, any>;
  traceId?: string;
  spanId?: string;
}

export interface TraceSpan {
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  operationName: string;
  startTime: number;
  endTime?: number;
  tags?: Record<string, any>;
  logs?: LogEntry[];
  status?: 'ok' | 'error' | 'timeout';
}

export interface AlertRule {
  id: string;
  name: string;
  condition: string;
  threshold: number;
  enabled: boolean;
  notificationChannels: string[];
}

export class CloudflareObservability {
  private env: CloudflareEnv;
  private activeSpans: Map<string, TraceSpan> = new Map();
  private metrics: MetricData[] = [];
  private logs: LogEntry[] = [];

  constructor(env: CloudflareEnv) {
    this.env = env;
  }

  /**
   * Запис метрики
   */
  async recordMetric(
    name: string,
    value: number,
    labels?: Record<string, string>,
    unit?: string
  ): Promise<void> {
    try {
      const metric: MetricData = {
        name,
        value,
        timestamp: Date.now(),
        labels,
        unit
      };

      this.metrics.push(metric);

      // Відправляємо в Analytics Engine
      if (this.env.ANALYTICS) {
        await this.env.ANALYTICS.writeDataPoint({
          blobs: [name, JSON.stringify(labels || {})],
          doubles: [value],
          indexes: [name]
        });
      }

      console.log(`📊 Метрика записана: ${name} = ${value} ${unit || ''}`);
    } catch (error) {
      console.error('❌ Помилка запису метрики:', error);
    }
  }

  /**
   * Запис логу
   */
  async log(
    level: LogEntry['level'],
    message: string,
    context?: Record<string, any>,
    traceId?: string,
    spanId?: string
  ): Promise<void> {
    try {
      const logEntry: LogEntry = {
        level,
        message,
        timestamp: Date.now(),
        context,
        traceId,
        spanId
      };

      this.logs.push(logEntry);

      // Зберігаємо в KV для пошуку
      if (this.env.CACHE_KV) {
        const logKey = `log:${Date.now()}:${crypto.randomUUID()}`;
        await this.env.CACHE_KV.put(logKey, JSON.stringify(logEntry), {
          expirationTtl: 86400 // 24 години
        });
      }

      // Виводимо в консоль з форматуванням
      const emoji = this.getLogEmoji(level);
      const contextStr = context ? ` | ${JSON.stringify(context)}` : '';
      const traceStr = traceId ? ` | trace:${traceId}` : '';
      console.log(`${emoji} [${level.toUpperCase()}] ${message}${contextStr}${traceStr}`);
    } catch (error) {
      console.error('❌ Помилка запису логу:', error);
    }
  }

  /**
   * Початок трейсу
   */
  startSpan(
    operationName: string,
    parentSpanId?: string,
    tags?: Record<string, any>
  ): TraceSpan {
    const span: TraceSpan = {
      traceId: parentSpanId ? this.getTraceIdFromSpan(parentSpanId) : crypto.randomUUID(),
      spanId: crypto.randomUUID(),
      parentSpanId,
      operationName,
      startTime: Date.now(),
      tags,
      logs: []
    };

    this.activeSpans.set(span.spanId, span);
    
    console.log(`🔍 Початок span: ${operationName} | ${span.spanId}`);
    return span;
  }

  /**
   * Завершення трейсу
   */
  finishSpan(spanId: string, status: TraceSpan['status'] = 'ok'): void {
    const span = this.activeSpans.get(spanId);
    if (!span) {
      console.warn(`⚠️ Span не знайдено: ${spanId}`);
      return;
    }

    span.endTime = Date.now();
    span.status = status;

    const duration = span.endTime - span.startTime;
    console.log(`✅ Завершення span: ${span.operationName} | ${duration}ms | ${status}`);

    // Записуємо метрику тривалості
    this.recordMetric(
      'span_duration_ms',
      duration,
      {
        operation: span.operationName,
        status: status
      },
      'milliseconds'
    );

    this.activeSpans.delete(spanId);
  }

  /**
   * Додавання логу до span
   */
  addSpanLog(spanId: string, level: LogEntry['level'], message: string, context?: Record<string, any>): void {
    const span = this.activeSpans.get(spanId);
    if (!span) {
      console.warn(`⚠️ Span не знайдено для логу: ${spanId}`);
      return;
    }

    const logEntry: LogEntry = {
      level,
      message,
      timestamp: Date.now(),
      context,
      traceId: span.traceId,
      spanId: span.spanId
    };

    span.logs = span.logs || [];
    span.logs.push(logEntry);

    // Також записуємо в загальні логи
    this.log(level, message, context, span.traceId, span.spanId);
  }

  /**
   * Запис помилки
   */
  async recordError(
    error: Error,
    context?: Record<string, any>,
    spanId?: string
  ): Promise<void> {
    const errorData = {
      message: error.message,
      stack: error.stack,
      name: error.name,
      ...context
    };

    await this.log('error', error.message, errorData, undefined, spanId);
    
    // Записуємо метрику помилки
    await this.recordMetric(
      'errors_total',
      1,
      {
        error_type: error.name,
        ...context
      }
    );

    if (spanId) {
      this.addSpanLog(spanId, 'error', error.message, errorData);
      this.finishSpan(spanId, 'error');
    }
  }

  /**
   * Метрики скрапінгу
   */
  async recordScrapingMetrics(
    platform: string,
    success: boolean,
    duration: number,
    modelsFound: number
  ): Promise<void> {
    const labels = { platform, status: success ? 'success' : 'error' };

    await Promise.all([
      this.recordMetric('scraping_requests_total', 1, labels),
      this.recordMetric('scraping_duration_ms', duration, labels, 'milliseconds'),
      this.recordMetric('scraping_models_found', modelsFound, labels)
    ]);
  }

  /**
   * Метрики завантаження
   */
  async recordDownloadMetrics(
    platform: string,
    success: boolean,
    fileSize: number,
    duration: number
  ): Promise<void> {
    const labels = { platform, status: success ? 'success' : 'error' };

    await Promise.all([
      this.recordMetric('download_requests_total', 1, labels),
      this.recordMetric('download_duration_ms', duration, labels, 'milliseconds'),
      this.recordMetric('download_file_size_bytes', fileSize, labels, 'bytes')
    ]);
  }

  /**
   * Метрики API
   */
  async recordAPIMetrics(
    endpoint: string,
    method: string,
    statusCode: number,
    duration: number
  ): Promise<void> {
    const labels = {
      endpoint,
      method,
      status_code: statusCode.toString(),
      status_class: Math.floor(statusCode / 100) + 'xx'
    };

    await Promise.all([
      this.recordMetric('api_requests_total', 1, labels),
      this.recordMetric('api_request_duration_ms', duration, labels, 'milliseconds')
    ]);
  }

  /**
   * Отримання метрик за період
   */
  getMetrics(
    startTime?: number,
    endTime?: number,
    nameFilter?: string
  ): MetricData[] {
    let filtered = this.metrics;

    if (startTime) {
      filtered = filtered.filter(m => m.timestamp >= startTime);
    }

    if (endTime) {
      filtered = filtered.filter(m => m.timestamp <= endTime);
    }

    if (nameFilter) {
      filtered = filtered.filter(m => m.name.includes(nameFilter));
    }

    return filtered.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Отримання логів за період
   */
  getLogs(
    startTime?: number,
    endTime?: number,
    levelFilter?: LogEntry['level']
  ): LogEntry[] {
    let filtered = this.logs;

    if (startTime) {
      filtered = filtered.filter(l => l.timestamp >= startTime);
    }

    if (endTime) {
      filtered = filtered.filter(l => l.timestamp <= endTime);
    }

    if (levelFilter) {
      filtered = filtered.filter(l => l.level === levelFilter);
    }

    return filtered.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Очистка старих даних
   */
  cleanup(maxAge: number = 86400000): void { // 24 години за замовчуванням
    const cutoff = Date.now() - maxAge;
    
    this.metrics = this.metrics.filter(m => m.timestamp > cutoff);
    this.logs = this.logs.filter(l => l.timestamp > cutoff);
    
    console.log(`🧹 Очищено старі дані observability (старше ${maxAge}ms)`);
  }

  /**
   * Допоміжні методи
   */
  private getLogEmoji(level: LogEntry['level']): string {
    const emojis = {
      debug: '🐛',
      info: 'ℹ️',
      warn: '⚠️',
      error: '❌'
    };
    return emojis[level] || 'ℹ️';
  }

  private getTraceIdFromSpan(spanId: string): string {
    const span = this.activeSpans.get(spanId);
    return span?.traceId || crypto.randomUUID();
  }

  /**
   * Експорт даних для зовнішніх систем
   */
  exportData() {
    return {
      metrics: this.metrics,
      logs: this.logs,
      activeSpans: Array.from(this.activeSpans.values()),
      timestamp: Date.now()
    };
  }
}
