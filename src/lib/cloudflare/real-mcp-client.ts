/**
 * Real Cloudflare MCP Client
 * Реальна інтеграція з Cloudflare MCP tools
 */

import {
  CloudflareMCPConfig,
  CloudflareR2Operation,
  CloudflareD1Operation,
  CloudflareKVOperation,
  CloudflareAnalyticsEvent,
  MCPResponse,
  MCPCallOptions,
  MCPError,
  ServiceHealth
} from '../mcp/types';

export class CloudflareMCPClient {
  private config: CloudflareMCPConfig;
  private bindings: any;
  private metrics: Map<string, any> = new Map();
  private lastHealthCheck: Date = new Date();

  constructor(config: CloudflareMCPConfig) {
    this.config = config;
  }

  /**
   * Ініціалізація клієнта з Cloudflare bindings
   */
  async initialize(bindings: any): Promise<void> {
    this.bindings = bindings;
    console.log('🌐 Cloudflare MCP Client ініціалізовано');
    
    // Перевіряємо доступність сервісів
    await this.healthCheck();
  }

  /**
   * Виконання операції з R2 Storage
   */
  async r2Operation(operation: CloudflareR2Operation, options?: MCPCallOptions): Promise<MCPResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🗄️ R2 операція: ${operation.action} для ключа ${operation.key}`);

      if (!this.bindings?.R2_BUCKET) {
        throw new Error('R2_BUCKET binding не знайдено');
      }

      let result: any;
      const bucket = this.bindings.R2_BUCKET;

      switch (operation.action) {
        case 'put':
          result = await bucket.put(operation.key, operation.data, {
            customMetadata: operation.metadata,
            httpMetadata: {
              contentType: operation.metadata?.['content-type'] || 'application/octet-stream'
            }
          });
          break;

        case 'get':
          result = await bucket.get(operation.key);
          if (result) {
            result = {
              key: operation.key,
              size: result.size,
              etag: result.etag,
              uploaded: result.uploaded,
              customMetadata: result.customMetadata,
              httpMetadata: result.httpMetadata,
              body: await result.arrayBuffer()
            };
          }
          break;

        case 'delete':
          await bucket.delete(operation.key);
          result = { deleted: true, key: operation.key };
          break;

        case 'list':
          const listResult = await bucket.list({
            prefix: operation.options?.prefix,
            limit: operation.options?.limit || 100,
            cursor: operation.options?.cursor
          });
          result = {
            objects: listResult.objects.map(obj => ({
              key: obj.key,
              size: obj.size,
              etag: obj.etag,
              uploaded: obj.uploaded,
              customMetadata: obj.customMetadata
            })),
            truncated: listResult.truncated,
            cursor: listResult.cursor
          };
          break;

        default:
          throw new Error(`Непідтримувана R2 операція: ${operation.action}`);
      }

      const executionTime = Date.now() - startTime;
      this.updateMetrics('r2', operation.action, true, executionTime);

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
        executionTime
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.updateMetrics('r2', operation.action, false, executionTime);
      
      console.error('❌ Помилка R2 операції:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Невідома помилка R2',
        timestamp: new Date().toISOString(),
        executionTime
      };
    }
  }

  /**
   * Виконання операції з D1 Database
   */
  async d1Operation(operation: CloudflareD1Operation, options?: MCPCallOptions): Promise<MCPResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🗃️ D1 операція: ${operation.action}`);

      if (!this.bindings?.DB) {
        throw new Error('DB binding не знайдено');
      }

      let result: any;
      const db = this.bindings.DB;

      switch (operation.action) {
        case 'query':
          const queryResult = await db.prepare(operation.sql).bind(...(operation.params || [])).all();
          result = {
            results: queryResult.results,
            meta: queryResult.meta
          };
          break;

        case 'execute':
          const execResult = await db.prepare(operation.sql).bind(...(operation.params || [])).run();
          result = {
            changes: execResult.changes,
            lastRowId: execResult.meta?.last_row_id,
            duration: execResult.meta?.duration
          };
          break;

        case 'batch':
          // Для batch операцій operation.sql повинен бути масивом
          const statements = Array.isArray(operation.sql) ? operation.sql : [operation.sql];
          const batchResult = await db.batch(
            statements.map(sql => db.prepare(sql).bind(...(operation.params || [])))
          );
          result = batchResult.map(res => ({
            changes: res.changes,
            lastRowId: res.meta?.last_row_id
          }));
          break;

        default:
          throw new Error(`Непідтримувана D1 операція: ${operation.action}`);
      }

      const executionTime = Date.now() - startTime;
      this.updateMetrics('d1', operation.action, true, executionTime);

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
        executionTime
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.updateMetrics('d1', operation.action, false, executionTime);
      
      console.error('❌ Помилка D1 операції:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Невідома помилка D1',
        timestamp: new Date().toISOString(),
        executionTime
      };
    }
  }

  /**
   * Виконання операції з KV Storage
   */
  async kvOperation(operation: CloudflareKVOperation, options?: MCPCallOptions): Promise<MCPResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🔑 KV операція: ${operation.action} для ключа ${operation.key}`);

      if (!this.bindings?.CACHE_KV) {
        throw new Error('CACHE_KV binding не знайдено');
      }

      let result: any;
      const kv = this.bindings.CACHE_KV;

      switch (operation.action) {
        case 'put':
          await kv.put(operation.key, operation.value, {
            expirationTtl: operation.options?.ttl,
            metadata: operation.options?.metadata
          });
          result = { stored: true, key: operation.key };
          break;

        case 'get':
          const value = await kv.get(operation.key, { type: 'text' });
          const metadata = await kv.getWithMetadata(operation.key);
          result = {
            key: operation.key,
            value,
            metadata: metadata.metadata
          };
          break;

        case 'delete':
          await kv.delete(operation.key);
          result = { deleted: true, key: operation.key };
          break;

        case 'list':
          const listResult = await kv.list({
            prefix: operation.options?.prefix,
            limit: operation.options?.limit || 100
          });
          result = {
            keys: listResult.keys.map(key => ({
              name: key.name,
              metadata: key.metadata
            })),
            listComplete: listResult.list_complete,
            cursor: listResult.cursor
          };
          break;

        default:
          throw new Error(`Непідтримувана KV операція: ${operation.action}`);
      }

      const executionTime = Date.now() - startTime;
      this.updateMetrics('kv', operation.action, true, executionTime);

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
        executionTime
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.updateMetrics('kv', operation.action, false, executionTime);
      
      console.error('❌ Помилка KV операції:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Невідома помилка KV',
        timestamp: new Date().toISOString(),
        executionTime
      };
    }
  }

  /**
   * Відправка події до Analytics Engine
   */
  async sendAnalyticsEvent(event: CloudflareAnalyticsEvent, options?: MCPCallOptions): Promise<MCPResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`📊 Analytics подія: ${event.event}`);

      if (!this.bindings?.ANALYTICS) {
        throw new Error('ANALYTICS binding не знайдено');
      }

      const analytics = this.bindings.ANALYTICS;
      
      await analytics.writeDataPoint({
        blobs: [event.event, event.userId || '', event.sessionId || ''],
        doubles: [Date.now()],
        indexes: [event.event]
      });

      const executionTime = Date.now() - startTime;
      this.updateMetrics('analytics', 'writeDataPoint', true, executionTime);

      return {
        success: true,
        data: { eventSent: true, event: event.event },
        timestamp: new Date().toISOString(),
        executionTime
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.updateMetrics('analytics', 'writeDataPoint', false, executionTime);
      
      console.error('❌ Помилка Analytics операції:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Невідома помилка Analytics',
        timestamp: new Date().toISOString(),
        executionTime
      };
    }
  }

  /**
   * Health check всіх сервісів
   */
  async healthCheck(): Promise<ServiceHealth[]> {
    const services: ServiceHealth[] = [];
    const checkTime = new Date();

    // Перевірка R2
    try {
      const startTime = Date.now();
      if (this.bindings?.R2_BUCKET) {
        await this.bindings.R2_BUCKET.head('health-check');
        services.push({
          service: 'r2',
          status: 'healthy',
          responseTime: Date.now() - startTime,
          lastCheck: checkTime.toISOString()
        });
      } else {
        services.push({
          service: 'r2',
          status: 'unhealthy',
          responseTime: 0,
          lastCheck: checkTime.toISOString(),
          error: 'R2_BUCKET binding не знайдено'
        });
      }
    } catch (error) {
      services.push({
        service: 'r2',
        status: 'degraded',
        responseTime: 0,
        lastCheck: checkTime.toISOString(),
        error: error instanceof Error ? error.message : 'Невідома помилка'
      });
    }

    // Перевірка D1
    try {
      const startTime = Date.now();
      if (this.bindings?.DB) {
        await this.bindings.DB.prepare('SELECT 1').first();
        services.push({
          service: 'd1',
          status: 'healthy',
          responseTime: Date.now() - startTime,
          lastCheck: checkTime.toISOString()
        });
      } else {
        services.push({
          service: 'd1',
          status: 'unhealthy',
          responseTime: 0,
          lastCheck: checkTime.toISOString(),
          error: 'DB binding не знайдено'
        });
      }
    } catch (error) {
      services.push({
        service: 'd1',
        status: 'degraded',
        responseTime: 0,
        lastCheck: checkTime.toISOString(),
        error: error instanceof Error ? error.message : 'Невідома помилка'
      });
    }

    // Перевірка KV
    try {
      const startTime = Date.now();
      if (this.bindings?.CACHE_KV) {
        await this.bindings.CACHE_KV.get('health-check');
        services.push({
          service: 'kv',
          status: 'healthy',
          responseTime: Date.now() - startTime,
          lastCheck: checkTime.toISOString()
        });
      } else {
        services.push({
          service: 'kv',
          status: 'unhealthy',
          responseTime: 0,
          lastCheck: checkTime.toISOString(),
          error: 'CACHE_KV binding не знайдено'
        });
      }
    } catch (error) {
      services.push({
        service: 'kv',
        status: 'degraded',
        responseTime: 0,
        lastCheck: checkTime.toISOString(),
        error: error instanceof Error ? error.message : 'Невідома помилка'
      });
    }

    this.lastHealthCheck = checkTime;
    return services;
  }

  /**
   * Отримання метрик
   */
  getMetrics(): Map<string, any> {
    return this.metrics;
  }

  /**
   * Оновлення метрик
   */
  private updateMetrics(service: string, operation: string, success: boolean, executionTime: number): void {
    const key = `${service}.${operation}`;
    const current = this.metrics.get(key) || {
      count: 0,
      successCount: 0,
      totalTime: 0,
      errors: 0
    };

    current.count++;
    current.totalTime += executionTime;
    
    if (success) {
      current.successCount++;
    } else {
      current.errors++;
    }

    current.successRate = (current.successCount / current.count) * 100;
    current.averageTime = current.totalTime / current.count;

    this.metrics.set(key, current);
  }
}
