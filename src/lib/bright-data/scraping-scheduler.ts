/**
 * Планувальник скрапінгу для автоматизованого збору даних
 * Інтегрується з Cloudflare Cron Triggers та Durable Objects
 */

import { AutomatedScraper, ScrapingJob } from './automated-scraper';
import { AIModelAnalyzer } from './ai-model-analyzer';

export interface ScheduledTask {
  id: string;
  name: string;
  description: string;
  cronExpression: string;
  platforms: string[];
  isActive: boolean;
  lastRun?: string;
  nextRun: string;
  runCount: number;
  successCount: number;
  failureCount: number;
  config: TaskConfig;
  createdAt: string;
  updatedAt: string;
}

export interface TaskConfig {
  maxModelsPerPlatform: number;
  qualityThreshold: number;
  enableAIAnalysis: boolean;
  notifyOnCompletion: boolean;
  retryFailedJobs: boolean;
  cleanupOldData: boolean;
  dataRetentionDays: number;
}

export interface SchedulerStats {
  totalTasks: number;
  activeTasks: number;
  totalRuns: number;
  successRate: number;
  lastActivity: string;
  upcomingTasks: UpcomingTask[];
  recentJobs: RecentJob[];
}

export interface UpcomingTask {
  taskId: string;
  taskName: string;
  scheduledTime: string;
  platforms: string[];
}

export interface RecentJob {
  jobId: string;
  taskId: string;
  status: string;
  startTime: string;
  endTime?: string;
  modelsScraped: number;
}

export class ScrapingScheduler {
  private tasks: Map<string, ScheduledTask> = new Map();
  private scraper: AutomatedScraper;
  private analyzer: AIModelAnalyzer;
  private isRunning: boolean = false;
  private intervalId?: NodeJS.Timeout;

  constructor() {
    this.scraper = new AutomatedScraper();
    this.analyzer = new AIModelAnalyzer();
    this.initializeDefaultTasks();
  }

  /**
   * Ініціалізація стандартних завдань
   */
  private initializeDefaultTasks(): void {
    console.log('📅 Ініціалізація планувальника скрапінгу');

    // Щоденний скрапінг популярних моделей
    this.createTask({
      name: 'Daily Popular Models',
      description: 'Щоденний збір популярних моделей з усіх платформ',
      cronExpression: '0 2 * * *', // Щодня о 2:00
      platforms: ['printables.com', 'makerworld.com', 'thangs.com'],
      config: {
        maxModelsPerPlatform: 100,
        qualityThreshold: 0.7,
        enableAIAnalysis: true,
        notifyOnCompletion: true,
        retryFailedJobs: true,
        cleanupOldData: true,
        dataRetentionDays: 30
      }
    });

    // Щотижневий глибокий аналіз
    this.createTask({
      name: 'Weekly Deep Analysis',
      description: 'Щотижневий глибокий аналіз трендів та якісних моделей',
      cronExpression: '0 1 * * 0', // Щонеділі о 1:00
      platforms: ['printables.com', 'makerworld.com', 'thangs.com'],
      config: {
        maxModelsPerPlatform: 500,
        qualityThreshold: 0.8,
        enableAIAnalysis: true,
        notifyOnCompletion: true,
        retryFailedJobs: true,
        cleanupOldData: false,
        dataRetentionDays: 90
      }
    });

    // Швидкий скрапінг трендових моделей
    this.createTask({
      name: 'Trending Models Check',
      description: 'Швидка перевірка трендових моделей кожні 6 годин',
      cronExpression: '0 */6 * * *', // Кожні 6 годин
      platforms: ['printables.com', 'makerworld.com'],
      config: {
        maxModelsPerPlatform: 50,
        qualityThreshold: 0.6,
        enableAIAnalysis: false,
        notifyOnCompletion: false,
        retryFailedJobs: false,
        cleanupOldData: false,
        dataRetentionDays: 7
      }
    });
  }

  /**
   * Створення нового завдання
   */
  createTask(taskData: {
    name: string;
    description: string;
    cronExpression: string;
    platforms: string[];
    config: TaskConfig;
  }): string {
    const taskId = this.generateTaskId();
    
    const task: ScheduledTask = {
      id: taskId,
      name: taskData.name,
      description: taskData.description,
      cronExpression: taskData.cronExpression,
      platforms: taskData.platforms,
      isActive: true,
      nextRun: this.calculateNextRun(taskData.cronExpression),
      runCount: 0,
      successCount: 0,
      failureCount: 0,
      config: taskData.config,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.tasks.set(taskId, task);
    console.log(`✅ Створено завдання: ${task.name} (${taskId})`);
    
    return taskId;
  }

  /**
   * Запуск планувальника
   */
  start(): void {
    if (this.isRunning) {
      console.log('⚠️ Планувальник вже запущений');
      return;
    }

    console.log('🚀 Запуск планувальника скрапінгу');
    this.isRunning = true;

    // Перевіряємо завдання кожну хвилину
    this.intervalId = setInterval(() => {
      this.checkScheduledTasks();
    }, 60000);

    // Одразу перевіряємо завдання
    this.checkScheduledTasks();
  }

  /**
   * Зупинка планувальника
   */
  stop(): void {
    if (!this.isRunning) {
      console.log('⚠️ Планувальник вже зупинений');
      return;
    }

    console.log('🛑 Зупинка планувальника скрапінгу');
    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
  }

  /**
   * Перевірка запланованих завдань
   */
  private async checkScheduledTasks(): Promise<void> {
    const now = new Date();
    
    for (const [taskId, task] of this.tasks) {
      if (!task.isActive) continue;

      const nextRunTime = new Date(task.nextRun);
      
      if (now >= nextRunTime) {
        console.log(`⏰ Час виконання завдання: ${task.name}`);
        await this.executeTask(taskId);
      }
    }
  }

  /**
   * Виконання завдання
   */
  private async executeTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) return;

    try {
      console.log(`🔄 Виконання завдання: ${task.name}`);
      
      // Оновлюємо статистику
      task.runCount++;
      task.lastRun = new Date().toISOString();
      task.nextRun = this.calculateNextRun(task.cronExpression);
      task.updatedAt = new Date().toISOString();

      // Запускаємо скрапінг
      const jobId = await this.scraper.scrapePopularModels(task.platforms);
      
      // Моніторимо виконання
      await this.monitorJob(jobId, taskId);

    } catch (error) {
      console.error(`❌ Помилка виконання завдання ${task.name}:`, error);
      task.failureCount++;
      this.tasks.set(taskId, task);
    }
  }

  /**
   * Моніторинг виконання завдання
   */
  private async monitorJob(jobId: string, taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) return;

    const maxWaitTime = 30 * 60 * 1000; // 30 хвилин
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const jobStatus = this.scraper.getJobStatus(jobId);
      
      if (!jobStatus) {
        console.error(`❌ Завдання ${jobId} не знайдено`);
        task.failureCount++;
        break;
      }

      if (jobStatus.status === 'completed') {
        console.log(`✅ Завдання ${task.name} успішно завершено`);
        task.successCount++;
        
        // Якщо увімкнений AI аналіз, обробляємо результати
        if (task.config.enableAIAnalysis) {
          await this.processJobResults(jobStatus);
        }
        
        break;
      }

      if (jobStatus.status === 'failed') {
        console.error(`❌ Завдання ${task.name} не вдалося`);
        task.failureCount++;
        
        // Повторна спроба, якщо увімкнено
        if (task.config.retryFailedJobs) {
          console.log(`🔄 Повторна спроба завдання ${task.name}`);
          setTimeout(() => {
            this.scraper.scrapePopularModels(task.platforms);
          }, 5 * 60 * 1000); // Через 5 хвилин
        }
        
        break;
      }

      // Чекаємо 30 секунд перед наступною перевіркою
      await new Promise(resolve => setTimeout(resolve, 30000));
    }

    this.tasks.set(taskId, task);
  }

  /**
   * Обробка результатів завдання з AI аналізом
   */
  private async processJobResults(job: ScrapingJob): Promise<void> {
    console.log(`🤖 AI обробка результатів завдання ${job.id}`);
    
    try {
      for (const result of job.results) {
        if (result.success && result.data) {
          // Тут буде логіка витягування даних моделі та AI аналізу
          console.log(`🔍 Аналіз моделі з ${result.platform}`);
        }
      }
    } catch (error) {
      console.error(`❌ Помилка AI обробки:`, error);
    }
  }

  /**
   * Розрахунок наступного запуску
   */
  private calculateNextRun(cronExpression: string): string {
    // Спрощена логіка розрахунку наступного запуску
    // В реальному проекті використовувати бібліотеку cron-parser
    const now = new Date();
    
    // Парсимо cron вираз (хвилини години дні місяці дні_тижня)
    const parts = cronExpression.split(' ');
    
    if (parts.length !== 5) {
      throw new Error(`Невірний cron вираз: ${cronExpression}`);
    }

    // Спрощений розрахунок - додаємо 1 день для щоденних завдань
    if (cronExpression.includes('* * *')) {
      now.setDate(now.getDate() + 1);
    } else if (cronExpression.includes('*/6')) {
      now.setHours(now.getHours() + 6);
    } else if (cronExpression.includes('* * 0')) {
      // Щотижня
      const daysUntilSunday = 7 - now.getDay();
      now.setDate(now.getDate() + daysUntilSunday);
    }

    return now.toISOString();
  }

  /**
   * Отримання статистики планувальника
   */
  getStats(): SchedulerStats {
    const tasks = Array.from(this.tasks.values());
    const activeTasks = tasks.filter(t => t.isActive);
    const totalRuns = tasks.reduce((sum, t) => sum + t.runCount, 0);
    const totalSuccess = tasks.reduce((sum, t) => sum + t.successCount, 0);

    return {
      totalTasks: tasks.length,
      activeTasks: activeTasks.length,
      totalRuns,
      successRate: totalRuns > 0 ? totalSuccess / totalRuns : 0,
      lastActivity: this.getLastActivity(),
      upcomingTasks: this.getUpcomingTasks(),
      recentJobs: this.getRecentJobs()
    };
  }

  /**
   * Отримання найближчих завдань
   */
  private getUpcomingTasks(): UpcomingTask[] {
    return Array.from(this.tasks.values())
      .filter(t => t.isActive)
      .sort((a, b) => new Date(a.nextRun).getTime() - new Date(b.nextRun).getTime())
      .slice(0, 5)
      .map(t => ({
        taskId: t.id,
        taskName: t.name,
        scheduledTime: t.nextRun,
        platforms: t.platforms
      }));
  }

  /**
   * Отримання останніх завдань
   */
  private getRecentJobs(): RecentJob[] {
    const activeJobs = this.scraper.getActiveJobs();
    
    return activeJobs
      .sort((a, b) => new Date(b.startTime || '').getTime() - new Date(a.startTime || '').getTime())
      .slice(0, 10)
      .map(job => ({
        jobId: job.id,
        taskId: 'unknown', // Тут буде зв'язок з завданням
        status: job.status,
        startTime: job.startTime || '',
        endTime: job.endTime,
        modelsScraped: job.results.length
      }));
  }

  /**
   * Отримання часу останньої активності
   */
  private getLastActivity(): string {
    const tasks = Array.from(this.tasks.values());
    const lastRuns = tasks
      .filter(t => t.lastRun)
      .map(t => new Date(t.lastRun!).getTime());
    
    if (lastRuns.length === 0) return new Date().toISOString();
    
    const lastActivity = Math.max(...lastRuns);
    return new Date(lastActivity).toISOString();
  }

  /**
   * Допоміжні методи
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Публічні методи управління
   */
  getTask(taskId: string): ScheduledTask | null {
    return this.tasks.get(taskId) || null;
  }

  getAllTasks(): ScheduledTask[] {
    return Array.from(this.tasks.values());
  }

  updateTask(taskId: string, updates: Partial<ScheduledTask>): boolean {
    const task = this.tasks.get(taskId);
    if (!task) return false;

    const updatedTask = { ...task, ...updates, updatedAt: new Date().toISOString() };
    this.tasks.set(taskId, updatedTask);
    return true;
  }

  deleteTask(taskId: string): boolean {
    return this.tasks.delete(taskId);
  }

  activateTask(taskId: string): boolean {
    return this.updateTask(taskId, { isActive: true });
  }

  deactivateTask(taskId: string): boolean {
    return this.updateTask(taskId, { isActive: false });
  }

  isSchedulerRunning(): boolean {
    return this.isRunning;
  }
}
