/**
 * Тестування інтеграції Bright Data
 * Цей файл для тестування функціональності скрапінгу
 */

import { BrightDataScraper } from './scraper';
import { BrightDataScraperManager } from './scraper-manager';
import { MakerWorldScraper } from './platform-scrapers/makerworld-scraper';
import { PrintablesScraper } from './platform-scrapers/printables-scraper';
import { ThangsScraper } from './platform-scrapers/thangs-scraper';
import { MarketIntelligenceService } from './market-intelligence';

export class BrightDataTestSuite {
  private brightDataScraper: BrightDataScraper;
  private scraperManager: BrightDataScraperManager;
  private marketIntelligence: MarketIntelligenceService;

  constructor() {
    this.brightDataScraper = new BrightDataScraper();
    this.scraperManager = new BrightDataScraperManager();
    this.marketIntelligence = new MarketIntelligenceService();
  }

  /**
   * Тестування базового Bright Data скрапера
   */
  async testBasicScraper(): Promise<void> {
    console.log('🧪 Тестування базового Bright Data скрапера...');

    try {
      // Тест скрапінгу як Markdown
      const markdownResult = await this.brightDataScraper.scrapeAsMarkdown('https://www.printables.com');
      console.log('✅ Markdown скрапінг:', markdownResult.success ? 'Успішно' : 'Помилка');

      // Тест скрапінгу як HTML
      const htmlResult = await this.brightDataScraper.scrapeAsHtml('https://www.printables.com');
      console.log('✅ HTML скрапінг:', htmlResult.success ? 'Успішно' : 'Помилка');

      // Тест пошуку
      const searchResult = await this.brightDataScraper.searchEngine('3D printing models');
      console.log('✅ Пошук в Google:', searchResult.success ? 'Успішно' : 'Помилка');

      // Тест статистики
      const stats = await this.brightDataScraper.getSessionStats();
      console.log('✅ Статистика сесії:', stats ? 'Отримано' : 'Помилка');

    } catch (error) {
      console.error('❌ Помилка тестування базового скрапера:', error);
    }
  }

  /**
   * Тестування платформо-специфічних скраперів
   */
  async testPlatformScrapers(): Promise<void> {
    console.log('🧪 Тестування платформо-специфічних скраперів...');

    try {
      // Тест MakerWorld скрапера
      console.log('📡 Тестування MakerWorld скрапера...');
      const makerWorldScraper = new MakerWorldScraper();
      const makerWorldModels = await makerWorldScraper.scrapePopularModels(5);
      console.log(`✅ MakerWorld: отримано ${makerWorldModels.length} моделей`);

      // Тест Printables скрапера
      console.log('📡 Тестування Printables скрапера...');
      const printablesScraper = new PrintablesScraper();
      const printablesModels = await printablesScraper.scrapePopularModels(5);
      console.log(`✅ Printables: отримано ${printablesModels.length} моделей`);

      // Тест Thangs скрапера
      console.log('📡 Тестування Thangs скрапера...');
      const thangsScraper = new ThangsScraper();
      const thangsModels = await thangsScraper.scrapePopularModels(5);
      console.log(`✅ Thangs: отримано ${thangsModels.length} моделей`);

    } catch (error) {
      console.error('❌ Помилка тестування платформних скраперів:', error);
    }
  }

  /**
   * Тестування менеджера скраперів
   */
  async testScraperManager(): Promise<void> {
    console.log('🧪 Тестування менеджера скраперів...');

    try {
      // Тест скрапінгу з усіх платформ
      console.log('📡 Тестування скрапінгу з усіх платформ...');
      const allModels = await this.scraperManager.scrapeAllPlatforms({
        modelsPerPlatform: 3,
        delayBetweenRequests: 1000
      });
      console.log(`✅ Всі платформи: отримано ${allModels.length} моделей`);

      // Тест пошуку
      console.log('🔍 Тестування пошуку...');
      const searchModels = await this.scraperManager.searchAllPlatforms('miniature', {
        modelsPerPlatform: 2
      });
      console.log(`✅ Пошук: знайдено ${searchModels.length} моделей`);

      // Тест скрапінгу за категорією
      console.log('📂 Тестування скрапінгу за категорією...');
      const categoryModels = await this.scraperManager.scrapeCategoryAllPlatforms('Games', {
        modelsPerPlatform: 2
      });
      console.log(`✅ Категорія: отримано ${categoryModels.length} моделей`);

      // Отримання статистики
      const stats = this.scraperManager.getStats();
      console.log('📊 Статистика скрапінгу:', stats);

    } catch (error) {
      console.error('❌ Помилка тестування менеджера скраперів:', error);
    }
  }

  /**
   * Тестування Market Intelligence
   */
  async testMarketIntelligence(): Promise<void> {
    console.log('🧪 Тестування Market Intelligence...');

    try {
      // Тест аналізу конкурентів (mock дані)
      console.log('📊 Тестування аналізу конкурентів (mock)...');
      const mockCompetitors = await this.marketIntelligence.analyzeCompetitors(false);
      console.log(`✅ Mock конкуренти: отримано ${mockCompetitors.length} записів`);

      // Тест аналізу конкурентів (реальні дані)
      console.log('📊 Тестування аналізу конкурентів (реальні дані)...');
      const realCompetitors = await this.marketIntelligence.analyzeCompetitors(true);
      console.log(`✅ Реальні конкуренти: отримано ${realCompetitors.length} записів`);

      // Тест аналізу трендів
      console.log('📈 Тестування аналізу трендів...');
      const trends = await this.marketIntelligence.analyzeMarketTrends();
      console.log(`✅ Тренди: отримано ${trends.length} записів`);

      // Тест рекомендацій по ціноутворенню
      console.log('💰 Тестування рекомендацій по ціноутворенню...');
      const pricingInsights = await this.marketIntelligence.generatePricingInsights('Miniatures');
      console.log('✅ Ціноутворення: отримано рекомендації');

      // Тест популярних ключових слів
      console.log('🔑 Тестування популярних ключових слів...');
      const keywords = await this.marketIntelligence.getPopularKeywords();
      console.log(`✅ Ключові слова: отримано ${keywords.length} записів`);

      // Тест SWOT аналізу
      console.log('🎯 Тестування SWOT аналізу...');
      const swot = await this.marketIntelligence.analyzeCompetitiveAdvantages();
      console.log('✅ SWOT: отримано аналіз');

    } catch (error) {
      console.error('❌ Помилка тестування Market Intelligence:', error);
    }
  }

  /**
   * Тестування скрапінгу окремої моделі
   */
  async testSingleModelScraping(): Promise<void> {
    console.log('🧪 Тестування скрапінгу окремої моделі...');

    const testUrls = [
      'https://makerworld.com/en/models/12345',
      'https://www.printables.com/model/67890',
      'https://thangs.com/designer/test/model-name'
    ];

    for (const url of testUrls) {
      try {
        console.log(`🔍 Тестування URL: ${url}`);
        const model = await this.scraperManager.scrapeModelByUrl(url);
        
        if (model) {
          console.log(`✅ Модель скрапнута: ${model.title}`);
        } else {
          console.log(`⚠️ Модель не знайдена: ${url}`);
        }
      } catch (error) {
        console.error(`❌ Помилка скрапінгу ${url}:`, error);
      }
    }
  }

  /**
   * Запуск всіх тестів
   */
  async runAllTests(): Promise<void> {
    console.log('🚀 Запуск повного тестування Bright Data інтеграції...');
    console.log('=' .repeat(60));

    await this.testBasicScraper();
    console.log('-'.repeat(60));

    await this.testPlatformScrapers();
    console.log('-'.repeat(60));

    await this.testScraperManager();
    console.log('-'.repeat(60));

    await this.testMarketIntelligence();
    console.log('-'.repeat(60));

    await this.testSingleModelScraping();
    console.log('=' .repeat(60));

    console.log('✅ Тестування завершено!');
  }

  /**
   * Швидкий тест для перевірки базової функціональності
   */
  async quickTest(): Promise<void> {
    console.log('⚡ Швидкий тест Bright Data...');

    try {
      // Тест базового скрапінгу
      const result = await this.brightDataScraper.scrapeAsMarkdown('https://www.printables.com');
      console.log('📄 Базовий скрапінг:', result.success ? '✅ Працює' : '❌ Помилка');

      // Тест менеджера
      const models = await this.scraperManager.scrapePopularModels({
        modelsPerPlatform: 1,
        platforms: ['printables']
      });
      console.log('🔥 Популярні моделі:', models.length > 0 ? '✅ Працює' : '❌ Помилка');

      // Тест Market Intelligence
      const competitors = await this.marketIntelligence.analyzeCompetitors(false);
      console.log('📊 Market Intelligence:', competitors.length > 0 ? '✅ Працює' : '❌ Помилка');

      console.log('⚡ Швидкий тест завершено!');
    } catch (error) {
      console.error('❌ Помилка швидкого тесту:', error);
    }
  }
}

// Експорт для використання в інших файлах
export const testBrightDataIntegration = async () => {
  const testSuite = new BrightDataTestSuite();
  await testSuite.quickTest();
};

export const runFullBrightDataTests = async () => {
  const testSuite = new BrightDataTestSuite();
  await testSuite.runAllTests();
};
