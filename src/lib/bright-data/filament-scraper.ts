import { BrightDataMCPClient } from './mcp-client';

export interface FilamentProduct {
  id: string;
  title: string;
  manufacturer: string;
  manufacturerLogo?: string;
  price: number;
  originalPrice?: number;
  currency: string;
  discount?: number;
  rating: number;
  reviewCount: number;
  likes: number;
  sales: number;
  thumbnail: string;
  images: string[];
  category: string;
  tags: string[];
  description: string;
  specifications: {
    material: string;
    diameter: string;
    weight: string;
    spoolDiameter?: string;
    spoolWidth?: string;
    color: string;
    printingTemperature: string;
    bedTemperature?: string;
    density?: string;
  };
  features: string[];
  isFeatured: boolean;
  inStock: boolean;
  stockLevel?: number;
  warranty?: string;
  shipping: string;
  source: 'amazon' | 'aliexpress' | 'prusa' | 'bambu' | 'other';
  sourceUrl: string;
  lastUpdated: Date;
  availability: {
    inStock: boolean;
    quantity?: number;
    estimatedDelivery?: string;
  };
  priceHistory?: {
    date: Date;
    price: number;
  }[];
}

export interface FilamentScrapingResult {
  products: FilamentProduct[];
  totalFound: number;
  scrapedAt: Date;
  source: string;
  success: boolean;
  errors?: string[];
}

export class FilamentScraper {
  private mcpClient: BrightDataMCPClient;
  private readonly POPULAR_FILAMENT_KEYWORDS = [
    'PLA filament 1.75mm',
    'PETG filament 3D printing',
    'ABS filament black',
    'TPU flexible filament',
    'Wood PLA filament',
    'Carbon fiber filament',
    'Silk PLA filament',
    'Transparent PETG',
    'Glow in dark PLA',
    'Metal filled filament'
  ];

  constructor() {
    this.mcpClient = new BrightDataMCPClient();
  }

  /**
   * Скрапінг філаментів з Amazon
   */
  async scrapeAmazonFilaments(keyword: string = 'PLA filament 1.75mm', maxResults: number = 20): Promise<FilamentScrapingResult> {
    try {
      console.log(`🔍 Скрапінг Amazon філаментів за ключовим словом: ${keyword}`);

      const searchResults = await this.mcpClient.callTool('search_engine_Bright_Data', {
        query: `site:amazon.com ${keyword}`,
        engine: 'google'
      });

      const products: FilamentProduct[] = [];
      const errors: string[] = [];

      // Обробляємо результати пошуку
      for (const result of searchResults.results?.slice(0, maxResults) || []) {
        try {
          const productData = await this.scrapeAmazonProduct(result.url);
          if (productData) {
            products.push(productData);
          }
        } catch (error) {
          console.error(`❌ Помилка скрапінгу продукту ${result.url}:`, error);
          errors.push(`Failed to scrape ${result.url}: ${error}`);
        }
      }

      return {
        products,
        totalFound: products.length,
        scrapedAt: new Date(),
        source: 'amazon',
        success: true,
        errors: errors.length > 0 ? errors : undefined
      };

    } catch (error) {
      console.error('❌ Помилка скрапінгу Amazon:', error);
      return {
        products: [],
        totalFound: 0,
        scrapedAt: new Date(),
        source: 'amazon',
        success: false,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Скрапінг конкретного продукту з Amazon
   */
  private async scrapeAmazonProduct(url: string): Promise<FilamentProduct | null> {
    try {
      const productHtml = await this.mcpClient.callTool('scrape_as_html_Bright_Data', { url });
      
      // Парсимо HTML для отримання даних продукту
      const productData = this.parseAmazonProductHtml(productHtml, url);
      
      return productData;
    } catch (error) {
      console.error(`❌ Помилка скрапінгу Amazon продукту ${url}:`, error);
      return null;
    }
  }

  /**
   * Парсинг HTML Amazon продукту
   */
  private parseAmazonProductHtml(html: string, sourceUrl: string): FilamentProduct | null {
    try {
      // Симуляція парсингу Amazon HTML
      // В реальному проекті тут буде використовуватися cheerio або подібна бібліотека
      
      const mockProduct: FilamentProduct = {
        id: `amazon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        title: this.extractTitle(html) || 'Amazon PLA Filament',
        manufacturer: this.extractManufacturer(html) || 'Unknown',
        price: this.extractPrice(html) || 25.99,
        currency: 'USD',
        rating: this.extractRating(html) || 4.2,
        reviewCount: this.extractReviewCount(html) || 150,
        likes: Math.floor(Math.random() * 1000),
        sales: Math.floor(Math.random() * 5000),
        thumbnail: this.extractMainImage(html) || 'https://via.placeholder.com/400x400?text=Filament',
        images: this.extractImages(html) || ['https://via.placeholder.com/400x400?text=Filament'],
        category: this.extractCategory(html) || 'PLA',
        tags: this.extractTags(html) || ['pla', '1.75mm'],
        description: this.extractDescription(html) || 'High quality 3D printing filament',
        specifications: {
          material: 'PLA',
          diameter: '1.75mm',
          weight: '1kg',
          color: this.extractColor(html) || 'Black',
          printingTemperature: '190-220°C',
          bedTemperature: '50-60°C',
        },
        features: [
          'High precision diameter tolerance',
          'Vacuum sealed packaging',
          'Easy to print',
          'Low odor'
        ],
        isFeatured: Math.random() > 0.7,
        inStock: true,
        shipping: 'Free shipping',
        source: 'amazon',
        sourceUrl,
        lastUpdated: new Date(),
        availability: {
          inStock: true,
          quantity: Math.floor(Math.random() * 100) + 10,
          estimatedDelivery: '2-3 business days'
        }
      };

      return mockProduct;
    } catch (error) {
      console.error('❌ Помилка парсингу Amazon HTML:', error);
      return null;
    }
  }

  /**
   * Скрапінг популярних філаментів з усіх джерел
   */
  async scrapePopularFilaments(maxResults: number = 50): Promise<FilamentScrapingResult> {
    try {
      console.log('🔍 Скрапінг популярних філаментів з усіх джерел...');

      const allProducts: FilamentProduct[] = [];
      const allErrors: string[] = [];

      // Скрапимо з кількох ключових слів
      for (const keyword of this.POPULAR_FILAMENT_KEYWORDS.slice(0, 5)) {
        try {
          const result = await this.scrapeAmazonFilaments(keyword, Math.ceil(maxResults / 5));
          allProducts.push(...result.products);
          if (result.errors) {
            allErrors.push(...result.errors);
          }
        } catch (error) {
          console.error(`❌ Помилка скрапінгу за ключовим словом ${keyword}:`, error);
          allErrors.push(`Failed to scrape keyword ${keyword}: ${error}`);
        }
      }

      // Видаляємо дублікати за назвою
      const uniqueProducts = this.removeDuplicateProducts(allProducts);

      return {
        products: uniqueProducts.slice(0, maxResults),
        totalFound: uniqueProducts.length,
        scrapedAt: new Date(),
        source: 'multiple',
        success: true,
        errors: allErrors.length > 0 ? allErrors : undefined
      };

    } catch (error) {
      console.error('❌ Помилка скрапінгу популярних філаментів:', error);
      return {
        products: [],
        totalFound: 0,
        scrapedAt: new Date(),
        source: 'multiple',
        success: false,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Видалення дублікатів продуктів
   */
  private removeDuplicateProducts(products: FilamentProduct[]): FilamentProduct[] {
    const seen = new Set<string>();
    return products.filter(product => {
      const key = `${product.title.toLowerCase()}_${product.manufacturer.toLowerCase()}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * Методи для парсингу HTML елементів
   */
  private extractTitle(html: string): string | null {
    // Симуляція витягування назви з HTML
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    return titleMatch ? titleMatch[1].trim() : null;
  }

  private extractManufacturer(html: string): string | null {
    // Симуляція витягування виробника
    const brands = ['Prusament', 'eSUN', 'Hatchbox', 'SUNLU', 'Overture', 'PETG', 'Polymaker'];
    const foundBrand = brands.find(brand => html.toLowerCase().includes(brand.toLowerCase()));
    return foundBrand || null;
  }

  private extractPrice(html: string): number | null {
    // Симуляція витягування ціни
    const priceMatch = html.match(/\$(\d+\.?\d*)/);
    return priceMatch ? parseFloat(priceMatch[1]) : null;
  }

  private extractRating(html: string): number | null {
    // Симуляція витягування рейтингу
    return Math.round((Math.random() * 2 + 3) * 10) / 10; // 3.0 - 5.0
  }

  private extractReviewCount(html: string): number | null {
    // Симуляція витягування кількості відгуків
    return Math.floor(Math.random() * 1000) + 50;
  }

  private extractMainImage(html: string): string | null {
    // Симуляція витягування головного зображення
    const imageMatch = html.match(/<img[^>]+src="([^"]+)"/i);
    return imageMatch ? imageMatch[1] : null;
  }

  private extractImages(html: string): string[] | null {
    // Симуляція витягування всіх зображень
    const images = [];
    const imageMatches = html.matchAll(/<img[^>]+src="([^"]+)"/gi);
    for (const match of imageMatches) {
      images.push(match[1]);
    }
    return images.length > 0 ? images.slice(0, 5) : null;
  }

  private extractCategory(html: string): string | null {
    // Симуляція витягування категорії
    const categories = ['PLA', 'PETG', 'ABS', 'TPU', 'Nylon', 'Specialty'];
    const foundCategory = categories.find(cat => html.toLowerCase().includes(cat.toLowerCase()));
    return foundCategory || null;
  }

  private extractTags(html: string): string[] | null {
    // Симуляція витягування тегів
    const commonTags = ['1.75mm', '3mm', 'black', 'white', 'transparent', 'flexible', 'strong'];
    const foundTags = commonTags.filter(tag => html.toLowerCase().includes(tag.toLowerCase()));
    return foundTags.length > 0 ? foundTags : null;
  }

  private extractDescription(html: string): string | null {
    // Симуляція витягування опису
    const descMatch = html.match(/<meta[^>]+name="description"[^>]+content="([^"]+)"/i);
    return descMatch ? descMatch[1].trim() : null;
  }

  private extractColor(html: string): string | null {
    // Симуляція витягування кольору
    const colors = ['Black', 'White', 'Red', 'Blue', 'Green', 'Yellow', 'Orange', 'Purple', 'Transparent'];
    const foundColor = colors.find(color => html.toLowerCase().includes(color.toLowerCase()));
    return foundColor || null;
  }

  /**
   * Отримання трендових філаментів
   */
  async getTrendingFilaments(limit: number = 10): Promise<FilamentProduct[]> {
    try {
      const result = await this.scrapePopularFilaments(limit * 2);

      // Сортуємо за популярністю (лайки + продажі)
      const trending = result.products
        .sort((a, b) => (b.likes + b.sales) - (a.likes + a.sales))
        .slice(0, limit);

      return trending;
    } catch (error) {
      console.error('❌ Помилка отримання трендових філаментів:', error);
      return [];
    }
  }

  /**
   * Пошук філаментів за критеріями
   */
  async searchFilaments(criteria: {
    material?: string;
    priceRange?: [number, number];
    color?: string;
    diameter?: string;
    brand?: string;
  }): Promise<FilamentProduct[]> {
    try {
      let searchQuery = 'filament 3D printing';

      if (criteria.material) searchQuery += ` ${criteria.material}`;
      if (criteria.color) searchQuery += ` ${criteria.color}`;
      if (criteria.diameter) searchQuery += ` ${criteria.diameter}`;
      if (criteria.brand) searchQuery += ` ${criteria.brand}`;

      const result = await this.scrapeAmazonFilaments(searchQuery, 30);

      // Фільтруємо результати за критеріями
      let filteredProducts = result.products;

      if (criteria.priceRange) {
        filteredProducts = filteredProducts.filter(p =>
          p.price >= criteria.priceRange![0] && p.price <= criteria.priceRange![1]
        );
      }

      return filteredProducts;
    } catch (error) {
      console.error('❌ Помилка пошуку філаментів:', error);
      return [];
    }
  }
}
