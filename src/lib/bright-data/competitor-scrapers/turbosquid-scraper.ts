/**
 * TurboSquid platform scraper for high-end 3D models
 */

import { BaseCompetitorScraper } from './base-competitor-scraper';
import { CompetitorModel } from '@/types/competitor-models';
import * as cheerio from 'cheerio';

export class TurboSquidScraper extends BaseCompetitorScraper {
  constructor() {
    super('turbosquid', {
      rateLimitDelay: 3000, // TurboSquid has the strictest rate limiting
      maxModelsPerPlatform: 50
    });
  }

  buildTrendingUrl(): string {
    return 'https://www.turbosquid.com/3d-models?sort_column=popularity&sort_order=desc';
  }

  buildCategoryUrl(category: string): string {
    const categoryMap: Record<string, string> = {
      'characters': 'characters',
      'vehicles': 'vehicles',
      'architecture': 'architectural',
      'furniture': 'furniture',
      'electronics': 'electronics',
      'weapons': 'weapons',
      'animals': 'animals',
      'plants': 'plants',
      'aircraft': 'aircraft',
      'watercraft': 'watercraft',
      'space': 'space',
      'medical': 'medical',
      'industrial': 'industrial'
    };

    const turbosquidCategory = categoryMap[category.toLowerCase()] || category;
    return `https://www.turbosquid.com/3d-models/${turbosquidCategory}`;
  }

  buildSearchUrl(query: string): string {
    const encodedQuery = encodeURIComponent(query);
    return `https://www.turbosquid.com/Search/3D-Models?keyword=${encodedQuery}&sort_column=popularity`;
  }

  getPlatformCategories(): string[] {
    return [
      'characters',
      'vehicles',
      'architecture',
      'furniture',
      'electronics',
      'weapons',
      'animals',
      'plants',
      'aircraft',
      'watercraft',
      'space',
      'medical',
      'industrial',
      'sports',
      'food',
      'jewelry'
    ];
  }

  extractModelUrls(html: string): string[] {
    const $ = cheerio.load(html);
    const urls: string[] = [];

    // TurboSquid model links
    $('a[href*="/3d-models/"]').each((_, element) => {
      const href = $(element).attr('href');
      if (href && href.includes('/3d-models/') && !href.includes('/upload') && !href.includes('/edit')) {
        const fullUrl = href.startsWith('http') ? href : `https://www.turbosquid.com${href}`;
        if (!urls.includes(fullUrl)) {
          urls.push(fullUrl);
        }
      }
    });

    // Product grid selectors
    $('.product-item a, .grid-item a, .model-link').each((_, element) => {
      const href = $(element).attr('href');
      if (href && href.includes('/3d-models/')) {
        const fullUrl = href.startsWith('http') ? href : `https://www.turbosquid.com${href}`;
        if (!urls.includes(fullUrl)) {
          urls.push(fullUrl);
        }
      }
    });

    console.log(`🔗 Extracted ${urls.length} model URLs from TurboSquid`);
    return urls.slice(0, 30); // Smaller limit due to stricter rate limiting
  }

  parseModelData(html: string, url: string): CompetitorModel | null {
    try {
      const $ = cheerio.load(html);

      const title = this.extractTitle($);
      if (!title) {
        console.warn(`⚠️ No title found for ${url}`);
        return null;
      }

      const description = this.extractDescription($);
      const creatorName = this.extractCreatorName($);
      const category = this.extractCategory($);
      const tags = this.extractModelTags($);
      const pricing = this.extractPricing($);
      const stats = this.extractStats($);
      const technicalInfo = this.extractTechnicalInfo($);
      const thumbnailUrl = this.extractThumbnail($);

      const model: CompetitorModel = {
        id: this.generateModelId(url),
        platform: 'turbosquid',
        title,
        description,
        price: pricing.price,
        currency: pricing.currency,
        isFree: pricing.isFree,
        category: category || 'uncategorized',
        tags,
        formats: technicalInfo.formats,
        downloadCount: stats.downloads,
        viewCount: stats.views,
        likeCount: stats.likes,
        rating: stats.rating,
        reviewCount: stats.reviews,
        creatorName: creatorName || 'Unknown',
        thumbnailUrl,
        modelUrl: url,
        uploadDate: this.extractUploadDate($),
        fileSize: technicalInfo.fileSize,
        polygonCount: technicalInfo.polygonCount,
        textureResolution: technicalInfo.textureResolution,
        isAnimated: technicalInfo.isAnimated,
        isRigged: technicalInfo.isRigged,
        license: this.extractLicense($),
        scrapedAt: new Date().toISOString(),
        dataQuality: 'medium'
      };

      return model;

    } catch (error) {
      console.error(`❌ Error parsing TurboSquid model data for ${url}:`, error);
      return null;
    }
  }

  private extractTitle($: cheerio.CheerioAPI): string {
    return $('.product-title, .model-title, h1.title').first().text().trim() ||
           $('meta[property="og:title"]').attr('content') ||
           $('title').text().replace(' | TurboSquid', '').trim();
  }

  private extractDescription($: cheerio.CheerioAPI): string {
    return $('.product-description, .model-description, .description').first().text().trim() ||
           $('meta[property="og:description"]').attr('content') ||
           '';
  }

  private extractCreatorName($: cheerio.CheerioAPI): string {
    return $('.artist-name, .seller-name, .author').first().text().trim() ||
           $('.artist-link, .seller-link').first().text().trim() ||
           '';
  }

  private extractCategory($: cheerio.CheerioAPI): string {
    const breadcrumbs = $('.breadcrumb, .breadcrumbs, .nav-breadcrumb');
    if (breadcrumbs.length > 0) {
      const categoryLinks = breadcrumbs.find('a');
      if (categoryLinks.length > 1) {
        return categoryLinks.eq(-2).text().trim(); // Second to last breadcrumb
      }
    }

    return $('.category, .product-category').first().text().trim() || 
           this.inferCategoryFromTags($);
  }

  private extractModelTags($: cheerio.CheerioAPI): string[] {
    const tags: string[] = [];

    // Extract from tag elements
    $('.tags a, .tag-list a, .keywords a').each((_, element) => {
      const tag = $(element).text().trim();
      if (tag && !tags.includes(tag)) {
        tags.push(tag);
      }
    });

    // Extract from meta keywords
    const keywords = $('meta[name="keywords"]').attr('content');
    if (keywords) {
      const keywordTags = keywords.split(',').map(k => k.trim());
      keywordTags.forEach(tag => {
        if (tag && !tags.includes(tag)) {
          tags.push(tag);
        }
      });
    }

    // Extract from title and description
    const titleWords = this.extractTitle($).toLowerCase().split(/\s+/);
    titleWords.forEach(word => {
      if (word.length > 3 && !tags.includes(word)) {
        tags.push(word);
      }
    });

    return tags.slice(0, 15);
  }

  private extractPricing($: cheerio.CheerioAPI): { price: number; currency: string; isFree: boolean } {
    // Check for free models (rare on TurboSquid)
    const freeText = $('.free, .no-cost').text().toLowerCase();
    if (freeText.includes('free')) {
      return { price: 0, currency: 'USD', isFree: true };
    }

    // Extract price - TurboSquid typically shows prices prominently
    const priceElements = $('.price, .cost, .product-price, .buy-price');
    
    for (let i = 0; i < priceElements.length; i++) {
      const priceText = $(priceElements[i]).text();
      const priceMatch = priceText.match(/\$(\d+(?:\.\d{2})?)/);
      
      if (priceMatch) {
        const price = parseFloat(priceMatch[1]);
        return { price, currency: 'USD', isFree: price === 0 };
      }
    }

    // Check for subscription/credit-based pricing
    const creditText = $('.credits, .subscription').text();
    if (creditText) {
      const creditMatch = creditText.match(/(\d+)/);
      if (creditMatch) {
        // Convert credits to approximate USD (1 credit ≈ $1)
        const price = parseInt(creditMatch[1]);
        return { price, currency: 'USD', isFree: false };
      }
    }

    // Default to premium pricing for TurboSquid
    return { price: 49.99, currency: 'USD', isFree: false };
  }

  private extractStats($: cheerio.CheerioAPI): {
    views?: number;
    likes?: number;
    downloads?: number;
    rating?: number;
    reviews?: number;
  } {
    const stats = {
      views: undefined,
      likes: undefined,
      downloads: undefined,
      rating: undefined,
      reviews: undefined
    };

    // Extract view count
    const viewText = $('.views, .view-count').text();
    const viewMatch = viewText.match(/(\d+(?:,\d+)*)/);
    if (viewMatch) {
      stats.views = parseInt(viewMatch[1].replace(/,/g, ''));
    }

    // Extract rating
    const ratingElements = $('.rating, .stars, .score');
    ratingElements.each((_, element) => {
      const ratingText = $(element).text();
      const ratingMatch = ratingText.match(/(\d+(?:\.\d+)?)/);
      if (ratingMatch) {
        stats.rating = parseFloat(ratingMatch[1]);
        return false; // Break the loop
      }
    });

    // Extract review count
    const reviewText = $('.reviews, .review-count').text();
    const reviewMatch = reviewText.match(/(\d+)/);
    if (reviewMatch) {
      stats.reviews = parseInt(reviewMatch[1]);
    }

    // TurboSquid doesn't typically show download counts publicly
    // Estimate based on views and rating
    if (stats.views && stats.rating) {
      stats.downloads = Math.floor(stats.views * 0.02 * (stats.rating / 5));
    }

    return stats;
  }

  private extractTechnicalInfo($: cheerio.CheerioAPI): {
    formats: string[];
    fileSize?: number;
    polygonCount?: number;
    textureResolution?: string;
    isAnimated?: boolean;
    isRigged?: boolean;
  } {
    const info = {
      formats: [] as string[],
      fileSize: undefined,
      polygonCount: undefined,
      textureResolution: undefined,
      isAnimated: undefined,
      isRigged: undefined
    };

    // Extract file formats from specifications
    $('.specs, .specifications, .technical-details').find('li, .spec-row').each((_, element) => {
      const text = $(element).text().toLowerCase();
      
      // Look for format mentions
      const formatPatterns = ['max', 'obj', 'fbx', 'ma', 'mb', 'c4d', 'blend', '3ds'];
      formatPatterns.forEach(format => {
        if (text.includes(format) && !info.formats.includes(format.toUpperCase())) {
          info.formats.push(format.toUpperCase());
        }
      });
      
      // Extract polygon count
      if (text.includes('polygon') || text.includes('triangle')) {
        const polyMatch = text.match(/(\d+(?:,\d+)*)/);
        if (polyMatch) {
          info.polygonCount = parseInt(polyMatch[1].replace(/,/g, ''));
        }
      }
      
      // Extract texture resolution
      if (text.includes('texture') && text.includes('x')) {
        const texMatch = text.match(/(\d+x\d+)/);
        if (texMatch) {
          info.textureResolution = texMatch[1];
        }
      }
      
      // Check for animation
      if (text.includes('animated') || text.includes('animation')) {
        info.isAnimated = true;
      }
      
      // Check for rigging
      if (text.includes('rigged') || text.includes('rig')) {
        info.isRigged = true;
      }
    });

    // Default formats if none found
    if (info.formats.length === 0) {
      info.formats = ['MAX', 'OBJ', 'FBX'];
    }

    return info;
  }

  private extractThumbnail($: cheerio.CheerioAPI): string {
    return $('meta[property="og:image"]').attr('content') ||
           $('.product-image img, .model-preview img, .main-image img').first().attr('src') ||
           '';
  }

  private extractUploadDate($: cheerio.CheerioAPI): string {
    const dateText = $('.upload-date, .created-date, .published-date').text();
    const dateMatch = dateText.match(/(\d{4}-\d{2}-\d{2})/);
    return dateMatch ? dateMatch[1] : '';
  }

  private extractLicense($: cheerio.CheerioAPI): string {
    return $('.license, .usage-rights, .royalty-free').text().trim() || 'Royalty Free License';
  }

  private inferCategoryFromTags($: cheerio.CheerioAPI): string {
    const tags = this.extractModelTags($);
    const categoryKeywords = {
      'characters': ['character', 'person', 'human', 'avatar', 'figure'],
      'vehicles': ['car', 'vehicle', 'truck', 'motorcycle', 'automobile'],
      'architecture': ['building', 'house', 'architecture', 'structure'],
      'furniture': ['chair', 'table', 'furniture', 'sofa', 'desk'],
      'electronics': ['phone', 'computer', 'device', 'gadget', 'electronic'],
      'weapons': ['weapon', 'gun', 'sword', 'military', 'rifle'],
      'animals': ['animal', 'creature', 'pet', 'wildlife', 'beast'],
      'aircraft': ['plane', 'aircraft', 'helicopter', 'jet', 'airplane']
    };

    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      if (tags.some(tag => keywords.some(keyword => tag.toLowerCase().includes(keyword)))) {
        return category;
      }
    }

    return 'miscellaneous';
  }

  private generateModelId(url: string): string {
    const match = url.match(/\/3d-models\/([^\/]+)/);
    return match ? `turbosquid_${match[1]}` : `turbosquid_${Date.now()}`;
  }
}
