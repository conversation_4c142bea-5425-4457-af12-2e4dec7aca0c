/**
 * CGTrader platform scraper for professional 3D models
 */

import { BaseCompetitorScraper } from './base-competitor-scraper';
import { CompetitorModel } from '@/types/competitor-models';
import * as cheerio from 'cheerio';

export class CGTraderScraper extends BaseCompetitorScraper {
  constructor() {
    super('cgtrader', {
      rateLimitDelay: 2000, // CGTrader has stricter rate limiting
      maxModelsPerPlatform: 75
    });
  }

  buildTrendingUrl(): string {
    return 'https://www.cgtrader.com/3d-models?sort_by=trending';
  }

  buildCategoryUrl(category: string): string {
    const categoryMap: Record<string, string> = {
      'characters': 'characters',
      'vehicles': 'vehicles',
      'architecture': 'architectural',
      'furniture': 'furniture',
      'electronics': 'electronics',
      'weapons': 'weapons',
      'animals': 'animals',
      'plants': 'plants',
      'food': 'food-drinks',
      'sports': 'sports',
      'medical': 'medical',
      'industrial': 'industrial'
    };

    const cgtraderCategory = categoryMap[category.toLowerCase()] || category;
    return `https://www.cgtrader.com/3d-models/${cgtraderCategory}`;
  }

  buildSearchUrl(query: string): string {
    const encodedQuery = encodeURIComponent(query);
    return `https://www.cgtrader.com/3d-models?keywords=${encodedQuery}&sort_by=trending`;
  }

  getPlatformCategories(): string[] {
    return [
      'characters',
      'vehicles',
      'architecture', 
      'furniture',
      'electronics',
      'weapons',
      'animals',
      'plants',
      'food',
      'sports',
      'medical',
      'industrial',
      'jewelry',
      'toys',
      'science'
    ];
  }

  extractModelUrls(html: string): string[] {
    const $ = cheerio.load(html);
    const urls: string[] = [];

    // CGTrader model links
    $('a[href*="/3d-models/"]').each((_, element) => {
      const href = $(element).attr('href');
      if (href && href.includes('/3d-models/') && !href.includes('/edit') && !href.includes('/upload')) {
        const fullUrl = href.startsWith('http') ? href : `https://www.cgtrader.com${href}`;
        if (!urls.includes(fullUrl)) {
          urls.push(fullUrl);
        }
      }
    });

    // Model card selectors
    $('.product-item a, .model-item a, .grid-item a').each((_, element) => {
      const href = $(element).attr('href');
      if (href && href.includes('/3d-models/')) {
        const fullUrl = href.startsWith('http') ? href : `https://www.cgtrader.com${href}`;
        if (!urls.includes(fullUrl)) {
          urls.push(fullUrl);
        }
      }
    });

    console.log(`🔗 Extracted ${urls.length} model URLs from CGTrader`);
    return urls.slice(0, 50);
  }

  parseModelData(html: string, url: string): CompetitorModel | null {
    try {
      const $ = cheerio.load(html);

      const title = this.extractTitle($);
      if (!title) {
        console.warn(`⚠️ No title found for ${url}`);
        return null;
      }

      const description = this.extractDescription($);
      const creatorName = this.extractCreatorName($);
      const category = this.extractCategory($);
      const tags = this.extractModelTags($);
      const pricing = this.extractPricing($);
      const stats = this.extractStats($);
      const technicalInfo = this.extractTechnicalInfo($);
      const thumbnailUrl = this.extractThumbnail($);

      const model: CompetitorModel = {
        id: this.generateModelId(url),
        platform: 'cgtrader',
        title,
        description,
        price: pricing.price,
        currency: pricing.currency,
        isFree: pricing.isFree,
        category: category || 'uncategorized',
        tags,
        formats: technicalInfo.formats,
        downloadCount: stats.downloads,
        viewCount: stats.views,
        likeCount: stats.likes,
        rating: stats.rating,
        reviewCount: stats.reviews,
        creatorName: creatorName || 'Unknown',
        thumbnailUrl,
        modelUrl: url,
        uploadDate: this.extractUploadDate($),
        fileSize: technicalInfo.fileSize,
        polygonCount: technicalInfo.polygonCount,
        textureResolution: technicalInfo.textureResolution,
        isAnimated: technicalInfo.isAnimated,
        isRigged: technicalInfo.isRigged,
        license: this.extractLicense($),
        scrapedAt: new Date().toISOString(),
        dataQuality: 'medium'
      };

      return model;

    } catch (error) {
      console.error(`❌ Error parsing CGTrader model data for ${url}:`, error);
      return null;
    }
  }

  private extractTitle($: cheerio.CheerioAPI): string {
    return $('.product-title h1, .model-title h1, h1.title').first().text().trim() ||
           $('meta[property="og:title"]').attr('content') ||
           $('title').text().replace(' | CGTrader', '').trim();
  }

  private extractDescription($: cheerio.CheerioAPI): string {
    return $('.product-description, .model-description, .description-content').first().text().trim() ||
           $('meta[property="og:description"]').attr('content') ||
           '';
  }

  private extractCreatorName($: cheerio.CheerioAPI): string {
    return $('.seller-name, .author-name, .creator-name').first().text().trim() ||
           $('.user-link, .profile-link').first().text().trim() ||
           '';
  }

  private extractCategory($: cheerio.CheerioAPI): string {
    const breadcrumbs = $('.breadcrumb, .breadcrumbs');
    if (breadcrumbs.length > 0) {
      const categoryLink = breadcrumbs.find('a').last().text().trim();
      if (categoryLink) return categoryLink;
    }

    return $('.category, .product-category').first().text().trim() || 
           this.inferCategoryFromTags($);
  }

  private extractModelTags($: cheerio.CheerioAPI): string[] {
    const tags: string[] = [];

    // Extract from tag elements
    $('.tags a, .tag-list a, .product-tags a').each((_, element) => {
      const tag = $(element).text().trim();
      if (tag && !tags.includes(tag)) {
        tags.push(tag);
      }
    });

    // Extract from keywords
    const keywords = $('meta[name="keywords"]').attr('content');
    if (keywords) {
      const keywordTags = keywords.split(',').map(k => k.trim());
      keywordTags.forEach(tag => {
        if (tag && !tags.includes(tag)) {
          tags.push(tag);
        }
      });
    }

    return tags.slice(0, 15);
  }

  private extractPricing($: cheerio.CheerioAPI): { price: number; currency: string; isFree: boolean } {
    // Check for free models
    const freeIndicators = $('.free, .price-free, .no-cost').text().toLowerCase();
    if (freeIndicators.includes('free')) {
      return { price: 0, currency: 'USD', isFree: true };
    }

    // Extract price
    const priceText = $('.price, .cost, .product-price').first().text();
    const priceMatch = priceText.match(/[\$€£]?(\d+(?:\.\d{2})?)/);
    
    if (priceMatch) {
      const price = parseFloat(priceMatch[1]);
      let currency = 'USD';
      
      if (priceText.includes('€')) currency = 'EUR';
      else if (priceText.includes('£')) currency = 'GBP';
      
      return { price, currency, isFree: price === 0 };
    }

    // Check for subscription/premium models
    const subscriptionText = $('.subscription, .premium, .pro').text();
    if (subscriptionText) {
      return { price: 29.99, currency: 'USD', isFree: false }; // Estimated subscription price
    }

    return { price: 0, currency: 'USD', isFree: true };
  }

  private extractStats($: cheerio.CheerioAPI): {
    views?: number;
    likes?: number;
    downloads?: number;
    rating?: number;
    reviews?: number;
  } {
    const stats = {
      views: undefined,
      likes: undefined,
      downloads: undefined,
      rating: undefined,
      reviews: undefined
    };

    // Extract view count
    const viewText = $('.views, .view-count').text();
    const viewMatch = viewText.match(/(\d+(?:,\d+)*)/);
    if (viewMatch) {
      stats.views = parseInt(viewMatch[1].replace(/,/g, ''));
    }

    // Extract like count
    const likeText = $('.likes, .like-count, .favorites').text();
    const likeMatch = likeText.match(/(\d+(?:,\d+)*)/);
    if (likeMatch) {
      stats.likes = parseInt(likeMatch[1].replace(/,/g, ''));
    }

    // Extract download count
    const downloadText = $('.downloads, .download-count').text();
    const downloadMatch = downloadText.match(/(\d+(?:,\d+)*)/);
    if (downloadMatch) {
      stats.downloads = parseInt(downloadMatch[1].replace(/,/g, ''));
    }

    // Extract rating
    const ratingText = $('.rating, .stars, .score').text();
    const ratingMatch = ratingText.match(/(\d+(?:\.\d+)?)/);
    if (ratingMatch) {
      stats.rating = parseFloat(ratingMatch[1]);
    }

    // Extract review count
    const reviewText = $('.reviews, .review-count').text();
    const reviewMatch = reviewText.match(/(\d+)/);
    if (reviewMatch) {
      stats.reviews = parseInt(reviewMatch[1]);
    }

    return stats;
  }

  private extractTechnicalInfo($: cheerio.CheerioAPI): {
    formats: string[];
    fileSize?: number;
    polygonCount?: number;
    textureResolution?: string;
    isAnimated?: boolean;
    isRigged?: boolean;
  } {
    const info = {
      formats: [] as string[],
      fileSize: undefined,
      polygonCount: undefined,
      textureResolution: undefined,
      isAnimated: undefined,
      isRigged: undefined
    };

    // Extract file formats
    $('.formats, .file-formats, .available-formats').find('.format, span').each((_, element) => {
      const format = $(element).text().trim().toUpperCase();
      if (format && format.length <= 5 && !info.formats.includes(format)) {
        info.formats.push(format);
      }
    });

    // Extract from technical specifications
    $('.specs, .specifications, .technical-info').find('li, .spec-item').each((_, element) => {
      const text = $(element).text().toLowerCase();
      
      if (text.includes('polygon') || text.includes('triangle')) {
        const polyMatch = text.match(/(\d+(?:,\d+)*)/);
        if (polyMatch) {
          info.polygonCount = parseInt(polyMatch[1].replace(/,/g, ''));
        }
      }
      
      if (text.includes('texture') && text.includes('x')) {
        const texMatch = text.match(/(\d+x\d+)/);
        if (texMatch) {
          info.textureResolution = texMatch[1];
        }
      }
      
      if (text.includes('animated') || text.includes('animation')) {
        info.isAnimated = true;
      }
      
      if (text.includes('rigged') || text.includes('rig')) {
        info.isRigged = true;
      }
    });

    return info;
  }

  private extractThumbnail($: cheerio.CheerioAPI): string {
    return $('meta[property="og:image"]').attr('content') ||
           $('.product-image img, .model-preview img').first().attr('src') ||
           '';
  }

  private extractUploadDate($: cheerio.CheerioAPI): string {
    const dateText = $('.upload-date, .created-date, .published').text();
    const dateMatch = dateText.match(/(\d{4}-\d{2}-\d{2})/);
    return dateMatch ? dateMatch[1] : '';
  }

  private extractLicense($: cheerio.CheerioAPI): string {
    return $('.license, .usage-rights, .commercial-license').text().trim() || 'Royalty Free License';
  }

  private inferCategoryFromTags($: cheerio.CheerioAPI): string {
    const tags = this.extractModelTags($);
    const categoryKeywords = {
      'characters': ['character', 'person', 'human', 'avatar'],
      'vehicles': ['car', 'vehicle', 'truck', 'aircraft', 'ship'],
      'architecture': ['building', 'house', 'architecture', 'interior'],
      'furniture': ['chair', 'table', 'furniture', 'sofa'],
      'electronics': ['phone', 'computer', 'device', 'gadget'],
      'weapons': ['weapon', 'gun', 'sword', 'military'],
      'animals': ['animal', 'creature', 'pet', 'wildlife']
    };

    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      if (tags.some(tag => keywords.some(keyword => tag.toLowerCase().includes(keyword)))) {
        return category;
      }
    }

    return 'miscellaneous';
  }

  private generateModelId(url: string): string {
    const match = url.match(/\/3d-models\/([^\/]+)/);
    return match ? `cgtrader_${match[1]}` : `cgtrader_${Date.now()}`;
  }
}
