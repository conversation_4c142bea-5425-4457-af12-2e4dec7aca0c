/**
 * Base class for competitor platform scrapers
 */

import { BrightDataScraper } from '../scraper';
import { 
  CompetitorModel, 
  CompetitorScrapingConfig, 
  PlatformMetrics,
  ScrapingJob 
} from '@/types/competitor-models';

export abstract class BaseCompetitorScraper {
  protected brightData: BrightDataScraper;
  protected platform: string;
  protected config: CompetitorScrapingConfig;
  protected metrics: PlatformMetrics;

  constructor(platform: string, config: Partial<CompetitorScrapingConfig> = {}) {
    this.platform = platform;
    this.brightData = new BrightDataScraper();
    this.config = {
      platforms: [platform as any],
      maxModelsPerPlatform: 50,
      includeFreebies: true,
      rateLimitDelay: 2000,
      retryAttempts: 3,
      timeout: 30000,
      enableCaching: true,
      cacheExpiry: 3600000, // 1 hour
      dataQualityThreshold: 0.7,
      ...config
    };
    
    this.metrics = {
      platform,
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      healthStatus: 'healthy',
      errorRate: 0,
      rateLimitHits: 0
    };
  }

  /**
   * Abstract methods to be implemented by platform-specific scrapers
   */
  abstract buildTrendingUrl(): string;
  abstract buildCategoryUrl(category: string): string;
  abstract buildSearchUrl(query: string): string;
  abstract parseModelData(html: string, url: string): CompetitorModel | null;
  abstract extractModelUrls(html: string): string[];
  abstract getPlatformCategories(): string[];

  /**
   * Scrape trending models from the platform
   */
  async scrapeTrendingModels(maxModels: number = 20): Promise<CompetitorModel[]> {
    console.log(`🔥 Scraping trending models from ${this.platform}...`);
    
    try {
      const trendingUrl = this.buildTrendingUrl();
      const models = await this.scrapeModelsFromUrl(trendingUrl, maxModels);
      
      console.log(`✅ Scraped ${models.length} trending models from ${this.platform}`);
      return models;
    } catch (error) {
      console.error(`❌ Error scraping trending models from ${this.platform}:`, error);
      this.updateMetrics(false);
      return [];
    }
  }

  /**
   * Scrape models from a specific category
   */
  async scrapeCategoryModels(category: string, maxModels: number = 20): Promise<CompetitorModel[]> {
    console.log(`📂 Scraping ${category} models from ${this.platform}...`);
    
    try {
      const categoryUrl = this.buildCategoryUrl(category);
      const models = await this.scrapeModelsFromUrl(categoryUrl, maxModels);
      
      console.log(`✅ Scraped ${models.length} ${category} models from ${this.platform}`);
      return models;
    } catch (error) {
      console.error(`❌ Error scraping ${category} models from ${this.platform}:`, error);
      this.updateMetrics(false);
      return [];
    }
  }

  /**
   * Search for models by query
   */
  async searchModels(query: string, maxModels: number = 20): Promise<CompetitorModel[]> {
    console.log(`🔍 Searching "${query}" on ${this.platform}...`);
    
    try {
      const searchUrl = this.buildSearchUrl(query);
      const models = await this.scrapeModelsFromUrl(searchUrl, maxModels);
      
      console.log(`✅ Found ${models.length} models for "${query}" on ${this.platform}`);
      return models;
    } catch (error) {
      console.error(`❌ Error searching "${query}" on ${this.platform}:`, error);
      this.updateMetrics(false);
      return [];
    }
  }

  /**
   * Core scraping logic for extracting models from a URL
   */
  protected async scrapeModelsFromUrl(url: string, maxModels: number): Promise<CompetitorModel[]> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Get the page content using Bright Data
      const result = await this.brightData.scrapeAsHtml(url);
      
      if (!result.success) {
        throw new Error(`Failed to scrape ${url}: ${result.error}`);
      }

      const html = result.data;
      
      // Extract model URLs from the page
      const modelUrls = this.extractModelUrls(html);
      console.log(`📋 Found ${modelUrls.length} model URLs on ${url}`);

      const models: CompetitorModel[] = [];
      const urlsToScrape = modelUrls.slice(0, maxModels);

      // Scrape individual model pages
      for (let i = 0; i < urlsToScrape.length; i++) {
        const modelUrl = urlsToScrape[i];
        
        try {
          console.log(`📄 Scraping model ${i + 1}/${urlsToScrape.length}: ${modelUrl}`);
          
          const modelResult = await this.brightData.scrapeAsHtml(modelUrl);
          
          if (modelResult.success) {
            const model = this.parseModelData(modelResult.data, modelUrl);
            
            if (model && this.validateModelData(model)) {
              models.push(model);
            }
          }

          // Rate limiting
          if (i < urlsToScrape.length - 1) {
            await this.delay(this.config.rateLimitDelay);
          }

        } catch (error) {
          console.error(`❌ Error scraping model ${modelUrl}:`, error);
          continue;
        }
      }

      this.updateMetrics(true, Date.now() - startTime);
      return models;

    } catch (error) {
      this.updateMetrics(false, Date.now() - startTime);
      throw error;
    }
  }

  /**
   * Validate scraped model data quality
   */
  protected validateModelData(model: CompetitorModel): boolean {
    const requiredFields = ['title', 'platform', 'modelUrl'];
    const hasRequiredFields = requiredFields.every(field => model[field as keyof CompetitorModel]);
    
    if (!hasRequiredFields) {
      return false;
    }

    // Calculate data quality score
    let qualityScore = 0;
    const totalFields = 20; // Approximate number of fields
    
    if (model.title) qualityScore++;
    if (model.description) qualityScore++;
    if (model.price >= 0) qualityScore++;
    if (model.category) qualityScore++;
    if (model.tags && model.tags.length > 0) qualityScore++;
    if (model.formats && model.formats.length > 0) qualityScore++;
    if (model.creatorName) qualityScore++;
    if (model.thumbnailUrl) qualityScore++;
    if (model.downloadCount !== undefined) qualityScore++;
    if (model.rating !== undefined) qualityScore++;

    const quality = qualityScore / 10; // Normalize to 0-1
    model.dataQuality = quality >= 0.8 ? 'high' : quality >= 0.5 ? 'medium' : 'low';

    return quality >= this.config.dataQualityThreshold;
  }

  /**
   * Update platform metrics
   */
  protected updateMetrics(success: boolean, responseTime?: number): void {
    if (success) {
      this.metrics.successfulRequests++;
    } else {
      this.metrics.failedRequests++;
    }

    if (responseTime) {
      const totalTime = this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime;
      this.metrics.averageResponseTime = totalTime / this.metrics.totalRequests;
    }

    this.metrics.errorRate = this.metrics.failedRequests / this.metrics.totalRequests;
    this.metrics.lastRequestAt = new Date().toISOString();

    // Update health status
    if (this.metrics.errorRate > 0.5) {
      this.metrics.healthStatus = 'critical';
    } else if (this.metrics.errorRate > 0.2) {
      this.metrics.healthStatus = 'warning';
    } else {
      this.metrics.healthStatus = 'healthy';
    }
  }

  /**
   * Get platform metrics
   */
  getMetrics(): PlatformMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.metrics = {
      ...this.metrics,
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      errorRate: 0,
      rateLimitHits: 0
    };
  }

  /**
   * Utility method for delays
   */
  protected async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Extract price from text
   */
  protected extractPrice(text: string): { price: number; currency: string; isFree: boolean } {
    const freePatterns = /free|gratis|kostenlos|gratuit/i;
    
    if (freePatterns.test(text)) {
      return { price: 0, currency: 'USD', isFree: true };
    }

    const pricePattern = /[\$€£¥₹]?(\d+(?:\.\d{2})?)/;
    const match = text.match(pricePattern);
    
    if (match) {
      const price = parseFloat(match[1]);
      let currency = 'USD';
      
      if (text.includes('€')) currency = 'EUR';
      else if (text.includes('£')) currency = 'GBP';
      else if (text.includes('¥')) currency = 'JPY';
      else if (text.includes('₹')) currency = 'INR';
      
      return { price, currency, isFree: price === 0 };
    }

    return { price: 0, currency: 'USD', isFree: true };
  }

  /**
   * Clean and normalize text
   */
  protected cleanText(text: string): string {
    return text
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s-]/g, '')
      .trim();
  }

  /**
   * Extract tags from text
   */
  protected extractTags(text: string): string[] {
    const tagPattern = /#(\w+)/g;
    const matches = text.match(tagPattern);
    
    if (matches) {
      return matches.map(tag => tag.substring(1).toLowerCase());
    }

    // Fallback: split by common separators
    return text
      .split(/[,;|]/)
      .map(tag => this.cleanText(tag))
      .filter(tag => tag.length > 0 && tag.length < 30)
      .slice(0, 10); // Limit to 10 tags
  }
}
