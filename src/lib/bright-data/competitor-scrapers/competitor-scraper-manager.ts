/**
 * Manager for coordinating competitor scraping across multiple platforms
 */

import { SketchfabScraper } from './sketchfab-scraper';
import { CGTraderScraper } from './cgtrader-scraper';
import { TurboSquidScraper } from './turbosquid-scraper';
import { BaseCompetitorScraper } from './base-competitor-scraper';
import { 
  CompetitorModel, 
  CompetitorScrapingConfig, 
  CompetitorAnalysisResult,
  ScrapingJob,
  PlatformMetrics 
} from '@/types/competitor-models';

export class CompetitorScraperManager {
  private scrapers: Map<string, BaseCompetitorScraper>;
  private activeJobs: Map<string, ScrapingJob>;
  private config: CompetitorScrapingConfig;

  constructor(config: Partial<CompetitorScrapingConfig> = {}) {
    this.scrapers = new Map();
    this.activeJobs = new Map();
    
    this.config = {
      platforms: ['sketchfab', 'cgtrader', 'turbosquid'],
      maxModelsPerPlatform: 50,
      includeFreebies: true,
      rateLimitDelay: 2000,
      retryAttempts: 3,
      timeout: 30000,
      enableCaching: true,
      cacheExpiry: 3600000,
      dataQualityThreshold: 0.7,
      ...config
    };

    this.initializeScrapers();
  }

  private initializeScrapers(): void {
    this.scrapers.set('sketchfab', new SketchfabScraper());
    this.scrapers.set('cgtrader', new CGTraderScraper());
    this.scrapers.set('turbosquid', new TurboSquidScraper());
    
    console.log(`🚀 Initialized ${this.scrapers.size} competitor scrapers`);
  }

  /**
   * Scrape trending models from all configured platforms
   */
  async scrapeTrendingModels(): Promise<CompetitorAnalysisResult[]> {
    console.log('🔥 Starting trending models scraping across all platforms...');
    
    const jobId = this.generateJobId('trending');
    const job: ScrapingJob = {
      id: jobId,
      type: 'trending',
      platform: 'all',
      status: 'running',
      progress: 0,
      startedAt: new Date().toISOString(),
      config: {
        maxModels: this.config.maxModelsPerPlatform
      },
      results: {
        modelsScraped: 0,
        insightsGenerated: 0,
        trendsUpdated: 0
      }
    };

    this.activeJobs.set(jobId, job);

    try {
      const results: CompetitorAnalysisResult[] = [];
      const platforms = this.config.platforms;
      
      for (let i = 0; i < platforms.length; i++) {
        const platform = platforms[i];
        const scraper = this.scrapers.get(platform);
        
        if (!scraper) {
          console.warn(`⚠️ No scraper found for platform: ${platform}`);
          continue;
        }

        try {
          console.log(`📡 Scraping trending models from ${platform}...`);
          
          const models = await scraper.scrapeTrendingModels(this.config.maxModelsPerPlatform);
          const analysis = this.analyzeScrapedModels(platform, models);
          
          results.push(analysis);
          
          // Update job progress
          job.progress = Math.round(((i + 1) / platforms.length) * 100);
          job.results!.modelsScraped += models.length;
          
          console.log(`✅ Scraped ${models.length} trending models from ${platform}`);
          
          // Rate limiting between platforms
          if (i < platforms.length - 1) {
            await this.delay(this.config.rateLimitDelay);
          }
          
        } catch (error) {
          console.error(`❌ Error scraping ${platform}:`, error);
          continue;
        }
      }

      job.status = 'completed';
      job.completedAt = new Date().toISOString();
      
      console.log(`🎉 Completed trending models scraping. Total models: ${job.results!.modelsScraped}`);
      return results;

    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.completedAt = new Date().toISOString();
      
      console.error('❌ Trending models scraping failed:', error);
      throw error;
    }
  }

  /**
   * Scrape models from specific categories across platforms
   */
  async scrapeCategoryModels(category: string): Promise<CompetitorAnalysisResult[]> {
    console.log(`📂 Starting category scraping for: ${category}`);
    
    const jobId = this.generateJobId('category');
    const job: ScrapingJob = {
      id: jobId,
      type: 'category',
      platform: 'all',
      status: 'running',
      progress: 0,
      startedAt: new Date().toISOString(),
      config: {
        categories: [category],
        maxModels: this.config.maxModelsPerPlatform
      },
      results: {
        modelsScraped: 0,
        insightsGenerated: 0,
        trendsUpdated: 0
      }
    };

    this.activeJobs.set(jobId, job);

    try {
      const results: CompetitorAnalysisResult[] = [];
      const platforms = this.config.platforms;
      
      for (let i = 0; i < platforms.length; i++) {
        const platform = platforms[i];
        const scraper = this.scrapers.get(platform);
        
        if (!scraper) continue;

        try {
          const models = await scraper.scrapeCategoryModels(category, this.config.maxModelsPerPlatform);
          const analysis = this.analyzeScrapedModels(platform, models);
          
          results.push(analysis);
          
          job.progress = Math.round(((i + 1) / platforms.length) * 100);
          job.results!.modelsScraped += models.length;
          
          if (i < platforms.length - 1) {
            await this.delay(this.config.rateLimitDelay);
          }
          
        } catch (error) {
          console.error(`❌ Error scraping ${category} from ${platform}:`, error);
          continue;
        }
      }

      job.status = 'completed';
      job.completedAt = new Date().toISOString();
      
      return results;

    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.completedAt = new Date().toISOString();
      
      throw error;
    }
  }

  /**
   * Search for models across platforms
   */
  async searchModels(query: string): Promise<CompetitorAnalysisResult[]> {
    console.log(`🔍 Starting search for: "${query}"`);
    
    const results: CompetitorAnalysisResult[] = [];
    const platforms = this.config.platforms;
    
    for (const platform of platforms) {
      const scraper = this.scrapers.get(platform);
      
      if (!scraper) continue;

      try {
        const models = await scraper.searchModels(query, this.config.maxModelsPerPlatform);
        const analysis = this.analyzeScrapedModels(platform, models);
        
        results.push(analysis);
        
        await this.delay(this.config.rateLimitDelay);
        
      } catch (error) {
        console.error(`❌ Error searching "${query}" on ${platform}:`, error);
        continue;
      }
    }
    
    return results;
  }

  /**
   * Analyze scraped models and generate insights
   */
  private analyzeScrapedModels(platform: string, models: CompetitorModel[]): CompetitorAnalysisResult {
    const totalModels = models.length;
    const freeModels = models.filter(m => m.isFree).length;
    const paidModels = totalModels - freeModels;
    
    // Calculate average price (excluding free models)
    const paidModelPrices = models.filter(m => !m.isFree && m.price > 0).map(m => m.price);
    const averagePrice = paidModelPrices.length > 0 
      ? paidModelPrices.reduce((sum, price) => sum + price, 0) / paidModelPrices.length 
      : 0;

    // Analyze categories
    const categoryCount = new Map<string, number>();
    models.forEach(model => {
      const count = categoryCount.get(model.category) || 0;
      categoryCount.set(model.category, count + 1);
    });

    const topCategories = Array.from(categoryCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([category, count]) => ({
        id: `${platform}_${category}`,
        platform,
        category,
        modelCount: count,
        averagePrice: this.calculateCategoryAveragePrice(models, category),
        popularityScore: count / totalModels,
        competitionLevel: count > totalModels * 0.3 ? 'high' : count > totalModels * 0.1 ? 'medium' : 'low',
        growthRate: 0, // Would need historical data
        topTags: this.getTopTagsForCategory(models, category),
        topCreators: this.getTopCreatorsForCategory(models, category),
        lastAnalyzed: new Date().toISOString()
      }));

    // Analyze tags
    const tagCount = new Map<string, number>();
    models.forEach(model => {
      model.tags.forEach(tag => {
        const count = tagCount.get(tag) || 0;
        tagCount.set(tag, count + 1);
      });
    });

    const popularTags = Array.from(tagCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 20)
      .map(([tag, frequency]) => ({
        id: `${platform}_${tag}`,
        tag,
        platform,
        frequency,
        averagePrice: this.calculateTagAveragePrice(models, tag),
        averageDownloads: this.calculateTagAverageDownloads(models, tag),
        popularityScore: frequency / totalModels,
        trend: 'stable' as const,
        relatedTags: this.getRelatedTags(models, tag),
        lastUpdated: new Date().toISOString()
      }));

    return {
      platform,
      totalModels,
      scrapedModels: totalModels,
      successRate: 1.0, // Assuming all scraped models are successful
      averagePrice,
      topCategories,
      pricingTrends: [], // Would be populated with historical data
      popularTags,
      insights: [], // Would be generated by AI analysis
      lastUpdated: new Date().toISOString(),
      nextUpdate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now
    };
  }

  /**
   * Get platform metrics
   */
  getPlatformMetrics(): Record<string, PlatformMetrics> {
    const metrics: Record<string, PlatformMetrics> = {};
    
    this.scrapers.forEach((scraper, platform) => {
      metrics[platform] = scraper.getMetrics();
    });
    
    return metrics;
  }

  /**
   * Get active scraping jobs
   */
  getActiveJobs(): ScrapingJob[] {
    return Array.from(this.activeJobs.values());
  }

  /**
   * Get job by ID
   */
  getJob(jobId: string): ScrapingJob | undefined {
    return this.activeJobs.get(jobId);
  }

  /**
   * Cancel a scraping job
   */
  cancelJob(jobId: string): boolean {
    const job = this.activeJobs.get(jobId);
    if (job && job.status === 'running') {
      job.status = 'cancelled';
      job.completedAt = new Date().toISOString();
      return true;
    }
    return false;
  }

  // Helper methods
  private generateJobId(type: string): string {
    return `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private calculateCategoryAveragePrice(models: CompetitorModel[], category: string): number {
    const categoryModels = models.filter(m => m.category === category && !m.isFree);
    if (categoryModels.length === 0) return 0;
    
    const totalPrice = categoryModels.reduce((sum, model) => sum + model.price, 0);
    return totalPrice / categoryModels.length;
  }

  private calculateTagAveragePrice(models: CompetitorModel[], tag: string): number {
    const tagModels = models.filter(m => m.tags.includes(tag) && !m.isFree);
    if (tagModels.length === 0) return 0;
    
    const totalPrice = tagModels.reduce((sum, model) => sum + model.price, 0);
    return totalPrice / tagModels.length;
  }

  private calculateTagAverageDownloads(models: CompetitorModel[], tag: string): number {
    const tagModels = models.filter(m => m.tags.includes(tag) && m.downloadCount !== undefined);
    if (tagModels.length === 0) return 0;
    
    const totalDownloads = tagModels.reduce((sum, model) => sum + (model.downloadCount || 0), 0);
    return totalDownloads / tagModels.length;
  }

  private getTopTagsForCategory(models: CompetitorModel[], category: string): string[] {
    const categoryModels = models.filter(m => m.category === category);
    const tagCount = new Map<string, number>();
    
    categoryModels.forEach(model => {
      model.tags.forEach(tag => {
        const count = tagCount.get(tag) || 0;
        tagCount.set(tag, count + 1);
      });
    });
    
    return Array.from(tagCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([tag]) => tag);
  }

  private getTopCreatorsForCategory(models: CompetitorModel[], category: string): string[] {
    const categoryModels = models.filter(m => m.category === category);
    const creatorCount = new Map<string, number>();
    
    categoryModels.forEach(model => {
      const count = creatorCount.get(model.creatorName) || 0;
      creatorCount.set(model.creatorName, count + 1);
    });
    
    return Array.from(creatorCount.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([creator]) => creator);
  }

  private getRelatedTags(models: CompetitorModel[], targetTag: string): string[] {
    const relatedTags = new Map<string, number>();
    
    models.forEach(model => {
      if (model.tags.includes(targetTag)) {
        model.tags.forEach(tag => {
          if (tag !== targetTag) {
            const count = relatedTags.get(tag) || 0;
            relatedTags.set(tag, count + 1);
          }
        });
      }
    });
    
    return Array.from(relatedTags.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([tag]) => tag);
  }
}
