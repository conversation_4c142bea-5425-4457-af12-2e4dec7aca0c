/**
 * Sketchfab platform scraper for 3D models
 */

import { BaseCompetitorScraper } from './base-competitor-scraper';
import { CompetitorModel } from '@/types/competitor-models';
import * as cheerio from 'cheerio';

export class SketchfabScraper extends BaseCompetitorScraper {
  constructor() {
    super('sketchfab', {
      rateLimitDelay: 1500, // Sketchfab rate limiting
      maxModelsPerPlatform: 100
    });
  }

  buildTrendingUrl(): string {
    return 'https://sketchfab.com/3d-models/popular';
  }

  buildCategoryUrl(category: string): string {
    const categoryMap: Record<string, string> = {
      'characters': 'characters-creatures',
      'vehicles': 'cars-vehicles',
      'architecture': 'architecture',
      'nature': 'nature-plants',
      'animals': 'animals-pets',
      'furniture': 'furniture-home',
      'weapons': 'weapons-military',
      'food': 'food-drink',
      'electronics': 'electronics-gadgets',
      'art': 'art-abstract'
    };

    const sketchfabCategory = categoryMap[category.toLowerCase()] || category;
    return `https://sketchfab.com/3d-models/categories/${sketchfabCategory}`;
  }

  buildSearchUrl(query: string): string {
    const encodedQuery = encodeURIComponent(query);
    return `https://sketchfab.com/3d-models?q=${encodedQuery}&sort_by=-likeCount`;
  }

  getPlatformCategories(): string[] {
    return [
      'characters',
      'vehicles', 
      'architecture',
      'nature',
      'animals',
      'furniture',
      'weapons',
      'food',
      'electronics',
      'art',
      'science',
      'sports',
      'music',
      'travel'
    ];
  }

  extractModelUrls(html: string): string[] {
    const $ = cheerio.load(html);
    const urls: string[] = [];

    // Sketchfab model links
    $('a[href*="/3d-models/"]').each((_, element) => {
      const href = $(element).attr('href');
      if (href && href.includes('/3d-models/') && !href.includes('/edit')) {
        const fullUrl = href.startsWith('http') ? href : `https://sketchfab.com${href}`;
        if (!urls.includes(fullUrl)) {
          urls.push(fullUrl);
        }
      }
    });

    // Alternative selector for model cards
    $('.c-model-card a, .model-card a').each((_, element) => {
      const href = $(element).attr('href');
      if (href) {
        const fullUrl = href.startsWith('http') ? href : `https://sketchfab.com${href}`;
        if (!urls.includes(fullUrl)) {
          urls.push(fullUrl);
        }
      }
    });

    console.log(`🔗 Extracted ${urls.length} model URLs from Sketchfab`);
    return urls.slice(0, 50); // Limit to prevent overwhelming
  }

  parseModelData(html: string, url: string): CompetitorModel | null {
    try {
      const $ = cheerio.load(html);

      // Extract basic model information
      const title = this.extractTitle($);
      if (!title) {
        console.warn(`⚠️ No title found for ${url}`);
        return null;
      }

      const description = this.extractDescription($);
      const creatorName = this.extractCreatorName($);
      const category = this.extractCategory($);
      const tags = this.extractModelTags($);
      const stats = this.extractStats($);
      const pricing = this.extractPricing($);
      const technicalInfo = this.extractTechnicalInfo($);
      const thumbnailUrl = this.extractThumbnail($);

      const model: CompetitorModel = {
        id: this.generateModelId(url),
        platform: 'sketchfab',
        title,
        description,
        price: pricing.price,
        currency: pricing.currency,
        isFree: pricing.isFree,
        category: category || 'uncategorized',
        tags,
        formats: technicalInfo.formats,
        downloadCount: stats.downloads,
        viewCount: stats.views,
        likeCount: stats.likes,
        creatorName: creatorName || 'Unknown',
        thumbnailUrl,
        modelUrl: url,
        uploadDate: this.extractUploadDate($),
        fileSize: technicalInfo.fileSize,
        polygonCount: technicalInfo.polygonCount,
        textureResolution: technicalInfo.textureResolution,
        isAnimated: technicalInfo.isAnimated,
        isRigged: technicalInfo.isRigged,
        license: this.extractLicense($),
        scrapedAt: new Date().toISOString(),
        dataQuality: 'medium'
      };

      return model;

    } catch (error) {
      console.error(`❌ Error parsing Sketchfab model data for ${url}:`, error);
      return null;
    }
  }

  private extractTitle($: cheerio.CheerioAPI): string {
    return $('.c-model-title h1, .model-title h1, h1.model-name').first().text().trim() ||
           $('meta[property="og:title"]').attr('content') ||
           $('title').text().replace(' - Sketchfab', '').trim();
  }

  private extractDescription($: cheerio.CheerioAPI): string {
    return $('.c-model-description, .model-description, .description').first().text().trim() ||
           $('meta[property="og:description"]').attr('content') ||
           '';
  }

  private extractCreatorName($: cheerio.CheerioAPI): string {
    return $('.c-model-author a, .model-author a, .author-name').first().text().trim() ||
           $('.username, .user-name').first().text().trim() ||
           '';
  }

  private extractCategory($: cheerio.CheerioAPI): string {
    const categoryText = $('.c-model-category, .model-category, .category').first().text().trim();
    return categoryText || this.inferCategoryFromTags($);
  }

  private extractModelTags($: cheerio.CheerioAPI): string[] {
    const tags: string[] = [];

    // Extract from tag elements
    $('.c-model-tags a, .model-tags a, .tag').each((_, element) => {
      const tag = $(element).text().trim();
      if (tag && !tags.includes(tag)) {
        tags.push(tag);
      }
    });

    // Extract from keywords meta tag
    const keywords = $('meta[name="keywords"]').attr('content');
    if (keywords) {
      const keywordTags = keywords.split(',').map(k => k.trim());
      keywordTags.forEach(tag => {
        if (tag && !tags.includes(tag)) {
          tags.push(tag);
        }
      });
    }

    return tags.slice(0, 15); // Limit to 15 tags
  }

  private extractStats($: cheerio.CheerioAPI): { views?: number; likes?: number; downloads?: number } {
    const stats = { views: undefined, likes: undefined, downloads: undefined };

    // Extract view count
    const viewText = $('.c-model-stats .views, .model-stats .views, .view-count').text();
    const viewMatch = viewText.match(/(\d+(?:,\d+)*)/);
    if (viewMatch) {
      stats.views = parseInt(viewMatch[1].replace(/,/g, ''));
    }

    // Extract like count
    const likeText = $('.c-model-stats .likes, .model-stats .likes, .like-count').text();
    const likeMatch = likeText.match(/(\d+(?:,\d+)*)/);
    if (likeMatch) {
      stats.likes = parseInt(likeMatch[1].replace(/,/g, ''));
    }

    // Sketchfab doesn't typically show download counts publicly
    // We'll estimate based on views and likes
    if (stats.views && stats.likes) {
      stats.downloads = Math.floor((stats.views * 0.05) + (stats.likes * 0.3));
    }

    return stats;
  }

  private extractPricing($: cheerio.CheerioAPI): { price: number; currency: string; isFree: boolean } {
    // Most Sketchfab models are free to view, but may have download restrictions
    const priceText = $('.price, .cost, .download-price').text().toLowerCase();
    
    if (priceText.includes('free') || priceText.includes('download')) {
      return { price: 0, currency: 'USD', isFree: true };
    }

    // Check for premium indicators
    const isPremium = $('.premium, .pro-only, .subscription').length > 0;
    if (isPremium) {
      return { price: 9.99, currency: 'USD', isFree: false }; // Estimated premium price
    }

    return { price: 0, currency: 'USD', isFree: true };
  }

  private extractTechnicalInfo($: cheerio.CheerioAPI): {
    formats: string[];
    fileSize?: number;
    polygonCount?: number;
    textureResolution?: string;
    isAnimated?: boolean;
    isRigged?: boolean;
  } {
    const info = {
      formats: [] as string[],
      fileSize: undefined,
      polygonCount: undefined,
      textureResolution: undefined,
      isAnimated: undefined,
      isRigged: undefined
    };

    // Extract file formats
    $('.formats, .file-formats, .download-formats').find('span, .format').each((_, element) => {
      const format = $(element).text().trim().toUpperCase();
      if (format && !info.formats.includes(format)) {
        info.formats.push(format);
      }
    });

    // Default Sketchfab formats
    if (info.formats.length === 0) {
      info.formats = ['GLB', 'GLTF', 'OBJ'];
    }

    // Extract polygon count
    const polyText = $('.polygons, .poly-count, .triangle-count').text();
    const polyMatch = polyText.match(/(\d+(?:,\d+)*)/);
    if (polyMatch) {
      info.polygonCount = parseInt(polyMatch[1].replace(/,/g, ''));
    }

    // Check for animation
    info.isAnimated = $('.animated, .animation, .has-animation').length > 0;
    
    // Check for rigging
    info.isRigged = $('.rigged, .rig, .has-rig').length > 0;

    return info;
  }

  private extractThumbnail($: cheerio.CheerioAPI): string {
    return $('meta[property="og:image"]').attr('content') ||
           $('.model-thumbnail img, .preview-image img').first().attr('src') ||
           '';
  }

  private extractUploadDate($: cheerio.CheerioAPI): string {
    const dateText = $('.upload-date, .created-date, .published-date').text();
    const dateMatch = dateText.match(/(\d{4}-\d{2}-\d{2})/);
    return dateMatch ? dateMatch[1] : '';
  }

  private extractLicense($: cheerio.CheerioAPI): string {
    return $('.license, .rights, .usage-rights').text().trim() || 'Standard License';
  }

  private inferCategoryFromTags($: cheerio.CheerioAPI): string {
    const tags = this.extractModelTags($);
    const categoryKeywords = {
      'characters': ['character', 'person', 'human', 'avatar', 'figure'],
      'vehicles': ['car', 'vehicle', 'truck', 'bike', 'motorcycle', 'aircraft'],
      'architecture': ['building', 'house', 'architecture', 'structure'],
      'nature': ['tree', 'plant', 'nature', 'landscape', 'environment'],
      'animals': ['animal', 'creature', 'pet', 'wildlife'],
      'furniture': ['chair', 'table', 'furniture', 'sofa', 'bed'],
      'weapons': ['weapon', 'gun', 'sword', 'military'],
      'electronics': ['phone', 'computer', 'device', 'gadget']
    };

    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      if (tags.some(tag => keywords.some(keyword => tag.toLowerCase().includes(keyword)))) {
        return category;
      }
    }

    return 'miscellaneous';
  }

  private generateModelId(url: string): string {
    const match = url.match(/\/3d-models\/([^\/]+)/);
    return match ? `sketchfab_${match[1]}` : `sketchfab_${Date.now()}`;
  }
}
