/**
 * Enhanced Image Scraper - Покращений скрапер зображень для 3D моделей
 * Використовує Bright Data MCP для отримання високоякісних зображень
 */

export interface ScrapedImage {
  url: string;
  alt?: string;
  width?: number;
  height?: number;
  size?: number;
  format?: string;
  quality?: 'low' | 'medium' | 'high';
  isPrimary?: boolean;
}

export interface ModelImageData {
  primaryImage?: ScrapedImage;
  galleryImages: ScrapedImage[];
  thumbnails: ScrapedImage[];
  totalImages: number;
  scrapedAt: string;
  platform: string;
  sourceUrl: string;
}

export interface ImageScrapingConfig {
  minWidth: number;
  minHeight: number;
  maxImages: number;
  preferredFormats: string[];
  qualityThreshold: number;
}

export class EnhancedImageScraper {
  private config: ImageScrapingConfig;

  constructor(config?: Partial<ImageScrapingConfig>) {
    this.config = {
      minWidth: 800,
      minHeight: 600,
      maxImages: 10,
      preferredFormats: ['webp', 'jpg', 'jpeg', 'png'],
      qualityThreshold: 0.7,
      ...config
    };
  }

  /**
   * Скрапінг зображень моделі з URL
   */
  async scrapeModelImages(url: string): Promise<ModelImageData> {
    try {
      console.log(`🖼️ Скрапінг зображень з: ${url}`);
      
      const platform = this.detectPlatform(url);
      const htmlContent = await this.getPageHTML(url);
      
      if (!htmlContent) {
        throw new Error('Не вдалося отримати HTML контент');
      }

      const images = await this.extractImagesFromHTML(htmlContent, url, platform);
      const processedImages = await this.processAndFilterImages(images);
      
      return {
        primaryImage: this.selectPrimaryImage(processedImages),
        galleryImages: processedImages.filter(img => !img.isPrimary),
        thumbnails: this.generateThumbnails(processedImages),
        totalImages: processedImages.length,
        scrapedAt: new Date().toISOString(),
        platform,
        sourceUrl: url
      };

    } catch (error) {
      console.error(`❌ Помилка скрапінгу зображень з ${url}:`, error);
      return this.createEmptyResult(url);
    }
  }

  /**
   * Отримання HTML контенту сторінки через Bright Data MCP
   */
  private async getPageHTML(url: string): Promise<string | null> {
    try {
      // Використовуємо динамічний імпорт для MCP клієнта
      const { scrape_as_html_Bright_Data } = await import('./mcp-client');
      const result = await scrape_as_html_Bright_Data({ url });
      return result || null;
    } catch (error) {
      console.error('Помилка отримання HTML:', error);
      return null;
    }
  }

  /**
   * Витягування зображень з HTML контенту
   */
  private async extractImagesFromHTML(
    html: string, 
    baseUrl: string, 
    platform: string
  ): Promise<ScrapedImage[]> {
    const images: ScrapedImage[] = [];
    
    // Використовуємо регулярні вирази для пошуку зображень
    const imgRegex = /<img[^>]+src=["']([^"']+)["'][^>]*>/gi;
    const altRegex = /alt=["']([^"']+)["']/i;
    const widthRegex = /width=["']?(\d+)["']?/i;
    const heightRegex = /height=["']?(\d+)["']?/i;

    let match;
    while ((match = imgRegex.exec(html)) !== null) {
      const imgTag = match[0];
      const src = match[1];
      
      if (!this.isValidImageUrl(src, platform)) {
        continue;
      }

      const fullUrl = this.resolveImageUrl(src, baseUrl);
      const altMatch = altRegex.exec(imgTag);
      const widthMatch = widthRegex.exec(imgTag);
      const heightMatch = heightRegex.exec(imgTag);
      
      images.push({
        url: fullUrl,
        alt: altMatch ? altMatch[1] : undefined,
        width: widthMatch ? parseInt(widthMatch[1]) : undefined,
        height: heightMatch ? parseInt(heightMatch[1]) : undefined,
        format: this.getImageFormat(fullUrl),
        quality: this.estimateImageQuality(fullUrl, imgTag)
      });
    }

    return images;
  }

  /**
   * Обробка та фільтрація зображень
   */
  private async processAndFilterImages(images: ScrapedImage[]): Promise<ScrapedImage[]> {
    const filtered = images.filter(img => {
      // Фільтруємо за розміром
      if (img.width && img.width < this.config.minWidth) return false;
      if (img.height && img.height < this.config.minHeight) return false;
      
      // Фільтруємо за форматом
      if (img.format && !this.config.preferredFormats.includes(img.format)) return false;
      
      // Фільтруємо за якістю
      if (img.quality === 'low') return false;
      
      return true;
    });

    // Сортуємо за якістю та розміром
    filtered.sort((a, b) => {
      const qualityScore = (img: ScrapedImage) => {
        let score = 0;
        if (img.quality === 'high') score += 3;
        else if (img.quality === 'medium') score += 2;
        else score += 1;
        
        if (img.width && img.height) {
          score += (img.width * img.height) / 1000000; // Додаємо бали за розмір
        }
        
        return score;
      };
      
      return qualityScore(b) - qualityScore(a);
    });

    return filtered.slice(0, this.config.maxImages);
  }

  /**
   * Вибір основного зображення
   */
  private selectPrimaryImage(images: ScrapedImage[]): ScrapedImage | undefined {
    if (images.length === 0) return undefined;
    
    // Перше зображення після сортування є найкращим
    const primary = { ...images[0], isPrimary: true };
    return primary;
  }

  /**
   * Генерація мініатюр
   */
  private generateThumbnails(images: ScrapedImage[]): ScrapedImage[] {
    return images.slice(0, 5).map(img => ({
      ...img,
      width: Math.min(img.width || 300, 300),
      height: Math.min(img.height || 300, 300)
    }));
  }

  /**
   * Визначення платформи за URL
   */
  private detectPlatform(url: string): string {
    if (url.includes('printables.com')) return 'printables';
    if (url.includes('makerworld.com')) return 'makerworld';
    if (url.includes('thangs.com')) return 'thangs';
    if (url.includes('thingiverse.com')) return 'thingiverse';
    if (url.includes('myminifactory.com')) return 'myminifactory';
    return 'unknown';
  }

  /**
   * Перевірка валідності URL зображення
   */
  private isValidImageUrl(src: string, platform: string): boolean {
    if (!src || src.length < 10) return false;
    if (src.startsWith('data:')) return false;
    if (src.includes('icon') || src.includes('logo')) return false;
    if (src.includes('avatar') || src.includes('profile')) return false;
    
    // Платформо-специфічні фільтри
    switch (platform) {
      case 'printables':
        return src.includes('media.printables.com') || src.includes('cdn.printables.com');
      case 'makerworld':
        return src.includes('makerworld.com') && (src.includes('image') || src.includes('photo'));
      case 'thangs':
        return src.includes('thangs.com') && src.includes('image');
      default:
        return true;
    }
  }

  /**
   * Розв'язання відносних URL
   */
  private resolveImageUrl(src: string, baseUrl: string): string {
    if (src.startsWith('http')) return src;
    if (src.startsWith('//')) return 'https:' + src;
    if (src.startsWith('/')) {
      const base = new URL(baseUrl);
      return `${base.protocol}//${base.host}${src}`;
    }
    return new URL(src, baseUrl).href;
  }

  /**
   * Визначення формату зображення
   */
  private getImageFormat(url: string): string {
    const extension = url.split('.').pop()?.toLowerCase().split('?')[0];
    return extension || 'unknown';
  }

  /**
   * Оцінка якості зображення
   */
  private estimateImageQuality(url: string, imgTag: string): 'low' | 'medium' | 'high' {
    // Перевіряємо розмір за URL
    if (url.includes('thumb') || url.includes('small') || url.includes('150x')) return 'low';
    if (url.includes('medium') || url.includes('300x')) return 'medium';
    if (url.includes('large') || url.includes('original') || url.includes('1200x')) return 'high';
    
    // Перевіряємо атрибути
    if (imgTag.includes('loading="lazy"') || imgTag.includes('thumbnail')) return 'medium';
    
    return 'medium';
  }

  /**
   * Створення порожнього результату
   */
  private createEmptyResult(url: string): ModelImageData {
    return {
      galleryImages: [],
      thumbnails: [],
      totalImages: 0,
      scrapedAt: new Date().toISOString(),
      platform: this.detectPlatform(url),
      sourceUrl: url
    };
  }
}

// Експорт екземпляра для використання
export const enhancedImageScraper = new EnhancedImageScraper();
