/**
 * Mock Data Generator for Testing Real Marketplace
 * Generates realistic sample 3D models for development and testing
 */

import { ScrapedModel, ModelSource } from '@/types/models';

export class MockDataGenerator {
  private static readonly SAMPLE_MODELS = {
    printables: [
      {
        id: 'printables_1051413',
        title: 'Articulated Dragon',
        description: 'A fully articulated dragon that prints in place without supports. Perfect for display or as a fidget toy.',
        thumbnail: 'https://cdn.printables.com/uploads/models/1051413/images/8234567/dragon_preview.jpg',
        category: 'Toys & Games',
        tags: ['dragon', 'articulated', 'no-supports', 'fidget'],
        designer: 'DragonMaker3D',
        downloads: 15420,
        likes: 2340,
        views: 45600
      },
      {
        id: 'printables_1050987',
        title: 'Modular Desk Organizer',
        description: 'Customizable desk organizer with multiple compartments. Mix and match different modules.',
        thumbnail: 'https://cdn.printables.com/uploads/models/1050987/images/8234568/organizer_preview.jpg',
        category: 'Household & Garden',
        tags: ['organizer', 'modular', 'desk', 'office'],
        designer: '<PERSON><PERSON>el<PERSON>',
        downloads: 8930,
        likes: 1560,
        views: 23400
      },
      {
        id: 'printables_1050765',
        title: 'Miniature Castle Set',
        description: 'Complete medieval castle set for tabletop gaming. Includes walls, towers, and accessories.',
        thumbnail: 'https://cdn.printables.com/uploads/models/1050765/images/8234569/castle_preview.jpg',
        category: 'Miniatures & Gaming',
        tags: ['castle', 'medieval', 'tabletop', 'gaming', 'dnd'],
        designer: 'CastleCrafter',
        downloads: 12100,
        likes: 1890,
        views: 34500
      },
      {
        id: 'printables_1050543',
        title: 'Plant Pot with Drainage',
        description: 'Self-watering plant pot with built-in drainage system. Perfect for herbs and small plants.',
        thumbnail: 'https://cdn.printables.com/uploads/models/1050543/images/8234570/pot_preview.jpg',
        category: 'Household & Garden',
        tags: ['plant-pot', 'gardening', 'drainage', 'herbs'],
        designer: 'GreenThumb3D',
        downloads: 6780,
        likes: 980,
        views: 18900
      },
      {
        id: 'printables_1050321',
        title: 'Mechanical Keyboard Keycaps',
        description: 'Custom keycaps for mechanical keyboards. Cherry MX compatible with unique designs.',
        thumbnail: 'https://cdn.printables.com/uploads/models/1050321/images/8234571/keycaps_preview.jpg',
        category: 'Tools & Gadgets',
        tags: ['keyboard', 'keycaps', 'mechanical', 'cherry-mx'],
        designer: 'KeyboardArtist',
        downloads: 4560,
        likes: 720,
        views: 12300
      }
    ],
    makerworld: [
      {
        id: 'makerworld_MW001234',
        title: 'Bambu Lab Cable Chain',
        description: 'Replacement cable chain for Bambu Lab printers. Improved design with better flexibility.',
        thumbnail: 'https://cdn.makerworld.com/model/MW001234/preview.jpg',
        category: 'Tools & Gadgets',
        tags: ['bambu-lab', 'cable-chain', 'printer-parts', 'replacement'],
        designer: 'BambuMods',
        downloads: 23400,
        likes: 3450,
        views: 67800
      },
      {
        id: 'makerworld_MW001235',
        title: 'Spiral Vase Collection',
        description: 'Beautiful collection of spiral vases optimized for vase mode printing.',
        thumbnail: 'https://cdn.makerworld.com/model/MW001235/preview.jpg',
        category: 'Art & Decoration',
        tags: ['vase', 'spiral', 'vase-mode', 'decoration'],
        designer: 'VaseDesigner',
        downloads: 18900,
        likes: 2780,
        views: 45600
      },
      {
        id: 'makerworld_MW001236',
        title: 'Functional Phone Stand',
        description: 'Adjustable phone stand with cable management. Works with all phone sizes.',
        thumbnail: 'https://cdn.makerworld.com/model/MW001236/preview.jpg',
        category: 'Tools & Gadgets',
        tags: ['phone-stand', 'adjustable', 'cable-management', 'functional'],
        designer: 'TechStands',
        downloads: 14500,
        likes: 2100,
        views: 38900
      },
      {
        id: 'makerworld_MW001237',
        title: 'Miniature Furniture Set',
        description: 'Detailed miniature furniture for dollhouses or dioramas. Highly detailed and realistic.',
        thumbnail: 'https://cdn.makerworld.com/model/MW001237/preview.jpg',
        category: 'Miniatures & Gaming',
        tags: ['miniature', 'furniture', 'dollhouse', 'detailed'],
        designer: 'MiniatureMaster',
        downloads: 9870,
        likes: 1450,
        views: 28700
      },
      {
        id: 'makerworld_MW001238',
        title: 'Geometric Wall Art',
        description: 'Modern geometric wall art pieces. Can be combined to create larger installations.',
        thumbnail: 'https://cdn.makerworld.com/model/MW001238/preview.jpg',
        category: 'Art & Decoration',
        tags: ['wall-art', 'geometric', 'modern', 'modular'],
        designer: 'GeometricArt',
        downloads: 7650,
        likes: 1120,
        views: 21400
      }
    ],
    thangs: [
      {
        id: 'thangs_TH789012',
        title: 'Professional Tool Holder',
        description: 'Heavy-duty tool holder for workshop organization. Modular design for different tools.',
        thumbnail: 'https://cdn.thangs.com/model/TH789012/preview.jpg',
        category: 'Tools & Gadgets',
        tags: ['tool-holder', 'workshop', 'organization', 'heavy-duty'],
        designer: 'WorkshopPro',
        downloads: 11200,
        likes: 1680,
        views: 32400
      },
      {
        id: 'thangs_TH789013',
        title: 'Architectural Model Kit',
        description: 'Detailed architectural model components for creating building models and prototypes.',
        thumbnail: 'https://cdn.thangs.com/model/TH789013/preview.jpg',
        category: 'Educational',
        tags: ['architecture', 'model', 'building', 'educational'],
        designer: 'ArchModels',
        downloads: 8940,
        likes: 1340,
        views: 26800
      },
      {
        id: 'thangs_TH789014',
        title: 'Jewelry Display Stand',
        description: 'Elegant jewelry display stand for showcasing rings, necklaces, and bracelets.',
        thumbnail: 'https://cdn.thangs.com/model/TH789014/preview.jpg',
        category: 'Fashion & Accessories',
        tags: ['jewelry', 'display', 'stand', 'elegant'],
        designer: 'JewelryDisplay',
        downloads: 5670,
        likes: 890,
        views: 16700
      },
      {
        id: 'thangs_TH789015',
        title: 'Educational Gear Set',
        description: 'Working gear mechanism for teaching mechanical principles. Great for STEM education.',
        thumbnail: 'https://cdn.thangs.com/model/TH789015/preview.jpg',
        category: 'Educational',
        tags: ['gears', 'mechanical', 'educational', 'stem'],
        designer: 'STEMEducator',
        downloads: 13400,
        likes: 2010,
        views: 39800
      },
      {
        id: 'thangs_TH789016',
        title: 'Custom Cookie Cutters',
        description: 'Set of custom cookie cutters with unique shapes. Food-safe design.',
        thumbnail: 'https://cdn.thangs.com/model/TH789016/preview.jpg',
        category: 'Household & Garden',
        tags: ['cookie-cutters', 'food-safe', 'custom', 'baking'],
        designer: 'BakingTools',
        downloads: 7890,
        likes: 1230,
        views: 22100
      }
    ]
  };

  /**
   * Generate mock models for a specific platform
   */
  static generateMockModels(platform: ModelSource, count: number = 5): ScrapedModel[] {
    const platformModels = this.SAMPLE_MODELS[platform] || [];
    const selectedModels = platformModels.slice(0, count);
    
    return selectedModels.map(model => this.convertToScrapedModel(model, platform));
  }

  /**
   * Generate mock models for all platforms
   */
  static generateAllPlatformModels(modelsPerPlatform: number = 5): ScrapedModel[] {
    const allModels: ScrapedModel[] = [];
    
    const platforms: ModelSource[] = ['printables', 'makerworld', 'thangs'];
    
    for (const platform of platforms) {
      const models = this.generateMockModels(platform, modelsPerPlatform);
      allModels.push(...models);
    }
    
    return allModels;
  }

  /**
   * Convert sample model data to ScrapedModel format
   */
  private static convertToScrapedModel(model: any, platform: ModelSource): ScrapedModel {
    return {
      title: model.title,
      description: model.description,
      thumbnail: model.thumbnail,
      images: [
        { url: model.thumbnail, alt: model.title, isPrimary: true },
        { url: model.thumbnail.replace('preview', 'detail1'), alt: `${model.title} detail 1`, isPrimary: false },
        { url: model.thumbnail.replace('preview', 'detail2'), alt: `${model.title} detail 2`, isPrimary: false }
      ],
      category: model.category,
      tags: model.tags,
      fileFormats: ['STL', '3MF'],
      totalSize: Math.floor(Math.random() * 50 + 5) * 1024 * 1024, // 5-55 MB
      designer: {
        id: model.designer.toLowerCase().replace(/[^a-z0-9]/g, ''),
        name: model.designer,
        avatar: `https://avatar.example.com/${model.designer.toLowerCase()}.jpg`,
        profileUrl: `https://${platform}.com/user/${model.designer.toLowerCase()}`,
      },
      stats: {
        downloads: model.downloads,
        likes: model.likes,
        views: model.views,
        comments: Math.floor(model.likes * 0.1),
      },
      files: [
        {
          name: `${model.title.replace(/[^a-zA-Z0-9]/g, '_')}.stl`,
          url: `https://${platform}.com/download/${model.id}/model.stl`,
          format: 'STL',
          size: Math.floor(Math.random() * 30 + 5) * 1024 * 1024,
        },
        {
          name: `${model.title.replace(/[^a-zA-Z0-9]/g, '_')}.3mf`,
          url: `https://${platform}.com/download/${model.id}/model.3mf`,
          format: '3MF',
          size: Math.floor(Math.random() * 20 + 3) * 1024 * 1024,
        }
      ],
      isFree: platform === 'printables', // Printables is free, others might have paid models
      price: platform === 'printables' ? 0 : Math.floor(Math.random() * 20 + 5),
      currency: 'USD',
      platform,
      originalId: model.id,
      originalUrl: `https://${platform}.com/model/${model.id}`,
      scrapedAt: new Date().toISOString(),
      license: {
        type: 'Creative Commons - Attribution',
        name: 'CC BY 4.0',
        allowCommercialUse: true,
        requireAttribution: true,
        allowDerivatives: true,
        detected: true,
        confidence: 0.9,
      },
    };
  }
}
