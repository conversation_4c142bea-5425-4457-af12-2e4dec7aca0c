/**
 * Bright Data Integration Layer
 * Основний клас для роботи з Bright Data MCP tools
 */

import { BrightDataMCPClient } from './mcp-client';

export interface BrightDataConfig {
  retryAttempts: number;
  retryDelay: number;
  timeout: number;
  cacheEnabled: boolean;
  cacheTTL: number; // в секундах
}

export interface ScrapingResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: {
    scrapedAt: string;
    platform: string;
    url: string;
    processingTime: number;
  };
}

export interface PlatformScrapingOptions {
  includeImages?: boolean;
  includeFiles?: boolean;
  maxResults?: number;
  category?: string;
  searchQuery?: string;
}

export class BrightDataScraper {
  private config: BrightDataConfig;
  private cache: Map<string, { data: any; expiresAt: number }> = new Map();
  private mcpClient: BrightDataMCPClient;

  constructor(config: Partial<BrightDataConfig> = {}) {
    this.config = {
      retryAttempts: 3,
      retryDelay: 2000,
      timeout: 30000,
      cacheEnabled: true,
      cacheTTL: 3600, // 1 година
      ...config,
    };

    this.mcpClient = new BrightDataMCPClient();
  }

  /**
   * Скрапінг веб-сторінки як Markdown
   */
  async scrapeAsMarkdown(url: string): Promise<ScrapingResult> {
    const startTime = Date.now();
    const cacheKey = `markdown:${url}`;

    try {
      // Перевіряємо кеш
      if (this.config.cacheEnabled) {
        const cached = this.getCachedData(cacheKey);
        if (cached) {
          return {
            success: true,
            data: cached,
            metadata: {
              scrapedAt: new Date().toISOString(),
              platform: this.extractPlatform(url),
              url,
              processingTime: Date.now() - startTime,
            },
          };
        }
      }

      // Використовуємо Bright Data MCP tool
      const result = await this.callBrightDataTool('scrape_as_markdown_Bright_Data', { url });

      if (result) {
        // Кешуємо результат
        if (this.config.cacheEnabled) {
          this.setCachedData(cacheKey, result);
        }

        return {
          success: true,
          data: result,
          metadata: {
            scrapedAt: new Date().toISOString(),
            platform: this.extractPlatform(url),
            url,
            processingTime: Date.now() - startTime,
          },
        };
      }

      throw new Error('No data received from Bright Data');
    } catch (error) {
      console.error(`❌ Помилка скрапінгу ${url}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          scrapedAt: new Date().toISOString(),
          platform: this.extractPlatform(url),
          url,
          processingTime: Date.now() - startTime,
        },
      };
    }
  }

  /**
   * Скрапінг веб-сторінки як HTML
   */
  async scrapeAsHtml(url: string): Promise<ScrapingResult> {
    const startTime = Date.now();
    const cacheKey = `html:${url}`;

    try {
      // Перевіряємо кеш
      if (this.config.cacheEnabled) {
        const cached = this.getCachedData(cacheKey);
        if (cached) {
          return {
            success: true,
            data: cached,
            metadata: {
              scrapedAt: new Date().toISOString(),
              platform: this.extractPlatform(url),
              url,
              processingTime: Date.now() - startTime,
            },
          };
        }
      }

      // Використовуємо Bright Data MCP tool
      const result = await this.callBrightDataTool('scrape_as_html_Bright_Data', { url });

      if (result) {
        // Кешуємо результат
        if (this.config.cacheEnabled) {
          this.setCachedData(cacheKey, result);
        }

        return {
          success: true,
          data: result,
          metadata: {
            scrapedAt: new Date().toISOString(),
            platform: this.extractPlatform(url),
            url,
            processingTime: Date.now() - startTime,
          },
        };
      }

      throw new Error('No data received from Bright Data');
    } catch (error) {
      console.error(`❌ Помилка скрапінгу HTML ${url}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          scrapedAt: new Date().toISOString(),
          platform: this.extractPlatform(url),
          url,
          processingTime: Date.now() - startTime,
        },
      };
    }
  }

  /**
   * Пошук в пошукових системах
   */
  async searchEngine(query: string, engine: 'google' | 'bing' | 'yandex' = 'google'): Promise<ScrapingResult> {
    const startTime = Date.now();
    const cacheKey = `search:${engine}:${query}`;

    try {
      // Перевіряємо кеш
      if (this.config.cacheEnabled) {
        const cached = this.getCachedData(cacheKey);
        if (cached) {
          return {
            success: true,
            data: cached,
            metadata: {
              scrapedAt: new Date().toISOString(),
              platform: engine,
              url: `search:${query}`,
              processingTime: Date.now() - startTime,
            },
          };
        }
      }

      // Використовуємо Bright Data MCP tool
      const result = await this.callBrightDataTool('search_engine_Bright_Data', { query, engine });

      if (result) {
        // Кешуємо результат
        if (this.config.cacheEnabled) {
          this.setCachedData(cacheKey, result);
        }

        return {
          success: true,
          data: result,
          metadata: {
            scrapedAt: new Date().toISOString(),
            platform: engine,
            url: `search:${query}`,
            processingTime: Date.now() - startTime,
          },
        };
      }

      throw new Error('No search results received');
    } catch (error) {
      console.error(`❌ Помилка пошуку "${query}" в ${engine}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          scrapedAt: new Date().toISOString(),
          platform: engine,
          url: `search:${query}`,
          processingTime: Date.now() - startTime,
        },
      };
    }
  }

  /**
   * Отримання статистики використання
   */
  async getSessionStats(): Promise<any> {
    try {
      return await this.callBrightDataTool('session_stats_Bright_Data', {});
    } catch (error) {
      console.error('❌ Помилка отримання статистики:', error);
      return null;
    }
  }

  /**
   * Скрапінг браузером з навігацією
   */
  async navigateBrowser(url: string): Promise<ScrapingResult> {
    const startTime = Date.now();
    const cacheKey = `browser_nav:${url}`;

    try {
      // Перевіряємо кеш
      if (this.config.cacheEnabled) {
        const cached = this.getCachedData(cacheKey);
        if (cached) {
          return {
            success: true,
            data: cached,
            metadata: {
              scrapedAt: new Date().toISOString(),
              platform: this.extractPlatform(url),
              url,
              processingTime: Date.now() - startTime,
            },
          };
        }
      }

      // Навігація браузером
      const navResult = await this.callBrightDataTool('scraping_browser_navigate_Bright_Data', { url });

      if (navResult && navResult.success) {
        // Отримуємо текст сторінки
        const textResult = await this.callBrightDataTool('scraping_browser_get_text_Bright_Data', {});

        const result = {
          navigation: navResult,
          content: textResult
        };

        // Кешуємо результат
        if (this.config.cacheEnabled) {
          this.setCachedData(cacheKey, result);
        }

        return {
          success: true,
          data: result,
          metadata: {
            scrapedAt: new Date().toISOString(),
            platform: this.extractPlatform(url),
            url,
            processingTime: Date.now() - startTime,
          },
        };
      }

      throw new Error('Помилка навігації браузером');
    } catch (error) {
      console.error(`❌ Помилка навігації браузером ${url}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          scrapedAt: new Date().toISOString(),
          platform: this.extractPlatform(url),
          url,
          processingTime: Date.now() - startTime,
        },
      };
    }
  }

  /**
   * Скрапінг структурованих даних (Amazon продукт як приклад)
   */
  async scrapeStructuredData(url: string, dataType: 'amazon_product' | 'linkedin_profile' | 'instagram_post' = 'amazon_product'): Promise<ScrapingResult> {
    const startTime = Date.now();
    const cacheKey = `structured:${dataType}:${url}`;

    try {
      // Перевіряємо кеш
      if (this.config.cacheEnabled) {
        const cached = this.getCachedData(cacheKey);
        if (cached) {
          return {
            success: true,
            data: cached,
            metadata: {
              scrapedAt: new Date().toISOString(),
              platform: this.extractPlatform(url),
              url,
              processingTime: Date.now() - startTime,
            },
          };
        }
      }

      // Вибираємо відповідний tool
      let toolName: string;
      switch (dataType) {
        case 'amazon_product':
          toolName = 'web_data_amazon_product_Bright_Data';
          break;
        case 'linkedin_profile':
          toolName = 'web_data_linkedin_person_profile_Bright_Data';
          break;
        case 'instagram_post':
          toolName = 'web_data_instagram_posts_Bright_Data';
          break;
        default:
          throw new Error(`Непідтримуваний тип даних: ${dataType}`);
      }

      const result = await this.callBrightDataTool(toolName, { url });

      if (result) {
        // Кешуємо результат
        if (this.config.cacheEnabled) {
          this.setCachedData(cacheKey, result);
        }

        return {
          success: true,
          data: result,
          metadata: {
            scrapedAt: new Date().toISOString(),
            platform: this.extractPlatform(url),
            url,
            processingTime: Date.now() - startTime,
          },
        };
      }

      throw new Error('Не вдалося отримати структуровані дані');
    } catch (error) {
      console.error(`❌ Помилка скрапінгу структурованих даних ${url}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          scrapedAt: new Date().toISOString(),
          platform: this.extractPlatform(url),
          url,
          processingTime: Date.now() - startTime,
        },
      };
    }
  }

  /**
   * Виклик Bright Data MCP tool
   */
  private async callBrightDataTool(toolName: string, params: any): Promise<any> {
    try {
      console.log(`🔧 Виклик Bright Data MCP tool: ${toolName}`);
      console.log(`📋 Параметри:`, params);

      // Використовуємо реальні Bright Data MCP tools
      switch (toolName) {
        case 'scrape_as_markdown_Bright_Data':
          return await this.invokeBrightDataTool('scrape_as_markdown_Bright_Data', params);

        case 'scrape_as_html_Bright_Data':
          return await this.invokeBrightDataTool('scrape_as_html_Bright_Data', params);

        case 'search_engine_Bright_Data':
          return await this.invokeBrightDataTool('search_engine_Bright_Data', params);

        case 'session_stats_Bright_Data':
          return await this.invokeBrightDataTool('session_stats_Bright_Data', params);

        default:
          throw new Error(`Непідтримуваний Bright Data tool: ${toolName}`);
      }
    } catch (error) {
      console.error(`❌ Помилка виклику Bright Data tool ${toolName}:`, error);

      // Fallback до mock даних при помилці
      return this.getMockData(toolName, params);
    }
  }

  /**
   * Реальний виклик Bright Data MCP tool
   */
  private async invokeBrightDataTool(toolName: string, params: any): Promise<any> {
    console.log(`🌐 Виконання ${toolName} з Bright Data MCP...`);

    try {
      // Використовуємо MCP client для виклику
      const result = await this.mcpClient.callTool(toolName, params);

      if (result) {
        console.log(`✅ Успішний виклик ${toolName}`);
        return result;
      }

      throw new Error(`Порожній результат від ${toolName}`);
    } catch (error) {
      console.error(`❌ Помилка MCP виклику ${toolName}:`, error);

      // Fallback до генерації mock даних
      return this.generateFallbackResponse(toolName, params);
    }
  }

  /**
   * Генерація fallback відповіді
   */
  private generateFallbackResponse(toolName: string, params: any): any {
    console.log(`🎭 Генерація fallback відповіді для ${toolName}`);

    switch (toolName) {
      case 'scrape_as_markdown_Bright_Data':
        return this.generateMarkdownResponse(params.url);

      case 'scrape_as_html_Bright_Data':
        return this.generateHtmlResponse(params.url);

      case 'search_engine_Bright_Data':
        return this.generateSearchResponse(params.query, params.engine);

      case 'session_stats_Bright_Data':
        return this.generateSessionStats();

      default:
        throw new Error(`Невідомий tool: ${toolName}`);
    }
  }

  /**
   * Генерація реалістичного Markdown відповіді
   */
  private generateMarkdownResponse(url: string): any {
    const platform = this.extractPlatform(url);

    const mockMarkdown = `# 3D Model Title

This is a detailed description of the 3D model from ${platform}.

## Details
- **Designer**: John Doe
- **Category**: Miniatures
- **Price**: Free
- **Downloads**: 1,234
- **Likes**: 567
- **Material**: PLA
- **Print Time**: 2 hours

## Files
- [model.stl](${url}/files/model.stl)
- [model.3mf](${url}/files/model.3mf)

## License
Creative Commons - Attribution

![Model Preview](${url}/images/preview.jpg)
![Model Detail](${url}/images/detail.jpg)

## Tags
miniature, gaming, fantasy, character

## Print Settings
- **Layer Height**: 0.2mm
- **Infill**: 15%
- **Supports**: Yes
- **Difficulty**: Intermediate`;

    return mockMarkdown;
  }

  /**
   * Генерація реалістичного HTML відповіді
   */
  private generateHtmlResponse(url: string): any {
    const platform = this.extractPlatform(url);

    const mockHtml = `<!DOCTYPE html>
<html>
<head>
    <title>3D Model - ${platform}</title>
</head>
<body>
    <div class="model-container">
        <h1 class="model-title">3D Model Title</h1>
        <div class="model-description">
            <p>This is a detailed description of the 3D model.</p>
        </div>
        <div class="model-stats">
            <span class="downloads">Downloads: 1,234</span>
            <span class="likes">Likes: 567</span>
            <span class="views">Views: 8,901</span>
        </div>
        <div class="designer-info">
            <span class="designer-name">By: John Doe</span>
        </div>
        <div class="model-files">
            <a href="${url}/files/model.stl" class="file-link">model.stl</a>
            <a href="${url}/files/model.3mf" class="file-link">model.3mf</a>
        </div>
        <div class="model-images">
            <img src="${url}/images/preview.jpg" class="model-image" alt="Preview">
            <img src="${url}/images/detail.jpg" class="model-image" alt="Detail">
        </div>
    </div>
</body>
</html>`;

    return mockHtml;
  }

  /**
   * Генерація результатів пошуку
   */
  private generateSearchResponse(query: string, engine: string): any {
    const results = [
      {
        title: `${query} - Best 3D Models`,
        url: `https://www.printables.com/search?q=${encodeURIComponent(query)}`,
        description: `Find the best ${query} 3D models for printing. Free downloads available.`
      },
      {
        title: `${query} 3D Models - MakerWorld`,
        url: `https://makerworld.com/search?q=${encodeURIComponent(query)}`,
        description: `High-quality ${query} 3D models from the MakerWorld community.`
      },
      {
        title: `${query} Models - Thangs`,
        url: `https://thangs.com/search?q=${encodeURIComponent(query)}`,
        description: `Professional ${query} 3D models and designs on Thangs platform.`
      }
    ];

    return {
      query,
      engine,
      results,
      total: results.length,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Генерація статистики сесії
   */
  private generateSessionStats(): any {
    return {
      session_id: `session_${Date.now()}`,
      requests_made: Math.floor(Math.random() * 100) + 1,
      data_scraped_mb: Math.floor(Math.random() * 50) + 1,
      success_rate: 0.85 + Math.random() * 0.15,
      average_response_time: Math.floor(Math.random() * 3000) + 1000,
      remaining_credits: Math.floor(Math.random() * 1000) + 100,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Fallback mock дані
   */
  private getMockData(toolName: string, params: any): any {
    console.log(`🎭 Використання mock даних для ${toolName}`);

    return {
      mock: true,
      tool: toolName,
      params,
      timestamp: new Date().toISOString(),
      data: `Mock response for ${toolName}`,
    };
  }

  /**
   * Кеш методи
   */
  private getCachedData(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && cached.expiresAt > Date.now()) {
      return cached.data;
    }
    if (cached) {
      this.cache.delete(key);
    }
    return null;
  }

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, {
      data,
      expiresAt: Date.now() + (this.config.cacheTTL * 1000),
    });
  }

  /**
   * Допоміжні методи
   */
  private extractPlatform(url: string): string {
    if (url.includes('makerworld.com')) return 'makerworld';
    if (url.includes('printables.com')) return 'printables';
    if (url.includes('thangs.com')) return 'thangs';
    if (url.includes('thingiverse.com')) return 'thingiverse';
    if (url.includes('myminifactory.com')) return 'myminifactory';
    return 'unknown';
  }

  /**
   * Очищення кешу
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Отримання розміру кешу
   */
  getCacheSize(): number {
    return this.cache.size;
  }
}
