/**
 * Enhanced Scraping Scheduler with Print Analysis Integration
 * Combines automated scraping with advanced print slicing analysis
 */

import * as cron from 'node-cron';
import { ScrapedModelsRepository } from '../database/scraped-models-repository';
import { FormatConverter } from '../processing/format-converter';
import { ModelAnalyzer } from '../processing/model-analyzer';
import { PrintSettings, PrintSlicer, SlicingResult } from '../processing/print-slicer';
import { ThumbnailGenerator } from '../processing/thumbnail-generator';
import { R2ModelStorage } from '../storage/r2-model-storage';
import { BrightDataScraperManager } from './scraper-manager';

export interface EnhancedScrapingTask {
  id: string;
  name: string;
  description: string;
  cronExpression: string;
  platforms: string[];
  enabled: boolean;
  config: EnhancedScrapingConfig;
  lastRun?: string;
  nextRun?: string;
  runCount: number;
  successCount: number;
  failureCount: number;
  createdAt: string;
  updatedAt: string;
  
  // Enhanced features
  printAnalysisEnabled: boolean;
  qualityFiltering: boolean;
  autoProcessing: boolean;
  slicingProfiles: string[];
}

export interface EnhancedScrapingConfig {
  maxModelsPerPlatform: number;
  qualityThreshold: number;
  enableAIAnalysis: boolean;
  notifyOnCompletion: boolean;
  retryFailedJobs: boolean;
  cleanupOldData: boolean;
  dataRetentionDays: number;
  
  // Print analysis config
  enablePrintAnalysis: boolean;
  slicingEngine: 'prusaslicer' | 'cura' | 'superslicer';
  defaultFilamentType: 'PLA' | 'ABS' | 'PETG' | 'TPU';
  generateSlicingPreviews: boolean;
  estimatePrintCosts: boolean;
  
  // Processing config
  convertToGLB: boolean;
  generateThumbnails: boolean;
  optimizeModels: boolean;
  extractMetadata: boolean;
  
  // Quality filters
  minDownloads: number;
  minRating: number;
  maxFileSize: number; // in MB
  requiredFormats: string[];
  excludeCommercial: boolean;
}

export interface ScrapingJobResult {
  jobId: string;
  taskId: string;
  status: 'completed' | 'failed' | 'partial';
  startTime: string;
  endTime: string;
  duration: number;
  
  // Scraping results
  totalModelsFound: number;
  modelsProcessed: number;
  modelsStored: number;
  modelsSkipped: number;
  
  // Print analysis results
  modelsAnalyzed: number;
  printableModels: number;
  averagePrintTime: number;
  averageFilamentUsage: number;
  
  // Processing results
  filesConverted: number;
  thumbnailsGenerated: number;
  
  // Errors and warnings
  errors: string[];
  warnings: string[];
  
  // Performance metrics
  scrapingTime: number;
  processingTime: number;
  analysisTime: number;
  storageTime: number;
}

export class EnhancedScrapingScheduler {
  private tasks: Map<string, EnhancedScrapingTask> = new Map();
  private activeJobs: Map<string, any> = new Map(); // TODO: Track active jobs
  private cronJobs: Map<string, any> = new Map();

  private scraper: BrightDataScraperManager;
  private printSlicer: PrintSlicer; // TODO: Integrate real print slicing
  private formatConverter: FormatConverter; // TODO: Integrate format conversion
  private thumbnailGenerator: ThumbnailGenerator; // TODO: Integrate thumbnail generation
  private modelAnalyzer: ModelAnalyzer; // TODO: Integrate model analysis
  private repository: ScrapedModelsRepository; // TODO: Integrate database storage
  private storage: R2ModelStorage; // TODO: Integrate R2 storage

  constructor() {
    this.scraper = new BrightDataScraperManager();
    this.printSlicer = new PrintSlicer();
    this.formatConverter = new FormatConverter();
    this.thumbnailGenerator = new ThumbnailGenerator();
    this.modelAnalyzer = new ModelAnalyzer();
    this.repository = new ScrapedModelsRepository();
    this.storage = new R2ModelStorage();
    
    this.initializeEnhancedTasks();
  }

  /**
   * Initialize enhanced scraping tasks with print analysis
   */
  private initializeEnhancedTasks(): void {
    console.log('🚀 Initializing enhanced scraping scheduler with print analysis...');

    // Daily comprehensive scraping with full analysis
    this.createEnhancedTask({
      name: 'Daily Comprehensive Analysis',
      description: 'Daily scraping with full print analysis and processing',
      cronExpression: '0 2 * * *', // Daily at 2:00 AM
      platforms: ['printables.com', 'makerworld.com', 'thangs.com'],
      printAnalysisEnabled: true,
      qualityFiltering: true,
      autoProcessing: true,
      slicingProfiles: ['PLA', 'ABS', 'PETG'],
      config: {
        maxModelsPerPlatform: 100,
        qualityThreshold: 0.8,
        enableAIAnalysis: true,
        notifyOnCompletion: true,
        retryFailedJobs: true,
        cleanupOldData: true,
        dataRetentionDays: 30,
        
        enablePrintAnalysis: true,
        slicingEngine: 'prusaslicer',
        defaultFilamentType: 'PLA',
        generateSlicingPreviews: true,
        estimatePrintCosts: true,
        
        convertToGLB: true,
        generateThumbnails: true,
        optimizeModels: true,
        extractMetadata: true,
        
        minDownloads: 100,
        minRating: 4.0,
        maxFileSize: 100,
        requiredFormats: ['stl', 'obj'],
        excludeCommercial: false
      }
    });

    // Quick trending analysis
    this.createEnhancedTask({
      name: 'Trending Models Quick Analysis',
      description: 'Fast analysis of trending models every 6 hours',
      cronExpression: '0 */6 * * *', // Every 6 hours
      platforms: ['printables.com', 'makerworld.com'],
      printAnalysisEnabled: true,
      qualityFiltering: true,
      autoProcessing: false,
      slicingProfiles: ['PLA'],
      config: {
        maxModelsPerPlatform: 25,
        qualityThreshold: 0.7,
        enableAIAnalysis: false,
        notifyOnCompletion: false,
        retryFailedJobs: false,
        cleanupOldData: false,
        dataRetentionDays: 7,
        
        enablePrintAnalysis: true,
        slicingEngine: 'cura',
        defaultFilamentType: 'PLA',
        generateSlicingPreviews: false,
        estimatePrintCosts: true,
        
        convertToGLB: false,
        generateThumbnails: true,
        optimizeModels: false,
        extractMetadata: true,
        
        minDownloads: 50,
        minRating: 3.5,
        maxFileSize: 50,
        requiredFormats: ['stl'],
        excludeCommercial: true
      }
    });

    // Weekly deep analysis with multiple slicing profiles
    this.createEnhancedTask({
      name: 'Weekly Deep Print Analysis',
      description: 'Comprehensive weekly analysis with multiple filament profiles',
      cronExpression: '0 1 * * 0', // Sundays at 1:00 AM
      platforms: ['printables.com', 'makerworld.com', 'thangs.com'],
      printAnalysisEnabled: true,
      qualityFiltering: true,
      autoProcessing: true,
      slicingProfiles: ['PLA', 'ABS', 'PETG', 'TPU'],
      config: {
        maxModelsPerPlatform: 200,
        qualityThreshold: 0.9,
        enableAIAnalysis: true,
        notifyOnCompletion: true,
        retryFailedJobs: true,
        cleanupOldData: false,
        dataRetentionDays: 90,
        
        enablePrintAnalysis: true,
        slicingEngine: 'superslicer',
        defaultFilamentType: 'PLA',
        generateSlicingPreviews: true,
        estimatePrintCosts: true,
        
        convertToGLB: true,
        generateThumbnails: true,
        optimizeModels: true,
        extractMetadata: true,
        
        minDownloads: 200,
        minRating: 4.5,
        maxFileSize: 200,
        requiredFormats: ['stl', 'obj', '3mf'],
        excludeCommercial: false
      }
    });
  }

  /**
   * Create enhanced scraping task
   */
  createEnhancedTask(taskData: Partial<EnhancedScrapingTask>): string {
    const taskId = this.generateTaskId();
    
    const task: EnhancedScrapingTask = {
      id: taskId,
      name: taskData.name || 'Unnamed Task',
      description: taskData.description || '',
      cronExpression: taskData.cronExpression || '0 0 * * *',
      platforms: taskData.platforms || ['printables.com'],
      enabled: true,
      config: taskData.config || this.getDefaultConfig(),
      runCount: 0,
      successCount: 0,
      failureCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      printAnalysisEnabled: taskData.printAnalysisEnabled || false,
      qualityFiltering: taskData.qualityFiltering || false,
      autoProcessing: taskData.autoProcessing || false,
      slicingProfiles: taskData.slicingProfiles || ['PLA']
    };

    task.nextRun = this.calculateNextRun(task.cronExpression);
    
    this.tasks.set(taskId, task);
    this.scheduleTask(taskId);
    
    console.log(`✅ Created enhanced task: ${task.name} (${taskId})`);
    return taskId;
  }

  /**
   * Execute enhanced scraping task with print analysis
   */
  private async executeEnhancedTask(taskId: string): Promise<ScrapingJobResult> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`Task not found: ${taskId}`);
    }

    const jobId = this.generateJobId();
    const startTime = new Date().toISOString();
    
    console.log(`🚀 Starting enhanced scraping job: ${jobId} for task: ${task.name}`);

    try {
      // Update task statistics
      task.runCount++;
      task.lastRun = startTime;
      task.nextRun = this.calculateNextRun(task.cronExpression);
      task.updatedAt = new Date().toISOString();

      // Phase 1: Scraping
      console.log('📡 Phase 1: Scraping models...');
      const scrapingStartTime = Date.now();

      // Convert platform names from .com format to internal format
      const normalizedPlatforms = task.platforms.map(platform => {
        const normalized = platform.replace('.com', '').replace('www.', '');
        // Ensure we return valid ModelSource types
        switch (normalized) {
          case 'printables': return 'printables' as const;
          case 'makerworld': return 'makerworld' as const;
          case 'thangs': return 'thangs' as const;
          default: return 'printables' as const; // fallback
        }
      });

      const scrapedModels = await this.scraper.scrapeAllPlatforms({
        platforms: normalizedPlatforms,
        modelsPerPlatform: task.config.maxModelsPerPlatform,
        delayBetweenRequests: 2000
      });

      const scrapingTime = Date.now() - scrapingStartTime;
      console.log(`✅ Scraped ${scrapedModels.length} models in ${scrapingTime}ms`);

      // Phase 2: Quality filtering
      console.log('🔍 Phase 2: Quality filtering...');
      const filteredModels = task.qualityFiltering 
        ? await this.applyQualityFilters(scrapedModels, task.config)
        : scrapedModels;

      console.log(`✅ ${filteredModels.length} models passed quality filters`);

      // Phase 3: Processing and analysis
      console.log('⚙️ Phase 3: Processing and analysis...');
      const processingStartTime = Date.now();
      
      const processedResults = await this.processModelsWithPrintAnalysis(
        filteredModels,
        task,
        jobId
      );

      const processingTime = Date.now() - processingStartTime;

      // Phase 4: Storage
      console.log('💾 Phase 4: Storing results...');
      const storageStartTime = Date.now();
      
      const storageResults = await this.storeProcessedModels(processedResults);
      
      const storageTime = Date.now() - storageStartTime;

      // Update task success count
      task.successCount++;
      this.tasks.set(taskId, task);

      const endTime = new Date().toISOString();
      const totalDuration = Date.now() - new Date(startTime).getTime();

      const jobResult: ScrapingJobResult = {
        jobId,
        taskId,
        status: 'completed',
        startTime,
        endTime,
        duration: totalDuration,
        
        totalModelsFound: scrapedModels.length,
        modelsProcessed: filteredModels.length,
        modelsStored: storageResults.stored,
        modelsSkipped: storageResults.skipped,
        
        modelsAnalyzed: processedResults.analyzed,
        printableModels: processedResults.printable,
        averagePrintTime: processedResults.avgPrintTime,
        averageFilamentUsage: processedResults.avgFilamentUsage,
        
        filesConverted: processedResults.converted,
        thumbnailsGenerated: processedResults.thumbnails,
        
        errors: processedResults.errors,
        warnings: processedResults.warnings,
        
        scrapingTime,
        processingTime,
        analysisTime: processedResults.analysisTime,
        storageTime
      };

      console.log(`✅ Enhanced scraping job completed: ${jobId}`);
      console.log(`📊 Results: ${jobResult.modelsStored} stored, ${jobResult.modelsAnalyzed} analyzed`);

      return jobResult;

    } catch (error) {
      console.error(`❌ Enhanced scraping job failed: ${jobId}`, error);
      
      task.failureCount++;
      this.tasks.set(taskId, task);

      throw error;
    }
  }

  /**
   * Apply quality filters to scraped models
   */
  private async applyQualityFilters(models: any[], config: EnhancedScrapingConfig): Promise<any[]> {
    return models.filter(model => {
      // Download count filter
      if (model.download_count < config.minDownloads) return false;
      
      // Rating filter
      if (model.rating && model.rating < config.minRating) return false;
      
      // File size filter
      if (model.file_size > config.maxFileSize * 1024 * 1024) return false;
      
      // Format filter
      if (config.requiredFormats.length > 0) {
        const hasRequiredFormat = config.requiredFormats.some(format => 
          model.file_formats?.includes(format)
        );
        if (!hasRequiredFormat) return false;
      }
      
      // Commercial filter
      if (config.excludeCommercial && !model.is_free) return false;
      
      return true;
    });
  }

  /**
   * Process models with print analysis
   */
  private async processModelsWithPrintAnalysis(
    models: any[],
    task: EnhancedScrapingTask,
    _jobId: string
  ): Promise<any> {
    const analysisStartTime = Date.now();
    let analyzed = 0;
    let printable = 0;
    let converted = 0;
    let thumbnails = 0;
    let totalPrintTime = 0;
    let totalFilamentUsage = 0;
    const errors: string[] = [];
    const warnings: string[] = [];

    for (const model of models) {
      try {
        // Convert model if needed
        if (task.config.convertToGLB) {
          // Conversion logic here
          converted++;
        }

        // Generate thumbnails
        if (task.config.generateThumbnails) {
          // Thumbnail generation logic here
          thumbnails++;
        }

        // Print analysis
        if (task.printAnalysisEnabled) {
          for (const filamentType of task.slicingProfiles) {
            const printSettings = PrintSlicer.getDefaultSettings(filamentType as any);
            
            // Simulate slicing analysis
            const slicingResult = await this.simulatePrintAnalysis(model, printSettings);
            
            if (slicingResult.success) {
              analyzed++;
              if (slicingResult.printabilityScore > 70) {
                printable++;
              }
              totalPrintTime += slicingResult.estimatedPrintTime;
              totalFilamentUsage += slicingResult.filamentUsed;
            }
          }
        }

      } catch (error) {
        errors.push(`Failed to process model ${model.id}: ${error}`);
      }
    }

    const analysisTime = Date.now() - analysisStartTime;

    return {
      analyzed,
      printable,
      converted,
      thumbnails,
      avgPrintTime: analyzed > 0 ? totalPrintTime / analyzed : 0,
      avgFilamentUsage: analyzed > 0 ? totalFilamentUsage / analyzed : 0,
      errors,
      warnings,
      analysisTime
    };
  }

  /**
   * Simulate print analysis for development
   */
  private async simulatePrintAnalysis(_model: any, _settings: PrintSettings): Promise<SlicingResult> {
    // Simulate print analysis
    return {
      success: true,
      layerCount: Math.floor(Math.random() * 500) + 100,
      estimatedPrintTime: Math.floor(Math.random() * 600) + 60, // 1-10 hours
      filamentUsed: Math.floor(Math.random() * 50) + 10, // 10-60g
      filamentLength: Math.floor(Math.random() * 100) + 20, // 20-120m
      supportVolume: Math.floor(Math.random() * 5),
      layers: [],
      printabilityScore: Math.floor(Math.random() * 40) + 60, // 60-100
      warnings: [],
      recommendations: [],
      previewImages: []
    };
  }

  /**
   * Store processed models
   */
  private async storeProcessedModels(processedResults: any): Promise<{ stored: number; skipped: number }> {
    // Simulate storage
    return {
      stored: Math.floor(processedResults.analyzed * 0.9),
      skipped: Math.floor(processedResults.analyzed * 0.1)
    };
  }

  /**
   * Schedule a task
   */
  private scheduleTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task || !task.enabled) return;

    const cronJob = cron.schedule(task.cronExpression, async () => {
      try {
        await this.executeEnhancedTask(taskId);
      } catch (error) {
        console.error(`❌ Scheduled task failed: ${taskId}`, error);
      }
    });

    this.cronJobs.set(taskId, cronJob);
    cronJob.start();
    
    console.log(`⏰ Scheduled task: ${task.name} (${task.cronExpression})`);
  }

  /**
   * Get default enhanced config
   */
  private getDefaultConfig(): EnhancedScrapingConfig {
    return {
      maxModelsPerPlatform: 50,
      qualityThreshold: 0.7,
      enableAIAnalysis: true,
      notifyOnCompletion: true,
      retryFailedJobs: true,
      cleanupOldData: true,
      dataRetentionDays: 30,
      
      enablePrintAnalysis: true,
      slicingEngine: 'prusaslicer',
      defaultFilamentType: 'PLA',
      generateSlicingPreviews: true,
      estimatePrintCosts: true,
      
      convertToGLB: true,
      generateThumbnails: true,
      optimizeModels: true,
      extractMetadata: true,
      
      minDownloads: 10,
      minRating: 3.0,
      maxFileSize: 100,
      requiredFormats: ['stl'],
      excludeCommercial: false
    };
  }

  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private generateJobId(): string {
    return `job_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private calculateNextRun(_cronExpression: string): string {
    // Simple next run calculation - in production use a proper cron parser
    const now = new Date();
    now.setHours(now.getHours() + 1);
    return now.toISOString();
  }

  /**
   * Get all tasks
   */
  getAllTasks(): EnhancedScrapingTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * Get task by ID
   */
  getTask(taskId: string): EnhancedScrapingTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * Start scheduler
   */
  start(): void {
    console.log('🚀 Starting enhanced scraping scheduler...');

    this.tasks.forEach((task, taskId) => {
      if (task.enabled) {
        this.scheduleTask(taskId);
      }
    });

    console.log(`✅ Enhanced scheduler started with ${this.tasks.size} tasks`);
  }

  /**
   * Stop scheduler
   */
  stop(): void {
    console.log('🛑 Stopping enhanced scraping scheduler...');

    this.cronJobs.forEach((cronJob) => {
      cronJob.destroy();
    });

    this.cronJobs.clear();
    console.log('✅ Enhanced scheduler stopped');
  }
}
