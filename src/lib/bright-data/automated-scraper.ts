/**
 * Автоматизований скрапер для 3D моделей
 * Використовує Bright Data MCP для збору популярних моделей
 */

import { EnhancedBrightDataMCPClient, ScrapingResult } from './enhanced-mcp-client';

export interface ScrapingJob {
  id: string;
  platform: string;
  urls: string[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  startTime?: string;
  endTime?: string;
  results: ScrapingResult[];
  errors: string[];
  config: ScrapingConfig;
}

export interface ScrapingConfig {
  maxConcurrent: number;
  delayBetweenRequests: number;
  retryFailedUrls: boolean;
  extractImages: boolean;
  followRedirects: boolean;
  filterDuplicates: boolean;
  minQualityScore: number;
}

export interface PopularModelData {
  title: string;
  description: string;
  designer: string;
  platform: string;
  url: string;
  imageUrl?: string;
  downloadCount: number;
  likeCount: number;
  viewCount: number;
  rating: number;
  tags: string[];
  category: string;
  license: string;
  files: ModelFile[];
  printSettings?: PrintSettings;
  qualityScore: number;
  scrapedAt: string;
}

export interface ModelFile {
  name: string;
  url: string;
  size: string;
  format: string;
}

export interface PrintSettings {
  layerHeight: string;
  infill: string;
  supports: boolean;
  printTime?: string;
  material?: string;
}

export class AutomatedScraper {
  private mcpClient: EnhancedBrightDataMCPClient;
  private activeJobs: Map<string, ScrapingJob> = new Map();
  private defaultConfig: ScrapingConfig;

  constructor() {
    this.mcpClient = new EnhancedBrightDataMCPClient({
      timeout: 30000,
      retryAttempts: 3,
      rateLimitDelay: 2000,
      enableFallback: true
    });

    this.defaultConfig = {
      maxConcurrent: 5,
      delayBetweenRequests: 1000,
      retryFailedUrls: true,
      extractImages: true,
      followRedirects: true,
      filterDuplicates: true,
      minQualityScore: 0.7
    };
  }

  /**
   * Запуск автоматизованого скрапінгу популярних моделей
   */
  async scrapePopularModels(platforms: string[] = ['printables.com', 'makerworld.com', 'thangs.com']): Promise<string> {
    const jobId = this.generateJobId();
    
    console.log(`🚀 Запуск автоматизованого скрапінгу: ${jobId}`);

    // Створюємо завдання
    const job: ScrapingJob = {
      id: jobId,
      platform: platforms.join(','),
      urls: [],
      status: 'pending',
      progress: 0,
      results: [],
      errors: [],
      config: this.defaultConfig
    };

    this.activeJobs.set(jobId, job);

    // Запускаємо асинхронно
    this.executeScrapingJob(jobId, platforms).catch(error => {
      console.error(`❌ Помилка виконання завдання ${jobId}:`, error);
      this.updateJobStatus(jobId, 'failed', [error.message]);
    });

    return jobId;
  }

  /**
   * Виконання завдання скрапінгу
   */
  private async executeScrapingJob(jobId: string, platforms: string[]): Promise<void> {
    const job = this.activeJobs.get(jobId);
    if (!job) return;

    try {
      this.updateJobStatus(jobId, 'running');

      // Крок 1: Знаходимо популярні URL для кожної платформи
      console.log(`📋 Збір URL для платформ: ${platforms.join(', ')}`);
      const allUrls = await this.discoverPopularUrls(platforms);
      
      job.urls = allUrls;
      job.progress = 10;

      console.log(`🔍 Знайдено ${allUrls.length} URL для скрапінгу`);

      // Крок 2: Скрапимо кожен URL
      const results: ScrapingResult[] = [];
      const errors: string[] = [];

      for (let i = 0; i < allUrls.length; i++) {
        const url = allUrls[i];
        
        try {
          console.log(`📄 Скрапінг ${i + 1}/${allUrls.length}: ${url}`);
          
          const result = await this.mcpClient.scrapePage(url, {
            format: 'markdown',
            useStructuredData: true,
            extractImages: job.config.extractImages,
            followRedirects: job.config.followRedirects
          });

          if (result.success) {
            results.push(result);
          } else {
            errors.push(`${url}: ${result.error}`);
          }

          // Оновлюємо прогрес
          job.progress = 10 + Math.floor((i + 1) / allUrls.length * 80);
          
          // Затримка між запитами
          if (i < allUrls.length - 1) {
            await this.delay(job.config.delayBetweenRequests);
          }

        } catch (error) {
          const errorMsg = `${url}: ${error instanceof Error ? error.message : 'Невідома помилка'}`;
          errors.push(errorMsg);
          console.error(`❌ Помилка скрапінгу ${url}:`, error);
        }
      }

      // Крок 3: Обробляємо результати
      console.log(`🔄 Обробка ${results.length} результатів`);
      const processedModels = await this.processScrapingResults(results);
      
      // Крок 4: Фільтруємо та зберігаємо
      const filteredModels = this.filterQualityModels(processedModels, job.config.minQualityScore);
      
      job.results = results;
      job.errors = errors;
      job.progress = 100;
      
      this.updateJobStatus(jobId, 'completed');

      console.log(`✅ Завдання ${jobId} завершено. Оброблено ${filteredModels.length} якісних моделей`);

      // Зберігаємо результати в базу даних
      await this.saveModelsToDatabase(filteredModels);

    } catch (error) {
      console.error(`❌ Критична помилка завдання ${jobId}:`, error);
      this.updateJobStatus(jobId, 'failed', [error instanceof Error ? error.message : 'Критична помилка']);
    }
  }

  /**
   * Знаходження популярних URL
   */
  private async discoverPopularUrls(platforms: string[]): Promise<string[]> {
    const allUrls: string[] = [];

    for (const platform of platforms) {
      try {
        const urls = await this.getPopularUrlsForPlatform(platform);
        allUrls.push(...urls);
        console.log(`📋 ${platform}: знайдено ${urls.length} URL`);
      } catch (error) {
        console.error(`❌ Помилка збору URL для ${platform}:`, error);
      }
    }

    return allUrls;
  }

  /**
   * Отримання популярних URL для конкретної платформи
   */
  private async getPopularUrlsForPlatform(platform: string): Promise<string[]> {
    // Використовуємо пошукову систему для знаходження популярних моделей
    const searchQueries = [
      'popular 3d models',
      'trending 3d prints',
      'most downloaded 3d models',
      'best 3d designs',
      'top rated 3d models'
    ];

    const urls: string[] = [];

    for (const query of searchQueries) {
      try {
        const searchResults = await this.mcpClient.callMCPTool('search_engine_Bright_Data', {
          query: `site:${platform} ${query}`,
          engine: 'google'
        });

        if (searchResults && searchResults.results) {
          const platformUrls = searchResults.results
            .filter((result: any) => result.url.includes(platform))
            .map((result: any) => result.url);
          
          urls.push(...platformUrls);
        }
      } catch (error) {
        console.error(`❌ Помилка пошуку для ${platform} з запитом "${query}":`, error);
      }
    }

    // Видаляємо дублікати
    return [...new Set(urls)];
  }

  /**
   * Обробка результатів скрапінгу
   */
  private async processScrapingResults(results: ScrapingResult[]): Promise<PopularModelData[]> {
    const models: PopularModelData[] = [];

    for (const result of results) {
      try {
        if (!result.success || !result.data) continue;

        const modelData = await this.extractModelData(result);
        if (modelData) {
          models.push(modelData);
        }
      } catch (error) {
        console.error(`❌ Помилка обробки результату:`, error);
      }
    }

    return models;
  }

  /**
   * Витягування даних моделі з результату скрапінгу
   */
  private async extractModelData(result: ScrapingResult): Promise<PopularModelData | null> {
    try {
      // Тут буде логіка витягування структурованих даних з результату скрапінгу
      // Поки що повертаємо симуляцію
      return {
        title: `Model from ${result.platform}`,
        description: 'Extracted model description',
        designer: 'Unknown Designer',
        platform: result.platform,
        url: result.url,
        downloadCount: Math.floor(Math.random() * 10000),
        likeCount: Math.floor(Math.random() * 1000),
        viewCount: Math.floor(Math.random() * 50000),
        rating: 4 + Math.random(),
        tags: ['3d-printing', 'popular'],
        category: 'General',
        license: 'Creative Commons',
        files: [],
        qualityScore: 0.8 + Math.random() * 0.2,
        scrapedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error(`❌ Помилка витягування даних моделі:`, error);
      return null;
    }
  }

  /**
   * Фільтрація моделей за якістю
   */
  private filterQualityModels(models: PopularModelData[], minScore: number): PopularModelData[] {
    return models.filter(model => model.qualityScore >= minScore);
  }

  /**
   * Збереження моделей в базу даних
   */
  private async saveModelsToDatabase(models: PopularModelData[]): Promise<void> {
    // Тут буде логіка збереження в базу даних
    console.log(`💾 Збереження ${models.length} моделей в базу даних`);
  }

  /**
   * Оновлення статусу завдання
   */
  private updateJobStatus(jobId: string, status: ScrapingJob['status'], errors: string[] = []): void {
    const job = this.activeJobs.get(jobId);
    if (!job) return;

    job.status = status;
    if (errors.length > 0) {
      job.errors.push(...errors);
    }

    if (status === 'running' && !job.startTime) {
      job.startTime = new Date().toISOString();
    }

    if (status === 'completed' || status === 'failed') {
      job.endTime = new Date().toISOString();
    }

    this.activeJobs.set(jobId, job);
  }

  /**
   * Генерація ID завдання
   */
  private generateJobId(): string {
    return `scraping_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Затримка
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Отримання статусу завдання
   */
  getJobStatus(jobId: string): ScrapingJob | null {
    return this.activeJobs.get(jobId) || null;
  }

  /**
   * Отримання всіх активних завдань
   */
  getActiveJobs(): ScrapingJob[] {
    return Array.from(this.activeJobs.values());
  }

  /**
   * Скасування завдання
   */
  cancelJob(jobId: string): boolean {
    const job = this.activeJobs.get(jobId);
    if (!job || job.status === 'completed' || job.status === 'failed') {
      return false;
    }

    this.updateJobStatus(jobId, 'failed', ['Завдання скасовано користувачем']);
    return true;
  }
}
