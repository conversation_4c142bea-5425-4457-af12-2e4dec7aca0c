# Bright Data Integration для 3D Marketplace

Цей модуль забезпечує інтеграцію з Bright Data MCP tools для скрапінгу даних з основних платформ 3D моделей.

## 🎯 Підтримувані платформи

- **MakerWorld** (makerworld.com) - Платформа Bambu Lab
- **Printables** (printables.com) - Безкоштовна платформа Prusa
- **Thangs** (thangs.com) - Платформа з платними та безкоштовними моделями

## 📁 Структура модуля

```
src/lib/bright-data/
├── scraper.ts                    # Базовий Bright Data клас
├── scraper-manager.ts            # Менеджер для управління скраперами
├── market-intelligence.ts        # Аналіз ринку та конкурентів
├── test-integration.ts           # Тестування інтеграції
├── platform-scrapers/
│   ├── base-platform-scraper.ts  # Базовий клас для платформ
│   ├── makerworld-scraper.ts     # Скрапер MakerWorld
│   ├── printables-scraper.ts     # Скрапер Printables
│   └── thangs-scraper.ts         # Скрапер Thangs
└── README.md                     # Ця документація
```

## 🚀 Основні класи

### BrightDataScraper
Базовий клас для роботи з Bright Data MCP tools.

```typescript
import { BrightDataScraper } from '@/lib/bright-data/scraper';

const scraper = new BrightDataScraper();

// Скрапінг як Markdown
const markdownResult = await scraper.scrapeAsMarkdown('https://www.printables.com/model/123');

// Скрапінг як HTML
const htmlResult = await scraper.scrapeAsHtml('https://www.printables.com/model/123');

// Пошук в Google
const searchResult = await scraper.searchEngine('3D printing miniatures');
```

### BrightDataScraperManager
Менеджер для координації скрапінгу з усіх платформ.

```typescript
import { BrightDataScraperManager } from '@/lib/bright-data/scraper-manager';

const manager = new BrightDataScraperManager();

// Скрапінг з усіх платформ
const allModels = await manager.scrapeAllPlatforms({
  modelsPerPlatform: 20,
  platforms: ['makerworld', 'printables', 'thangs']
});

// Пошук на всіх платформах
const searchResults = await manager.searchAllPlatforms('miniature', {
  modelsPerPlatform: 10
});

// Скрапінг популярних моделей
const popularModels = await manager.scrapePopularModels({
  modelsPerPlatform: 15
});
```

### MarketIntelligenceService
Сервіс для аналізу ринку та конкурентів.

```typescript
import { MarketIntelligenceService } from '@/lib/bright-data/market-intelligence';

const marketService = new MarketIntelligenceService();

// Аналіз конкурентів (з реальними даними)
const competitors = await marketService.analyzeCompetitors(true);

// Аналіз ринкових трендів
const trends = await marketService.analyzeMarketTrends();

// Рекомендації по ціноутворенню
const pricing = await marketService.generatePricingInsights('Miniatures');
```

## 🔧 Платформо-специфічні скрапери

### MakerWorldScraper
```typescript
import { MakerWorldScraper } from '@/lib/bright-data/platform-scrapers/makerworld-scraper';

const scraper = new MakerWorldScraper();
const models = await scraper.scrapePopularModels(10);
```

### PrintablesScraper
```typescript
import { PrintablesScraper } from '@/lib/bright-data/platform-scrapers/printables-scraper';

const scraper = new PrintablesScraper();
const models = await scraper.searchModels('miniature', 10);
```

### ThangsScraper
```typescript
import { ThangsScraper } from '@/lib/bright-data/platform-scrapers/thangs-scraper';

const scraper = new ThangsScraper();
const models = await scraper.scrapeByCategory('Games', 10);
```

## 📡 API Endpoints

### POST /api/scrape
Основний endpoint для скрапінгу.

```bash
# Скрапінг з Bright Data
curl -X POST /api/scrape \
  -H "Content-Type: application/json" \
  -d '{"action": "bright-data-scrape", "count": 50}'
```

### GET /api/market-intelligence
Endpoint для Market Intelligence.

```bash
# Аналіз конкурентів з реальними даними
curl "/api/market-intelligence?action=competitors&real=true"

# Аналіз трендів
curl "/api/market-intelligence?action=trends"

# Рекомендації по ціноутворенню
curl "/api/market-intelligence?action=pricing&category=Miniatures"
```

## 🧪 Тестування

### Швидкий тест
```typescript
import { testBrightDataIntegration } from '@/lib/bright-data/test-integration';

await testBrightDataIntegration();
```

### Повне тестування
```typescript
import { runFullBrightDataTests } from '@/lib/bright-data/test-integration';

await runFullBrightDataTests();
```

### Ручне тестування
```typescript
import { BrightDataTestSuite } from '@/lib/bright-data/test-integration';

const testSuite = new BrightDataTestSuite();

// Тестування окремих компонентів
await testSuite.testBasicScraper();
await testSuite.testPlatformScrapers();
await testSuite.testScraperManager();
await testSuite.testMarketIntelligence();
```

## ⚙️ Конфігурація

### BrightDataConfig
```typescript
interface BrightDataConfig {
  retryAttempts: number;     // Кількість спроб повтору (за замовчуванням: 3)
  retryDelay: number;        // Затримка між спробами в мс (за замовчуванням: 2000)
  timeout: number;           // Таймаут запиту в мс (за замовчуванням: 30000)
  cacheEnabled: boolean;     // Увімкнення кешування (за замовчуванням: true)
  cacheTTL: number;          // Час життя кешу в секундах (за замовчуванням: 3600)
}
```

### BatchScrapingOptions
```typescript
interface BatchScrapingOptions {
  platforms?: ModelSource[];           // Платформи для скрапінгу
  modelsPerPlatform?: number;         // Кількість моделей з кожної платформи
  delayBetweenRequests?: number;      // Затримка між запитами в мс
  retryFailedScrapes?: boolean;       // Повторювати невдалі спроби
  maxRetries?: number;                // Максимальна кількість повторів
  includeImages?: boolean;            // Включати зображення
  includeFiles?: boolean;             // Включати файли
}
```

## 📊 Структура даних

### ScrapedModel
Модель отримана в результаті скрапінгу відповідає інтерфейсу `ScrapedModel` з `/types/models.ts`.

### CompetitorData
```typescript
interface CompetitorData {
  platform: string;
  url: string;
  modelCount: number;
  averagePrice: number;
  topCategories: string[];
  popularModels: Array<{
    name: string;
    price: number;
    downloads: number;
    rating: number;
  }>;
  pricingStrategy: {
    freeModels: number;
    premiumModels: number;
    subscriptionPlans: boolean;
  };
  lastUpdated: string;
  dataSource: 'real' | 'mock';
}
```

## 🔄 Fallback механізми

Система має вбудовані fallback механізми:

1. **Bright Data недоступний** → Використання mock даних
2. **Платформа недоступна** → Пропуск платформи
3. **Помилка парсингу** → Логування та продовження роботи
4. **Таймаут запиту** → Автоматичний повтор з експоненційною затримкою

## 🚨 Обробка помилок

Всі методи мають вбудовану обробку помилок:

```typescript
try {
  const models = await scraperManager.scrapeAllPlatforms();
} catch (error) {
  console.error('Помилка скрапінгу:', error);
  // Fallback до mock даних або альтернативного джерела
}
```

## 📈 Моніторинг та метрики

### Статистика скрапінгу
```typescript
const stats = scraperManager.getStats();
console.log('Статистика:', {
  totalModels: stats.totalModels,
  successfulScrapes: stats.successfulScrapes,
  failedScrapes: stats.failedScrapes,
  platformStats: stats.platformStats,
  duration: stats.duration
});
```

### Статистика сесії Bright Data
```typescript
const sessionStats = await scraper.getSessionStats();
console.log('Статистика Bright Data:', sessionStats);
```

## 🔐 Безпека

- Всі запити мають rate limiting
- Використання User-Agent для ідентифікації
- Автоматичні затримки між запитами
- Обробка CAPTCHA через Bright Data
- Ротація IP адрес через Bright Data

## 🎛️ Налаштування продакшн

Для продакшн середовища рекомендується:

1. Увімкнути кешування результатів
2. Налаштувати моніторинг помилок
3. Використовувати черги для великих завдань
4. Налаштувати алерти для критичних помилок
5. Регулярно оновлювати дані конкурентів
