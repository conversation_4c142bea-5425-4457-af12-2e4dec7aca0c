/**
 * Bright Data Scraper Manager
 * Управління всіма платформо-специфічними скраперами
 */

import { MakerWorldScraper } from './platform-scrapers/makerworld-scraper';
import { PrintablesScraper } from './platform-scrapers/printables-scraper';
import { ThangsScraper } from './platform-scrapers/thangs-scraper';
import { ScrapedModel, ModelSource } from '@/types/models';
import { PlatformScrapingOptions } from './scraper';

export interface ScrapingStats {
  totalModels: number;
  successfulScrapes: number;
  failedScrapes: number;
  platformStats: Record<string, {
    models: number;
    success: number;
    failed: number;
  }>;
  startTime: string;
  endTime?: string;
  duration?: number;
}

export interface BatchScrapingOptions extends PlatformScrapingOptions {
  platforms?: ModelSource[];
  modelsPerPlatform?: number;
  delayBetweenRequests?: number;
  retryFailedScrapes?: boolean;
  maxRetries?: number;
}

export class BrightDataScraperManager {
  private makerWorldScraper: MakerWorldScraper;
  private printablesScraper: PrintablesScraper;
  private thangsScraper: ThangsScraper;
  private stats: ScrapingStats;

  constructor() {
    this.makerWorldScraper = new MakerWorldScraper();
    this.printablesScraper = new PrintablesScraper();
    this.thangsScraper = new ThangsScraper();
    
    this.stats = {
      totalModels: 0,
      successfulScrapes: 0,
      failedScrapes: 0,
      platformStats: {
        makerworld: { models: 0, success: 0, failed: 0 },
        printables: { models: 0, success: 0, failed: 0 },
        thangs: { models: 0, success: 0, failed: 0 },
      },
      startTime: new Date().toISOString(),
    };
  }

  /**
   * Скрапінг з усіх платформ
   */
  async scrapeAllPlatforms(options: BatchScrapingOptions = {}): Promise<ScrapedModel[]> {
    const {
      platforms = ['makerworld', 'printables', 'thangs'],
      modelsPerPlatform = 20,
      delayBetweenRequests = 2000,
    } = options;

    console.log('🚀 Початок скрапінгу з усіх платформ...');
    this.resetStats();

    const allModels: ScrapedModel[] = [];

    for (const platform of platforms) {
      try {
        console.log(`📡 Скрапінг з ${platform}...`);
        
        const models = await this.scrapePlatform(platform, modelsPerPlatform, options);
        allModels.push(...models);
        
        this.updatePlatformStats(platform, models.length, 0);
        
        // Затримка між платформами
        if (delayBetweenRequests > 0) {
          await new Promise(resolve => setTimeout(resolve, delayBetweenRequests));
        }
      } catch (error) {
        console.error(`❌ Помилка скрапінгу ${platform}:`, error);
        this.updatePlatformStats(platform, 0, 1);
      }
    }

    this.finalizeStats();
    console.log(`✅ Скрапінг завершено. Отримано ${allModels.length} моделей`);
    
    return allModels;
  }

  /**
   * Скрапінг популярних моделей з усіх платформ
   */
  async scrapePopularModels(options: BatchScrapingOptions = {}): Promise<ScrapedModel[]> {
    const {
      platforms = ['makerworld', 'printables', 'thangs'],
      modelsPerPlatform = 15,
      delayBetweenRequests = 2000,
    } = options;

    console.log('🔥 Скрапінг популярних моделей...');
    this.resetStats();

    const allModels: ScrapedModel[] = [];

    for (const platform of platforms) {
      try {
        console.log(`📈 Скрапінг популярних моделей з ${platform}...`);
        
        const scraper = this.getScraper(platform);
        const models = await scraper.scrapePopularModels(modelsPerPlatform, options);
        
        allModels.push(...models);
        this.updatePlatformStats(platform, models.length, 0);
        
        if (delayBetweenRequests > 0) {
          await new Promise(resolve => setTimeout(resolve, delayBetweenRequests));
        }
      } catch (error) {
        console.error(`❌ Помилка скрапінгу популярних моделей ${platform}:`, error);
        this.updatePlatformStats(platform, 0, 1);
      }
    }

    this.finalizeStats();
    console.log(`✅ Скрапінг популярних моделей завершено. Отримано ${allModels.length} моделей`);
    
    return allModels;
  }

  /**
   * Пошук моделей на всіх платформах
   */
  async searchAllPlatforms(query: string, options: BatchScrapingOptions = {}): Promise<ScrapedModel[]> {
    const {
      platforms = ['makerworld', 'printables', 'thangs'],
      modelsPerPlatform = 10,
      delayBetweenRequests = 2000,
    } = options;

    console.log(`🔍 Пошук "${query}" на всіх платформах...`);
    this.resetStats();

    const allModels: ScrapedModel[] = [];

    for (const platform of platforms) {
      try {
        console.log(`🔎 Пошук "${query}" на ${platform}...`);
        
        const scraper = this.getScraper(platform);
        const models = await scraper.searchModels(query, modelsPerPlatform, options);
        
        allModels.push(...models);
        this.updatePlatformStats(platform, models.length, 0);
        
        if (delayBetweenRequests > 0) {
          await new Promise(resolve => setTimeout(resolve, delayBetweenRequests));
        }
      } catch (error) {
        console.error(`❌ Помилка пошуку на ${platform}:`, error);
        this.updatePlatformStats(platform, 0, 1);
      }
    }

    this.finalizeStats();
    console.log(`✅ Пошук завершено. Знайдено ${allModels.length} моделей`);
    
    return allModels;
  }

  /**
   * Скрапінг за категорією на всіх платформах
   */
  async scrapeCategoryAllPlatforms(category: string, options: BatchScrapingOptions = {}): Promise<ScrapedModel[]> {
    const {
      platforms = ['makerworld', 'printables', 'thangs'],
      modelsPerPlatform = 15,
      delayBetweenRequests = 2000,
    } = options;

    console.log(`📂 Скрапінг категорії "${category}" на всіх платформах...`);
    this.resetStats();

    const allModels: ScrapedModel[] = [];

    for (const platform of platforms) {
      try {
        console.log(`📁 Скрапінг категорії "${category}" на ${platform}...`);
        
        const scraper = this.getScraper(platform);
        const models = await scraper.scrapeByCategory(category, modelsPerPlatform, options);
        
        allModels.push(...models);
        this.updatePlatformStats(platform, models.length, 0);
        
        if (delayBetweenRequests > 0) {
          await new Promise(resolve => setTimeout(resolve, delayBetweenRequests));
        }
      } catch (error) {
        console.error(`❌ Помилка скрапінгу категорії на ${platform}:`, error);
        this.updatePlatformStats(platform, 0, 1);
      }
    }

    this.finalizeStats();
    console.log(`✅ Скрапінг категорії завершено. Отримано ${allModels.length} моделей`);
    
    return allModels;
  }

  /**
   * Скрапінг окремої моделі за URL
   */
  async scrapeModelByUrl(url: string, options: PlatformScrapingOptions = {}): Promise<ScrapedModel | null> {
    const platform = this.detectPlatformFromUrl(url);
    
    if (!platform) {
      console.error(`❌ Не вдалося визначити платформу для URL: ${url}`);
      return null;
    }

    console.log(`🔍 Скрапінг моделі з ${platform}: ${url}`);
    
    try {
      const scraper = this.getScraper(platform);
      const model = await scraper.scrapeModel(url, options);
      
      if (model) {
        console.log(`✅ Модель успішно скрапнута: ${model.title}`);
      } else {
        console.log(`⚠️ Не вдалося скрапнути модель з ${url}`);
      }
      
      return model;
    } catch (error) {
      console.error(`❌ Помилка скрапінгу моделі:`, error);
      return null;
    }
  }

  /**
   * Отримання статистики скрапінгу
   */
  getStats(): ScrapingStats {
    return { ...this.stats };
  }

  /**
   * Приватні методи
   */
  private async scrapePlatform(platform: ModelSource, maxModels: number, options: PlatformScrapingOptions): Promise<ScrapedModel[]> {
    const scraper = this.getScraper(platform);
    return await scraper.scrapePopularModels(maxModels, options);
  }

  private getScraper(platform: ModelSource) {
    switch (platform) {
      case 'makerworld':
        return this.makerWorldScraper;
      case 'printables':
        return this.printablesScraper;
      case 'thangs':
        return this.thangsScraper;
      default:
        throw new Error(`Непідтримувана платформа: ${platform}`);
    }
  }

  private detectPlatformFromUrl(url: string): ModelSource | null {
    if (url.includes('makerworld.com')) return 'makerworld';
    if (url.includes('printables.com')) return 'printables';
    if (url.includes('thangs.com')) return 'thangs';
    return null;
  }

  private resetStats(): void {
    this.stats = {
      totalModels: 0,
      successfulScrapes: 0,
      failedScrapes: 0,
      platformStats: {
        makerworld: { models: 0, success: 0, failed: 0 },
        printables: { models: 0, success: 0, failed: 0 },
        thangs: { models: 0, success: 0, failed: 0 },
      },
      startTime: new Date().toISOString(),
    };
  }

  private updatePlatformStats(platform: string, successCount: number, failedCount: number): void {
    if (this.stats.platformStats[platform]) {
      this.stats.platformStats[platform].models += successCount;
      this.stats.platformStats[platform].success += successCount;
      this.stats.platformStats[platform].failed += failedCount;
    }
    
    this.stats.totalModels += successCount;
    this.stats.successfulScrapes += successCount;
    this.stats.failedScrapes += failedCount;
  }

  private finalizeStats(): void {
    this.stats.endTime = new Date().toISOString();
    this.stats.duration = new Date(this.stats.endTime).getTime() - new Date(this.stats.startTime).getTime();
  }
}
