/**
 * Тести інтеграції Bright Data MCP
 */

import { EnhancedBrightDataMCPClient } from '../enhanced-mcp-client';
import { AutomatedScraper } from '../automated-scraper';
import { ScrapingScheduler } from '../scraping-scheduler';
import { AIModelAnalyzer } from '../ai-model-analyzer';

describe('Bright Data MCP Integration', () => {
  let mcpClient: EnhancedBrightDataMCPClient;
  let scraper: AutomatedScraper;
  let scheduler: ScrapingScheduler;
  let analyzer: AIModelAnalyzer;

  beforeEach(() => {
    mcpClient = new EnhancedBrightDataMCPClient({
      timeout: 5000,
      retryAttempts: 2,
      rateLimitDelay: 100,
      enableFallback: true
    });

    scraper = new AutomatedScraper();
    scheduler = new ScrapingScheduler();
    analyzer = new AIModelAnalyzer();
  });

  afterEach(() => {
    scheduler.stop();
  });

  describe('EnhancedBrightDataMCPClient', () => {
    test('should initialize with default config', () => {
      const client = new EnhancedBrightDataMCPClient();
      expect(client).toBeDefined();
      
      const stats = client.getStats();
      expect(stats.size).toBeGreaterThan(0);
    });

    test('should scrape page with fallback', async () => {
      const url = 'https://www.printables.com/model/1286204-dummy-13-terminator-t800-armor';
      
      const result = await mcpClient.scrapePage(url, {
        format: 'markdown',
        useStructuredData: false
      });

      expect(result.success).toBe(true);
      expect(result.platform).toBe('printables.com');
      expect(result.url).toBe(url);
      expect(result.data).toBeDefined();
      expect(result.processingTime).toBeGreaterThan(0);
    });

    test('should handle multiple platforms', async () => {
      const urls = [
        'https://www.printables.com/model/123',
        'https://www.makerworld.com/model/456',
        'https://thangs.com/model/789'
      ];

      const results = await Promise.all(
        urls.map(url => mcpClient.scrapePage(url))
      );

      expect(results).toHaveLength(3);
      results.forEach((result, index) => {
        expect(result.success).toBe(true);
        expect(result.url).toBe(urls[index]);
      });

      // Перевіряємо статистику
      const stats = mcpClient.getStats();
      expect(stats.get('printables.com')?.totalRequests).toBeGreaterThan(0);
      expect(stats.get('makerworld.com')?.totalRequests).toBeGreaterThan(0);
      expect(stats.get('thangs.com')?.totalRequests).toBeGreaterThan(0);
    });

    test('should handle rate limiting', async () => {
      const urls = Array(5).fill('https://www.printables.com/model/test');
      
      const startTime = Date.now();
      await Promise.all(urls.map(url => mcpClient.scrapePage(url)));
      const endTime = Date.now();

      // Перевіряємо, що rate limiting працює (мінімальна затримка)
      expect(endTime - startTime).toBeGreaterThan(400); // 5 запитів * 100ms delay
    });

    test('should retry on failures', async () => {
      // Мокаємо метод для симуляції помилки
      const originalSimulate = mcpClient['simulateMCPCall'];
      let attemptCount = 0;
      
      mcpClient['simulateMCPCall'] = jest.fn().mockImplementation(async (toolName, params) => {
        attemptCount++;
        if (attemptCount < 2) {
          throw new Error('Симуляція помилки');
        }
        return originalSimulate.call(mcpClient, toolName, params);
      });

      const result = await mcpClient.scrapePage('https://test.com');
      
      expect(result.success).toBe(true);
      expect(attemptCount).toBe(2); // Перша спроба не вдалася, друга успішна
    });
  });

  describe('AutomatedScraper', () => {
    test('should start scraping job', async () => {
      const jobId = await scraper.scrapePopularModels(['printables.com']);
      
      expect(jobId).toBeDefined();
      expect(typeof jobId).toBe('string');
      expect(jobId).toMatch(/^scraping_\d+_/);

      // Перевіряємо статус завдання
      const jobStatus = scraper.getJobStatus(jobId);
      expect(jobStatus).toBeDefined();
      expect(jobStatus?.id).toBe(jobId);
      expect(['pending', 'running']).toContain(jobStatus?.status);
    });

    test('should track multiple jobs', async () => {
      const jobId1 = await scraper.scrapePopularModels(['printables.com']);
      const jobId2 = await scraper.scrapePopularModels(['makerworld.com']);

      const activeJobs = scraper.getActiveJobs();
      expect(activeJobs.length).toBeGreaterThanOrEqual(2);
      
      const jobIds = activeJobs.map(job => job.id);
      expect(jobIds).toContain(jobId1);
      expect(jobIds).toContain(jobId2);
    });

    test('should cancel job', async () => {
      const jobId = await scraper.scrapePopularModels(['printables.com']);
      
      const cancelled = scraper.cancelJob(jobId);
      expect(cancelled).toBe(true);

      const jobStatus = scraper.getJobStatus(jobId);
      expect(jobStatus?.status).toBe('failed');
      expect(jobStatus?.errors).toContain('Завдання скасовано користувачем');
    });
  });

  describe('ScrapingScheduler', () => {
    test('should create and manage tasks', () => {
      const taskId = scheduler.createTask({
        name: 'Test Task',
        description: 'Test description',
        cronExpression: '0 */6 * * *',
        platforms: ['printables.com'],
        config: {
          maxModelsPerPlatform: 50,
          qualityThreshold: 0.7,
          enableAIAnalysis: true,
          notifyOnCompletion: false,
          retryFailedJobs: false,
          cleanupOldData: false,
          dataRetentionDays: 7
        }
      });

      expect(taskId).toBeDefined();
      
      const task = scheduler.getTask(taskId);
      expect(task).toBeDefined();
      expect(task?.name).toBe('Test Task');
      expect(task?.isActive).toBe(true);
    });

    test('should start and stop scheduler', () => {
      expect(scheduler.isSchedulerRunning()).toBe(false);
      
      scheduler.start();
      expect(scheduler.isSchedulerRunning()).toBe(true);
      
      scheduler.stop();
      expect(scheduler.isSchedulerRunning()).toBe(false);
    });

    test('should get scheduler stats', () => {
      const stats = scheduler.getStats();
      
      expect(stats).toBeDefined();
      expect(stats.totalTasks).toBeGreaterThanOrEqual(3); // Default tasks
      expect(stats.activeTasks).toBeGreaterThanOrEqual(0);
      expect(stats.upcomingTasks).toBeDefined();
      expect(Array.isArray(stats.upcomingTasks)).toBe(true);
    });

    test('should update task', () => {
      const taskId = scheduler.createTask({
        name: 'Original Task',
        description: 'Original description',
        cronExpression: '0 */6 * * *',
        platforms: ['printables.com'],
        config: {
          maxModelsPerPlatform: 50,
          qualityThreshold: 0.7,
          enableAIAnalysis: true,
          notifyOnCompletion: false,
          retryFailedJobs: false,
          cleanupOldData: false,
          dataRetentionDays: 7
        }
      });

      const updated = scheduler.updateTask(taskId, {
        name: 'Updated Task',
        description: 'Updated description'
      });

      expect(updated).toBe(true);
      
      const task = scheduler.getTask(taskId);
      expect(task?.name).toBe('Updated Task');
      expect(task?.description).toBe('Updated description');
    });

    test('should activate and deactivate tasks', () => {
      const taskId = scheduler.createTask({
        name: 'Toggle Task',
        description: 'Test toggle',
        cronExpression: '0 */6 * * *',
        platforms: ['printables.com'],
        config: {
          maxModelsPerPlatform: 50,
          qualityThreshold: 0.7,
          enableAIAnalysis: true,
          notifyOnCompletion: false,
          retryFailedJobs: false,
          cleanupOldData: false,
          dataRetentionDays: 7
        }
      });

      // Деактивуємо
      const deactivated = scheduler.deactivateTask(taskId);
      expect(deactivated).toBe(true);
      expect(scheduler.getTask(taskId)?.isActive).toBe(false);

      // Активуємо
      const activated = scheduler.activateTask(taskId);
      expect(activated).toBe(true);
      expect(scheduler.getTask(taskId)?.isActive).toBe(true);
    });
  });

  describe('AIModelAnalyzer', () => {
    const mockModelData = {
      title: 'Test Model',
      description: 'Test description for analysis',
      designer: 'Test Designer',
      platform: 'printables.com',
      url: 'https://printables.com/model/test',
      downloadCount: 5000,
      likeCount: 500,
      viewCount: 25000,
      rating: 4.5,
      tags: ['test', 'model', 'popular'],
      category: 'Tools',
      license: 'Creative Commons',
      files: [
        { name: 'model.stl', url: 'test.stl', size: '1MB', format: 'STL' }
      ],
      qualityScore: 0.85,
      scrapedAt: new Date().toISOString()
    };

    test('should analyze model', async () => {
      const analysis = await analyzer.analyzeModel(mockModelData);
      
      expect(analysis).toBeDefined();
      expect(analysis.modelId).toBeDefined();
      expect(analysis.popularityScore).toBeGreaterThan(0);
      expect(analysis.qualityScore).toBeGreaterThan(0);
      expect(analysis.trendingScore).toBeGreaterThan(0);
      expect(analysis.marketPotential).toBeGreaterThan(0);
      expect(analysis.recommendationScore).toBeGreaterThan(0);
      expect(analysis.insights).toBeDefined();
      expect(Array.isArray(analysis.insights)).toBe(true);
      expect(analysis.predictions).toBeDefined();
      expect(Array.isArray(analysis.predictions)).toBe(true);
    });

    test('should generate insights for high-quality model', async () => {
      const highQualityModel = {
        ...mockModelData,
        downloadCount: 10000,
        rating: 4.8,
        likeCount: 1000
      };

      const analysis = await analyzer.analyzeModel(highQualityModel);
      
      const popularityInsight = analysis.insights.find(i => i.type === 'popularity');
      const qualityInsight = analysis.insights.find(i => i.type === 'quality');
      
      expect(popularityInsight).toBeDefined();
      expect(qualityInsight).toBeDefined();
      expect(popularityInsight?.impact).toBe('high');
      expect(qualityInsight?.impact).toBe('high');
    });

    test('should generate predictions', async () => {
      const analysis = await analyzer.analyzeModel(mockModelData);
      
      expect(analysis.predictions.length).toBeGreaterThan(0);
      
      const downloadPrediction = analysis.predictions.find(p => p.metric === 'downloads');
      const likePrediction = analysis.predictions.find(p => p.metric === 'likes');
      
      expect(downloadPrediction).toBeDefined();
      expect(likePrediction).toBeDefined();
      expect(downloadPrediction?.predictedValue).toBeGreaterThan(downloadPrediction?.currentValue);
      expect(likePrediction?.predictedValue).toBeGreaterThan(likePrediction?.currentValue);
    });

    test('should analyze competitors', async () => {
      const analysis = await analyzer.analyzeModel(mockModelData);
      
      expect(analysis.competitorAnalysis).toBeDefined();
      expect(analysis.competitorAnalysis.marketPosition).toBeDefined();
      expect(['leader', 'follower', 'niche', 'emerging']).toContain(analysis.competitorAnalysis.marketPosition);
      expect(Array.isArray(analysis.competitorAnalysis.competitiveAdvantages)).toBe(true);
      expect(Array.isArray(analysis.competitorAnalysis.improvementSuggestions)).toBe(true);
    });
  });

  describe('Integration Tests', () => {
    test('should complete full scraping workflow', async () => {
      // 1. Запускаємо скрапінг
      const jobId = await scraper.scrapePopularModels(['printables.com']);
      expect(jobId).toBeDefined();

      // 2. Чекаємо статус
      let jobStatus = scraper.getJobStatus(jobId);
      expect(jobStatus).toBeDefined();

      // 3. Симулюємо завершення (в реальному тесті чекали б завершення)
      // Для тесту просто перевіряємо, що завдання створено
      expect(jobStatus?.id).toBe(jobId);
      expect(['pending', 'running']).toContain(jobStatus?.status);

      // 4. Перевіряємо статистику MCP клієнта
      const stats = mcpClient.getOverallStats();
      expect(stats.totalRequests).toBeGreaterThanOrEqual(0);
    }, 10000);

    test('should handle scheduler with scraper integration', () => {
      // 1. Створюємо завдання планувальника
      const taskId = scheduler.createTask({
        name: 'Integration Test Task',
        description: 'Test scheduler integration',
        cronExpression: '0 */6 * * *',
        platforms: ['printables.com'],
        config: {
          maxModelsPerPlatform: 10,
          qualityThreshold: 0.8,
          enableAIAnalysis: true,
          notifyOnCompletion: false,
          retryFailedJobs: false,
          cleanupOldData: false,
          dataRetentionDays: 1
        }
      });

      // 2. Перевіряємо створення
      const task = scheduler.getTask(taskId);
      expect(task).toBeDefined();
      expect(task?.isActive).toBe(true);

      // 3. Перевіряємо статистику
      const stats = scheduler.getStats();
      expect(stats.totalTasks).toBeGreaterThan(0);
    });
  });
});
