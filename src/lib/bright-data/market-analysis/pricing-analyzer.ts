/**
 * Pricing analysis and recommendation engine
 */

import { 
  CompetitorModel, 
  CompetitorAnalysisResult, 
  PricingTrend, 
  MarketInsight,
  SellerRecommendation 
} from '@/types/competitor-models';

export interface PricingAnalysis {
  category: string;
  platform: string;
  averagePrice: number;
  medianPrice: number;
  priceRange: {
    min: number;
    max: number;
    q25: number;
    q75: number;
    q90: number;
    q95: number;
  };
  freeModelPercentage: number;
  premiumThreshold: number;
  competitivePrice: number;
  recommendedPriceRange: {
    min: number;
    max: number;
    optimal: number;
  };
  confidence: number;
}

export interface PricingStrategy {
  strategy: 'premium' | 'competitive' | 'budget' | 'freemium';
  basePrice: number;
  reasoning: string;
  expectedMarketPosition: string;
  riskLevel: 'low' | 'medium' | 'high';
  marketShare: number;
}

export class PricingAnalyzer {
  /**
   * Analyze pricing trends across platforms and categories
   */
  analyzePricingTrends(results: CompetitorAnalysisResult[]): PricingTrend[] {
    const trends: PricingTrend[] = [];

    results.forEach(result => {
      // Group models by category
      const categoryGroups = this.groupModelsByCategory(result);

      Object.entries(categoryGroups).forEach(([category, models]) => {
        if (models.length === 0) return;

        const paidModels = models.filter(m => !m.isFree && m.price > 0);
        const prices = paidModels.map(m => m.price).sort((a, b) => a - b);

        if (prices.length === 0) return;

        const trend: PricingTrend = {
          id: `${result.platform}_${category}_${Date.now()}`,
          platform: result.platform,
          category,
          averagePrice: this.calculateAverage(prices),
          medianPrice: this.calculateMedian(prices),
          minPrice: Math.min(...prices),
          maxPrice: Math.max(...prices),
          pricePercentiles: {
            p25: this.calculatePercentile(prices, 25),
            p50: this.calculatePercentile(prices, 50),
            p75: this.calculatePercentile(prices, 75),
            p90: this.calculatePercentile(prices, 90),
            p95: this.calculatePercentile(prices, 95)
          },
          freeModelPercentage: (models.filter(m => m.isFree).length / models.length) * 100,
          totalModels: models.length,
          currency: 'USD',
          periodStart: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          periodEnd: new Date().toISOString(),
          trend: 'stable', // Would need historical data to determine actual trend
          trendPercentage: 0
        };

        trends.push(trend);
      });
    });

    return trends;
  }

  /**
   * Generate detailed pricing analysis for a specific category
   */
  analyzeCategoryPricing(models: CompetitorModel[], category: string, platform: string): PricingAnalysis {
    const categoryModels = models.filter(m => m.category.toLowerCase() === category.toLowerCase());
    const paidModels = categoryModels.filter(m => !m.isFree && m.price > 0);
    const prices = paidModels.map(m => m.price).sort((a, b) => a - b);

    if (prices.length === 0) {
      return this.createEmptyPricingAnalysis(category, platform);
    }

    const averagePrice = this.calculateAverage(prices);
    const medianPrice = this.calculateMedian(prices);
    const freeModelPercentage = (categoryModels.filter(m => m.isFree).length / categoryModels.length) * 100;

    const priceRange = {
      min: Math.min(...prices),
      max: Math.max(...prices),
      q25: this.calculatePercentile(prices, 25),
      q75: this.calculatePercentile(prices, 75),
      q90: this.calculatePercentile(prices, 90),
      q95: this.calculatePercentile(prices, 95)
    };

    // Calculate competitive pricing recommendations
    const premiumThreshold = priceRange.q75;
    const competitivePrice = medianPrice;
    
    const recommendedPriceRange = {
      min: Math.max(priceRange.q25 * 0.8, 1), // At least $1
      max: priceRange.q90,
      optimal: this.calculateOptimalPrice(prices, averagePrice, medianPrice)
    };

    // Calculate confidence based on sample size and price distribution
    const confidence = this.calculatePricingConfidence(prices.length, priceRange);

    return {
      category,
      platform,
      averagePrice,
      medianPrice,
      priceRange,
      freeModelPercentage,
      premiumThreshold,
      competitivePrice,
      recommendedPriceRange,
      confidence
    };
  }

  /**
   * Generate pricing strategy recommendations
   */
  generatePricingStrategy(analysis: PricingAnalysis, modelQuality: 'high' | 'medium' | 'low' = 'medium'): PricingStrategy {
    const { averagePrice, medianPrice, priceRange, freeModelPercentage, recommendedPriceRange } = analysis;

    // Determine strategy based on market conditions and model quality
    let strategy: PricingStrategy['strategy'];
    let basePrice: number;
    let reasoning: string;
    let expectedMarketPosition: string;
    let riskLevel: PricingStrategy['riskLevel'];
    let marketShare: number;

    if (modelQuality === 'high' && averagePrice > 30) {
      // Premium strategy for high-quality models in expensive markets
      strategy = 'premium';
      basePrice = Math.min(priceRange.q90, recommendedPriceRange.max);
      reasoning = 'High-quality model in premium market segment';
      expectedMarketPosition = 'Top 10% of market';
      riskLevel = 'medium';
      marketShare = 5;
    } else if (freeModelPercentage > 70) {
      // Freemium strategy in markets dominated by free models
      strategy = 'freemium';
      basePrice = 0;
      reasoning = 'Market dominated by free models, use freemium to build audience';
      expectedMarketPosition = 'High visibility, monetize through premium versions';
      riskLevel = 'low';
      marketShare = 25;
    } else if (modelQuality === 'low' || averagePrice < 10) {
      // Budget strategy for lower quality or low-price markets
      strategy = 'budget';
      basePrice = Math.max(recommendedPriceRange.min, priceRange.q25 * 0.9);
      reasoning = 'Competitive pricing for budget-conscious market';
      expectedMarketPosition = 'High volume, lower margins';
      riskLevel = 'low';
      marketShare = 20;
    } else {
      // Competitive strategy for most scenarios
      strategy = 'competitive';
      basePrice = recommendedPriceRange.optimal;
      reasoning = 'Balanced pricing for competitive market position';
      expectedMarketPosition = 'Middle market segment with good visibility';
      riskLevel = 'low';
      marketShare = 15;
    }

    return {
      strategy,
      basePrice: Math.round(basePrice * 100) / 100, // Round to 2 decimal places
      reasoning,
      expectedMarketPosition,
      riskLevel,
      marketShare
    };
  }

  /**
   * Generate pricing insights for sellers
   */
  generatePricingInsights(analyses: PricingAnalysis[]): MarketInsight[] {
    const insights: MarketInsight[] = [];

    analyses.forEach(analysis => {
      // High competition insight
      if (analysis.freeModelPercentage > 60) {
        insights.push({
          id: `high-free-${analysis.platform}-${analysis.category}`,
          type: 'pricing',
          title: `High Free Model Competition in ${analysis.category}`,
          description: `${analysis.freeModelPercentage.toFixed(1)}% of models are free in this category`,
          platform: analysis.platform,
          category: analysis.category,
          confidence: 0.9,
          impact: 'high',
          actionable: true,
          recommendation: 'Consider freemium strategy or focus on premium features',
          supportingData: {
            freePercentage: analysis.freeModelPercentage,
            averagePrice: analysis.averagePrice
          },
          validUntil: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date().toISOString()
        });
      }

      // Premium opportunity insight
      if (analysis.averagePrice > 50 && analysis.confidence > 0.7) {
        insights.push({
          id: `premium-opportunity-${analysis.platform}-${analysis.category}`,
          type: 'pricing',
          title: `Premium Pricing Opportunity in ${analysis.category}`,
          description: `Market supports premium pricing with average of $${analysis.averagePrice.toFixed(2)}`,
          platform: analysis.platform,
          category: analysis.category,
          confidence: analysis.confidence,
          impact: 'high',
          actionable: true,
          recommendation: `Price models between $${analysis.recommendedPriceRange.min} - $${analysis.recommendedPriceRange.max}`,
          supportingData: {
            averagePrice: analysis.averagePrice,
            recommendedRange: analysis.recommendedPriceRange
          },
          validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date().toISOString()
        });
      }

      // Price gap opportunity
      const priceGap = analysis.priceRange.q75 - analysis.priceRange.q25;
      if (priceGap > 20) {
        insights.push({
          id: `price-gap-${analysis.platform}-${analysis.category}`,
          type: 'opportunity',
          title: `Price Gap Opportunity in ${analysis.category}`,
          description: `Large price gap ($${priceGap.toFixed(2)}) suggests market segmentation opportunity`,
          platform: analysis.platform,
          category: analysis.category,
          confidence: 0.8,
          impact: 'medium',
          actionable: true,
          recommendation: 'Create models targeting the mid-price segment',
          supportingData: {
            priceGap,
            q25: analysis.priceRange.q25,
            q75: analysis.priceRange.q75
          },
          validUntil: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date().toISOString()
        });
      }
    });

    return insights;
  }

  /**
   * Generate seller-specific pricing recommendations
   */
  generateSellerRecommendations(analyses: PricingAnalysis[], sellerId: string): SellerRecommendation[] {
    const recommendations: SellerRecommendation[] = [];

    analyses.forEach(analysis => {
      const strategy = this.generatePricingStrategy(analysis);

      recommendations.push({
        id: `pricing-${sellerId}-${analysis.platform}-${analysis.category}`,
        sellerId,
        type: 'pricing',
        title: `${strategy.strategy.charAt(0).toUpperCase() + strategy.strategy.slice(1)} Pricing for ${analysis.category}`,
        description: strategy.reasoning,
        recommendedValue: `$${strategy.basePrice.toFixed(2)}`,
        expectedImpact: `${strategy.marketShare}% market share potential`,
        confidence: analysis.confidence,
        priority: strategy.riskLevel === 'low' ? 'high' : 'medium',
        basedOn: [`${analysis.platform} market analysis`, 'Competitive pricing data'],
        validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        implemented: false,
        createdAt: new Date().toISOString()
      });
    });

    return recommendations;
  }

  // Helper methods
  private groupModelsByCategory(result: CompetitorAnalysisResult): Record<string, CompetitorModel[]> {
    const groups: Record<string, CompetitorModel[]> = {};
    
    // Since we don't have direct access to models in CompetitorAnalysisResult,
    // we'll work with the category analysis data
    result.topCategories.forEach(categoryAnalysis => {
      groups[categoryAnalysis.category] = []; // Placeholder - would need actual models
    });

    return groups;
  }

  private calculateAverage(numbers: number[]): number {
    return numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  }

  private calculateMedian(numbers: number[]): number {
    const sorted = [...numbers].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 
      ? (sorted[mid - 1] + sorted[mid]) / 2 
      : sorted[mid];
  }

  private calculatePercentile(numbers: number[], percentile: number): number {
    const sorted = [...numbers].sort((a, b) => a - b);
    const index = (percentile / 100) * (sorted.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);
    
    if (lower === upper) {
      return sorted[lower];
    }
    
    const weight = index - lower;
    return sorted[lower] * (1 - weight) + sorted[upper] * weight;
  }

  private calculateOptimalPrice(prices: number[], average: number, median: number): number {
    // Optimal price is typically between median and 75th percentile
    const q75 = this.calculatePercentile(prices, 75);
    return (median + q75) / 2;
  }

  private calculatePricingConfidence(sampleSize: number, priceRange: any): number {
    // Confidence based on sample size and price consistency
    let confidence = Math.min(sampleSize / 50, 1); // Max confidence at 50+ samples
    
    // Reduce confidence if price range is too wide (indicates inconsistent market)
    const priceSpread = (priceRange.max - priceRange.min) / priceRange.min;
    if (priceSpread > 5) { // If max is 5x min price
      confidence *= 0.7;
    }
    
    return Math.max(confidence, 0.1); // Minimum 10% confidence
  }

  private createEmptyPricingAnalysis(category: string, platform: string): PricingAnalysis {
    return {
      category,
      platform,
      averagePrice: 0,
      medianPrice: 0,
      priceRange: {
        min: 0,
        max: 0,
        q25: 0,
        q75: 0,
        q90: 0,
        q95: 0
      },
      freeModelPercentage: 100,
      premiumThreshold: 0,
      competitivePrice: 0,
      recommendedPriceRange: {
        min: 1,
        max: 10,
        optimal: 5
      },
      confidence: 0.1
    };
  }
}
