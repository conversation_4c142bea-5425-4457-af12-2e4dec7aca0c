/**
 * Scheduled scraping system for competitor analysis
 */

import * as cron from 'node-cron';
import { CompetitorScraperManager } from '../competitor-scrapers/competitor-scraper-manager';
import { 
  CompetitorAnalysisResult, 
  ScrapingJob, 
  MarketInsight,
  SellerRecommendation 
} from '@/types/competitor-models';

export interface ScheduledTask {
  id: string;
  name: string;
  description: string;
  cronExpression: string;
  enabled: boolean;
  lastRun?: string;
  nextRun?: string;
  runCount: number;
  successCount: number;
  failureCount: number;
  averageRunTime: number;
  task: () => Promise<void>;
}

export class CompetitorScheduler {
  private scraperManager: CompetitorScraperManager;
  private tasks: Map<string, ScheduledTask>;
  private cronJobs: Map<string, cron.ScheduledTask>;
  private isRunning: boolean = false;

  constructor() {
    this.scraperManager = new CompetitorScraperManager({
      maxModelsPerPlatform: 100,
      rateLimitDelay: 2000,
      enableCaching: true
    });
    
    this.tasks = new Map();
    this.cronJobs = new Map();
    
    this.initializeDefaultTasks();
  }

  /**
   * Initialize default scheduled tasks
   */
  private initializeDefaultTasks(): void {
    // Daily trending models scraper
    this.addTask({
      id: 'daily-trending',
      name: 'Daily Trending Models',
      description: 'Scrape trending models from all platforms daily',
      cronExpression: '0 2 * * *', // 2 AM UTC daily
      enabled: true,
      runCount: 0,
      successCount: 0,
      failureCount: 0,
      averageRunTime: 0,
      task: this.scrapeTrendingModels.bind(this)
    });

    // Weekly deep analysis
    this.addTask({
      id: 'weekly-analysis',
      name: 'Weekly Market Analysis',
      description: 'Comprehensive market analysis and insights generation',
      cronExpression: '0 1 * * 0', // 1 AM UTC on Sundays
      enabled: true,
      runCount: 0,
      successCount: 0,
      failureCount: 0,
      averageRunTime: 0,
      task: this.performWeeklyAnalysis.bind(this)
    });

    // Price monitoring (every 4 hours)
    this.addTask({
      id: 'price-monitoring',
      name: 'Price Monitoring',
      description: 'Monitor price changes and market shifts',
      cronExpression: '0 */4 * * *', // Every 4 hours
      enabled: true,
      runCount: 0,
      successCount: 0,
      failureCount: 0,
      averageRunTime: 0,
      task: this.monitorPrices.bind(this)
    });

    // Category analysis (twice weekly)
    this.addTask({
      id: 'category-analysis',
      name: 'Category Analysis',
      description: 'Analyze popular categories and emerging trends',
      cronExpression: '0 3 * * 1,4', // 3 AM UTC on Mondays and Thursdays
      enabled: true,
      runCount: 0,
      successCount: 0,
      failureCount: 0,
      averageRunTime: 0,
      task: this.analyzeCategoryTrends.bind(this)
    });

    console.log(`📅 Initialized ${this.tasks.size} default scheduled tasks`);
  }

  /**
   * Start the scheduler
   */
  start(): void {
    if (this.isRunning) {
      console.log('⚠️ Scheduler is already running');
      return;
    }

    console.log('🚀 Starting competitor analysis scheduler...');

    this.tasks.forEach((task, taskId) => {
      if (task.enabled) {
        const cronJob = cron.schedule(task.cronExpression, async () => {
          await this.executeTask(taskId);
        }, {
          scheduled: false,
          timezone: 'UTC'
        });

        this.cronJobs.set(taskId, cronJob);
        cronJob.start();

        // Calculate next run time
        task.nextRun = this.getNextRunTime(task.cronExpression);
        
        console.log(`✅ Scheduled task: ${task.name} (${task.cronExpression})`);
        console.log(`   Next run: ${task.nextRun}`);
      }
    });

    this.isRunning = true;
    console.log(`🎯 Scheduler started with ${this.cronJobs.size} active tasks`);
  }

  /**
   * Stop the scheduler
   */
  stop(): void {
    if (!this.isRunning) {
      console.log('⚠️ Scheduler is not running');
      return;
    }

    console.log('🛑 Stopping competitor analysis scheduler...');

    this.cronJobs.forEach((cronJob, taskId) => {
      cronJob.stop();
      cronJob.destroy();
    });

    this.cronJobs.clear();
    this.isRunning = false;

    console.log('✅ Scheduler stopped');
  }

  /**
   * Add a new scheduled task
   */
  addTask(task: Omit<ScheduledTask, 'task'> & { task: () => Promise<void> }): void {
    this.tasks.set(task.id, task);
    
    if (this.isRunning && task.enabled) {
      const cronJob = cron.schedule(task.cronExpression, async () => {
        await this.executeTask(task.id);
      }, {
        scheduled: true,
        timezone: 'UTC'
      });

      this.cronJobs.set(task.id, cronJob);
      task.nextRun = this.getNextRunTime(task.cronExpression);
    }

    console.log(`📝 Added scheduled task: ${task.name}`);
  }

  /**
   * Remove a scheduled task
   */
  removeTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    if (!task) return false;

    // Stop and remove cron job if running
    const cronJob = this.cronJobs.get(taskId);
    if (cronJob) {
      cronJob.stop();
      cronJob.destroy();
      this.cronJobs.delete(taskId);
    }

    this.tasks.delete(taskId);
    console.log(`🗑️ Removed scheduled task: ${task.name}`);
    return true;
  }

  /**
   * Execute a specific task
   */
  private async executeTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      console.error(`❌ Task not found: ${taskId}`);
      return;
    }

    const startTime = Date.now();
    console.log(`🔄 Executing task: ${task.name}`);

    try {
      await task.task();
      
      const runTime = Date.now() - startTime;
      task.successCount++;
      task.runCount++;
      task.lastRun = new Date().toISOString();
      task.nextRun = this.getNextRunTime(task.cronExpression);
      
      // Update average run time
      task.averageRunTime = ((task.averageRunTime * (task.runCount - 1)) + runTime) / task.runCount;
      
      console.log(`✅ Task completed: ${task.name} (${runTime}ms)`);
      
    } catch (error) {
      task.failureCount++;
      task.runCount++;
      task.lastRun = new Date().toISOString();
      
      console.error(`❌ Task failed: ${task.name}`, error);
    }
  }

  /**
   * Scrape trending models from all platforms
   */
  private async scrapeTrendingModels(): Promise<void> {
    console.log('🔥 Starting daily trending models scrape...');
    
    try {
      const results = await this.scraperManager.scrapeTrendingModels();
      
      // Process and store results
      await this.processScrapingResults(results, 'trending');
      
      console.log(`✅ Daily trending scrape completed: ${results.length} platforms processed`);
      
    } catch (error) {
      console.error('❌ Daily trending scrape failed:', error);
      throw error;
    }
  }

  /**
   * Perform comprehensive weekly analysis
   */
  private async performWeeklyAnalysis(): Promise<void> {
    console.log('📊 Starting weekly market analysis...');
    
    try {
      // Scrape from multiple categories
      const categories = ['characters', 'vehicles', 'architecture', 'furniture', 'electronics'];
      const allResults: CompetitorAnalysisResult[] = [];
      
      for (const category of categories) {
        const categoryResults = await this.scraperManager.scrapeCategoryModels(category);
        allResults.push(...categoryResults);
        
        // Rate limiting between categories
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
      
      // Generate market insights
      const insights = await this.generateMarketInsights(allResults);
      
      // Generate seller recommendations
      const recommendations = await this.generateSellerRecommendations(allResults);
      
      console.log(`✅ Weekly analysis completed: ${insights.length} insights, ${recommendations.length} recommendations`);
      
    } catch (error) {
      console.error('❌ Weekly analysis failed:', error);
      throw error;
    }
  }

  /**
   * Monitor price changes
   */
  private async monitorPrices(): Promise<void> {
    console.log('💰 Starting price monitoring...');
    
    try {
      // This would typically compare current prices with historical data
      // For now, we'll do a basic trending scrape to get current pricing
      const results = await this.scraperManager.scrapeTrendingModels();
      
      // Analyze price trends (simplified)
      results.forEach(result => {
        console.log(`💲 ${result.platform}: Average price $${result.averagePrice.toFixed(2)}`);
      });
      
      console.log('✅ Price monitoring completed');
      
    } catch (error) {
      console.error('❌ Price monitoring failed:', error);
      throw error;
    }
  }

  /**
   * Analyze category trends
   */
  private async analyzeCategoryTrends(): Promise<void> {
    console.log('📂 Starting category trend analysis...');
    
    try {
      const trendingCategories = ['characters', 'vehicles', 'architecture'];
      
      for (const category of trendingCategories) {
        const results = await this.scraperManager.scrapeCategoryModels(category);
        
        results.forEach(result => {
          console.log(`📈 ${result.platform} - ${category}: ${result.scrapedModels} models`);
        });
        
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
      
      console.log('✅ Category trend analysis completed');
      
    } catch (error) {
      console.error('❌ Category trend analysis failed:', error);
      throw error;
    }
  }

  /**
   * Process scraping results and store insights
   */
  private async processScrapingResults(results: CompetitorAnalysisResult[], type: string): Promise<void> {
    // In a real implementation, this would store results in a database
    console.log(`📝 Processing ${results.length} scraping results (${type})`);
    
    results.forEach(result => {
      console.log(`  - ${result.platform}: ${result.scrapedModels} models, ${result.topCategories.length} categories`);
    });
  }

  /**
   * Generate market insights from analysis results
   */
  private async generateMarketInsights(results: CompetitorAnalysisResult[]): Promise<MarketInsight[]> {
    const insights: MarketInsight[] = [];
    
    // Example insight generation (simplified)
    results.forEach(result => {
      if (result.averagePrice > 50) {
        insights.push({
          id: `high-price-${result.platform}`,
          type: 'pricing',
          title: `High Average Pricing on ${result.platform}`,
          description: `Average model price of $${result.averagePrice.toFixed(2)} indicates premium market positioning`,
          platform: result.platform,
          confidence: 0.8,
          impact: 'medium',
          actionable: true,
          recommendation: 'Consider premium pricing strategy for high-quality models',
          supportingData: { averagePrice: result.averagePrice },
          validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          createdAt: new Date().toISOString()
        });
      }
    });
    
    return insights;
  }

  /**
   * Generate seller recommendations
   */
  private async generateSellerRecommendations(results: CompetitorAnalysisResult[]): Promise<SellerRecommendation[]> {
    const recommendations: SellerRecommendation[] = [];
    
    // Example recommendation generation (simplified)
    results.forEach(result => {
      const topCategory = result.topCategories[0];
      if (topCategory) {
        recommendations.push({
          id: `category-${result.platform}-${topCategory.category}`,
          sellerId: 'general', // Would be specific seller IDs in real implementation
          type: 'category',
          title: `Focus on ${topCategory.category} Category`,
          description: `${topCategory.category} shows high demand on ${result.platform}`,
          recommendedValue: topCategory.category,
          expectedImpact: `Potential ${topCategory.popularityScore * 100}% increase in visibility`,
          confidence: 0.7,
          priority: 'medium',
          basedOn: [`${result.platform} market analysis`],
          validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          implemented: false,
          createdAt: new Date().toISOString()
        });
      }
    });
    
    return recommendations;
  }

  /**
   * Get next run time for a cron expression
   */
  private getNextRunTime(cronExpression: string): string {
    try {
      // This is a simplified implementation
      // In a real scenario, you'd use a proper cron parser
      const now = new Date();
      const nextRun = new Date(now.getTime() + 24 * 60 * 60 * 1000); // Default to 24 hours
      return nextRun.toISOString();
    } catch (error) {
      return new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();
    }
  }

  /**
   * Get scheduler status
   */
  getStatus(): {
    isRunning: boolean;
    activeTasks: number;
    totalTasks: number;
    tasks: ScheduledTask[];
  } {
    return {
      isRunning: this.isRunning,
      activeTasks: this.cronJobs.size,
      totalTasks: this.tasks.size,
      tasks: Array.from(this.tasks.values())
    };
  }

  /**
   * Get task by ID
   */
  getTask(taskId: string): ScheduledTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * Update task configuration
   */
  updateTask(taskId: string, updates: Partial<ScheduledTask>): boolean {
    const task = this.tasks.get(taskId);
    if (!task) return false;

    // Update task properties
    Object.assign(task, updates);

    // If cron expression changed and scheduler is running, restart the job
    if (updates.cronExpression && this.isRunning) {
      const cronJob = this.cronJobs.get(taskId);
      if (cronJob) {
        cronJob.stop();
        cronJob.destroy();
      }

      if (task.enabled) {
        const newCronJob = cron.schedule(task.cronExpression, async () => {
          await this.executeTask(taskId);
        }, {
          scheduled: true,
          timezone: 'UTC'
        });

        this.cronJobs.set(taskId, newCronJob);
        task.nextRun = this.getNextRunTime(task.cronExpression);
      }
    }

    return true;
  }
}
