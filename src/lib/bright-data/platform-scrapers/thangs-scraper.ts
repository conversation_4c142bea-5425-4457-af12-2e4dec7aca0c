/**
 * Thangs Platform Scraper
 * Скрапінг даних з thangs.com
 */

import { ModelSource, ScrapedModel } from '@/types/models';
import { PlatformScrapingOptions } from '../scraper';
import { BasePlatformScraper, ModelParsingRules, PlatformUrls } from './base-platform-scraper';

export class ThangsScraper extends BasePlatformScraper {
  constructor() {
    const urls: PlatformUrls = {
      base: 'https://thangs.com',
      search: 'https://thangs.com/search',
      category: 'https://thangs.com/explore',
      popular: 'https://thangs.com/trending',
      model: 'https://thangs.com/designer',
    };

    const parsingRules: ModelParsingRules = {
      title: ['h1.model-title', '.model-name', 'h1'],
      description: ['.model-description', '.description', '.model-details'],
      thumbnail: ['.model-image img', '.thumbnail img', '.preview-image'],
      price: ['.price', '.model-price', '.cost'],
      designer: ['.designer-name', '.author', '.creator'],
      downloads: ['.download-count', '.downloads', '.stats-downloads'],
      likes: ['.like-count', '.likes', '.stats-likes'],
      category: ['.category', '.model-category', '.breadcrumb'],
      tags: ['.tags .tag', '.model-tags', '.keywords'],
      files: ['.file-list .file', '.downloads .file', '.model-files'],
    };

    super('thangs' as ModelSource, urls, parsingRules);
  }

  protected async parseModelData(_html: string, url: string, _options: PlatformScrapingOptions): Promise<ScrapedModel | null> {
    try {
      console.log(`🔍 Скрапінг Thangs моделі: ${url}`);

      // Використовуємо Bright Data для отримання структурованих даних
      const markdownResult = await this.brightData.scrapeAsMarkdown(url);

      if (!markdownResult.success) {
        console.error(`❌ Помилка скрапінгу Thangs: ${markdownResult.error}`);
        return null;
      }

      const markdown = markdownResult.data;
      console.log(`📄 Отримано markdown даних: ${markdown.length} символів`);

      // Парсимо дані з markdown
      const modelData = this.parseMarkdownData(markdown, url);

      if (!modelData.title) {
        console.error(`❌ Не вдалося знайти назву моделі для ${url}`);
        return null;
      }

      console.log(`✅ Успішно спарсено модель: ${modelData.title} (${modelData.price > 0 ? '$' + modelData.price : 'безкоштовно'})`);

      return {
        title: modelData.title,
        description: modelData.description || '',
        thumbnail: modelData.thumbnail || '',
        images: modelData.images || [],
        category: modelData.category || 'Other',
        tags: modelData.tags || [],
        fileFormats: modelData.files?.map((f: any) => f.format) || [],
        totalSize: 0,
        designer: {
          id: this.extractModelIdFromUrl(url) || 'unknown',
          name: modelData.designer || 'Unknown',
          avatar: modelData.designerAvatar || '',
          profileUrl: modelData.designerProfile || '',
        },
        stats: {
          downloads: modelData.downloads || 0,
          likes: modelData.likes || 0,
          views: modelData.views || 0,
          comments: modelData.comments || 0,
        },
        files: modelData.files || [],
        isFree: modelData.price === 0,
        price: modelData.price || 0,
        currency: 'USD',
        platform: 'thangs',
        originalId: this.extractModelIdFromUrl(url),
        originalUrl: url,
        scrapedAt: new Date().toISOString(),
        license: {
          type: modelData.licenseType || 'Unknown',
          name: modelData.licenseType || 'Unknown',
          allowCommercialUse: modelData.commercialUse || false,
          requireAttribution: modelData.attribution || false,
          allowDerivatives: true,
          detected: true,
          confidence: 0.8,
        },
      };
    } catch (error) {
      console.error(`❌ Помилка парсингу Thangs моделі:`, error);
      return null;
    }
  }

  protected async parseSearchResults(html: string, maxResults: number): Promise<string[]> {
    try {
      // Шукаємо посилання на моделі в HTML
      const modelUrlPattern = /href="(\/designer\/[^"]+\/[^"]+)"/g;
      const urls: string[] = [];
      let match: RegExpExecArray | null;

      while ((match = modelUrlPattern.exec(html)) !== null && urls.length < maxResults) {
        const relativeUrl = match[1];
        const fullUrl = `${this.urls.base}${relativeUrl}`;
        
        if (!urls.includes(fullUrl)) {
          urls.push(fullUrl);
        }
      }

      return urls;
    } catch (error) {
      console.error('❌ Помилка парсингу результатів пошуку Thangs:', error);
      return [];
    }
  }

  protected async parseCategoryResults(html: string, maxResults: number): Promise<string[]> {
    return this.parseSearchResults(html, maxResults);
  }

  protected async parsePopularResults(html: string, maxResults: number): Promise<string[]> {
    return this.parseSearchResults(html, maxResults);
  }

  protected buildSearchUrl(query: string): string {
    const encodedQuery = encodeURIComponent(query);
    return `${this.urls.search}?q=${encodedQuery}`;
  }

  protected buildCategoryUrl(category: string): string {
    const encodedCategory = encodeURIComponent(category.toLowerCase());
    return `${this.urls.category}?category=${encodedCategory}`;
  }

  /**
   * Парсинг даних з markdown для Thangs
   */
  private parseMarkdownData(markdown: string, _url: string): any {
    const data: any = {};

    try {
      // Витягуємо назву
      const titleMatch = markdown.match(/^#\s+(.+)$/m);
      if (titleMatch) {
        data.title = titleMatch[1].trim();
      }

      // Витягуємо опис
      const descriptionMatch = markdown.match(/^#\s+.+\n\n(.+?)(?:\n\n|\n#|$)/s);
      if (descriptionMatch) {
        data.description = descriptionMatch[1].trim();
      }

      // Витягуємо ціну (Thangs може мати як безкоштовні, так і платні моделі)
      const priceMatch = markdown.match(/(?:price|cost|ціна)[\s:]*\$?(\d+(?:\.\d{2})?)/i);
      if (priceMatch) {
        data.price = parseFloat(priceMatch[1]);
      } else {
        // Перевіряємо на безкоштовність
        const freeMatch = markdown.match(/(?:free|безкоштовно|gratis)/i);
        data.price = freeMatch ? 0 : 0; // За замовчуванням безкоштовно
      }

      // Витягуємо автора
      const designerMatch = markdown.match(/(?:by|author|designer|created by|uploaded by)[\s:]*([^\n]+)/i);
      if (designerMatch) {
        data.designer = designerMatch[1].trim();
      }

      // Витягуємо статистику
      const downloadsMatch = markdown.match(/(?:downloads?)[\s:]*(\d+(?:[\.,]\d+)*[km]?)/i);
      if (downloadsMatch) {
        data.downloads = this.normalizeNumber(downloadsMatch[1]);
      }

      const likesMatch = markdown.match(/(?:likes?|hearts?|favorites?)[\s:]*(\d+(?:[\.,]\d+)*[km]?)/i);
      if (likesMatch) {
        data.likes = this.normalizeNumber(likesMatch[1]);
      }

      const viewsMatch = markdown.match(/(?:views?)[\s:]*(\d+(?:[\.,]\d+)*[km]?)/i);
      if (viewsMatch) {
        data.views = this.normalizeNumber(viewsMatch[1]);
      }

      // Витягуємо категорію
      const categoryMatch = markdown.match(/(?:category|категорія)[\s:]*([^\n]+)/i);
      if (categoryMatch) {
        data.category = categoryMatch[1].trim();
      }

      // Витягуємо теги
      const tagsMatch = markdown.match(/(?:tags?|keywords?)[\s:]*([^\n]+)/i);
      if (tagsMatch) {
        data.tags = tagsMatch[1].split(/[,\s]+/).filter(tag => tag.length > 0);
      }

      // Витягуємо зображення
      const imageMatches = markdown.match(/!\[.*?\]\((https?:\/\/[^\)]+)\)/g);
      if (imageMatches) {
        data.images = imageMatches.map(match => {
          const urlMatch = match.match(/\((https?:\/\/[^\)]+)\)/);
          return urlMatch ? urlMatch[1] : '';
        }).filter(url => url);
        
        if (data.images.length > 0) {
          data.thumbnail = data.images[0];
        }
      }

      // Витягуємо інформацію про файли
      const fileMatches = markdown.match(/(?:files?|downloads?)[\s\S]*?(?:\n\n|\n#|$)/i);
      if (fileMatches) {
        data.files = this.parseFileInfo(fileMatches[0]);
      }

      // Витягуємо метадані
      const difficultyMatch = markdown.match(/(?:difficulty|складність|skill level)[\s:]*([^\n]+)/i);
      if (difficultyMatch) {
        data.difficulty = difficultyMatch[1].trim();
      }

      const materialMatch = markdown.match(/(?:material|матеріал|filament)[\s:]*([^\n]+)/i);
      if (materialMatch) {
        data.material = materialMatch[1].trim();
      }

      const printTimeMatch = markdown.match(/(?:print time|час друку|printing time)[\s:]*([^\n]+)/i);
      if (printTimeMatch) {
        data.printTime = printTimeMatch[1].trim();
      }

      const supportsMatch = markdown.match(/(?:supports?)[\s:]*([^\n]+)/i);
      if (supportsMatch) {
        data.supports = supportsMatch[1].toLowerCase().includes('yes') || 
                        supportsMatch[1].toLowerCase().includes('required') ||
                        supportsMatch[1].toLowerCase().includes('needed');
      }

      // Витягуємо ліцензію
      const licenseMatch = markdown.match(/(?:license|ліцензія)[\s:]*([^\n]+)/i);
      if (licenseMatch) {
        data.licenseType = licenseMatch[1].trim();
        
        const licenseText = licenseMatch[1].toLowerCase();
        data.commercialUse = !licenseText.includes('non-commercial') && 
                            !licenseText.includes('nc');
        data.attribution = licenseText.includes('attribution') || 
                          licenseText.includes('by');
      }

    } catch (error) {
      console.error('❌ Помилка парсингу markdown даних Thangs:', error);
    }

    return data;
  }

  /**
   * Парсинг інформації про файли
   */
  private parseFileInfo(fileSection: string): any[] {
    const files: any[] = [];
    
    try {
      // Шукаємо посилання на файли
      const fileMatches = fileSection.match(/\[([^\]]+)\]\(([^\)]+)\)/g);
      
      if (fileMatches) {
        fileMatches.forEach(match => {
          const linkMatch = match.match(/\[([^\]]+)\]\(([^\)]+)\)/);
          if (linkMatch) {
            const fileName = linkMatch[1];
            const fileUrl = linkMatch[2];
            
            files.push({
              name: fileName,
              url: fileUrl,
              format: this.extractFileFormat(fileName),
              size: 0,
            });
          }
        });
      }

      // Шукаємо згадки про формати файлів
      const formatMatches = fileSection.match(/\.(stl|obj|3mf|gcode|ply|amf|step|stp)\b/gi);
      if (formatMatches && files.length === 0) {
        const uniqueFormats = [...new Set(formatMatches.map(f => f.toLowerCase()))];
        uniqueFormats.forEach(format => {
          files.push({
            name: `model${format}`,
            url: '',
            format: format.replace('.', ''),
            size: 0,
          });
        });
      }
    } catch (error) {
      console.error('❌ Помилка парсингу файлів Thangs:', error);
    }

    return files;
  }

  /**
   * Витягування формату файлу з назви
   */
  private extractFileFormat(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    return extension || 'unknown';
  }

  /**
   * Витягування ID моделі з URL
   */
  private extractModelIdFromUrl(url: string): string {
    const match = url.match(/\/([^\/]+)$/);
    return match ? match[1] : '';
  }
}
