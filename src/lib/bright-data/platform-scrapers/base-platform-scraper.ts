/**
 * Базовий клас для платформо-специфічних скраперів
 */

import { BrightDataScraper, ScrapingResult, PlatformScrapingOptions } from '../scraper';
import { ScrapedModel, ModelSource } from '@/types/models';

export interface PlatformUrls {
  base: string;
  search: string;
  category: string;
  popular: string;
  model: string;
}

export interface ModelParsingRules {
  title: string[];
  description: string[];
  thumbnail: string[];
  price: string[];
  designer: string[];
  downloads: string[];
  likes: string[];
  category: string[];
  tags: string[];
  files: string[];
}

export abstract class BasePlatformScraper {
  protected brightData: BrightDataScraper;
  protected platform: ModelSource;
  protected urls: PlatformUrls;
  protected parsingRules: ModelParsingRules;

  constructor(platform: ModelSource, urls: PlatformUrls, parsingRules: ModelParsingRules) {
    this.brightData = new BrightDataScraper();
    this.platform = platform;
    this.urls = urls;
    this.parsingRules = parsingRules;
  }

  /**
   * Скрапінг окремої моделі за URL
   */
  async scrapeModel(url: string, options: PlatformScrapingOptions = {}): Promise<ScrapedModel | null> {
    try {
      console.log(`🔍 Скрапінг моделі з ${this.platform}: ${url}`);

      // Отримуємо HTML сторінки
      const result = await this.brightData.scrapeAsHtml(url);
      
      if (!result.success || !result.data) {
        console.error(`❌ Не вдалося отримати дані для ${url}`);
        return null;
      }

      // Парсимо дані моделі
      const modelData = await this.parseModelData(result.data, url, options);
      
      if (!modelData) {
        console.error(`❌ Не вдалося розпарсити дані для ${url}`);
        return null;
      }

      return modelData;
    } catch (error) {
      console.error(`❌ Помилка скрапінгу моделі ${url}:`, error);
      return null;
    }
  }

  /**
   * Пошук моделей за запитом
   */
  async searchModels(query: string, maxResults: number = 20, options: PlatformScrapingOptions = {}): Promise<ScrapedModel[]> {
    try {
      console.log(`🔎 Пошук моделей на ${this.platform}: "${query}"`);

      const searchUrl = this.buildSearchUrl(query);
      const result = await this.brightData.scrapeAsHtml(searchUrl);
      
      if (!result.success || !result.data) {
        console.error(`❌ Не вдалося отримати результати пошуку для "${query}"`);
        return [];
      }

      // Парсимо список моделей
      const modelUrls = await this.parseSearchResults(result.data, maxResults);
      
      if (modelUrls.length === 0) {
        console.log(`⚠️ Не знайдено моделей для запиту "${query}"`);
        return [];
      }

      // Скрапимо кожну модель
      const models: ScrapedModel[] = [];
      for (const modelUrl of modelUrls) {
        const model = await this.scrapeModel(modelUrl, options);
        if (model) {
          models.push(model);
        }
        
        // Затримка між запитами
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      console.log(`✅ Знайдено ${models.length} моделей для "${query}" на ${this.platform}`);
      return models;
    } catch (error) {
      console.error(`❌ Помилка пошуку на ${this.platform}:`, error);
      return [];
    }
  }

  /**
   * Скрапінг моделей за категорією
   */
  async scrapeByCategory(category: string, maxResults: number = 20, options: PlatformScrapingOptions = {}): Promise<ScrapedModel[]> {
    try {
      console.log(`📂 Скрапінг категорії "${category}" на ${this.platform}`);

      const categoryUrl = this.buildCategoryUrl(category);
      const result = await this.brightData.scrapeAsHtml(categoryUrl);
      
      if (!result.success || !result.data) {
        console.error(`❌ Не вдалося отримати дані категорії "${category}"`);
        return [];
      }

      // Парсимо список моделей
      const modelUrls = await this.parseCategoryResults(result.data, maxResults);
      
      if (modelUrls.length === 0) {
        console.log(`⚠️ Не знайдено моделей в категорії "${category}"`);
        return [];
      }

      // Скрапимо кожну модель
      const models: ScrapedModel[] = [];
      for (const modelUrl of modelUrls) {
        const model = await this.scrapeModel(modelUrl, options);
        if (model) {
          models.push(model);
        }
        
        // Затримка між запитами
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      console.log(`✅ Знайдено ${models.length} моделей в категорії "${category}" на ${this.platform}`);
      return models;
    } catch (error) {
      console.error(`❌ Помилка скрапінгу категорії на ${this.platform}:`, error);
      return [];
    }
  }

  /**
   * Скрапінг популярних моделей
   */
  async scrapePopularModels(maxResults: number = 20, options: PlatformScrapingOptions = {}): Promise<ScrapedModel[]> {
    try {
      console.log(`🔥 Скрапінг популярних моделей на ${this.platform}`);

      const result = await this.brightData.scrapeAsHtml(this.urls.popular);
      
      if (!result.success || !result.data) {
        console.error(`❌ Не вдалося отримати популярні моделі`);
        return [];
      }

      // Парсимо список моделей
      const modelUrls = await this.parsePopularResults(result.data, maxResults);
      
      if (modelUrls.length === 0) {
        console.log(`⚠️ Не знайдено популярних моделей`);
        return [];
      }

      // Скрапимо кожну модель
      const models: ScrapedModel[] = [];
      for (const modelUrl of modelUrls) {
        const model = await this.scrapeModel(modelUrl, options);
        if (model) {
          models.push(model);
        }
        
        // Затримка між запитами
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      console.log(`✅ Знайдено ${models.length} популярних моделей на ${this.platform}`);
      return models;
    } catch (error) {
      console.error(`❌ Помилка скрапінгу популярних моделей на ${this.platform}:`, error);
      return [];
    }
  }

  /**
   * Абстрактні методи для реалізації в дочірніх класах
   */
  protected abstract parseModelData(html: string, url: string, options: PlatformScrapingOptions): Promise<ScrapedModel | null>;
  protected abstract parseSearchResults(html: string, maxResults: number): Promise<string[]>;
  protected abstract parseCategoryResults(html: string, maxResults: number): Promise<string[]>;
  protected abstract parsePopularResults(html: string, maxResults: number): Promise<string[]>;
  protected abstract buildSearchUrl(query: string): string;
  protected abstract buildCategoryUrl(category: string): string;

  /**
   * Допоміжні методи
   */
  protected generateModelId(): string {
    return `${this.platform}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  protected normalizePrice(priceText: string): number {
    if (!priceText) return 0;
    
    // Видаляємо всі символи крім цифр та крапки
    const cleanPrice = priceText.replace(/[^\d.]/g, '');
    const price = parseFloat(cleanPrice);
    
    return isNaN(price) ? 0 : price;
  }

  protected normalizeNumber(numberText: string): number {
    if (!numberText) return 0;
    
    // Обробляємо скорочення типу "1.2k", "5.6M"
    const cleanText = numberText.toLowerCase().replace(/[^\d.km]/g, '');
    
    if (cleanText.includes('k')) {
      return parseFloat(cleanText.replace('k', '')) * 1000;
    }
    if (cleanText.includes('m')) {
      return parseFloat(cleanText.replace('m', '')) * 1000000;
    }
    
    const number = parseFloat(cleanText);
    return isNaN(number) ? 0 : number;
  }

  protected extractTextFromSelectors(html: string, selectors: string[]): string {
    // Тут би використовувався HTML parser для витягування тексту
    // Поки що повертаємо заглушку
    return '';
  }

  protected extractUrlsFromSelectors(html: string, selectors: string[]): string[] {
    // Тут би використовувався HTML parser для витягування URL
    // Поки що повертаємо заглушку
    return [];
  }
}
