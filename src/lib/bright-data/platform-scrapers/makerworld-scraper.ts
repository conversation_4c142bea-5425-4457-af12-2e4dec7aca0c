/**
 * MakerWorld Platform Scraper
 * Скрапінг даних з makerworld.com
 */

import { ModelSource, ScrapedModel } from '@/types/models';
import { PlatformScrapingOptions } from '../scraper';
import { BasePlatformScraper, ModelParsingRules, PlatformUrls } from './base-platform-scraper';

export class MakerWorldScraper extends BasePlatformScraper {
  constructor() {
    const urls: PlatformUrls = {
      base: 'https://makerworld.com',
      search: 'https://makerworld.com/en/search/models',
      category: 'https://makerworld.com/en/models',
      popular: 'https://makerworld.com/en/models?sort=trending',
      model: 'https://makerworld.com/en/models',
    };

    const parsingRules: ModelParsingRules = {
      title: ['h1.model-title', '.model-name', 'h1'],
      description: ['.model-description', '.description', '.model-info'],
      thumbnail: ['.model-image img', '.thumbnail img', '.preview-image'],
      price: ['.price', '.model-price', '.cost'],
      designer: ['.designer-name', '.author', '.creator'],
      downloads: ['.download-count', '.downloads', '.stats-downloads'],
      likes: ['.like-count', '.likes', '.stats-likes'],
      category: ['.category', '.model-category', '.breadcrumb'],
      tags: ['.tags .tag', '.model-tags', '.keywords'],
      files: ['.file-list .file', '.downloads .file', '.model-files'],
    };

    super('makerworld' as ModelSource, urls, parsingRules);
  }

  protected async parseModelData(_html: string, url: string, _options: PlatformScrapingOptions): Promise<ScrapedModel | null> {
    try {
      console.log(`🔍 Скрапінг MakerWorld моделі: ${url}`);

      // Використовуємо Bright Data для отримання структурованих даних
      const markdownResult = await this.brightData.scrapeAsMarkdown(url);

      if (!markdownResult.success) {
        console.error(`❌ Помилка скрапінгу MakerWorld: ${markdownResult.error}`);
        return null;
      }

      const markdown = markdownResult.data;
      console.log(`📄 Отримано markdown даних: ${markdown.length} символів`);

      // Парсимо дані з markdown
      const modelData = this.parseMarkdownData(markdown, url);

      if (!modelData.title) {
        console.error(`❌ Не вдалося знайти назву моделі для ${url}`);
        return null;
      }

      console.log(`✅ Успішно спарсено модель: ${modelData.title}`);

      return {
        title: modelData.title,
        description: modelData.description || '',
        thumbnail: modelData.thumbnail || '',
        images: modelData.images || [],
        category: modelData.category || 'Other',
        tags: modelData.tags || [],
        fileFormats: modelData.files?.map((f: any) => f.format) || [],
        totalSize: 0,
        designer: {
          id: this.extractModelIdFromUrl(url) || 'unknown',
          name: modelData.designer || 'Unknown',
          avatar: modelData.designerAvatar || '',
          profileUrl: modelData.designerProfile || '',
        },
        stats: {
          downloads: modelData.downloads || 0,
          likes: modelData.likes || 0,
          views: modelData.views || 0,
          comments: modelData.comments || 0,
        },
        files: modelData.files || [],
        isFree: modelData.price === 0,
        price: modelData.price || 0,
        currency: 'USD',
        platform: 'makerworld',
        originalId: this.extractModelIdFromUrl(url),
        originalUrl: url,
        scrapedAt: new Date().toISOString(),
        license: {
          type: modelData.licenseType || 'Unknown',
          name: modelData.licenseType || 'Unknown',
          allowCommercialUse: modelData.commercialUse || false,
          requireAttribution: modelData.attribution || false,
          allowDerivatives: true,
          detected: true,
          confidence: 0.8,
        },
      };
    } catch (error) {
      console.error(`❌ Помилка парсингу MakerWorld моделі:`, error);
      return null;
    }
  }

  protected async parseSearchResults(html: string, maxResults: number): Promise<string[]> {
    try {
      // Використовуємо регулярні вирази для пошуку посилань на моделі
      const modelUrlPattern = /href="(\/en\/models\/[^"]+)"/g;
      const urls: string[] = [];
      let match: RegExpExecArray | null;

      while ((match = modelUrlPattern.exec(html)) !== null && urls.length < maxResults) {
        const relativeUrl = match[1];
        const fullUrl = `${this.urls.base}${relativeUrl}`;
        
        if (!urls.includes(fullUrl)) {
          urls.push(fullUrl);
        }
      }

      return urls;
    } catch (error) {
      console.error('❌ Помилка парсингу результатів пошуку MakerWorld:', error);
      return [];
    }
  }

  protected async parseCategoryResults(html: string, maxResults: number): Promise<string[]> {
    // Використовуємо той же метод, що і для пошуку
    return this.parseSearchResults(html, maxResults);
  }

  protected async parsePopularResults(html: string, maxResults: number): Promise<string[]> {
    // Використовуємо той же метод, що і для пошуку
    return this.parseSearchResults(html, maxResults);
  }

  protected buildSearchUrl(query: string): string {
    const encodedQuery = encodeURIComponent(query);
    return `${this.urls.search}?q=${encodedQuery}`;
  }

  protected buildCategoryUrl(category: string): string {
    const encodedCategory = encodeURIComponent(category.toLowerCase());
    return `${this.urls.category}?category=${encodedCategory}`;
  }

  /**
   * Парсинг даних з markdown
   */
  private parseMarkdownData(markdown: string, _url: string): any {
    const data: any = {};

    try {
      // Витягуємо назву (зазвичай в заголовку H1)
      const titleMatch = markdown.match(/^#\s+(.+)$/m);
      if (titleMatch) {
        data.title = titleMatch[1].trim();
      }

      // Витягуємо опис (зазвичай після назви)
      const descriptionMatch = markdown.match(/^#\s+.+\n\n([\s\S]+?)(?=\n\n|\n#|$)/m);
      if (descriptionMatch) {
        data.description = descriptionMatch[1].trim();
      }

      // Витягуємо ціну
      const priceMatch = markdown.match(/(?:price|cost|ціна)[\s:]*\$?(\d+(?:\.\d{2})?)/i);
      if (priceMatch) {
        data.price = parseFloat(priceMatch[1]);
      } else {
        data.price = 0; // Безкоштовно, якщо ціна не знайдена
      }

      // Витягуємо автора/дизайнера
      const designerMatch = markdown.match(/(?:by|author|designer|created by)[\s:]*([^\n]+)/i);
      if (designerMatch) {
        data.designer = designerMatch[1].trim();
      }

      // Витягуємо статистику
      const downloadsMatch = markdown.match(/(?:downloads?)[\s:]*(\d+(?:[\.,]\d+)*[km]?)/i);
      if (downloadsMatch) {
        data.downloads = this.normalizeNumber(downloadsMatch[1]);
      }

      const likesMatch = markdown.match(/(?:likes?|hearts?)[\s:]*(\d+(?:[\.,]\d+)*[km]?)/i);
      if (likesMatch) {
        data.likes = this.normalizeNumber(likesMatch[1]);
      }

      const viewsMatch = markdown.match(/(?:views?)[\s:]*(\d+(?:[\.,]\d+)*[km]?)/i);
      if (viewsMatch) {
        data.views = this.normalizeNumber(viewsMatch[1]);
      }

      // Витягуємо категорію
      const categoryMatch = markdown.match(/(?:category|категорія)[\s:]*([^\n]+)/i);
      if (categoryMatch) {
        data.category = categoryMatch[1].trim();
      }

      // Витягуємо теги
      const tagsMatch = markdown.match(/(?:tags?|keywords?)[\s:]*([^\n]+)/i);
      if (tagsMatch) {
        data.tags = tagsMatch[1].split(/[,\s]+/).filter(tag => tag.length > 0);
      }

      // Витягуємо зображення
      const imageMatches = markdown.match(/!\[.*?\]\((https?:\/\/[^\)]+)\)/g);
      if (imageMatches) {
        data.images = imageMatches.map(match => {
          const urlMatch = match.match(/\((https?:\/\/[^\)]+)\)/);
          return urlMatch ? urlMatch[1] : '';
        }).filter(url => url);
        
        if (data.images.length > 0) {
          data.thumbnail = data.images[0];
        }
      }

      // Витягуємо інформацію про файли
      const fileMatches = markdown.match(/(?:files?|downloads?)[\s\S]*?(?:\n\n|\n#|$)/i);
      if (fileMatches) {
        data.files = this.parseFileInfo(fileMatches[0]);
      }

      // Витягуємо метадані
      const difficultyMatch = markdown.match(/(?:difficulty|складність)[\s:]*([^\n]+)/i);
      if (difficultyMatch) {
        data.difficulty = difficultyMatch[1].trim();
      }

      const materialMatch = markdown.match(/(?:material|матеріал)[\s:]*([^\n]+)/i);
      if (materialMatch) {
        data.material = materialMatch[1].trim();
      }

      const printTimeMatch = markdown.match(/(?:print time|час друку)[\s:]*([^\n]+)/i);
      if (printTimeMatch) {
        data.printTime = printTimeMatch[1].trim();
      }

    } catch (error) {
      console.error('❌ Помилка парсингу markdown даних:', error);
    }

    return data;
  }

  /**
   * Парсинг інформації про файли
   */
  private parseFileInfo(fileSection: string): any[] {
    const files: any[] = [];
    
    try {
      // Шукаємо посилання на файли
      const fileMatches = fileSection.match(/\[([^\]]+)\]\(([^\)]+)\)/g);
      
      if (fileMatches) {
        fileMatches.forEach(match => {
          const linkMatch = match.match(/\[([^\]]+)\]\(([^\)]+)\)/);
          if (linkMatch) {
            const fileName = linkMatch[1];
            const fileUrl = linkMatch[2];
            
            files.push({
              name: fileName,
              url: fileUrl,
              format: this.extractFileFormat(fileName),
              size: 0, // Розмір файлу не завжди доступний
            });
          }
        });
      }
    } catch (error) {
      console.error('❌ Помилка парсингу файлів:', error);
    }

    return files;
  }

  /**
   * Витягування формату файлу з назви
   */
  private extractFileFormat(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();
    return extension || 'unknown';
  }

  /**
   * Витягування ID моделі з URL
   */
  private extractModelIdFromUrl(url: string): string {
    const match = url.match(/\/models\/(\d+)/);
    return match ? match[1] : '';
  }
}

// Export for compatibility
export { MakerWorldScraper as MakerworldScraper };
