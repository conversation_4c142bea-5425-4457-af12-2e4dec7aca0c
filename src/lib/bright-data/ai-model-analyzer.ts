/**
 * AI аналізатор моделей для визначення популярності та якості
 * Використовує машинне навчання для оцінки потенціалу моделей
 */

import { PopularModelData } from './automated-scraper';

export interface ModelAnalysis {
  modelId: string;
  popularityScore: number;
  qualityScore: number;
  trendingScore: number;
  marketPotential: number;
  recommendationScore: number;
  insights: ModelInsight[];
  predictions: ModelPrediction[];
  competitorAnalysis: CompetitorAnalysis;
  analyzedAt: string;
}

export interface ModelInsight {
  type: 'popularity' | 'quality' | 'trend' | 'market' | 'technical';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
}

export interface ModelPrediction {
  metric: string;
  currentValue: number;
  predictedValue: number;
  timeframe: string;
  confidence: number;
}

export interface CompetitorAnalysis {
  similarModels: SimilarModel[];
  marketPosition: 'leader' | 'follower' | 'niche' | 'emerging';
  competitiveAdvantages: string[];
  improvementSuggestions: string[];
}

export interface SimilarModel {
  title: string;
  platform: string;
  url: string;
  similarityScore: number;
  performanceComparison: {
    downloads: number;
    likes: number;
    rating: number;
  };
}

export interface TrendAnalysis {
  category: string;
  trendDirection: 'rising' | 'stable' | 'declining';
  trendStrength: number;
  seasonality: SeasonalityData;
  keyDrivers: string[];
  futureOutlook: string;
}

export interface SeasonalityData {
  hasSeasonality: boolean;
  peakMonths: string[];
  lowMonths: string[];
  seasonalityStrength: number;
}

export class AIModelAnalyzer {
  private modelDatabase: Map<string, PopularModelData> = new Map();
  private analysisCache: Map<string, ModelAnalysis> = new Map();
  private trendData: Map<string, TrendAnalysis> = new Map();

  constructor() {
    this.initializeAnalyzer();
  }

  /**
   * Ініціалізація аналізатора
   */
  private initializeAnalyzer(): void {
    console.log('🤖 Ініціалізація AI аналізатора моделей');
    // Тут буде завантаження ML моделей та налаштування
  }

  /**
   * Аналіз моделі з використанням AI
   */
  async analyzeModel(modelData: PopularModelData): Promise<ModelAnalysis> {
    console.log(`🔍 AI аналіз моделі: ${modelData.title}`);

    try {
      // Перевіряємо кеш
      const cached = this.analysisCache.get(modelData.url);
      if (cached && this.isCacheValid(cached)) {
        return cached;
      }

      // Виконуємо повний аналіз
      const analysis: ModelAnalysis = {
        modelId: this.generateModelId(modelData),
        popularityScore: await this.calculatePopularityScore(modelData),
        qualityScore: await this.calculateQualityScore(modelData),
        trendingScore: await this.calculateTrendingScore(modelData),
        marketPotential: await this.calculateMarketPotential(modelData),
        recommendationScore: 0,
        insights: await this.generateInsights(modelData),
        predictions: await this.generatePredictions(modelData),
        competitorAnalysis: await this.analyzeCompetitors(modelData),
        analyzedAt: new Date().toISOString()
      };

      // Розраховуємо загальний рекомендаційний рейтинг
      analysis.recommendationScore = this.calculateRecommendationScore(analysis);

      // Зберігаємо в кеш
      this.analysisCache.set(modelData.url, analysis);

      return analysis;

    } catch (error) {
      console.error(`❌ Помилка AI аналізу моделі ${modelData.title}:`, error);
      throw error;
    }
  }

  /**
   * Розрахунок рейтингу популярності
   */
  private async calculatePopularityScore(model: PopularModelData): Promise<number> {
    // Нормалізуємо метрики
    const downloadWeight = 0.4;
    const likeWeight = 0.3;
    const viewWeight = 0.2;
    const ratingWeight = 0.1;

    // Нормалізуємо значення (0-1)
    const normalizedDownloads = Math.min(model.downloadCount / 10000, 1);
    const normalizedLikes = Math.min(model.likeCount / 1000, 1);
    const normalizedViews = Math.min(model.viewCount / 100000, 1);
    const normalizedRating = model.rating / 5;

    const score = (
      normalizedDownloads * downloadWeight +
      normalizedLikes * likeWeight +
      normalizedViews * viewWeight +
      normalizedRating * ratingWeight
    );

    return Math.round(score * 100) / 100;
  }

  /**
   * Розрахунок рейтингу якості
   */
  private async calculateQualityScore(model: PopularModelData): Promise<number> {
    let score = 0.5; // Базовий рейтинг

    // Фактори якості
    if (model.description && model.description.length > 100) score += 0.1;
    if (model.files && model.files.length > 1) score += 0.1;
    if (model.printSettings) score += 0.1;
    if (model.tags && model.tags.length >= 3) score += 0.1;
    if (model.rating >= 4.5) score += 0.1;
    if (model.imageUrl) score += 0.05;

    // Штрафи за низьку якість
    if (model.rating < 3) score -= 0.2;
    if (!model.description || model.description.length < 50) score -= 0.1;

    return Math.max(0, Math.min(1, score));
  }

  /**
   * Розрахунок трендового рейтингу
   */
  private async calculateTrendingScore(model: PopularModelData): Promise<number> {
    // Симуляція аналізу трендів
    const daysSinceUpload = this.getDaysSinceUpload(model.scrapedAt);
    
    // Нові моделі мають вищий трендовий потенціал
    let trendScore = Math.max(0, 1 - (daysSinceUpload / 365));
    
    // Бонус за високу активність
    const engagementRate = (model.likeCount + model.downloadCount) / Math.max(model.viewCount, 1);
    trendScore += engagementRate * 0.3;

    // Категорійні тренди
    const categoryTrend = await this.getCategoryTrend(model.category);
    trendScore *= categoryTrend;

    return Math.max(0, Math.min(1, trendScore));
  }

  /**
   * Розрахунок ринкового потенціалу
   */
  private async calculateMarketPotential(model: PopularModelData): Promise<number> {
    let potential = 0.5;

    // Аналіз категорії
    const categoryDemand = await this.getCategoryDemand(model.category);
    potential += categoryDemand * 0.3;

    // Аналіз конкуренції
    const competitionLevel = await this.getCompetitionLevel(model.category);
    potential += (1 - competitionLevel) * 0.2;

    // Унікальність
    const uniquenessScore = await this.calculateUniqueness(model);
    potential += uniquenessScore * 0.3;

    // Комерційний потенціал
    if (model.license.includes('Commercial')) potential += 0.2;

    return Math.max(0, Math.min(1, potential));
  }

  /**
   * Генерація інсайтів
   */
  private async generateInsights(model: PopularModelData): Promise<ModelInsight[]> {
    const insights: ModelInsight[] = [];

    // Інсайт популярності
    if (model.downloadCount > 5000) {
      insights.push({
        type: 'popularity',
        title: 'Висока популярність',
        description: `Модель має ${model.downloadCount} завантажень, що вказує на високий попит`,
        confidence: 0.9,
        impact: 'high'
      });
    }

    // Інсайт якості
    if (model.rating >= 4.5) {
      insights.push({
        type: 'quality',
        title: 'Висока якість',
        description: `Рейтинг ${model.rating}/5 свідчить про відмінну якість моделі`,
        confidence: 0.85,
        impact: 'high'
      });
    }

    // Трендовий інсайт
    const trendScore = await this.calculateTrendingScore(model);
    if (trendScore > 0.7) {
      insights.push({
        type: 'trend',
        title: 'Трендова модель',
        description: 'Модель показує ознаки зростаючої популярності',
        confidence: 0.75,
        impact: 'medium'
      });
    }

    // Ринковий інсайт
    const marketPotential = await this.calculateMarketPotential(model);
    if (marketPotential > 0.8) {
      insights.push({
        type: 'market',
        title: 'Високий ринковий потенціал',
        description: 'Модель має потенціал для комерційного успіху',
        confidence: 0.7,
        impact: 'high'
      });
    }

    return insights;
  }

  /**
   * Генерація прогнозів
   */
  private async generatePredictions(model: PopularModelData): Promise<ModelPrediction[]> {
    const predictions: ModelPrediction[] = [];

    // Прогноз завантажень
    const downloadGrowthRate = 0.1; // 10% зростання на місяць
    predictions.push({
      metric: 'downloads',
      currentValue: model.downloadCount,
      predictedValue: Math.round(model.downloadCount * (1 + downloadGrowthRate)),
      timeframe: '1 month',
      confidence: 0.7
    });

    // Прогноз лайків
    const likeGrowthRate = 0.15;
    predictions.push({
      metric: 'likes',
      currentValue: model.likeCount,
      predictedValue: Math.round(model.likeCount * (1 + likeGrowthRate)),
      timeframe: '1 month',
      confidence: 0.65
    });

    return predictions;
  }

  /**
   * Аналіз конкурентів
   */
  private async analyzeCompetitors(model: PopularModelData): Promise<CompetitorAnalysis> {
    // Симуляція аналізу конкурентів
    const similarModels: SimilarModel[] = [
      {
        title: 'Similar Model 1',
        platform: 'printables.com',
        url: 'https://example.com/model1',
        similarityScore: 0.85,
        performanceComparison: {
          downloads: model.downloadCount * 0.8,
          likes: model.likeCount * 0.9,
          rating: model.rating - 0.2
        }
      }
    ];

    return {
      similarModels,
      marketPosition: this.determineMarketPosition(model),
      competitiveAdvantages: this.identifyAdvantages(model),
      improvementSuggestions: this.generateImprovementSuggestions(model)
    };
  }

  /**
   * Розрахунок загального рекомендаційного рейтингу
   */
  private calculateRecommendationScore(analysis: ModelAnalysis): number {
    const weights = {
      popularity: 0.3,
      quality: 0.25,
      trending: 0.2,
      market: 0.25
    };

    return (
      analysis.popularityScore * weights.popularity +
      analysis.qualityScore * weights.quality +
      analysis.trendingScore * weights.trending +
      analysis.marketPotential * weights.market
    );
  }

  /**
   * Допоміжні методи
   */
  private generateModelId(model: PopularModelData): string {
    return `model_${model.platform}_${Date.now()}`;
  }

  private isCacheValid(analysis: ModelAnalysis): boolean {
    const cacheAge = Date.now() - new Date(analysis.analyzedAt).getTime();
    return cacheAge < 24 * 60 * 60 * 1000; // 24 години
  }

  private getDaysSinceUpload(dateString: string): number {
    const uploadDate = new Date(dateString);
    const now = new Date();
    return Math.floor((now.getTime() - uploadDate.getTime()) / (1000 * 60 * 60 * 24));
  }

  private async getCategoryTrend(category: string): Promise<number> {
    // Симуляція трендів категорій
    const trends: Record<string, number> = {
      'Miniatures': 1.2,
      'Tools': 1.1,
      'Home & Garden': 1.0,
      'Toys & Games': 1.3,
      'Art': 0.9,
      'Automotive': 1.1
    };
    return trends[category] || 1.0;
  }

  private async getCategoryDemand(category: string): Promise<number> {
    // Симуляція попиту на категорії
    return 0.5 + Math.random() * 0.5;
  }

  private async getCompetitionLevel(category: string): Promise<number> {
    // Симуляція рівня конкуренції
    return Math.random() * 0.8;
  }

  private async calculateUniqueness(model: PopularModelData): Promise<number> {
    // Симуляція розрахунку унікальності
    return 0.3 + Math.random() * 0.7;
  }

  private determineMarketPosition(model: PopularModelData): 'leader' | 'follower' | 'niche' | 'emerging' {
    if (model.downloadCount > 8000) return 'leader';
    if (model.downloadCount > 3000) return 'follower';
    if (model.downloadCount > 1000) return 'niche';
    return 'emerging';
  }

  private identifyAdvantages(model: PopularModelData): string[] {
    const advantages: string[] = [];
    
    if (model.rating >= 4.5) advantages.push('Високий рейтинг користувачів');
    if (model.files.length > 3) advantages.push('Множинні формати файлів');
    if (model.printSettings) advantages.push('Детальні налаштування друку');
    
    return advantages;
  }

  private generateImprovementSuggestions(model: PopularModelData): string[] {
    const suggestions: string[] = [];
    
    if (!model.imageUrl) suggestions.push('Додати зображення моделі');
    if (model.description.length < 100) suggestions.push('Розширити опис моделі');
    if (model.tags.length < 3) suggestions.push('Додати більше тегів');
    
    return suggestions;
  }

  /**
   * Публічні методи для отримання аналітики
   */
  async getModelAnalysis(modelUrl: string): Promise<ModelAnalysis | null> {
    return this.analysisCache.get(modelUrl) || null;
  }

  async getTrendAnalysis(category: string): Promise<TrendAnalysis | null> {
    return this.trendData.get(category) || null;
  }

  clearCache(): void {
    this.analysisCache.clear();
  }
}
