/**
 * Real Bright Data MCP Client
 * Реальна інтеграція з Bright Data MCP tools
 */

import {
    BrightDataMCPConfig,
    BrightDataOperation,
    MCPCallOptions,
    MCPResponse,
    ScrapingResult,
    ScrapingTarget,
    ServiceHealth
} from '../mcp/types';

export class BrightDataMCPClient {
  private config: BrightDataMCPConfig;
  private metrics: Map<string, any> = new Map();
  private lastHealthCheck: Date = new Date();
  private rateLimiter: Map<string, number> = new Map();

  constructor(config: BrightDataMCPConfig) {
    this.config = config;
  }

  /**
   * Ініціалізація клієнта
   */
  async initialize(): Promise<void> {
    console.log('🌐 Bright Data MCP Client ініціалізовано');
    
    // Перевіряємо доступність MCP tools
    await this.healthCheck();
  }

  /**
   * Виконання операції скрапінгу
   */
  async scrapingOperation(operation: BrightDataOperation, options?: MCPCallOptions): Promise<MCPResponse<ScrapingResult[]>> {
    const startTime = Date.now();
    
    try {
      console.log(`🕷️ Bright Data операція: ${operation.action} для ${operation.target.platform}`);

      // Перевіряємо rate limiting
      if (!this.checkRateLimit(operation.target.platform)) {
        throw new Error(`Rate limit досягнуто для ${operation.target.platform}`);
      }

      let result: ScrapingResult[] = [];

      switch (operation.action) {
        case 'scrape':
          result = await this.performScraping(operation.target, operation.options);
          break;

        case 'search':
          result = await this.performSearch(operation.target, operation.options);
          break;

        case 'download':
          result = await this.performDownload(operation.target, operation.options);
          break;

        default:
          throw new Error(`Непідтримувана операція: ${operation.action}`);
      }

      const executionTime = Date.now() - startTime;
      this.updateMetrics(operation.target.platform, operation.action, true, executionTime);

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
        executionTime
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.updateMetrics(operation.target.platform, operation.action, false, executionTime);
      
      console.error('❌ Помилка Bright Data операції:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Невідома помилка Bright Data',
        timestamp: new Date().toISOString(),
        executionTime
      };
    }
  }

  /**
   * Виконання скрапінгу через реальні MCP tools
   */
  private async performScraping(target: ScrapingTarget, options?: any): Promise<ScrapingResult[]> {
    try {
      // Спробуємо використати реальні MCP функції
      const mcpResult = await this.callRealMCPTool('scrape_as_markdown_Bright_Data', {
        url: target.url
      });

      if (mcpResult) {
        return this.parseScrapingResult(mcpResult, target);
      }

      // Fallback на HTTP API
      return await this.callBrightDataAPI(target, options);

    } catch (error) {
      console.error('❌ Помилка скрапінгу:', error);
      
      // Якщо реальний MCP не працює, використовуємо fallback
      if (this.config.enableFallback) {
        return await this.fallbackScraping(target, options);
      }
      
      throw error;
    }
  }

  /**
   * Виконання пошуку
   */
  private async performSearch(target: ScrapingTarget, options?: any): Promise<ScrapingResult[]> {
    try {
      // Формуємо пошуковий запит
      const searchQuery = target.parameters?.query || '';
      const searchUrl = this.buildSearchUrl(target.platform, searchQuery);

      const mcpResult = await this.callRealMCPTool('search_engine_Bright_Data', {
        query: searchQuery,
        engine: 'google'
      });

      if (mcpResult) {
        return this.parseSearchResults(mcpResult, target);
      }

      // Fallback
      return await this.fallbackSearch(target, options);

    } catch (error) {
      console.error('❌ Помилка пошуку:', error);
      throw error;
    }
  }

  /**
   * Виконання завантаження
   */
  private async performDownload(target: ScrapingTarget, options?: any): Promise<ScrapingResult[]> {
    try {
      // Для завантаження використовуємо спеціалізовані MCP tools
      let mcpResult;

      switch (target.platform) {
        case 'printables':
          mcpResult = await this.callRealMCPTool('scrape_as_html_Bright_Data', {
            url: target.url
          });
          break;

        case 'makerworld':
          mcpResult = await this.callRealMCPTool('scrape_as_markdown_Bright_Data', {
            url: target.url
          });
          break;

        default:
          mcpResult = await this.callRealMCPTool('scrape_as_html_Bright_Data', {
            url: target.url
          });
      }

      if (mcpResult) {
        return this.parseDownloadResult(mcpResult, target);
      }

      // Fallback
      return await this.fallbackDownload(target, options);

    } catch (error) {
      console.error('❌ Помилка завантаження:', error);
      throw error;
    }
  }

  /**
   * Виклик реального MCP tool
   */
  private async callRealMCPTool(toolName: string, params: Record<string, any>): Promise<any> {
    try {
      console.log(`🌐 Виклик реального MCP tool: ${toolName}`, params);

      // Використовуємо реальні MCP функції через HTTP API
      return await this.callMCPHTTPAPI(toolName, params);

    } catch (error) {
      console.error(`❌ Помилка виклику MCP tool ${toolName}:`, error);
      throw error;
    }
  }

  /**
   * HTTP API виклик до MCP сервера
   */
  private async callMCPHTTPAPI(toolName: string, params: Record<string, any>): Promise<any> {
    try {
      const endpoint = this.config.endpoint || '/api/mcp/bright-data';
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiToken}`,
          'User-Agent': '3D-Marketplace/1.0'
        },
        body: JSON.stringify({
          tool: toolName,
          params
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();

    } catch (error) {
      console.error(`❌ Помилка HTTP API виклику:`, error);
      throw error;
    }
  }

  /**
   * Виклик Bright Data API напряму
   */
  private async callBrightDataAPI(target: ScrapingTarget, options?: any): Promise<ScrapingResult[]> {
    try {
      console.log(`🌐 Прямий виклик Bright Data API для ${target.platform}`);

      const apiUrl = this.getBrightDataAPIUrl(target.platform);
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiToken}`,
          'X-Zone': this.config.zone || 'default'
        },
        body: JSON.stringify({
          url: target.url,
          type: target.type,
          parameters: target.parameters,
          options
        })
      });

      if (!response.ok) {
        throw new Error(`Bright Data API error: ${response.status}`);
      }

      const data = await response.json();
      return this.parseAPIResponse(data, target);

    } catch (error) {
      console.error('❌ Помилка Bright Data API:', error);
      throw error;
    }
  }

  /**
   * Fallback скрапінг (симуляція)
   */
  private async fallbackScraping(target: ScrapingTarget, options?: any): Promise<ScrapingResult[]> {
    console.log(`⚠️ Використовуємо fallback скрапінг для ${target.platform}`);
    
    // Повертаємо мок дані для тестування
    return [{
      id: `fallback-${Date.now()}`,
      platform: target.platform,
      url: target.url,
      title: `Fallback Model from ${target.platform}`,
      description: 'Це тестова модель з fallback режиму',
      author: 'Fallback Author',
      images: ['https://via.placeholder.com/400x300'],
      tags: ['fallback', 'test'],
      likes: 0,
      downloads: 0,
      metadata: {
        fallback: true,
        timestamp: new Date().toISOString()
      }
    }];
  }

  /**
   * Fallback пошук
   */
  private async fallbackSearch(target: ScrapingTarget, options?: any): Promise<ScrapingResult[]> {
    console.log(`⚠️ Використовуємо fallback пошук для ${target.platform}`);
    return await this.fallbackScraping(target, options);
  }

  /**
   * Fallback завантаження
   */
  private async fallbackDownload(target: ScrapingTarget, options?: any): Promise<ScrapingResult[]> {
    console.log(`⚠️ Використовуємо fallback завантаження для ${target.platform}`);
    return await this.fallbackScraping(target, options);
  }

  /**
   * Парсинг результатів скрапінгу
   */
  private parseScrapingResult(data: any, target: ScrapingTarget): ScrapingResult[] {
    // Тут буде логіка парсингу залежно від платформи
    // Поки що повертаємо базову структуру
    return [{
      id: `scraped-${Date.now()}`,
      platform: target.platform,
      url: target.url,
      title: data.title || 'Scraped Model',
      description: data.description || '',
      images: data.images || [],
      tags: data.tags || [],
      metadata: data
    }];
  }

  /**
   * Парсинг результатів пошуку
   */
  private parseSearchResults(data: any, target: ScrapingTarget): ScrapingResult[] {
    if (data.results && Array.isArray(data.results)) {
      return data.results.map((item: any, index: number) => ({
        id: `search-${Date.now()}-${index}`,
        platform: target.platform,
        url: item.url || '',
        title: item.title || '',
        description: item.description || '',
        images: [],
        tags: [],
        metadata: item
      }));
    }
    return [];
  }

  /**
   * Парсинг результатів завантаження
   */
  private parseDownloadResult(data: any, target: ScrapingTarget): ScrapingResult[] {
    return [{
      id: `download-${Date.now()}`,
      platform: target.platform,
      url: target.url,
      title: data.title || 'Downloaded Model',
      downloadUrl: data.downloadUrl || '',
      fileType: data.fileType || 'stl',
      images: data.images || [],
      tags: data.tags || [],
      metadata: data
    }];
  }

  /**
   * Парсинг API відповіді
   */
  private parseAPIResponse(data: any, target: ScrapingTarget): ScrapingResult[] {
    // Логіка парсингу API відповіді
    return data.results || [];
  }

  /**
   * Отримання URL для Bright Data API
   */
  private getBrightDataAPIUrl(platform: string): string {
    const baseUrl = 'https://api.brightdata.com/v1';
    return `${baseUrl}/scrape/${platform}`;
  }

  /**
   * Побудова URL для пошуку
   */
  private buildSearchUrl(platform: string, query: string): string {
    const searchUrls = {
      printables: `https://www.printables.com/search/models?q=${encodeURIComponent(query)}`,
      makerworld: `https://makerworld.com/search?keyword=${encodeURIComponent(query)}`,
      thangs: `https://thangs.com/search?q=${encodeURIComponent(query)}`,
      thingiverse: `https://www.thingiverse.com/search?q=${encodeURIComponent(query)}`
    };
    
    return searchUrls[platform as keyof typeof searchUrls] || '';
  }

  /**
   * Перевірка rate limiting
   */
  private checkRateLimit(platform: string): boolean {
    const now = Date.now();
    const lastCall = this.rateLimiter.get(platform) || 0;
    const minInterval = this.config.timeout || 2000; // 2 секунди між викликами

    if (now - lastCall < minInterval) {
      return false;
    }

    this.rateLimiter.set(platform, now);
    return true;
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<ServiceHealth[]> {
    const services: ServiceHealth[] = [];
    const checkTime = new Date();

    // Перевірка доступності MCP tools
    try {
      const startTime = Date.now();
      
      // Спробуємо простий виклик
      await this.callRealMCPTool('session_stats_Bright_Data', {});
      
      services.push({
        service: 'bright-data-mcp',
        status: 'healthy',
        responseTime: Date.now() - startTime,
        lastCheck: checkTime.toISOString()
      });
    } catch (error) {
      services.push({
        service: 'bright-data-mcp',
        status: 'degraded',
        responseTime: 0,
        lastCheck: checkTime.toISOString(),
        error: error instanceof Error ? error.message : 'Невідома помилка'
      });
    }

    this.lastHealthCheck = checkTime;
    return services;
  }

  /**
   * Отримання метрик
   */
  getMetrics(): Map<string, any> {
    return this.metrics;
  }

  /**
   * Оновлення метрик
   */
  private updateMetrics(platform: string, operation: string, success: boolean, executionTime: number): void {
    const key = `${platform}.${operation}`;
    const current = this.metrics.get(key) || {
      count: 0,
      successCount: 0,
      totalTime: 0,
      errors: 0
    };

    current.count++;
    current.totalTime += executionTime;
    
    if (success) {
      current.successCount++;
    } else {
      current.errors++;
    }

    current.successRate = (current.successCount / current.count) * 100;
    current.averageTime = current.totalTime / current.count;

    this.metrics.set(key, current);
  }
}
