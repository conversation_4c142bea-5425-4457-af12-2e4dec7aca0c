/**
 * Enhanced Model File Scraper
 * Scrapes real 3D model files from various platforms using Bright Data MCP
 */

import { ModelSource, ScrapedModel } from '@/types/models';
import { BrightDataMCPClient } from './mcp-client';

export interface ModelFile {
  name: string;
  url: string;
  format: string;
  size: number;
  downloadUrl?: string;
  isProcessed?: boolean;
  processedUrl?: string;
}

export interface ScrapedModelWithFiles extends ScrapedModel {
  modelFiles: ModelFile[];
  previewFile?: ModelFile;
  originalFiles: ModelFile[];
  processedFiles: ModelFile[];
}

export interface ModelFileScrapingOptions {
  platform: ModelSource;
  includeAllFormats?: boolean;
  downloadFiles?: boolean;
  processFiles?: boolean;
  generateThumbnails?: boolean;
}

export class ModelFileScraper {
  private brightData: BrightDataMCPClient;

  constructor() {
    this.brightData = new BrightDataMCPClient();
  }

  /**
   * Scrape a complete model with all its files
   */
  async scrapeModelWithFiles(
    modelUrl: string, 
    options: ModelFileScrapingOptions
  ): Promise<ScrapedModelWithFiles | null> {
    try {
      console.log(`🔍 Scraping model with files: ${modelUrl}`);

      // First, get the basic model data
      const basicModel = await this.scrapeBasicModelData(modelUrl, options.platform);
      if (!basicModel) {
        console.error(`❌ Failed to scrape basic model data from ${modelUrl}`);
        return null;
      }

      // Extract model files
      const modelFiles = await this.extractModelFiles(modelUrl, options);
      
      // Process files if requested
      let processedFiles: ModelFile[] = [];
      if (options.processFiles && modelFiles.length > 0) {
        processedFiles = await this.processModelFiles(modelFiles);
      }

      // Find the best preview file
      const previewFile = this.selectPreviewFile(modelFiles, processedFiles);

      const result: ScrapedModelWithFiles = {
        ...basicModel,
        modelFiles: [...modelFiles, ...processedFiles],
        previewFile,
        originalFiles: modelFiles,
        processedFiles
      };

      console.log(`✅ Successfully scraped model with ${modelFiles.length} files`);
      return result;

    } catch (error) {
      console.error(`❌ Error scraping model with files:`, error);
      return null;
    }
  }

  /**
   * Extract model files from a platform page
   */
  private async extractModelFiles(
    modelUrl: string, 
    options: ModelFileScrapingOptions
  ): Promise<ModelFile[]> {
    try {
      console.log(`📁 Extracting model files from: ${modelUrl}`);

      // Use Bright Data to scrape the page
      const scrapedData = await this.brightData.callTool('scrape_as_html_Bright_Data', {
        url: modelUrl
      });

      if (!scrapedData.success) {
        console.error(`❌ Failed to scrape page: ${scrapedData.error}`);
        return [];
      }

      const html = scrapedData.data;
      const files: ModelFile[] = [];

      // Platform-specific file extraction
      switch (options.platform) {
        case 'printables':
          files.push(...await this.extractPrintablesFiles(html, modelUrl));
          break;
        case 'makerworld':
          files.push(...await this.extractMakerworldFiles(html, modelUrl));
          break;
        case 'thangs':
          files.push(...await this.extractThangsFiles(html, modelUrl));
          break;
        case 'thingiverse':
          files.push(...await this.extractThingiverseFiles(html, modelUrl));
          break;
        default:
          files.push(...await this.extractGenericFiles(html, modelUrl));
      }

      // Download files if requested
      if (options.downloadFiles) {
        for (const file of files) {
          await this.downloadModelFile(file);
        }
      }

      console.log(`📁 Found ${files.length} model files`);
      return files;

    } catch (error) {
      console.error(`❌ Error extracting model files:`, error);
      return [];
    }
  }

  /**
   * Extract files from Printables
   */
  private async extractPrintablesFiles(html: string, baseUrl: string): Promise<ModelFile[]> {
    const files: ModelFile[] = [];

    try {
      // Look for download links in Printables format
      const downloadPatterns = [
        /href="([^"]*\/download\/[^"]*\.(?:stl|obj|3mf|gcode|ply|amf))"/gi,
        /data-download-url="([^"]*\.(?:stl|obj|3mf|gcode|ply|amf))"/gi,
        /"downloadUrl":"([^"]*\.(?:stl|obj|3mf|gcode|ply|amf))"/gi,
        /"file_url":"([^"]*\.(?:stl|obj|3mf|gcode|ply|amf))"/gi
      ];

      for (const pattern of downloadPatterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          const fileUrl = match[1];
          const fileName = this.extractFileName(fileUrl);
          const format = this.extractFileFormat(fileName);

          if (this.isValidModelFormat(format)) {
            files.push({
              name: fileName,
              url: this.resolveUrl(fileUrl, baseUrl),
              format,
              size: 0 // Will be determined during download
            });
          }
        }
      }

      // Also look for file information in JSON data
      const jsonFiles = this.extractFilesFromJson(html, baseUrl);
      files.push(...jsonFiles);

    } catch (error) {
      console.error(`❌ Error extracting Printables files:`, error);
    }

    return this.deduplicateFiles(files);
  }

  /**
   * Extract files from MakerWorld
   */
  private async extractMakerworldFiles(html: string, baseUrl: string): Promise<ModelFile[]> {
    const files: ModelFile[] = [];

    try {
      // MakerWorld specific patterns
      const patterns = [
        /href="([^"]*\/api\/file\/download\/[^"]*\.(?:stl|obj|3mf|gcode))"/gi,
        /"download_url":"([^"]*\.(?:stl|obj|3mf|gcode))"/gi,
        /"file_path":"([^"]*\.(?:stl|obj|3mf|gcode))"/gi
      ];

      for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          const fileUrl = match[1];
          const fileName = this.extractFileName(fileUrl);
          const format = this.extractFileFormat(fileName);

          if (this.isValidModelFormat(format)) {
            files.push({
              name: fileName,
              url: this.resolveUrl(fileUrl, baseUrl),
              format,
              size: 0
            });
          }
        }
      }

    } catch (error) {
      console.error(`❌ Error extracting MakerWorld files:`, error);
    }

    return this.deduplicateFiles(files);
  }

  /**
   * Extract files from Thangs
   */
  private async extractThangsFiles(html: string, baseUrl: string): Promise<ModelFile[]> {
    const files: ModelFile[] = [];

    try {
      // Thangs specific patterns
      const patterns = [
        /href="([^"]*\/download\/[^"]*\.(?:stl|obj|3mf|gcode))"/gi,
        /"downloadLink":"([^"]*\.(?:stl|obj|3mf|gcode))"/gi
      ];

      for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          const fileUrl = match[1];
          const fileName = this.extractFileName(fileUrl);
          const format = this.extractFileFormat(fileName);

          if (this.isValidModelFormat(format)) {
            files.push({
              name: fileName,
              url: this.resolveUrl(fileUrl, baseUrl),
              format,
              size: 0
            });
          }
        }
      }

    } catch (error) {
      console.error(`❌ Error extracting Thangs files:`, error);
    }

    return this.deduplicateFiles(files);
  }

  /**
   * Extract files from Thingiverse
   */
  private async extractThingiverseFiles(html: string, baseUrl: string): Promise<ModelFile[]> {
    const files: ModelFile[] = [];

    try {
      // Thingiverse specific patterns
      const patterns = [
        /href="([^"]*\/download:[^"]*\.(?:stl|obj|3mf|gcode))"/gi,
        /"download_url":"([^"]*\.(?:stl|obj|3mf|gcode))"/gi
      ];

      for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          const fileUrl = match[1];
          const fileName = this.extractFileName(fileUrl);
          const format = this.extractFileFormat(fileName);

          if (this.isValidModelFormat(format)) {
            files.push({
              name: fileName,
              url: this.resolveUrl(fileUrl, baseUrl),
              format,
              size: 0
            });
          }
        }
      }

    } catch (error) {
      console.error(`❌ Error extracting Thingiverse files:`, error);
    }

    return this.deduplicateFiles(files);
  }

  /**
   * Generic file extraction for unknown platforms
   */
  private async extractGenericFiles(html: string, baseUrl: string): Promise<ModelFile[]> {
    const files: ModelFile[] = [];

    try {
      // Generic patterns for 3D model files
      const patterns = [
        /href="([^"]*\.(?:stl|obj|3mf|gcode|ply|amf|glb|gltf))"/gi,
        /"url":"([^"]*\.(?:stl|obj|3mf|gcode|ply|amf|glb|gltf))"/gi,
        /"file":"([^"]*\.(?:stl|obj|3mf|gcode|ply|amf|glb|gltf))"/gi
      ];

      for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(html)) !== null) {
          const fileUrl = match[1];
          const fileName = this.extractFileName(fileUrl);
          const format = this.extractFileFormat(fileName);

          if (this.isValidModelFormat(format)) {
            files.push({
              name: fileName,
              url: this.resolveUrl(fileUrl, baseUrl),
              format,
              size: 0
            });
          }
        }
      }

    } catch (error) {
      console.error(`❌ Error extracting generic files:`, error);
    }

    return this.deduplicateFiles(files);
  }

  /**
   * Helper methods
   */
  private extractFileName(url: string): string {
    return url.split('/').pop()?.split('?')[0] || 'model';
  }

  private extractFileFormat(fileName: string): string {
    return fileName.split('.').pop()?.toLowerCase() || 'unknown';
  }

  private isValidModelFormat(format: string): boolean {
    const validFormats = ['stl', 'obj', '3mf', 'gcode', 'ply', 'amf', 'glb', 'gltf'];
    return validFormats.includes(format.toLowerCase());
  }

  private resolveUrl(url: string, baseUrl: string): string {
    if (url.startsWith('http')) {
      return url;
    }
    if (url.startsWith('/')) {
      const base = new URL(baseUrl);
      return `${base.protocol}//${base.host}${url}`;
    }
    return new URL(url, baseUrl).toString();
  }

  private deduplicateFiles(files: ModelFile[]): ModelFile[] {
    const seen = new Set<string>();
    return files.filter(file => {
      const key = `${file.name}-${file.format}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * Extract files from JSON data in HTML
   */
  private extractFilesFromJson(html: string, baseUrl: string): ModelFile[] {
    const files: ModelFile[] = [];

    try {
      // Look for JSON data containing file information
      const jsonMatches = html.match(/<script[^>]*>[\s\S]*?(?:window\.__INITIAL_STATE__|window\.__NUXT__|window\.APP_DATA)\s*=\s*({[\s\S]*?});[\s\S]*?<\/script>/g);

      if (jsonMatches) {
        for (const match of jsonMatches) {
          try {
            const jsonStr = match.replace(/<script[^>]*>[\s\S]*?(?:window\.__INITIAL_STATE__|window\.__NUXT__|window\.APP_DATA)\s*=\s*/, '')
                                .replace(/;[\s\S]*?<\/script>[\s\S]*$/, '');

            const data = JSON.parse(jsonStr);
            const foundFiles = this.findFilesInObject(data, baseUrl);
            files.push(...foundFiles);
          } catch (e) {
            // Continue to next match if JSON parsing fails
          }
        }
      }
    } catch (error) {
      console.error('❌ Error extracting files from JSON:', error);
    }

    return files;
  }

  /**
   * Recursively find model files in JSON object
   */
  private findFilesInObject(obj: any, baseUrl: string): ModelFile[] {
    const files: ModelFile[] = [];

    if (typeof obj === 'string') {
      if (this.looksLikeModelFile(obj)) {
        const fileName = this.extractFileName(obj);
        const format = this.extractFileFormat(fileName);

        if (this.isValidModelFormat(format)) {
          files.push({
            name: fileName,
            url: this.resolveUrl(obj, baseUrl),
            format,
            size: 0
          });
        }
      }
    } else if (Array.isArray(obj)) {
      for (const item of obj) {
        files.push(...this.findFilesInObject(item, baseUrl));
      }
    } else if (obj && typeof obj === 'object') {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          files.push(...this.findFilesInObject(obj[key], baseUrl));
        }
      }
    }

    return files;
  }

  /**
   * Check if a string looks like a model file URL
   */
  private looksLikeModelFile(str: string): boolean {
    return /\.(?:stl|obj|3mf|gcode|ply|amf|glb|gltf)(?:\?|$)/i.test(str);
  }

  /**
   * Download a model file
   */
  private async downloadModelFile(file: ModelFile): Promise<void> {
    try {
      console.log(`⬇️ Downloading file: ${file.name}`);

      // Use Bright Data to download the file
      const response = await fetch(file.url);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const buffer = await response.arrayBuffer();
      file.size = buffer.byteLength;

      // Store the file (implementation will be added with R2 integration)
      console.log(`✅ Downloaded ${file.name} (${file.size} bytes)`);

    } catch (error) {
      console.error(`❌ Error downloading file ${file.name}:`, error);
    }
  }

  /**
   * Process model files (convert formats, generate thumbnails, etc.)
   */
  private async processModelFiles(files: ModelFile[]): Promise<ModelFile[]> {
    const processedFiles: ModelFile[] = [];

    try {
      for (const file of files) {
        console.log(`🔄 Processing file: ${file.name}`);

        // Convert to web-friendly format if needed
        if (this.needsConversion(file.format)) {
          const convertedFile = await this.convertToWebFormat(file);
          if (convertedFile) {
            processedFiles.push(convertedFile);
          }
        }
      }
    } catch (error) {
      console.error(`❌ Error processing model files:`, error);
    }

    return processedFiles;
  }

  /**
   * Check if file needs conversion for web viewing
   */
  private needsConversion(format: string): boolean {
    const webFormats = ['glb', 'gltf'];
    return !webFormats.includes(format.toLowerCase());
  }

  /**
   * Convert file to web-friendly format
   */
  private async convertToWebFormat(file: ModelFile): Promise<ModelFile | null> {
    try {
      // This will be implemented with the file processing pipeline
      console.log(`🔄 Converting ${file.name} to GLB format`);

      // For now, return a placeholder converted file
      return {
        ...file,
        name: file.name.replace(/\.[^.]+$/, '.glb'),
        format: 'glb',
        isProcessed: true,
        processedUrl: file.url // Will be replaced with actual converted file URL
      };
    } catch (error) {
      console.error(`❌ Error converting file ${file.name}:`, error);
      return null;
    }
  }

  /**
   * Select the best file for preview
   */
  private selectPreviewFile(originalFiles: ModelFile[], processedFiles: ModelFile[]): ModelFile | undefined {
    // Prefer processed GLB/GLTF files for web viewing
    const webFiles = processedFiles.filter(f => ['glb', 'gltf'].includes(f.format));
    if (webFiles.length > 0) {
      return webFiles[0];
    }

    // Fall back to original files, preferring STL
    const stlFiles = originalFiles.filter(f => f.format === 'stl');
    if (stlFiles.length > 0) {
      return stlFiles[0];
    }

    // Return any available file
    return originalFiles[0] || processedFiles[0];
  }

  /**
   * Scrape basic model data (reuse existing scraper logic)
   */
  private async scrapeBasicModelData(modelUrl: string, platform: ModelSource): Promise<ScrapedModel | null> {
    try {
      // Use existing platform scrapers for basic data
      const { PrintablesScraper } = await import('./platform-scrapers/printables-scraper');
      const { MakerworldScraper } = await import('./platform-scrapers/makerworld-scraper');
      const { ThangsScraper } = await import('./platform-scrapers/thangs-scraper');

      let scraper;
      switch (platform) {
        case 'printables':
          scraper = new PrintablesScraper();
          break;
        case 'makerworld':
          scraper = new MakerworldScraper();
          break;
        case 'thangs':
          scraper = new ThangsScraper();
          break;
        default:
          return null;
      }

      return await scraper.scrapeModel(modelUrl, {});
    } catch (error) {
      console.error(`❌ Error scraping basic model data:`, error);
      return null;
    }
  }
}
