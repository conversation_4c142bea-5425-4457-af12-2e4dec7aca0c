import { D1Database, D1ExecResult, D1PreparedStatement, D1Result, QueryParams, TransactionCallback, TransactionOptions } from './types';

// Global variable for storing database instance
let db: D1Database | null = null;

/**
 * Generates a unique ID
 * @returns Unique string ID
 */
function generateId(): string {
  return `model_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Checks if D1 database is available
 * @returns boolean indicating D1 availability
 */
export function isD1DatabaseAvailable(): boolean {
  return !!(process.env.DB || globalThis.DB);
}

/**
 * Gets D1 database instance
 * @returns D1 database instance or null if not available
 */
export function getDb(): D1Database | null {
  if (!db && isD1DatabaseAvailable()) {
    // In Cloudflare Pages environment, D1 is available through env.DB
    if (process.env.DB) {
      db = process.env.DB as unknown as D1Database;
    } else if (globalThis.DB) {
      // For Next.js server components
      db = globalThis.DB as unknown as D1Database;
    }
  }
  return db;
}

/**
 * Gets D1 database instance with error throwing
 * @returns D1 database instance
 * @throws Error if database is not available
 */
export function getDbOrThrow(): D1Database {
  const database = getDb();
  if (!database) {
    throw new Error('Database unavailable. Make sure you configured D1 in wrangler.toml and run the project with --d1=DB flag');
  }
  return database;
}

/**
 * Executes SQL query to database
 * @param sql SQL query
 * @param params Query parameters
 * @returns Query result
 */
export async function query<T = unknown>(sql: string, params: QueryParams = []): Promise<T[]> {
  const db = getDbOrThrow();
  const stmt = db.prepare(sql);
  const result = await stmt.bind(...params).all<T>();
  return result.results;
}

/**
 * Gets one record from database
 * @param sql SQL query
 * @param params Query parameters
 * @returns First record or null
 */
export async function queryOne<T = unknown>(sql: string, params: QueryParams = []): Promise<T | null> {
  const db = getDbOrThrow();
  const stmt = db.prepare(sql);
  return await stmt.bind(...params).first<T>();
}

/**
 * Executes SQL query without returning results
 * @param sql SQL query
 * @param params Query parameters
 * @returns Execution result
 */
export async function execute(sql: string, params: QueryParams = []): Promise<D1ExecResult> {
  const db = getDbOrThrow();
  const stmt = db.prepare(sql);
  return await stmt.bind(...params).run();
}

/**
 * Executes batch of SQL queries
 * @param statements Array of prepared statements
 * @returns Execution results
 */
export async function batch(statements: D1PreparedStatement[]): Promise<D1Result[]> {
  const db = getDbOrThrow();
  return await db.batch(statements);
}

/**
 * Executes transaction
 * @param callback Function executed in transaction
 * @param options Transaction options
 * @returns Function execution result
 */
export async function transaction<T>(
  callback: TransactionCallback<T>,
  options: TransactionOptions = {}
): Promise<T> {
  const db = getDbOrThrow();

  // Begin transaction
  await db.exec('BEGIN TRANSACTION');

  try {
    // Execute function in transaction
    const result = await callback(db);

    // Commit transaction
    await db.exec('COMMIT');

    return result;
  } catch (error) {
    // Rollback transaction on error
    await db.exec('ROLLBACK');

    if (options.rollbackOnError !== false) {
      throw error;
    }

    return null as unknown as T;
  }
}

/**
 * Creates prepared statement
 * @param sql SQL query
 * @returns Prepared statement
 */
export function prepareStatement(sql: string): D1PreparedStatement {
  const db = getDbOrThrow();
  return db.prepare(sql);
}

/**
 * Executes query with pagination
 * @param sql SQL query without LIMIT and OFFSET
 * @param params Query parameters
 * @param page Page number (starts from 1)
 * @param limit Records per page
 * @returns Query result with pagination
 */
export async function queryWithPagination<T = unknown>(
  sql: string,
  params: QueryParams = [],
  page: number = 1,
  limit: number = 10
): Promise<{ data: T[]; total: number; page: number; limit: number; totalPages: number }> {
  const db = getDbOrThrow();

  // Query to get total record count
  const countSql = `SELECT COUNT(*) as total FROM (${sql.replace(/SELECT .* FROM/i, 'SELECT 1 FROM')})`;
  const countResult = await db.prepare(countSql).bind(...params).first<{ total: number }>();
  const total = countResult?.total || 0;

  // Query with pagination
  const paginatedSql = `${sql} LIMIT ? OFFSET ?`;
  const offset = (page - 1) * limit;
  const paginatedParams = [...params, limit, offset];

  const result = await db.prepare(paginatedSql).bind(...paginatedParams).all<T>();

  return {
    data: result.results,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
  };
}

// ==================== MODELS DATABASE FUNCTIONS ====================

/**
 * Initializes tables for storing models
 */
export async function initializeModelsDatabase(): Promise<void> {
  if (!isD1DatabaseAvailable()) {
    console.log('⚠️ D1 database not available, skipping database initialization');
    return;
  }

  const db = getDbOrThrow();

  // Create models table
  await db.exec(`
    CREATE TABLE IF NOT EXISTS models (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      description TEXT,
      thumbnail TEXT,
      designer_name TEXT,
      designer_avatar TEXT,
      price REAL DEFAULT 0,
      category TEXT,
      likes INTEGER DEFAULT 0,
      downloads INTEGER DEFAULT 0,
      tags TEXT, -- JSON array as string
      is_featured BOOLEAN DEFAULT FALSE,
      images TEXT, -- JSON array as string
      file_formats TEXT, -- JSON array as string
      file_size TEXT,
      source TEXT NOT NULL, -- 'printables', 'makerworld', 'thangs'
      original_id TEXT,
      original_url TEXT,
      is_free BOOLEAN DEFAULT TRUE,
      license TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      scraped_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create indexes for fast search
  await db.exec(`CREATE INDEX IF NOT EXISTS idx_models_category ON models(category)`);
  await db.exec(`CREATE INDEX IF NOT EXISTS idx_models_source ON models(source)`);
  await db.exec(`CREATE INDEX IF NOT EXISTS idx_models_is_free ON models(is_free)`);
  await db.exec(`CREATE INDEX IF NOT EXISTS idx_models_downloads ON models(downloads DESC)`);
  await db.exec(`CREATE INDEX IF NOT EXISTS idx_models_likes ON models(likes DESC)`);
  await db.exec(`CREATE INDEX IF NOT EXISTS idx_models_created_at ON models(created_at DESC)`);

  console.log('✅ Models database initialized');
}

/**
 * Gets all models from database
 */
export async function getAllModels(): Promise<any[]> {
  try {
    if (!isD1DatabaseAvailable()) {
      console.log('⚠️ D1 database not available, returning empty models list');
      return [];
    }

    // First check if table exists
    const tableExists = await queryOne(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name='models'
    `);

    if (!tableExists) {
      console.log('⚠️ Models table does not exist, initializing database');
      await initializeModelsDatabase();
      return [];
    }

    const models = await query(`
      SELECT * FROM models
      ORDER BY downloads DESC, likes DESC, created_at DESC
    `);

    return models.map(transformDbModelToApi);
  } catch (error) {
    console.error('❌ Error getting all models:', error);
    return [];
  }
}

/**
 * Gets models by category
 */
export async function getModelsByCategory(category: string): Promise<any[]> {
  try {
    if (!isD1DatabaseAvailable()) {
      console.log('⚠️ D1 database not available, returning empty models list');
      return [];
    }

    const models = await query(`
      SELECT * FROM models
      WHERE category = ?
      ORDER BY downloads DESC, likes DESC, created_at DESC
    `, [category]);

    return models.map(transformDbModelToApi);
  } catch (error) {
    console.error('❌ Error getting models by category:', error);
    return [];
  }
}

/**
 * Search models by title and description
 */
export async function searchModels(searchTerm: string): Promise<any[]> {
  try {
    if (!isD1DatabaseAvailable()) {
      console.log('⚠️ D1 database not available, returning empty models list');
      return [];
    }

    const models = await query(`
      SELECT * FROM models
      WHERE title LIKE ? OR description LIKE ? OR tags LIKE ?
      ORDER BY downloads DESC, likes DESC, created_at DESC
    `, [`%${searchTerm}%`, `%${searchTerm}%`, `%${searchTerm}%`]);

    return models.map(transformDbModelToApi);
  } catch (error) {
    console.error('❌ Error searching models:', error);
    return [];
  }
}

/**
 * Saves model to database
 */
export async function saveModel(model: any): Promise<boolean> {
  try {
    if (!isD1DatabaseAvailable()) {
      console.log('⚠️ D1 database not available, skipping model save');
      return false;
    }

    const id = model.id || generateId();

    await execute(`
      INSERT OR REPLACE INTO models (
        id, title, description, thumbnail, designer_name, designer_avatar,
        price, category, likes, downloads, tags, is_featured, images,
        file_formats, file_size, source, original_id, original_url,
        is_free, license, updated_at, scraped_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      id,
      model.title,
      model.description || '',
      model.thumbnail || '',
      model.designer?.name || model.designer_name || '',
      model.designer?.avatar || model.designer_avatar || '',
      model.price || 0,
      model.category || 'Other',
      model.likes || model.stats?.likes || 0,
      model.downloads || model.stats?.downloads || 0,
      JSON.stringify(model.tags || []),
      model.is_featured || false,
      JSON.stringify(model.images || []),
      JSON.stringify(model.file_formats || model.fileFormats || []),
      model.file_size || model.fileSize || '',
      model.source || model.platform || 'unknown',
      model.original_id || model.originalId || '',
      model.original_url || model.originalUrl || '',
      model.is_free !== false,
      model.license?.type || model.license || 'Unknown',
      new Date().toISOString(),
      new Date().toISOString()
    ]);

    return true;
  } catch (error) {
    console.error('❌ Error saving model:', error);
    return false;
  }
}

/**
 * Перетворює модель з бази даних в API формат
 */
function transformDbModelToApi(dbModel: any): any {
  return {
    id: dbModel.id,
    title: dbModel.title,
    description: dbModel.description,
    thumbnail: dbModel.thumbnail,
    designer: {
      name: dbModel.designer_name,
      avatar: dbModel.designer_avatar
    },
    price: dbModel.price,
    category: dbModel.category,
    likes: dbModel.likes,
    downloads: dbModel.downloads,
    tags: JSON.parse(dbModel.tags || '[]'),
    isFeatured: dbModel.is_featured,
    images: JSON.parse(dbModel.images || '[]'),
    fileFormats: JSON.parse(dbModel.file_formats || '[]'),
    fileSize: dbModel.file_size,
    source: dbModel.source,
    externalSource: {
      platform: dbModel.source,
      originalId: dbModel.original_id,
      originalUrl: dbModel.original_url
    },
    isFree: dbModel.is_free,
    license: {
      type: dbModel.license
    },
    createdAt: dbModel.created_at,
    updatedAt: dbModel.updated_at,
    scrapedAt: dbModel.scraped_at
  };
}
