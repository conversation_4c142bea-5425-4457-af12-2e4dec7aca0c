import { NextRequest, NextResponse } from 'next/server';
import { D1Database } from './types';

/**
 * Middleware для доступу до D1 в API маршрутах
 * @param handler Обробник запиту
 * @returns Функція-обробник з доступом до D1
 */
export function withD1<T>(
  handler: (
    req: NextRequest,
    context: { params?: Record<string, string>; db: D1Database }
  ) => Promise<NextResponse<T> | NextResponse<any>>
) {
  return async (
    req: NextRequest,
    context: any
  ): Promise<NextResponse<T>> => {
    // Отримання доступу до D1
    const db = process.env.DB as unknown as D1Database;

    if (!db) {
      return NextResponse.json(
        { error: 'Database not available' },
        { status: 500 }
      ) as NextResponse<T>;
    }

    // Виклик обробника з доступом до D1
    return handler(req, { ...context, db });
  };
}

/**
 * Middleware для обробки помилок в API маршрутах
 * @param handler Обробник запиту
 * @returns Функція-обробник з обробкою помилок
 */
export function withErrorHandling<T>(
  handler: (
    req: NextRequest,
    context: any
  ) => Promise<NextResponse<T> | NextResponse<any>>
) {
  return async (
    req: NextRequest,
    context: any
  ): Promise<NextResponse<T>> => {
    try {
      return await handler(req, context);
    } catch (error) {
      console.error('API Error:', error);

      return NextResponse.json(
        {
          error: 'Internal Server Error',
          message: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined,
        },
        { status: 500 }
      ) as NextResponse<T>;
    }
  };
}

/**
 * Комбінує middleware для API маршрутів
 * @param handler Обробник запиту
 * @returns Функція-обробник з усіма middleware
 */
export function withApiMiddleware<T>(
  handler: (
    req: NextRequest,
    context: { params?: Record<string, string>; db: D1Database }
  ) => Promise<NextResponse<T> | NextResponse<any>>
) {
  return withErrorHandling(withD1(handler));
}
