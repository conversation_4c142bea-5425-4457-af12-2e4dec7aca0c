/**
 * Утиліти для роботи з базою даних
 */

/**
 * Генерує унікальний ідентифікатор
 * @returns Унікальний ідентифікатор
 */
export function generateId(): string {
  return crypto.randomUUID();
}

/**
 * Форматує дату для SQL-запиту
 * @param date Дата
 * @returns Рядок з датою у форматі ISO
 */
export function formatDate(date: Date): string {
  return date.toISOString();
}

/**
 * Перетворює рядок з датою у форматі ISO в об'єкт Date
 * @param dateString Рядок з датою
 * @returns Об'єкт Date
 */
export function parseDate(dateString: string): Date {
  return new Date(dateString);
}

/**
 * Перетворює об'єкт у параметри для SQL-запиту
 * @param obj Об'єкт з параметрами
 * @returns Масив параметрів
 */
export function objectToParams(obj: Record<string, any>): any[] {
  return Object.values(obj);
}

/**
 * Створює SQL-запит для вставки даних
 * @param table Назва таблиці
 * @param data Дані для вставки
 * @returns SQL-запит та параметри
 */
export function createInsertQuery(
  table: string,
  data: Record<string, any>
): { sql: string; params: any[] } {
  const keys = Object.keys(data);
  const placeholders = keys.map(() => '?').join(', ');
  const columns = keys.join(', ');
  
  return {
    sql: `INSERT INTO ${table} (${columns}) VALUES (${placeholders})`,
    params: objectToParams(data),
  };
}

/**
 * Створює SQL-запит для оновлення даних
 * @param table Назва таблиці
 * @param data Дані для оновлення
 * @param where Умова WHERE
 * @param whereParams Параметри для умови WHERE
 * @returns SQL-запит та параметри
 */
export function createUpdateQuery(
  table: string,
  data: Record<string, any>,
  where: string,
  whereParams: any[] = []
): { sql: string; params: any[] } {
  const keys = Object.keys(data);
  const setClause = keys.map(key => `${key} = ?`).join(', ');
  
  return {
    sql: `UPDATE ${table} SET ${setClause} WHERE ${where}`,
    params: [...objectToParams(data), ...whereParams],
  };
}

/**
 * Створює SQL-запит для видалення даних
 * @param table Назва таблиці
 * @param where Умова WHERE
 * @param whereParams Параметри для умови WHERE
 * @returns SQL-запит та параметри
 */
export function createDeleteQuery(
  table: string,
  where: string,
  whereParams: any[] = []
): { sql: string; params: any[] } {
  return {
    sql: `DELETE FROM ${table} WHERE ${where}`,
    params: whereParams,
  };
}

/**
 * Створює SQL-запит для вибірки даних
 * @param table Назва таблиці
 * @param columns Колонки для вибірки
 * @param where Умова WHERE
 * @param whereParams Параметри для умови WHERE
 * @param orderBy Умова ORDER BY
 * @param limit Обмеження кількості записів
 * @param offset Зміщення
 * @returns SQL-запит та параметри
 */
export function createSelectQuery(
  table: string,
  columns: string[] = ['*'],
  where: string = '',
  whereParams: any[] = [],
  orderBy: string = '',
  limit: number = 0,
  offset: number = 0
): { sql: string; params: any[] } {
  let sql = `SELECT ${columns.join(', ')} FROM ${table}`;
  
  if (where) {
    sql += ` WHERE ${where}`;
  }
  
  if (orderBy) {
    sql += ` ORDER BY ${orderBy}`;
  }
  
  if (limit > 0) {
    sql += ` LIMIT ?`;
    whereParams.push(limit);
    
    if (offset > 0) {
      sql += ` OFFSET ?`;
      whereParams.push(offset);
    }
  }
  
  return {
    sql,
    params: whereParams,
  };
}
