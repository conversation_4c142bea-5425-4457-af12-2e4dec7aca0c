/**
 * Cloudflare адаптер для NextAuth
 * Використовує D1 для зберігання користувачів та KV для сесій
 */

import { execute, getDb } from '@/lib/cloudflare/d1';
import { getKV } from '@/lib/cloudflare/kv';
import { Adapter, AdapterAccount, AdapterSession, AdapterUser, VerificationToken } from 'next-auth/adapters';

export function CloudflareAdapter(): Adapter {
  return {
    async createUser(user) {
      try {
        const db = getDb();
        const id = crypto.randomUUID();

        const result = await execute(
          `INSERT INTO users (id, name, email, email_verified, image, created_at, updated_at)
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [
            id,
            user.name,
            user.email,
            user.emailVerified?.toISOString() || null,
            user.image,
            new Date().toISOString(),
            new Date().toISOString()
          ]
        );

        return {
          id,
          name: user.name,
          email: user.email!,
          emailVerified: user.emailVerified,
          image: user.image,
        } as AdapterUser;
      } catch (error) {
        console.error('Error creating user:', error);
        throw error;
      }
    },

    async getUser(id) {
      try {
        const db = getDb();
        const user = await queryOne(
          'SELECT * FROM users WHERE id = ?',
          [id]
        );

        if (!user) return null;
        return {
          id: user.id,
          name: user.name,
          email: user.email,
          emailVerified: user.email_verified ? new Date(user.email_verified) : null,
          image: user.image,
        } as AdapterUser;
      } catch (error) {
        console.error('Error getting user:', error);
        return null;
      }
    },

    async getUserByEmail(email) {
      try {
        const db = getDb();
        const result = await executeQuery(
          'SELECT * FROM users WHERE email = ?',
          [email]
        );

        if (result.results.length === 0) return null;

        const user = result.results[0] as any;
        return {
          id: user.id,
          name: user.name,
          email: user.email,
          emailVerified: user.email_verified ? new Date(user.email_verified) : null,
          image: user.image,
        } as AdapterUser;
      } catch (error) {
        console.error('Error getting user by email:', error);
        return null;
      }
    },

    async getUserByAccount({ providerAccountId, provider }) {
      try {
        const db = getDb();
        const result = await executeQuery(
          `SELECT u.* FROM users u
           JOIN accounts a ON u.id = a.user_id
           WHERE a.provider = ? AND a.provider_account_id = ?`,
          [provider, providerAccountId]
        );

        if (result.results.length === 0) return null;

        const user = result.results[0] as any;
        return {
          id: user.id,
          name: user.name,
          email: user.email,
          emailVerified: user.email_verified ? new Date(user.email_verified) : null,
          image: user.image,
        } as AdapterUser;
      } catch (error) {
        console.error('Error getting user by account:', error);
        return null;
      }
    },

    async updateUser(user) {
      try {
        const db = getDb();
        await executeQuery(
          `UPDATE users SET
           name = ?, email = ?, email_verified = ?, image = ?, updated_at = ?
           WHERE id = ?`,
          [
            user.name,
            user.email,
            user.emailVerified?.toISOString() || null,
            user.image,
            new Date().toISOString(),
            user.id
          ]
        );

        return {
          id: user.id,
          name: user.name,
          email: user.email!,
          emailVerified: user.emailVerified,
          image: user.image,
        } as AdapterUser;
      } catch (error) {
        console.error('Error updating user:', error);
        throw error;
      }
    },

    async deleteUser(userId) {
      try {
        const db = getDb();

        // Видаляємо пов'язані записи
        await executeQuery('DELETE FROM accounts WHERE user_id = ?', [userId]);
        await executeQuery('DELETE FROM sessions WHERE user_id = ?', [userId]);
        await executeQuery('DELETE FROM users WHERE id = ?', [userId]);

        return;
      } catch (error) {
        console.error('Error deleting user:', error);
        throw error;
      }
    },

    async linkAccount(account) {
      try {
        const db = getDb();
        await executeQuery(
          `INSERT INTO accounts (
            id, user_id, type, provider, provider_account_id,
            refresh_token, access_token, expires_at, token_type,
            scope, id_token, session_state, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            crypto.randomUUID(),
            account.userId,
            account.type,
            account.provider,
            account.providerAccountId,
            account.refresh_token,
            account.access_token,
            account.expires_at,
            account.token_type,
            account.scope,
            account.id_token,
            account.session_state,
            new Date().toISOString(),
            new Date().toISOString()
          ]
        );

        return account as AdapterAccount;
      } catch (error) {
        console.error('Error linking account:', error);
        throw error;
      }
    },

    async unlinkAccount({ providerAccountId, provider }) {
      try {
        const db = getDb();
        await executeQuery(
          'DELETE FROM accounts WHERE provider = ? AND provider_account_id = ?',
          [provider, providerAccountId]
        );
        return;
      } catch (error) {
        console.error('Error unlinking account:', error);
        throw error;
      }
    },

    async createSession({ sessionToken, userId, expires }) {
      try {
        const kv = getKV();
        const session = {
          sessionToken,
          userId,
          expires: expires.toISOString(),
          createdAt: new Date().toISOString()
        };

        // Зберігаємо сесію в KV з TTL
        const ttl = Math.floor((expires.getTime() - Date.now()) / 1000);
        await kv.put(`session:${sessionToken}`, JSON.stringify(session), {
          expirationTtl: ttl
        });

        return {
          sessionToken,
          userId,
          expires
        } as AdapterSession;
      } catch (error) {
        console.error('Error creating session:', error);
        throw error;
      }
    },

    async getSessionAndUser(sessionToken) {
      try {
        const kv = getKV();
        const sessionData = await kv.get(`session:${sessionToken}`, { type: 'text' });

        if (!sessionData) return null;

        const session = JSON.parse(sessionData);
        const user = await this.getUser!(session.userId);

        if (!user) return null;

        return {
          session: {
            sessionToken: session.sessionToken,
            userId: session.userId,
            expires: new Date(session.expires)
          } as AdapterSession,
          user
        };
      } catch (error) {
        console.error('Error getting session and user:', error);
        return null;
      }
    },

    async updateSession({ sessionToken, expires, userId }) {
      try {
        const kv = getKV();
        const sessionData = await kv.get(`session:${sessionToken}`, { type: 'text' });

        if (!sessionData) return null;

        const session = JSON.parse(sessionData);
        const updatedSession = {
          ...session,
          expires: expires?.toISOString() || session.expires,
          userId: userId || session.userId
        };

        // Оновлюємо TTL
        const newExpires = new Date(updatedSession.expires);
        const ttl = Math.floor((newExpires.getTime() - Date.now()) / 1000);

        await kv.put(`session:${sessionToken}`, JSON.stringify(updatedSession), {
          expirationTtl: ttl
        });

        return {
          sessionToken,
          userId: updatedSession.userId,
          expires: newExpires
        } as AdapterSession;
      } catch (error) {
        console.error('Error updating session:', error);
        return null;
      }
    },

    async deleteSession(sessionToken) {
      try {
        const kv = getKV();
        await kv.delete(`session:${sessionToken}`);
        return;
      } catch (error) {
        console.error('Error deleting session:', error);
        throw error;
      }
    },

    async createVerificationToken({ identifier, expires, token }) {
      try {
        const kv = getKV();
        const verificationToken = {
          identifier,
          token,
          expires: expires.toISOString()
        };

        // TTL для verification token
        const ttl = Math.floor((expires.getTime() - Date.now()) / 1000);
        await kv.put(`verification:${token}`, JSON.stringify(verificationToken), {
          expirationTtl: ttl
        });

        return {
          identifier,
          token,
          expires
        } as VerificationToken;
      } catch (error) {
        console.error('Error creating verification token:', error);
        throw error;
      }
    },

    async useVerificationToken({ identifier, token }) {
      try {
        const kv = getKV();
        const tokenData = await kv.get(`verification:${token}`, { type: 'text' });

        if (!tokenData) return null;

        const verificationToken = JSON.parse(tokenData);

        // Видаляємо токен після використання
        await kv.delete(`verification:${token}`);

        return {
          identifier: verificationToken.identifier,
          token: verificationToken.token,
          expires: new Date(verificationToken.expires)
        } as VerificationToken;
      } catch (error) {
        console.error('Error using verification token:', error);
        return null;
      }
    }
  };
}
