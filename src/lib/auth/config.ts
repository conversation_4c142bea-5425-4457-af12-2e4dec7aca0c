/**
 * NextAuth конфігурація з Cloudflare інтеграцією
 * Підтримка Google OAuth та інших провайдерів
 */

import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';

/**
 * Отримує environment змінні для NextAuth
 */
function getNextAuthEnv() {
  return {
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET || 'development-secret-key-for-local-testing-only',
    NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'http://localhost:3001',
  };
}

export const authOptions: NextAuthOptions = {
  // В розробці не використовуємо адаптер, щоб уникнути проблем з Cloudflare
  // adapter: CloudflareAdapter(),
  
  providers: [
    // Тестовий провайдер для розроб<PERSON><PERSON>
    CredentialsProvider({
      id: "demo",
      name: "De<PERSON> Lo<PERSON>",
      credentials: {
        email: {
          label: "Email",
          type: "email",
          placeholder: "<EMAIL>"
        },
        password: {
          label: "Password",
          type: "password",
          placeholder: "demo123"
        }
      },
      async authorize(credentials) {
        // Тестова автентифікація для розробки
        if (credentials?.email === "<EMAIL>" && credentials?.password === "demo123") {
          return {
            id: "demo-user-1",
            name: "Demo User",
            email: "<EMAIL>",
            image: "https://via.placeholder.com/150",
            provider: "demo"
          };
        }

        // Також дозволяємо будь-який email з паролем "demo"
        if (credentials?.password === "demo") {
          return {
            id: `user-${Date.now()}`,
            name: credentials.email?.split('@')[0] || "User",
            email: credentials.email,
            image: "https://via.placeholder.com/150",
            provider: "demo"
          };
        }

        return null;
      }
    }),

    // OAuth провайдери відключені для розробки
    // Розкоментуйте та налаштуйте реальні credentials для продакшену
    /*
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
          scope: "openid email profile"
        }
      }
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    })
    */
  ],

  pages: {
    signIn: '/auth/signin',
    signOut: '/auth/signout',
    error: '/auth/error',
    verifyRequest: '/auth/verify-request',
    newUser: '/auth/new-user'
  },

  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 днів
  },

  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 днів
  },

  callbacks: {
    async signIn({ user, account, profile, email, credentials }) {
      try {
        // В розробці просто логуємо
        console.log('User signed in:', {
          provider: account?.provider,
          email: user.email,
          name: user.name
        });
        return true;
      } catch (error) {
        console.error('Error in sign in callback:', error);
        return true;
      }
    },

    async jwt({ token, user, account, profile }) {
      // Додаємо додаткову інформацію до JWT токену
      if (user) {
        token.userId = user.id;
        token.provider = account?.provider;
      }
      return token;
    },

    async session({ session, token }) {
      // Додаємо інформацію з JWT до сесії
      if (token) {
        session.user.id = token.userId as string;
        session.user.provider = token.provider as string;
      }

      return session;
    },

    async redirect({ url, baseUrl }) {
      // Перенаправлення після аутентифікації
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    }
  },

  events: {
    async signOut({ token, session }) {
      console.log('User signed out');
    },

    async createUser({ user }) {
      console.log('User created:', user.email);
    }
  },

  debug: process.env.NODE_ENV === 'development',
  
  secret: getNextAuthEnv().NEXTAUTH_SECRET,
};

/**
 * Типи для розширеної сесії
 */
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      provider?: string;
    }
  }

  interface User {
    id: string;
    provider?: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    userId?: string;
    provider?: string;
  }
}
