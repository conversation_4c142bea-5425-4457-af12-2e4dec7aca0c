-- Database Schema for Scraped 3D Models
-- This schema supports storing scraped models with full metadata, files, and processing results

-- Main scraped models table
CREATE TABLE IF NOT EXISTS scraped_models (
    id VARCHAR(255) PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    platform VARCHAR(50) NOT NULL, -- printables, makerworld, thangs, etc.
    original_url VARCHAR(1000) NOT NULL,
    original_id VARCHAR(255),
    category VARCHAR(100),
    tags JSON, -- Array of tags
    
    -- Designer information
    designer_name VA<PERSON><PERSON><PERSON>(255),
    designer_avatar VARCHAR(500),
    designer_profile_url VARCHAR(500),
    designer_verified BOOLEAN DEFAULT FALSE,
    
    -- Statistics
    views_count INT DEFAULT 0,
    downloads_count INT DEFAULT 0,
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    rating DECIMAL(3,2), -- 0.00 to 5.00
    reviews_count INT DEFAULT 0,
    
    -- Pricing and licensing
    is_free BOOLEAN DEFAULT TRUE,
    price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'USD',
    license_type VARCHAR(100),
    license_name VA<PERSON>HA<PERSON>(255),
    allow_commercial_use BOOLEAN DEFAULT FALSE,
    require_attribution BOOLEAN DEFAULT TRUE,
    allow_derivatives BOOLEAN DEFAULT TRUE,
    
    -- Processing status
    processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    processing_started_at TIMESTAMP NULL,
    processing_completed_at TIMESTAMP NULL,
    processing_error TEXT,
    
    -- Metadata
    scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    scraped_by_user_id VARCHAR(255),
    featured BOOLEAN DEFAULT FALSE,
    published_at TIMESTAMP,
    
    -- Search and indexing
    search_vector TEXT, -- For full-text search
    popularity_score DECIMAL(10,4) DEFAULT 0, -- Calculated popularity
    
    INDEX idx_platform (platform),
    INDEX idx_category (category),
    INDEX idx_scraped_at (scraped_at),
    INDEX idx_popularity (popularity_score DESC),
    INDEX idx_processing_status (processing_status),
    INDEX idx_featured (featured),
    INDEX idx_is_free (is_free),
    FULLTEXT idx_search (title, description, designer_name)
);

-- Model files table
CREATE TABLE IF NOT EXISTS scraped_model_files (
    id VARCHAR(255) PRIMARY KEY,
    model_id VARCHAR(255) NOT NULL,
    file_name VARCHAR(500) NOT NULL,
    file_format VARCHAR(20) NOT NULL, -- stl, obj, glb, etc.
    file_size BIGINT NOT NULL, -- in bytes
    file_type ENUM('original', 'converted', 'thumbnail', 'preview') NOT NULL,
    
    -- Storage information
    original_url VARCHAR(1000),
    stored_path VARCHAR(500), -- Path in R2 storage
    public_url VARCHAR(1000), -- Public access URL
    cdn_url VARCHAR(1000), -- CDN URL for faster access
    
    -- File metadata
    mime_type VARCHAR(100),
    checksum VARCHAR(64), -- SHA-256 hash
    compression_ratio DECIMAL(5,2),
    
    -- Processing information
    is_processed BOOLEAN DEFAULT FALSE,
    processed_from_file_id VARCHAR(255), -- Reference to original file
    processing_options JSON, -- Conversion/processing options used
    
    -- Upload information
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    uploaded_by_user_id VARCHAR(255),
    
    FOREIGN KEY (model_id) REFERENCES scraped_models(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_from_file_id) REFERENCES scraped_model_files(id) ON DELETE SET NULL,
    
    INDEX idx_model_id (model_id),
    INDEX idx_file_format (file_format),
    INDEX idx_file_type (file_type),
    INDEX idx_is_processed (is_processed),
    INDEX idx_uploaded_at (uploaded_at)
);

-- Model analysis results table
CREATE TABLE IF NOT EXISTS model_analysis (
    id VARCHAR(255) PRIMARY KEY,
    model_id VARCHAR(255) NOT NULL,
    file_id VARCHAR(255), -- Specific file analyzed
    
    -- Dimensions
    width DECIMAL(10,4),
    height DECIMAL(10,4),
    depth DECIMAL(10,4),
    units VARCHAR(10) DEFAULT 'mm',
    bounding_box_min JSON, -- [x, y, z]
    bounding_box_max JSON, -- [x, y, z]
    bounding_box_center JSON, -- [x, y, z]
    
    -- Geometry
    vertex_count INT,
    face_count INT,
    triangle_count INT,
    edge_count INT,
    surface_area DECIMAL(15,6),
    volume DECIMAL(15,6),
    
    -- Quality assessment
    mesh_quality ENUM('excellent', 'good', 'fair', 'poor'),
    is_manifold BOOLEAN DEFAULT FALSE,
    is_watertight BOOLEAN DEFAULT FALSE,
    has_normals BOOLEAN DEFAULT FALSE,
    has_textures BOOLEAN DEFAULT FALSE,
    has_colors BOOLEAN DEFAULT FALSE,
    
    -- Printability
    is_printable BOOLEAN DEFAULT TRUE,
    support_required BOOLEAN DEFAULT FALSE,
    overhang_angle DECIMAL(5,2), -- degrees
    bridge_length DECIMAL(10,4),
    min_wall_thickness DECIMAL(10,4),
    printability_issues JSON, -- Array of issues
    printability_recommendations JSON, -- Array of recommendations
    
    -- Complexity
    complexity_level ENUM('simple', 'moderate', 'complex', 'expert'),
    complexity_score INT, -- 0-100
    geometry_complexity DECIMAL(5,2),
    detail_level DECIMAL(5,2),
    support_complexity DECIMAL(5,2),
    
    -- Material recommendations
    recommended_materials JSON, -- Array of materials
    material_difficulty JSON, -- Object with material -> difficulty mapping
    
    -- Print time estimates (in minutes)
    estimated_print_time_fast INT,
    estimated_print_time_normal INT,
    estimated_print_time_high INT,
    
    -- Analysis metadata
    analyzed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    analysis_version VARCHAR(20) DEFAULT '1.0',
    analysis_duration_ms INT, -- Time taken for analysis
    
    FOREIGN KEY (model_id) REFERENCES scraped_models(id) ON DELETE CASCADE,
    FOREIGN KEY (file_id) REFERENCES scraped_model_files(id) ON DELETE CASCADE,
    
    INDEX idx_model_id (model_id),
    INDEX idx_complexity_level (complexity_level),
    INDEX idx_is_printable (is_printable),
    INDEX idx_support_required (support_required),
    INDEX idx_analyzed_at (analyzed_at)
);

-- Thumbnails table
CREATE TABLE IF NOT EXISTS model_thumbnails (
    id VARCHAR(255) PRIMARY KEY,
    model_id VARCHAR(255) NOT NULL,
    file_id VARCHAR(255), -- Source file for thumbnail
    
    -- Thumbnail properties
    thumbnail_type ENUM('main', 'angle', 'wireframe', 'cross_section') DEFAULT 'main',
    width INT NOT NULL,
    height INT NOT NULL,
    format VARCHAR(10) NOT NULL, -- png, jpg, webp
    quality INT, -- 0-100 for lossy formats
    
    -- Storage
    stored_path VARCHAR(500),
    public_url VARCHAR(1000),
    cdn_url VARCHAR(1000),
    file_size INT, -- in bytes
    
    -- Generation options
    camera_position JSON, -- [x, y, z]
    camera_target JSON, -- [x, y, z]
    lighting_preset VARCHAR(50),
    background_color VARCHAR(7), -- hex color
    show_wireframe BOOLEAN DEFAULT FALSE,
    show_grid BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    generation_duration_ms INT,
    
    FOREIGN KEY (model_id) REFERENCES scraped_models(id) ON DELETE CASCADE,
    FOREIGN KEY (file_id) REFERENCES scraped_model_files(id) ON DELETE CASCADE,
    
    INDEX idx_model_id (model_id),
    INDEX idx_thumbnail_type (thumbnail_type),
    INDEX idx_generated_at (generated_at)
);

-- Processing jobs table
CREATE TABLE IF NOT EXISTS processing_jobs (
    id VARCHAR(255) PRIMARY KEY,
    job_type ENUM('conversion', 'thumbnail', 'analysis', 'full_processing') NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    
    -- Job details
    model_ids JSON, -- Array of model IDs to process
    file_ids JSON, -- Array of file IDs to process
    processing_options JSON, -- Options for processing
    
    -- Progress tracking
    progress INT DEFAULT 0, -- 0-100
    current_stage VARCHAR(100),
    total_files INT DEFAULT 0,
    processed_files INT DEFAULT 0,
    failed_files INT DEFAULT 0,
    
    -- Timing
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    estimated_duration INT, -- seconds
    actual_duration INT, -- seconds
    
    -- Results and errors
    results JSON, -- Processing results
    error_message TEXT,
    retry_count INT DEFAULT 0,
    max_retries INT DEFAULT 2,
    
    -- User context
    created_by_user_id VARCHAR(255),
    
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_job_type (job_type),
    INDEX idx_created_at (created_at),
    INDEX idx_created_by_user (created_by_user_id)
);

-- Download tracking table
CREATE TABLE IF NOT EXISTS model_downloads (
    id VARCHAR(255) PRIMARY KEY,
    model_id VARCHAR(255) NOT NULL,
    file_id VARCHAR(255), -- Specific file downloaded
    user_id VARCHAR(255),
    
    -- Download details
    download_type ENUM('single_file', 'all_files', 'package') DEFAULT 'single_file',
    file_format VARCHAR(20), -- Format requested
    download_size BIGINT, -- Total bytes downloaded
    
    -- Session information
    ip_address VARCHAR(45), -- IPv4 or IPv6
    user_agent TEXT,
    referer VARCHAR(1000),
    country_code VARCHAR(2),
    
    -- Timing
    downloaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    download_duration_ms INT, -- Time taken for download
    
    FOREIGN KEY (model_id) REFERENCES scraped_models(id) ON DELETE CASCADE,
    FOREIGN KEY (file_id) REFERENCES scraped_model_files(id) ON DELETE SET NULL,
    
    INDEX idx_model_id (model_id),
    INDEX idx_user_id (user_id),
    INDEX idx_downloaded_at (downloaded_at),
    INDEX idx_download_type (download_type),
    INDEX idx_country_code (country_code)
);

-- User favorites/likes table
CREATE TABLE IF NOT EXISTS user_model_favorites (
    id VARCHAR(255) PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    model_id VARCHAR(255) NOT NULL,
    favorited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (model_id) REFERENCES scraped_models(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_user_model (user_id, model_id),
    INDEX idx_user_id (user_id),
    INDEX idx_model_id (model_id),
    INDEX idx_favorited_at (favorited_at)
);

-- Model collections/playlists
CREATE TABLE IF NOT EXISTS model_collections (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_by_user_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_created_by (created_by_user_id),
    INDEX idx_is_public (is_public),
    INDEX idx_created_at (created_at)
);

CREATE TABLE IF NOT EXISTS collection_models (
    id VARCHAR(255) PRIMARY KEY,
    collection_id VARCHAR(255) NOT NULL,
    model_id VARCHAR(255) NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    added_by_user_id VARCHAR(255),
    sort_order INT DEFAULT 0,
    
    FOREIGN KEY (collection_id) REFERENCES model_collections(id) ON DELETE CASCADE,
    FOREIGN KEY (model_id) REFERENCES scraped_models(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_collection_model (collection_id, model_id),
    INDEX idx_collection_id (collection_id),
    INDEX idx_model_id (model_id),
    INDEX idx_sort_order (sort_order)
);

-- Views for common queries
CREATE VIEW IF NOT EXISTS popular_models AS
SELECT 
    sm.*,
    (sm.downloads_count * 0.4 + sm.likes_count * 0.3 + sm.views_count * 0.0001 + sm.rating * 20) AS popularity_score
FROM scraped_models sm
WHERE sm.processing_status = 'completed'
ORDER BY popularity_score DESC;

CREATE VIEW IF NOT EXISTS recent_models AS
SELECT sm.*
FROM scraped_models sm
WHERE sm.processing_status = 'completed'
ORDER BY sm.scraped_at DESC;

CREATE VIEW IF NOT EXISTS featured_models AS
SELECT sm.*
FROM scraped_models sm
WHERE sm.processing_status = 'completed' AND sm.featured = TRUE
ORDER BY sm.scraped_at DESC;
