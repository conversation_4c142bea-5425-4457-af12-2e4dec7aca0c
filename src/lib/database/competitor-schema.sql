-- Competitor Intelligence Database Schema for Cloudflare D1
-- Historical data storage for trend analysis and market intelligence

-- Competitor models historical data
CREATE TABLE IF NOT EXISTS competitor_models_history (
    id TEXT PRIMARY KEY,
    platform TEXT NOT NULL,
    model_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    price REAL NOT NULL DEFAULT 0,
    currency TEXT NOT NULL DEFAULT 'USD',
    is_free BOOLEAN NOT NULL DEFAULT false,
    category TEXT NOT NULL,
    subcategory TEXT,
    tags TEXT, -- JSON array of tags
    formats TEXT, -- JSON array of formats
    download_count INTEGER,
    view_count INTEGER,
    like_count INTEGER,
    rating REAL,
    review_count INTEGER,
    creator_name TEXT,
    creator_profile TEXT,
    thumbnail_url TEXT,
    model_url TEXT NOT NULL,
    upload_date TEXT,
    file_size INTEGER,
    polygon_count INTEGER,
    texture_resolution TEXT,
    is_animated BOOLEAN DEFAULT false,
    is_rigged BOOLEAN DEFAULT false,
    license TEXT,
    scraped_at TEXT NOT NULL,
    data_quality TEXT NOT NULL DEFAULT 'medium',
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(platform, model_id, scraped_at)
);

-- Pricing trends over time
CREATE TABLE IF NOT EXISTS pricing_trends (
    id TEXT PRIMARY KEY,
    platform TEXT NOT NULL,
    category TEXT NOT NULL,
    subcategory TEXT,
    period_start TEXT NOT NULL,
    period_end TEXT NOT NULL,
    average_price REAL NOT NULL,
    median_price REAL NOT NULL,
    min_price REAL NOT NULL,
    max_price REAL NOT NULL,
    p25_price REAL NOT NULL,
    p50_price REAL NOT NULL,
    p75_price REAL NOT NULL,
    p90_price REAL NOT NULL,
    p95_price REAL NOT NULL,
    free_model_percentage REAL NOT NULL,
    total_models INTEGER NOT NULL,
    currency TEXT NOT NULL DEFAULT 'USD',
    trend_direction TEXT NOT NULL DEFAULT 'stable', -- increasing, decreasing, stable
    trend_percentage REAL NOT NULL DEFAULT 0,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(platform, category, period_start, period_end)
);

-- Market insights and recommendations
CREATE TABLE IF NOT EXISTS market_insights (
    id TEXT PRIMARY KEY,
    type TEXT NOT NULL, -- pricing, category, tag, format, opportunity
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    platform TEXT,
    category TEXT,
    confidence REAL NOT NULL,
    impact TEXT NOT NULL, -- high, medium, low
    actionable BOOLEAN NOT NULL DEFAULT true,
    recommendation TEXT NOT NULL,
    supporting_data TEXT, -- JSON object
    valid_until TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'active', -- active, expired, dismissed
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Alert rules configuration
CREATE TABLE IF NOT EXISTS alert_rules (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    rule_type TEXT NOT NULL, -- price_change, new_competitor, trend_shift, opportunity
    platform TEXT,
    category TEXT,
    conditions TEXT NOT NULL, -- JSON object with rule conditions
    notification_channels TEXT NOT NULL, -- JSON array: email, websocket, webhook
    frequency TEXT NOT NULL DEFAULT 'immediate', -- immediate, daily, weekly
    severity TEXT NOT NULL DEFAULT 'medium', -- low, medium, high, critical
    enabled BOOLEAN NOT NULL DEFAULT true,
    last_triggered TEXT,
    trigger_count INTEGER NOT NULL DEFAULT 0,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Alert notifications log
CREATE TABLE IF NOT EXISTS alert_notifications (
    id TEXT PRIMARY KEY,
    alert_rule_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    severity TEXT NOT NULL,
    channel TEXT NOT NULL, -- email, websocket, webhook
    data TEXT, -- JSON object with alert data
    status TEXT NOT NULL DEFAULT 'sent', -- sent, delivered, failed, acknowledged
    sent_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    acknowledged_at TEXT,
    FOREIGN KEY (alert_rule_id) REFERENCES alert_rules (id)
);

-- Analytics snapshots for performance tracking
CREATE TABLE IF NOT EXISTS analytics_snapshots (
    id TEXT PRIMARY KEY,
    snapshot_type TEXT NOT NULL, -- daily, weekly, monthly
    platform TEXT,
    category TEXT,
    period_start TEXT NOT NULL,
    period_end TEXT NOT NULL,
    metrics TEXT NOT NULL, -- JSON object with calculated metrics
    market_share_data TEXT, -- JSON object with market share analysis
    roi_data TEXT, -- JSON object with ROI calculations
    performance_data TEXT, -- JSON object with performance metrics
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(snapshot_type, platform, category, period_start)
);

-- Seller performance tracking
CREATE TABLE IF NOT EXISTS seller_performance (
    id TEXT PRIMARY KEY,
    seller_id TEXT NOT NULL,
    platform TEXT NOT NULL,
    category TEXT,
    period_start TEXT NOT NULL,
    period_end TEXT NOT NULL,
    models_count INTEGER NOT NULL DEFAULT 0,
    total_revenue REAL NOT NULL DEFAULT 0,
    total_downloads INTEGER NOT NULL DEFAULT 0,
    average_price REAL NOT NULL DEFAULT 0,
    market_share REAL NOT NULL DEFAULT 0,
    roi_percentage REAL NOT NULL DEFAULT 0,
    ranking_position INTEGER,
    performance_score REAL NOT NULL DEFAULT 0,
    recommendations TEXT, -- JSON array of recommendations
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(seller_id, platform, category, period_start)
);

-- Price change events for real-time monitoring
CREATE TABLE IF NOT EXISTS price_change_events (
    id TEXT PRIMARY KEY,
    platform TEXT NOT NULL,
    model_id TEXT NOT NULL,
    old_price REAL NOT NULL,
    new_price REAL NOT NULL,
    price_change_percentage REAL NOT NULL,
    category TEXT NOT NULL,
    creator_name TEXT,
    change_type TEXT NOT NULL, -- increase, decrease
    significance TEXT NOT NULL, -- minor, moderate, major
    detected_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Market opportunity scores
CREATE TABLE IF NOT EXISTS market_opportunities (
    id TEXT PRIMARY KEY,
    platform TEXT NOT NULL,
    category TEXT NOT NULL,
    subcategory TEXT,
    opportunity_type TEXT NOT NULL, -- price_gap, low_competition, high_demand, emerging_trend
    score REAL NOT NULL, -- 0-100 opportunity score
    confidence REAL NOT NULL, -- 0-1 confidence level
    description TEXT NOT NULL,
    recommended_action TEXT NOT NULL,
    supporting_metrics TEXT, -- JSON object
    valid_until TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'active',
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(platform, category, opportunity_type, created_at)
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_competitor_models_platform_category ON competitor_models_history(platform, category);
CREATE INDEX IF NOT EXISTS idx_competitor_models_scraped_at ON competitor_models_history(scraped_at);
CREATE INDEX IF NOT EXISTS idx_pricing_trends_platform_category ON pricing_trends(platform, category);
CREATE INDEX IF NOT EXISTS idx_pricing_trends_period ON pricing_trends(period_start, period_end);
CREATE INDEX IF NOT EXISTS idx_market_insights_type_platform ON market_insights(type, platform);
CREATE INDEX IF NOT EXISTS idx_market_insights_status ON market_insights(status);
CREATE INDEX IF NOT EXISTS idx_alert_rules_user_enabled ON alert_rules(user_id, enabled);
CREATE INDEX IF NOT EXISTS idx_alert_notifications_user_status ON alert_notifications(user_id, status);
CREATE INDEX IF NOT EXISTS idx_analytics_snapshots_type_period ON analytics_snapshots(snapshot_type, period_start);
CREATE INDEX IF NOT EXISTS idx_seller_performance_seller_period ON seller_performance(seller_id, period_start);
CREATE INDEX IF NOT EXISTS idx_price_change_events_platform_detected ON price_change_events(platform, detected_at);
CREATE INDEX IF NOT EXISTS idx_market_opportunities_platform_score ON market_opportunities(platform, score DESC);

-- Views for common queries
CREATE VIEW IF NOT EXISTS latest_competitor_models AS
SELECT DISTINCT 
    platform,
    model_id,
    title,
    price,
    category,
    tags,
    creator_name,
    scraped_at,
    ROW_NUMBER() OVER (PARTITION BY platform, model_id ORDER BY scraped_at DESC) as rn
FROM competitor_models_history
WHERE rn = 1;

CREATE VIEW IF NOT EXISTS current_pricing_trends AS
SELECT 
    platform,
    category,
    average_price,
    median_price,
    trend_direction,
    trend_percentage,
    total_models,
    created_at
FROM pricing_trends
WHERE created_at >= date('now', '-7 days')
ORDER BY created_at DESC;

CREATE VIEW IF NOT EXISTS active_market_insights AS
SELECT 
    type,
    title,
    description,
    platform,
    category,
    confidence,
    impact,
    recommendation,
    created_at
FROM market_insights
WHERE status = 'active' 
    AND valid_until > datetime('now')
ORDER BY confidence DESC, impact DESC;
