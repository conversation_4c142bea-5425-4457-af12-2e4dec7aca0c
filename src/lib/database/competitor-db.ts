/**
 * Database service for competitor intelligence data
 */

import { CompetitorModel, PricingTrend, MarketInsight } from '@/types/competitor-models';

export interface DatabaseConfig {
  database: D1Database;
}

export interface HistoricalModel extends CompetitorModel {
  createdAt: string;
}

export interface PriceChangeEvent {
  id: string;
  platform: string;
  modelId: string;
  oldPrice: number;
  newPrice: number;
  priceChangePercentage: number;
  category: string;
  creatorName?: string;
  changeType: 'increase' | 'decrease';
  significance: 'minor' | 'moderate' | 'major';
  detectedAt: string;
}

export interface MarketOpportunity {
  id: string;
  platform: string;
  category: string;
  subcategory?: string;
  opportunityType: 'price_gap' | 'low_competition' | 'high_demand' | 'emerging_trend';
  score: number; // 0-100
  confidence: number; // 0-1
  description: string;
  recommendedAction: string;
  supportingMetrics: Record<string, any>;
  validUntil: string;
  status: 'active' | 'expired' | 'dismissed';
  createdAt: string;
}

export class CompetitorDatabase {
  private db: D1Database;

  constructor(config: DatabaseConfig) {
    this.db = config.database;
  }

  /**
   * Store competitor models with historical tracking
   */
  async storeCompetitorModels(models: CompetitorModel[]): Promise<void> {
    if (models.length === 0) return;

    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO competitor_models_history (
        id, platform, model_id, title, description, price, currency, is_free,
        category, subcategory, tags, formats, download_count, view_count,
        like_count, rating, review_count, creator_name, creator_profile,
        thumbnail_url, model_url, upload_date, file_size, polygon_count,
        texture_resolution, is_animated, is_rigged, license, scraped_at, data_quality
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const batch = models.map(model => 
      stmt.bind(
        model.id,
        model.platform,
        model.id.split('_')[1] || model.id, // Extract model_id from full id
        model.title,
        model.description || null,
        model.price,
        model.currency,
        model.isFree ? 1 : 0,
        model.category,
        model.subcategory || null,
        JSON.stringify(model.tags),
        JSON.stringify(model.formats),
        model.downloadCount || null,
        model.viewCount || null,
        model.likeCount || null,
        model.rating || null,
        model.reviewCount || null,
        model.creatorName,
        model.creatorProfile || null,
        model.thumbnailUrl || null,
        model.modelUrl,
        model.uploadDate || null,
        model.fileSize || null,
        model.polygonCount || null,
        model.textureResolution || null,
        model.isAnimated ? 1 : 0,
        model.isRigged ? 1 : 0,
        model.license || null,
        model.scrapedAt,
        model.dataQuality
      )
    );

    await this.db.batch(batch);
    console.log(`📊 Stored ${models.length} competitor models in database`);
  }

  /**
   * Store pricing trends
   */
  async storePricingTrends(trends: PricingTrend[]): Promise<void> {
    if (trends.length === 0) return;

    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO pricing_trends (
        id, platform, category, subcategory, period_start, period_end,
        average_price, median_price, min_price, max_price,
        p25_price, p50_price, p75_price, p90_price, p95_price,
        free_model_percentage, total_models, currency,
        trend_direction, trend_percentage
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const batch = trends.map(trend =>
      stmt.bind(
        trend.id,
        trend.platform,
        trend.category,
        trend.subcategory || null,
        trend.periodStart,
        trend.periodEnd,
        trend.averagePrice,
        trend.medianPrice,
        trend.minPrice,
        trend.maxPrice,
        trend.pricePercentiles.p25,
        trend.pricePercentiles.p50,
        trend.pricePercentiles.p75,
        trend.pricePercentiles.p90,
        trend.pricePercentiles.p95,
        trend.freeModelPercentage,
        trend.totalModels,
        trend.currency,
        trend.trend,
        trend.trendPercentage
      )
    );

    await this.db.batch(batch);
    console.log(`📈 Stored ${trends.length} pricing trends in database`);
  }

  /**
   * Store market insights
   */
  async storeMarketInsights(insights: MarketInsight[]): Promise<void> {
    if (insights.length === 0) return;

    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO market_insights (
        id, type, title, description, platform, category,
        confidence, impact, actionable, recommendation,
        supporting_data, valid_until, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const batch = insights.map(insight =>
      stmt.bind(
        insight.id,
        insight.type,
        insight.title,
        insight.description,
        insight.platform || null,
        insight.category || null,
        insight.confidence,
        insight.impact,
        insight.actionable ? 1 : 0,
        insight.recommendation,
        JSON.stringify(insight.supportingData),
        insight.validUntil,
        'active'
      )
    );

    await this.db.batch(batch);
    console.log(`💡 Stored ${insights.length} market insights in database`);
  }

  /**
   * Get historical pricing data for trend analysis
   */
  async getHistoricalPricing(platform: string, category: string, days: number = 30): Promise<PricingTrend[]> {
    const result = await this.db.prepare(`
      SELECT * FROM pricing_trends
      WHERE platform = ? AND category = ?
        AND period_start >= date('now', '-' || ? || ' days')
      ORDER BY period_start DESC
    `).bind(platform, category, days).all();

    return result.results.map(row => ({
      id: row.id as string,
      platform: row.platform as string,
      category: row.category as string,
      subcategory: row.subcategory as string,
      periodStart: row.period_start as string,
      periodEnd: row.period_end as string,
      averagePrice: row.average_price as number,
      medianPrice: row.median_price as number,
      minPrice: row.min_price as number,
      maxPrice: row.max_price as number,
      pricePercentiles: {
        p25: row.p25_price as number,
        p50: row.p50_price as number,
        p75: row.p75_price as number,
        p90: row.p90_price as number,
        p95: row.p95_price as number
      },
      freeModelPercentage: row.free_model_percentage as number,
      totalModels: row.total_models as number,
      currency: row.currency as string,
      trend: row.trend_direction as 'increasing' | 'decreasing' | 'stable',
      trendPercentage: row.trend_percentage as number
    }));
  }

  /**
   * Detect price changes and store events
   */
  async detectPriceChanges(): Promise<PriceChangeEvent[]> {
    // Get models that have price changes in the last 24 hours
    const result = await this.db.prepare(`
      WITH price_changes AS (
        SELECT 
          h1.platform,
          h1.model_id,
          h1.price as new_price,
          h1.category,
          h1.creator_name,
          h1.scraped_at as new_scraped_at,
          h2.price as old_price,
          h2.scraped_at as old_scraped_at,
          ((h1.price - h2.price) / h2.price * 100) as price_change_percentage
        FROM competitor_models_history h1
        JOIN competitor_models_history h2 ON 
          h1.platform = h2.platform AND 
          h1.model_id = h2.model_id
        WHERE h1.scraped_at >= datetime('now', '-24 hours')
          AND h2.scraped_at < h1.scraped_at
          AND h1.price != h2.price
          AND h2.scraped_at = (
            SELECT MAX(scraped_at) 
            FROM competitor_models_history h3 
            WHERE h3.platform = h1.platform 
              AND h3.model_id = h1.model_id 
              AND h3.scraped_at < h1.scraped_at
          )
      )
      SELECT * FROM price_changes
      WHERE ABS(price_change_percentage) >= 5 -- Only significant changes
      ORDER BY ABS(price_change_percentage) DESC
    `).all();

    const events: PriceChangeEvent[] = result.results.map(row => ({
      id: `price_change_${row.platform}_${row.model_id}_${Date.now()}`,
      platform: row.platform as string,
      modelId: row.model_id as string,
      oldPrice: row.old_price as number,
      newPrice: row.new_price as number,
      priceChangePercentage: row.price_change_percentage as number,
      category: row.category as string,
      creatorName: row.creator_name as string,
      changeType: (row.price_change_percentage as number) > 0 ? 'increase' : 'decrease',
      significance: Math.abs(row.price_change_percentage as number) > 20 ? 'major' : 
                   Math.abs(row.price_change_percentage as number) > 10 ? 'moderate' : 'minor',
      detectedAt: new Date().toISOString()
    }));

    // Store the events
    if (events.length > 0) {
      const stmt = this.db.prepare(`
        INSERT OR IGNORE INTO price_change_events (
          id, platform, model_id, old_price, new_price, price_change_percentage,
          category, creator_name, change_type, significance, detected_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const batch = events.map(event =>
        stmt.bind(
          event.id,
          event.platform,
          event.modelId,
          event.oldPrice,
          event.newPrice,
          event.priceChangePercentage,
          event.category,
          event.creatorName || null,
          event.changeType,
          event.significance,
          event.detectedAt
        )
      );

      await this.db.batch(batch);
      console.log(`🚨 Detected and stored ${events.length} price change events`);
    }

    return events;
  }

  /**
   * Get market opportunities
   */
  async getMarketOpportunities(platform?: string, category?: string): Promise<MarketOpportunity[]> {
    let query = `
      SELECT * FROM market_opportunities
      WHERE status = 'active' AND valid_until > datetime('now')
    `;
    const params: any[] = [];

    if (platform) {
      query += ` AND platform = ?`;
      params.push(platform);
    }

    if (category) {
      query += ` AND category = ?`;
      params.push(category);
    }

    query += ` ORDER BY score DESC, confidence DESC`;

    const result = await this.db.prepare(query).bind(...params).all();

    return result.results.map(row => ({
      id: row.id as string,
      platform: row.platform as string,
      category: row.category as string,
      subcategory: row.subcategory as string,
      opportunityType: row.opportunity_type as any,
      score: row.score as number,
      confidence: row.confidence as number,
      description: row.description as string,
      recommendedAction: row.recommended_action as string,
      supportingMetrics: JSON.parse(row.supporting_metrics as string),
      validUntil: row.valid_until as string,
      status: row.status as any,
      createdAt: row.created_at as string
    }));
  }

  /**
   * Get active market insights
   */
  async getActiveMarketInsights(platform?: string, category?: string): Promise<MarketInsight[]> {
    let query = `
      SELECT * FROM market_insights
      WHERE status = 'active' AND valid_until > datetime('now')
    `;
    const params: any[] = [];

    if (platform) {
      query += ` AND platform = ?`;
      params.push(platform);
    }

    if (category) {
      query += ` AND category = ?`;
      params.push(category);
    }

    query += ` ORDER BY confidence DESC, impact DESC`;

    const result = await this.db.prepare(query).bind(...params).all();

    return result.results.map(row => ({
      id: row.id as string,
      type: row.type as any,
      title: row.title as string,
      description: row.description as string,
      platform: row.platform as string,
      category: row.category as string,
      confidence: row.confidence as number,
      impact: row.impact as any,
      actionable: Boolean(row.actionable),
      recommendation: row.recommendation as string,
      supportingData: JSON.parse(row.supporting_data as string),
      validUntil: row.valid_until as string,
      createdAt: row.created_at as string
    }));
  }

  /**
   * Clean up old data based on retention policy
   */
  async cleanupOldData(retentionDays: number = 90): Promise<void> {
    const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000).toISOString();

    // Clean up old competitor models
    await this.db.prepare(`
      DELETE FROM competitor_models_history 
      WHERE scraped_at < ?
    `).bind(cutoffDate).run();

    // Clean up old pricing trends
    await this.db.prepare(`
      DELETE FROM pricing_trends 
      WHERE created_at < ?
    `).bind(cutoffDate).run();

    // Clean up expired insights
    await this.db.prepare(`
      DELETE FROM market_insights 
      WHERE valid_until < datetime('now')
    `).run();

    // Clean up old price change events
    await this.db.prepare(`
      DELETE FROM price_change_events 
      WHERE detected_at < ?
    `).bind(cutoffDate).run();

    console.log(`🧹 Cleaned up data older than ${retentionDays} days`);
  }

  /**
   * Get database statistics
   */
  async getStatistics(): Promise<Record<string, number>> {
    const stats: Record<string, number> = {};

    const tables = [
      'competitor_models_history',
      'pricing_trends', 
      'market_insights',
      'alert_rules',
      'alert_notifications',
      'analytics_snapshots',
      'seller_performance',
      'price_change_events',
      'market_opportunities'
    ];

    for (const table of tables) {
      const result = await this.db.prepare(`SELECT COUNT(*) as count FROM ${table}`).first();
      stats[table] = result?.count as number || 0;
    }

    return stats;
  }
}
