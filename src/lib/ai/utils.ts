/**
 * AI Utilities - Допоміжні функції для AI системи
 */

import { getDb } from '@/lib/db';
import { UserPreferences, NotificationPreferences } from './types';

/**
 * Генерує унікальний ID
 */
export function generateId(prefix: string = ''): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return prefix ? `${prefix}_${timestamp}_${random}` : `${timestamp}_${random}`;
}

/**
 * Нормалізує значення до діапазону 0-1
 */
export function normalize(value: number, min: number, max: number): number {
  if (max === min) return 0;
  return Math.max(0, Math.min(1, (value - min) / (max - min)));
}

/**
 * Розраховує зважене середнє
 */
export function weightedAverage(values: Array<{ value: number; weight: number }>): number {
  const totalWeight = values.reduce((sum, item) => sum + item.weight, 0);
  if (totalWeight === 0) return 0;
  
  const weightedSum = values.reduce((sum, item) => sum + (item.value * item.weight), 0);
  return weightedSum / totalWeight;
}

/**
 * Розраховує коефіцієнт кореляції Пірсона
 */
export function pearsonCorrelation(x: number[], y: number[]): number {
  if (x.length !== y.length || x.length === 0) return 0;

  const n = x.length;
  const sumX = x.reduce((a, b) => a + b, 0);
  const sumY = y.reduce((a, b) => a + b, 0);
  const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
  const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
  const sumYY = y.reduce((sum, yi) => sum + yi * yi, 0);

  const numerator = n * sumXY - sumX * sumY;
  const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));

  return denominator === 0 ? 0 : numerator / denominator;
}

/**
 * Розраховує експоненціальне згладжування
 */
export function exponentialSmoothing(data: number[], alpha: number = 0.3): number[] {
  if (data.length === 0) return [];
  
  const result = [data[0]];
  for (let i = 1; i < data.length; i++) {
    result[i] = alpha * data[i] + (1 - alpha) * result[i - 1];
  }
  return result;
}

/**
 * Знаходить викиди в даних
 */
export function findOutliers(data: number[], threshold: number = 2): number[] {
  if (data.length < 3) return [];

  const mean = data.reduce((a, b) => a + b, 0) / data.length;
  const variance = data.reduce((sum, x) => sum + Math.pow(x - mean, 2), 0) / data.length;
  const stdDev = Math.sqrt(variance);

  return data.filter(x => Math.abs(x - mean) > threshold * stdDev);
}

/**
 * Розраховує ковзне середнє
 */
export function movingAverage(data: number[], window: number): number[] {
  if (data.length < window) return [];

  const result: number[] = [];
  for (let i = window - 1; i < data.length; i++) {
    const sum = data.slice(i - window + 1, i + 1).reduce((a, b) => a + b, 0);
    result.push(sum / window);
  }
  return result;
}

/**
 * Класифікує тренд на основі даних
 */
export function classifyTrend(data: number[]): 'rising' | 'falling' | 'stable' {
  if (data.length < 2) return 'stable';

  const firstHalf = data.slice(0, Math.floor(data.length / 2));
  const secondHalf = data.slice(Math.floor(data.length / 2));

  const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
  const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;

  const change = (secondAvg - firstAvg) / firstAvg;

  if (Math.abs(change) < 0.05) return 'stable'; // менше 5% зміни
  return change > 0 ? 'rising' : 'falling';
}

/**
 * Розраховує сезонний індекс
 */
export function calculateSeasonalIndex(data: Array<{ value: number; period: number }>): Record<number, number> {
  const periodGroups: Record<number, number[]> = {};
  
  data.forEach(item => {
    if (!periodGroups[item.period]) {
      periodGroups[item.period] = [];
    }
    periodGroups[item.period].push(item.value);
  });

  const overallAverage = data.reduce((sum, item) => sum + item.value, 0) / data.length;
  const seasonalIndex: Record<number, number> = {};

  Object.keys(periodGroups).forEach(period => {
    const periodData = periodGroups[parseInt(period)];
    const periodAverage = periodData.reduce((a, b) => a + b, 0) / periodData.length;
    seasonalIndex[parseInt(period)] = periodAverage / overallAverage;
  });

  return seasonalIndex;
}

/**
 * Валідує та нормалізує користувацькі переваги
 */
export function validateUserPreferences(preferences: Partial<UserPreferences>): UserPreferences {
  const defaultNotifications: NotificationPreferences = {
    email: true,
    push: true,
    sms: false,
    opportunities: true,
    priceAlerts: true,
    marketUpdates: false
  };

  return {
    id: preferences.id || generateId('pref'),
    userId: preferences.userId || '',
    preferredCategories: preferences.preferredCategories || [],
    priceRangeMin: Math.max(0, preferences.priceRangeMin || 0),
    priceRangeMax: Math.max(preferences.priceRangeMin || 0, preferences.priceRangeMax || 1000),
    riskTolerance: ['low', 'medium', 'high'].includes(preferences.riskTolerance || '') 
      ? preferences.riskTolerance as 'low' | 'medium' | 'high' 
      : 'medium',
    investmentBudget: Math.max(0, preferences.investmentBudget || 0),
    autoTradingEnabled: preferences.autoTradingEnabled || false,
    notificationPreferences: { ...defaultNotifications, ...preferences.notificationPreferences },
    tradingFrequency: ['daily', 'weekly', 'monthly'].includes(preferences.tradingFrequency || '')
      ? preferences.tradingFrequency as 'daily' | 'weekly' | 'monthly'
      : 'weekly',
    profitTarget: Math.max(0.05, Math.min(2.0, preferences.profitTarget || 0.2)), // 5% - 200%
    createdAt: preferences.createdAt || new Date(),
    updatedAt: new Date()
  };
}

/**
 * Форматує числа для відображення
 */
export function formatCurrency(amount: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('uk-UA', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
}

export function formatPercentage(value: number, decimals: number = 1): string {
  return `${(value * 100).toFixed(decimals)}%`;
}

export function formatNumber(value: number, decimals: number = 0): string {
  return new Intl.NumberFormat('uk-UA', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(value);
}

/**
 * Розраховує час до закінчення
 */
export function getTimeUntilExpiry(expiryDate: Date): {
  days: number;
  hours: number;
  minutes: number;
  isExpired: boolean;
} {
  const now = new Date();
  const diff = expiryDate.getTime() - now.getTime();

  if (diff <= 0) {
    return { days: 0, hours: 0, minutes: 0, isExpired: true };
  }

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  return { days, hours, minutes, isExpired: false };
}

/**
 * Створює дефолтні користувацькі переваги
 */
export async function createDefaultUserPreferences(userId: string): Promise<void> {
  const db = getDb();
  
  const preferences = validateUserPreferences({ userId });
  
  const query = `
    INSERT INTO user_preferences (
      id, user_id, preferred_categories, price_range_min, price_range_max,
      risk_tolerance, investment_budget, auto_trading_enabled,
      notification_preferences, trading_frequency, profit_target
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  await db.prepare(query).bind(
    preferences.id,
    preferences.userId,
    JSON.stringify(preferences.preferredCategories),
    preferences.priceRangeMin,
    preferences.priceRangeMax,
    preferences.riskTolerance,
    preferences.investmentBudget,
    preferences.autoTradingEnabled,
    JSON.stringify(preferences.notificationPreferences),
    preferences.tradingFrequency,
    preferences.profitTarget
  ).run();
}

/**
 * Логування AI операцій
 */
export function logAIOperation(operation: string, data: any, success: boolean): void {
  const logEntry = {
    timestamp: new Date().toISOString(),
    operation,
    success,
    data: success ? 'success' : data?.error || 'unknown error'
  };

  // В продакшені це можна відправляти в систему логування
  console.log('[AI Operation]', logEntry);
}

/**
 * Кешування результатів AI
 */
const aiCache = new Map<string, { data: any; timestamp: number; ttl: number }>();

export function getCachedResult(key: string): any | null {
  const cached = aiCache.get(key);
  if (!cached) return null;

  if (Date.now() - cached.timestamp > cached.ttl) {
    aiCache.delete(key);
    return null;
  }

  return cached.data;
}

export function setCachedResult(key: string, data: any, ttlMinutes: number = 30): void {
  aiCache.set(key, {
    data,
    timestamp: Date.now(),
    ttl: ttlMinutes * 60 * 1000
  });
}

/**
 * Очищення застарілих записів з кешу
 */
export function cleanupCache(): void {
  const now = Date.now();
  for (const [key, value] of aiCache.entries()) {
    if (now - value.timestamp > value.ttl) {
      aiCache.delete(key);
    }
  }
}

// Автоматичне очищення кешу кожні 30 хвилин
if (typeof setInterval !== 'undefined') {
  setInterval(cleanupCache, 30 * 60 * 1000);
}
