/**
 * AI/ML Prediction Engine for Market Intelligence
 */

import { PricingTrend, MarketOpportunity } from '@/types/competitor-models';

export interface PricePrediction {
  platform: string;
  category: string;
  currentPrice: number;
  predictedPrice: number;
  priceChange: number;
  priceChangePercentage: number;
  confidence: number;
  timeframe: string; // '1week', '1month', '3months'
  factors: string[];
}

export interface TrendForecast {
  platform: string;
  category: string;
  currentTrend: 'increasing' | 'decreasing' | 'stable';
  predictedTrend: 'increasing' | 'decreasing' | 'stable';
  trendStrength: number; // 0-1
  confidence: number;
  timeframe: string;
  drivingFactors: string[];
}

export interface MarketOpportunityScore {
  platform: string;
  category: string;
  subcategory?: string;
  opportunityScore: number; // 0-100
  competitionLevel: 'low' | 'medium' | 'high';
  demandLevel: 'low' | 'medium' | 'high';
  priceGapOpportunity: number;
  growthPotential: number;
  riskLevel: 'low' | 'medium' | 'high';
  recommendedAction: string;
  confidence: number;
}

export class PredictionEngine {
  /**
   * Predict future prices using linear regression and trend analysis
   */
  predictPrices(historicalTrends: PricingTrend[], timeframe: string = '1month'): PricePrediction[] {
    const predictions: PricePrediction[] = [];

    // Group trends by platform and category
    const groupedTrends = this.groupTrendsByPlatformCategory(historicalTrends);

    Object.entries(groupedTrends).forEach(([key, trends]) => {
      const [platform, category] = key.split('|');
      
      if (trends.length < 3) return; // Need at least 3 data points

      const prediction = this.calculatePricePrediction(trends, platform, category, timeframe);
      if (prediction) {
        predictions.push(prediction);
      }
    });

    return predictions.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Forecast market trends using time series analysis
   */
  forecastTrends(historicalTrends: PricingTrend[], timeframe: string = '1month'): TrendForecast[] {
    const forecasts: TrendForecast[] = [];

    const groupedTrends = this.groupTrendsByPlatformCategory(historicalTrends);

    Object.entries(groupedTrends).forEach(([key, trends]) => {
      const [platform, category] = key.split('|');
      
      if (trends.length < 4) return; // Need more data for trend analysis

      const forecast = this.calculateTrendForecast(trends, platform, category, timeframe);
      if (forecast) {
        forecasts.push(forecast);
      }
    });

    return forecasts.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Score market opportunities using multiple factors
   */
  scoreMarketOpportunities(
    historicalTrends: PricingTrend[],
    currentMarketData: any[]
  ): MarketOpportunityScore[] {
    const opportunities: MarketOpportunityScore[] = [];

    const groupedTrends = this.groupTrendsByPlatformCategory(historicalTrends);

    Object.entries(groupedTrends).forEach(([key, trends]) => {
      const [platform, category] = key.split('|');
      
      const opportunity = this.calculateOpportunityScore(trends, platform, category);
      if (opportunity) {
        opportunities.push(opportunity);
      }
    });

    return opportunities.sort((a, b) => b.opportunityScore - a.opportunityScore);
  }

  /**
   * Predict optimal pricing strategy
   */
  predictOptimalPricing(
    category: string,
    platform: string,
    modelQuality: 'low' | 'medium' | 'high',
    historicalTrends: PricingTrend[]
  ): {
    recommendedPrice: number;
    priceRange: { min: number; max: number };
    strategy: 'premium' | 'competitive' | 'budget' | 'freemium';
    expectedMarketShare: number;
    confidence: number;
    reasoning: string[];
  } {
    const relevantTrends = historicalTrends.filter(
      t => t.platform === platform && t.category === category
    );

    if (relevantTrends.length === 0) {
      return this.getDefaultPricingStrategy(modelQuality);
    }

    const latestTrend = relevantTrends[0];
    const avgPrice = latestTrend.averagePrice;
    const medianPrice = latestTrend.medianPrice;
    const freePercentage = latestTrend.freeModelPercentage;

    // Calculate optimal price based on quality and market conditions
    let recommendedPrice: number;
    let strategy: 'premium' | 'competitive' | 'budget' | 'freemium';
    let expectedMarketShare: number;
    const reasoning: string[] = [];

    if (freePercentage > 70) {
      strategy = 'freemium';
      recommendedPrice = 0;
      expectedMarketShare = 25;
      reasoning.push('High free model percentage suggests freemium strategy');
    } else if (modelQuality === 'high' && avgPrice > 30) {
      strategy = 'premium';
      recommendedPrice = latestTrend.pricePercentiles.p90;
      expectedMarketShare = 5;
      reasoning.push('High quality model in premium market');
    } else if (modelQuality === 'low' || avgPrice < 10) {
      strategy = 'budget';
      recommendedPrice = latestTrend.pricePercentiles.p25;
      expectedMarketShare = 20;
      reasoning.push('Budget strategy for competitive pricing');
    } else {
      strategy = 'competitive';
      recommendedPrice = medianPrice;
      expectedMarketShare = 15;
      reasoning.push('Competitive pricing for balanced market position');
    }

    // Adjust for trend direction
    if (latestTrend.trend === 'increasing') {
      recommendedPrice *= 1.1;
      reasoning.push('Adjusted upward for increasing price trend');
    } else if (latestTrend.trend === 'decreasing') {
      recommendedPrice *= 0.9;
      reasoning.push('Adjusted downward for decreasing price trend');
    }

    const priceRange = {
      min: recommendedPrice * 0.8,
      max: recommendedPrice * 1.2
    };

    const confidence = this.calculatePricingConfidence(relevantTrends.length, latestTrend);

    return {
      recommendedPrice: Math.round(recommendedPrice * 100) / 100,
      priceRange: {
        min: Math.round(priceRange.min * 100) / 100,
        max: Math.round(priceRange.max * 100) / 100
      },
      strategy,
      expectedMarketShare,
      confidence,
      reasoning
    };
  }

  // Private helper methods

  private groupTrendsByPlatformCategory(trends: PricingTrend[]): Record<string, PricingTrend[]> {
    const grouped: Record<string, PricingTrend[]> = {};

    trends.forEach(trend => {
      const key = `${trend.platform}|${trend.category}`;
      if (!grouped[key]) {
        grouped[key] = [];
      }
      grouped[key].push(trend);
    });

    // Sort each group by date
    Object.keys(grouped).forEach(key => {
      grouped[key].sort((a, b) => new Date(b.periodStart).getTime() - new Date(a.periodStart).getTime());
    });

    return grouped;
  }

  private calculatePricePrediction(
    trends: PricingTrend[],
    platform: string,
    category: string,
    timeframe: string
  ): PricePrediction | null {
    if (trends.length < 3) return null;

    // Simple linear regression on average prices
    const prices = trends.map(t => t.averagePrice);
    const dates = trends.map(t => new Date(t.periodStart).getTime());

    const { slope, intercept, r2 } = this.linearRegression(dates, prices);

    // Predict future price based on timeframe
    const timeframeMs = this.getTimeframeMs(timeframe);
    const futureDate = Date.now() + timeframeMs;
    const predictedPrice = slope * futureDate + intercept;

    const currentPrice = prices[0];
    const priceChange = predictedPrice - currentPrice;
    const priceChangePercentage = (priceChange / currentPrice) * 100;

    // Determine factors influencing the prediction
    const factors: string[] = [];
    if (Math.abs(slope) > 0.001) {
      factors.push(slope > 0 ? 'Upward price trend' : 'Downward price trend');
    }
    if (trends[0].totalModels > trends[trends.length - 1].totalModels) {
      factors.push('Increasing market supply');
    }

    return {
      platform,
      category,
      currentPrice,
      predictedPrice: Math.round(predictedPrice * 100) / 100,
      priceChange: Math.round(priceChange * 100) / 100,
      priceChangePercentage: Math.round(priceChangePercentage * 100) / 100,
      confidence: Math.min(r2, 0.95), // Cap confidence at 95%
      timeframe,
      factors
    };
  }

  private calculateTrendForecast(
    trends: PricingTrend[],
    platform: string,
    category: string,
    timeframe: string
  ): TrendForecast | null {
    if (trends.length < 4) return null;

    const currentTrend = trends[0].trend;
    
    // Analyze trend consistency
    const recentTrends = trends.slice(0, 4).map(t => t.trend);
    const trendCounts = {
      increasing: recentTrends.filter(t => t === 'increasing').length,
      decreasing: recentTrends.filter(t => t === 'decreasing').length,
      stable: recentTrends.filter(t => t === 'stable').length
    };

    // Predict future trend based on momentum
    let predictedTrend: 'increasing' | 'decreasing' | 'stable';
    let trendStrength: number;

    if (trendCounts.increasing >= 3) {
      predictedTrend = 'increasing';
      trendStrength = trendCounts.increasing / 4;
    } else if (trendCounts.decreasing >= 3) {
      predictedTrend = 'decreasing';
      trendStrength = trendCounts.decreasing / 4;
    } else {
      predictedTrend = 'stable';
      trendStrength = trendCounts.stable / 4;
    }

    // Calculate confidence based on trend consistency
    const confidence = Math.max(...Object.values(trendCounts)) / 4;

    // Identify driving factors
    const drivingFactors: string[] = [];
    const priceVolatility = this.calculatePriceVolatility(trends);
    if (priceVolatility > 0.2) {
      drivingFactors.push('High price volatility');
    }
    if (trends[0].totalModels > trends[trends.length - 1].totalModels * 1.2) {
      drivingFactors.push('Rapid market growth');
    }

    return {
      platform,
      category,
      currentTrend,
      predictedTrend,
      trendStrength,
      confidence,
      timeframe,
      drivingFactors
    };
  }

  private calculateOpportunityScore(
    trends: PricingTrend[],
    platform: string,
    category: string
  ): MarketOpportunityScore | null {
    if (trends.length === 0) return null;

    const latestTrend = trends[0];
    
    // Calculate various opportunity factors
    const competitionScore = this.calculateCompetitionLevel(latestTrend);
    const demandScore = this.calculateDemandLevel(trends);
    const priceGapScore = this.calculatePriceGapOpportunity(latestTrend);
    const growthScore = this.calculateGrowthPotential(trends);

    // Weighted opportunity score
    const opportunityScore = Math.round(
      (competitionScore * 0.3 + 
       demandScore * 0.25 + 
       priceGapScore * 0.25 + 
       growthScore * 0.2) * 100
    );

    const competitionLevel = competitionScore > 0.7 ? 'high' : competitionScore > 0.4 ? 'medium' : 'low';
    const demandLevel = demandScore > 0.7 ? 'high' : demandScore > 0.4 ? 'medium' : 'low';
    const riskLevel = competitionLevel === 'high' ? 'high' : demandLevel === 'low' ? 'high' : 'medium';

    let recommendedAction = 'Monitor market conditions';
    if (opportunityScore > 75) {
      recommendedAction = 'High opportunity - enter market aggressively';
    } else if (opportunityScore > 50) {
      recommendedAction = 'Moderate opportunity - test market entry';
    }

    const confidence = Math.min(trends.length / 10, 0.9); // More data = higher confidence

    return {
      platform,
      category,
      opportunityScore,
      competitionLevel: competitionLevel as any,
      demandLevel: demandLevel as any,
      priceGapOpportunity: Math.round(priceGapScore * 100),
      growthPotential: Math.round(growthScore * 100),
      riskLevel: riskLevel as any,
      recommendedAction,
      confidence
    };
  }

  private linearRegression(x: number[], y: number[]): { slope: number; intercept: number; r2: number } {
    const n = x.length;
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
    const sumYY = y.reduce((sum, yi) => sum + yi * yi, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    // Calculate R-squared
    const yMean = sumY / n;
    const ssRes = y.reduce((sum, yi, i) => {
      const predicted = slope * x[i] + intercept;
      return sum + Math.pow(yi - predicted, 2);
    }, 0);
    const ssTot = y.reduce((sum, yi) => sum + Math.pow(yi - yMean, 2), 0);
    const r2 = 1 - (ssRes / ssTot);

    return { slope, intercept, r2: Math.max(0, r2) };
  }

  private getTimeframeMs(timeframe: string): number {
    const timeframes: Record<string, number> = {
      '1week': 7 * 24 * 60 * 60 * 1000,
      '1month': 30 * 24 * 60 * 60 * 1000,
      '3months': 90 * 24 * 60 * 60 * 1000,
      '6months': 180 * 24 * 60 * 60 * 1000
    };
    return timeframes[timeframe] || timeframes['1month'];
  }

  private calculatePriceVolatility(trends: PricingTrend[]): number {
    if (trends.length < 2) return 0;
    
    const prices = trends.map(t => t.averagePrice);
    const mean = prices.reduce((a, b) => a + b, 0) / prices.length;
    const variance = prices.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / prices.length;
    const stdDev = Math.sqrt(variance);
    
    return stdDev / mean; // Coefficient of variation
  }

  private calculateCompetitionLevel(trend: PricingTrend): number {
    // Higher model count = higher competition
    const modelCountScore = Math.min(trend.totalModels / 1000, 1);
    
    // Lower free percentage = higher competition for paid models
    const freePercentageScore = (100 - trend.freeModelPercentage) / 100;
    
    return (modelCountScore + freePercentageScore) / 2;
  }

  private calculateDemandLevel(trends: PricingTrend[]): number {
    if (trends.length < 2) return 0.5;
    
    // Increasing model count over time indicates demand
    const growthRate = (trends[0].totalModels - trends[trends.length - 1].totalModels) / trends[trends.length - 1].totalModels;
    
    return Math.min(Math.max(growthRate, 0), 1);
  }

  private calculatePriceGapOpportunity(trend: PricingTrend): number {
    // Large gap between percentiles indicates opportunity
    const priceGap = trend.pricePercentiles.p75 - trend.pricePercentiles.p25;
    const relativeGap = priceGap / trend.medianPrice;
    
    return Math.min(relativeGap / 2, 1); // Normalize to 0-1
  }

  private calculateGrowthPotential(trends: PricingTrend[]): number {
    if (trends.length < 3) return 0.5;
    
    // Analyze price trend direction and strength
    const recentTrend = trends[0].trend;
    const trendPercentage = Math.abs(trends[0].trendPercentage);
    
    if (recentTrend === 'increasing') {
      return Math.min(trendPercentage / 20, 1); // Normalize to 0-1
    } else if (recentTrend === 'stable') {
      return 0.5;
    } else {
      return Math.max(1 - trendPercentage / 20, 0);
    }
  }

  private getDefaultPricingStrategy(modelQuality: 'low' | 'medium' | 'high') {
    const strategies = {
      low: { price: 5, strategy: 'budget' as const, share: 20 },
      medium: { price: 15, strategy: 'competitive' as const, share: 15 },
      high: { price: 35, strategy: 'premium' as const, share: 8 }
    };

    const config = strategies[modelQuality];
    
    return {
      recommendedPrice: config.price,
      priceRange: { min: config.price * 0.8, max: config.price * 1.2 },
      strategy: config.strategy,
      expectedMarketShare: config.share,
      confidence: 0.3, // Low confidence without data
      reasoning: ['Default strategy based on model quality', 'Limited market data available']
    };
  }

  private calculatePricingConfidence(dataPoints: number, trend: PricingTrend): number {
    let confidence = Math.min(dataPoints / 10, 0.8); // More data points = higher confidence
    
    // Adjust based on sample size
    if (trend.totalModels > 100) {
      confidence += 0.1;
    }
    
    // Adjust based on price stability
    const priceRange = trend.maxPrice - trend.minPrice;
    const priceStability = 1 - (priceRange / trend.averagePrice);
    confidence *= priceStability;
    
    return Math.min(Math.max(confidence, 0.1), 0.95);
  }
}
