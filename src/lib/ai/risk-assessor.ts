/**
 * Risk Assessor - AI сервіс для оцінки ризиків арбітражних операцій
 */

import { getDb } from '@/lib/db';
import { AIAnalysisResult } from './types';

export interface RiskAssessmentInput {
  modelId: string;
  currentPrice: number;
  suggestedPrice: number;
  category: string;
  marketData: any;
}

export interface RiskAssessmentResult {
  level: 'low' | 'medium' | 'high';
  score: number; // 0-1, де 1 = найвищий ризик
  factors: RiskFactor[];
  recommendations: string[];
}

export interface RiskFactor {
  type: string;
  impact: 'low' | 'medium' | 'high';
  description: string;
  score: number;
}

export class RiskAssessor {
  private db = getDb();

  /**
   * Оцінює ризик арбітражної можливості
   */
  async assessOpportunityRisk(input: RiskAssessmentInput): Promise<RiskAssessmentResult> {
    const factors: RiskFactor[] = [];

    // Аналізуємо різні типи ризиків
    factors.push(...await this.assessPriceVolatilityRisk(input));
    factors.push(...await this.assessMarketLiquidityRisk(input));
    factors.push(...await this.assessCompetitionRisk(input));
    factors.push(...await this.assessDemandStabilityRisk(input));
    factors.push(...await this.assessSeasonalityRisk(input));

    // Розраховуємо загальний ризик
    const totalScore = this.calculateTotalRiskScore(factors);
    const level = this.determineRiskLevel(totalScore);
    const recommendations = this.generateRecommendations(factors, level);

    return {
      level,
      score: totalScore,
      factors,
      recommendations
    };
  }

  /**
   * Оцінює ризик волатильності цін
   */
  private async assessPriceVolatilityRisk(input: RiskAssessmentInput): Promise<RiskFactor[]> {
    const query = `
      SELECT price, recorded_at
      FROM price_history ph
      JOIN models m ON ph.model_id = m.id
      WHERE m.category = ? AND ph.recorded_at >= datetime('now', '-30 days')
      ORDER BY ph.recorded_at
    `;

    const priceHistory = await this.db.prepare(query).bind(input.category).all<any>();
    
    if (priceHistory.length < 5) {
      return [{
        type: 'price_volatility',
        impact: 'medium',
        description: 'Недостатньо історичних даних для оцінки волатильності',
        score: 0.5
      }];
    }

    const prices = priceHistory.map(p => p.price);
    const volatility = this.calculateVolatility(prices);
    const priceChange = Math.abs(input.suggestedPrice - input.currentPrice) / input.currentPrice;

    let impact: 'low' | 'medium' | 'high';
    let score: number;

    if (volatility > 0.3 || priceChange > 0.5) {
      impact = 'high';
      score = 0.8;
    } else if (volatility > 0.15 || priceChange > 0.25) {
      impact = 'medium';
      score = 0.5;
    } else {
      impact = 'low';
      score = 0.2;
    }

    return [{
      type: 'price_volatility',
      impact,
      description: `Волатільність цін: ${(volatility * 100).toFixed(1)}%, зміна ціни: ${(priceChange * 100).toFixed(1)}%`,
      score
    }];
  }

  /**
   * Оцінює ризик ліквідності ринку
   */
  private async assessMarketLiquidityRisk(input: RiskAssessmentInput): Promise<RiskFactor[]> {
    const query = `
      SELECT 
        COUNT(*) as total_models,
        SUM(download_count) as total_downloads,
        AVG(download_count) as avg_downloads
      FROM models 
      WHERE category = ? AND created_at >= datetime('now', '-30 days')
    `;

    const result = await this.db.prepare(query).bind(input.category).first<any>();
    
    const totalModels = result?.total_models || 0;
    const totalDownloads = result?.total_downloads || 0;
    const avgDownloads = result?.avg_downloads || 0;

    let impact: 'low' | 'medium' | 'high';
    let score: number;

    if (totalDownloads < 50 || avgDownloads < 2) {
      impact = 'high';
      score = 0.8;
    } else if (totalDownloads < 200 || avgDownloads < 5) {
      impact = 'medium';
      score = 0.5;
    } else {
      impact = 'low';
      score = 0.2;
    }

    return [{
      type: 'market_liquidity',
      impact,
      description: `Ліквідність ринку: ${totalDownloads} завантажень, ${avgDownloads.toFixed(1)} в середньому`,
      score
    }];
  }

  /**
   * Оцінює ризик конкуренції
   */
  private async assessCompetitionRisk(input: RiskAssessmentInput): Promise<RiskFactor[]> {
    const query = `
      SELECT 
        COUNT(*) as competitor_count,
        AVG(price) as avg_price,
        MIN(price) as min_price,
        MAX(price) as max_price
      FROM models 
      WHERE category = ? AND id != ? AND price > 0
    `;

    const result = await this.db.prepare(query).bind(input.category, input.modelId).first<any>();
    
    const competitorCount = result?.competitor_count || 0;
    const avgPrice = result?.avg_price || input.currentPrice;
    const pricePosition = input.suggestedPrice / avgPrice;

    let impact: 'low' | 'medium' | 'high';
    let score: number;

    if (competitorCount > 50 && (pricePosition > 1.5 || pricePosition < 0.7)) {
      impact = 'high';
      score = 0.8;
    } else if (competitorCount > 20 && (pricePosition > 1.3 || pricePosition < 0.8)) {
      impact = 'medium';
      score = 0.5;
    } else {
      impact = 'low';
      score = 0.2;
    }

    return [{
      type: 'competition',
      impact,
      description: `Конкуренція: ${competitorCount} конкурентів, позиція ціни: ${(pricePosition * 100).toFixed(0)}% від середньої`,
      score
    }];
  }

  /**
   * Оцінює ризик стабільності попиту
   */
  private async assessDemandStabilityRisk(input: RiskAssessmentInput): Promise<RiskFactor[]> {
    const query = `
      SELECT 
        COUNT(CASE WHEN p.created_at >= datetime('now', '-7 days') THEN 1 END) as recent_purchases,
        COUNT(CASE WHEN p.created_at >= datetime('now', '-14 days') AND p.created_at < datetime('now', '-7 days') THEN 1 END) as previous_purchases,
        COUNT(CASE WHEN p.created_at >= datetime('now', '-30 days') THEN 1 END) as monthly_purchases
      FROM purchases p
      JOIN models m ON p.model_id = m.id
      WHERE m.category = ?
    `;

    const result = await this.db.prepare(query).bind(input.category).first<any>();
    
    const recent = result?.recent_purchases || 0;
    const previous = result?.previous_purchases || 1;
    const monthly = result?.monthly_purchases || 0;

    const demandChange = (recent - previous) / previous;
    const demandLevel = monthly / 30; // середня кількість покупок на день

    let impact: 'low' | 'medium' | 'high';
    let score: number;

    if (Math.abs(demandChange) > 0.5 || demandLevel < 1) {
      impact = 'high';
      score = 0.7;
    } else if (Math.abs(demandChange) > 0.2 || demandLevel < 3) {
      impact = 'medium';
      score = 0.4;
    } else {
      impact = 'low';
      score = 0.2;
    }

    return [{
      type: 'demand_stability',
      impact,
      description: `Стабільність попиту: зміна ${(demandChange * 100).toFixed(1)}%, рівень ${demandLevel.toFixed(1)} покупок/день`,
      score
    }];
  }

  /**
   * Оцінює сезонні ризики
   */
  private async assessSeasonalityRisk(input: RiskAssessmentInput): Promise<RiskFactor[]> {
    const currentMonth = new Date().getMonth();
    const seasonalFactors = this.getSeasonalFactors(input.category, currentMonth);

    let impact: 'low' | 'medium' | 'high';
    let score: number;

    if (seasonalFactors.volatility > 0.3) {
      impact = 'high';
      score = 0.6;
    } else if (seasonalFactors.volatility > 0.15) {
      impact = 'medium';
      score = 0.3;
    } else {
      impact = 'low';
      score = 0.1;
    }

    return [{
      type: 'seasonality',
      impact,
      description: `Сезонність: ${seasonalFactors.description}`,
      score
    }];
  }

  /**
   * Допоміжні методи
   */
  private calculateVolatility(prices: number[]): number {
    if (prices.length < 2) return 0;

    const mean = prices.reduce((a, b) => a + b, 0) / prices.length;
    const variance = prices.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / prices.length;
    
    return Math.sqrt(variance) / mean;
  }

  private calculateTotalRiskScore(factors: RiskFactor[]): number {
    if (factors.length === 0) return 0.5;

    // Зважений розрахунок ризику
    const weights = {
      price_volatility: 0.3,
      market_liquidity: 0.25,
      competition: 0.2,
      demand_stability: 0.15,
      seasonality: 0.1
    };

    let totalScore = 0;
    let totalWeight = 0;

    factors.forEach(factor => {
      const weight = weights[factor.type as keyof typeof weights] || 0.1;
      totalScore += factor.score * weight;
      totalWeight += weight;
    });

    return totalWeight > 0 ? totalScore / totalWeight : 0.5;
  }

  private determineRiskLevel(score: number): 'low' | 'medium' | 'high' {
    if (score >= 0.7) return 'high';
    if (score >= 0.4) return 'medium';
    return 'low';
  }

  private generateRecommendations(factors: RiskFactor[], level: 'low' | 'medium' | 'high'): string[] {
    const recommendations: string[] = [];

    if (level === 'high') {
      recommendations.push('Розгляньте зменшення розміру позиції');
      recommendations.push('Встановіть жорсткі стоп-лосси');
      recommendations.push('Моніторьте ринок більш уважно');
    }

    factors.forEach(factor => {
      switch (factor.type) {
        case 'price_volatility':
          if (factor.impact === 'high') {
            recommendations.push('Очікуйте значних коливань цін');
          }
          break;
        case 'market_liquidity':
          if (factor.impact === 'high') {
            recommendations.push('Можуть виникнути труднощі з продажем');
          }
          break;
        case 'competition':
          if (factor.impact === 'high') {
            recommendations.push('Висока конкуренція може вплинути на продажі');
          }
          break;
        case 'demand_stability':
          if (factor.impact === 'high') {
            recommendations.push('Нестабільний попит може вплинути на прибутковість');
          }
          break;
      }
    });

    // Загальні рекомендації
    if (level === 'low') {
      recommendations.push('Сприятливі умови для арбітражу');
    } else if (level === 'medium') {
      recommendations.push('Помірний ризик, слідкуйте за ринком');
    }

    return [...new Set(recommendations)]; // видаляємо дублікати
  }

  private getSeasonalFactors(category: string, month: number): { volatility: number; description: string } {
    // Спрощена логіка сезонності для різних категорій
    const seasonalPatterns: Record<string, Record<number, number>> = {
      'games': {
        11: 1.3, // листопад - підготовка до свят
        0: 1.2,  // січень - новорічні подарунки
        5: 0.8,  // червень - літні канікули
        6: 0.8,  // липень
        7: 0.9   // серпень
      },
      'architecture': {
        2: 1.2,  // березень - початок будівельного сезону
        3: 1.3,  // квітень
        4: 1.2,  // травень
        10: 0.8, // листопад - кінець сезону
        11: 0.7, // грудень
        0: 0.7   // січень
      }
    };

    const pattern = seasonalPatterns[category];
    if (!pattern) {
      return { volatility: 0.1, description: 'Низька сезонність' };
    }

    const currentFactor = pattern[month] || 1.0;
    const factors = Object.values(pattern);
    const avgFactor = factors.reduce((a, b) => a + b, 0) / factors.length;
    const volatility = Math.abs(currentFactor - avgFactor) / avgFactor;

    let description = 'Нормальний сезон';
    if (currentFactor > 1.1) description = 'Високий сезон';
    if (currentFactor < 0.9) description = 'Низький сезон';

    return { volatility, description };
  }
}
