/**
 * Opportunity Finder - AI сервіс для пошуку арбітражних можливостей
 */

import { getDb } from '@/lib/db';
import { ArbitrageOpportunity, OpportunityFilters, AIAnalysisResult } from './types';
import { MarketAnalyzer } from './market-analyzer';
import { PricePredictor } from './price-predictor';
import { RiskAssessor } from './risk-assessor';

export class OpportunityFinder {
  private db = getDb();
  private marketAnalyzer = new MarketAnalyzer();
  private pricePredictor = new PricePredictor();
  private riskAssessor = new RiskAssessor();

  /**
   * Знаходить арбітражні можливості
   */
  async findOpportunities(filters?: OpportunityFilters): Promise<AIAnalysisResult> {
    try {
      const opportunities: ArbitrageOpportunity[] = [];

      // Знаходимо можливості різних типів
      const priceGapOpportunities = await this.findPriceGapOpportunities(filters);
      const demandSpikeOpportunities = await this.findDemandSpikeOpportunities(filters);
      const trendOpportunities = await this.findTrendPredictionOpportunities(filters);

      opportunities.push(...priceGapOpportunities);
      opportunities.push(...demandSpikeOpportunities);
      opportunities.push(...trendOpportunities);

      // Сортуємо за потенційним прибутком та впевненістю
      opportunities.sort((a, b) => {
        const scoreA = a.potentialProfit * a.confidenceScore;
        const scoreB = b.potentialProfit * b.confidenceScore;
        return scoreB - scoreA;
      });

      // Зберігаємо знайдені можливості
      for (const opportunity of opportunities) {
        await this.saveOpportunity(opportunity);
      }

      return {
        success: true,
        data: opportunities,
        confidence: this.calculateOverallConfidence(opportunities),
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Opportunity finding error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        confidence: 0,
        timestamp: new Date()
      };
    }
  }

  /**
   * Знаходить можливості на основі розриву цін
   */
  private async findPriceGapOpportunities(filters?: OpportunityFilters): Promise<ArbitrageOpportunity[]> {
    const query = `
      SELECT 
        m.id as model_id,
        m.user_id as seller_id,
        m.name,
        m.category,
        m.price as current_price,
        m.download_count,
        m.view_count,
        AVG(ph.price) as avg_historical_price
      FROM models m
      LEFT JOIN price_history ph ON m.id = ph.model_id 
        AND ph.recorded_at >= datetime('now', '-30 days')
      WHERE m.price > 0
        ${filters?.category ? 'AND m.category = ?' : ''}
        ${filters?.priceRange ? 'AND m.price BETWEEN ? AND ?' : ''}
      GROUP BY m.id
      HAVING COUNT(ph.id) >= 5
      ORDER BY m.download_count DESC
      LIMIT 50
    `;

    const params: any[] = [];
    if (filters?.category) params.push(filters.category);
    if (filters?.priceRange) {
      params.push(filters.priceRange.min, filters.priceRange.max);
    }

    const models = await this.db.prepare(query).bind(...params).all<any>();
    const opportunities: ArbitrageOpportunity[] = [];

    for (const model of models) {
      // Аналізуємо ринок для категорії
      const marketAnalysis = await this.marketAnalyzer.analyzeCategory(model.category);
      if (!marketAnalysis.success) continue;

      // Прогнозуємо оптимальну ціну
      const pricePrediction = await this.pricePredictor.predictOptimalPrice(model.model_id);
      if (!pricePrediction.success) continue;

      const suggestedPrice = pricePrediction.data.optimalPrice;
      const currentPrice = model.current_price;

      // Перевіряємо, чи є значний розрив
      const priceGap = Math.abs(suggestedPrice - currentPrice);
      const priceGapPercentage = priceGap / currentPrice;

      if (priceGapPercentage >= 0.15) { // мінімум 15% розрив
        const potentialProfit = suggestedPrice > currentPrice ? 
          (suggestedPrice - currentPrice) * (model.download_count / 30) : // прогнозований прибуток за місяць
          0;

        if (potentialProfit >= (filters?.minProfit || 10)) {
          // Оцінюємо ризик
          const riskAssessment = await this.riskAssessor.assessOpportunityRisk({
            modelId: model.model_id,
            currentPrice,
            suggestedPrice,
            category: model.category,
            marketData: marketAnalysis.data
          });

          const opportunity: ArbitrageOpportunity = {
            id: this.generateOpportunityId(),
            modelId: model.model_id,
            sellerId: model.seller_id,
            opportunityType: 'price_gap',
            currentPrice,
            suggestedPrice,
            potentialProfit,
            confidenceScore: Math.min(pricePrediction.confidence * marketAnalysis.confidence, 1.0),
            marketDemand: marketAnalysis.data.demandLevel,
            competitionLevel: marketAnalysis.data.competitionIndex,
            riskLevel: riskAssessment.level,
            status: 'active',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 днів
            createdAt: new Date(),
            updatedAt: new Date()
          };

          // Фільтруємо за критеріями
          if (this.matchesFilters(opportunity, filters)) {
            opportunities.push(opportunity);
          }
        }
      }
    }

    return opportunities;
  }

  /**
   * Знаходить можливості на основі сплесків попиту
   */
  private async findDemandSpikeOpportunities(filters?: OpportunityFilters): Promise<ArbitrageOpportunity[]> {
    const query = `
      SELECT 
        m.id as model_id,
        m.user_id as seller_id,
        m.name,
        m.category,
        m.price as current_price,
        COUNT(p.id) as recent_purchases,
        m.view_count,
        m.download_count
      FROM models m
      LEFT JOIN purchases p ON m.id = p.model_id 
        AND p.created_at >= datetime('now', '-7 days')
      WHERE m.price > 0
        ${filters?.category ? 'AND m.category = ?' : ''}
      GROUP BY m.id
      HAVING recent_purchases > 0
      ORDER BY recent_purchases DESC, m.view_count DESC
      LIMIT 30
    `;

    const params: any[] = [];
    if (filters?.category) params.push(filters.category);

    const models = await this.db.prepare(query).bind(...params).all<any>();
    const opportunities: ArbitrageOpportunity[] = [];

    for (const model of models) {
      // Розраховуємо сплеск попиту
      const demandSpike = await this.calculateDemandSpike(model.model_id);
      
      if (demandSpike.isSignificant) {
        const suggestedPrice = model.current_price * (1 + demandSpike.multiplier);
        const potentialProfit = (suggestedPrice - model.current_price) * demandSpike.projectedSales;

        if (potentialProfit >= (filters?.minProfit || 10)) {
          const opportunity: ArbitrageOpportunity = {
            id: this.generateOpportunityId(),
            modelId: model.model_id,
            sellerId: model.seller_id,
            opportunityType: 'demand_spike',
            currentPrice: model.current_price,
            suggestedPrice,
            potentialProfit,
            confidenceScore: demandSpike.confidence,
            marketDemand: demandSpike.demandLevel,
            competitionLevel: await this.getCompetitionLevel(model.category),
            riskLevel: demandSpike.riskLevel,
            status: 'active',
            expiresAt: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 дні для сплесків
            createdAt: new Date(),
            updatedAt: new Date()
          };

          if (this.matchesFilters(opportunity, filters)) {
            opportunities.push(opportunity);
          }
        }
      }
    }

    return opportunities;
  }

  /**
   * Знаходить можливості на основі прогнозування трендів
   */
  private async findTrendPredictionOpportunities(filters?: OpportunityFilters): Promise<ArbitrageOpportunity[]> {
    const categories = filters?.category ? [filters.category] : await this.getAllCategories();
    const opportunities: ArbitrageOpportunity[] = [];

    for (const category of categories) {
      const marketAnalysis = await this.marketAnalyzer.analyzeCategory(category);
      if (!marketAnalysis.success) continue;

      // Шукаємо моделі в категоріях з позитивними трендами
      if (marketAnalysis.data.priceTrend === 'rising' && marketAnalysis.data.growthRate > 10) {
        const trendOpportunities = await this.findTrendBasedOpportunities(category, marketAnalysis.data);
        opportunities.push(...trendOpportunities.filter(op => this.matchesFilters(op, filters)));
      }
    }

    return opportunities;
  }

  /**
   * Допоміжні методи
   */
  private async calculateDemandSpike(modelId: string): Promise<{
    isSignificant: boolean;
    multiplier: number;
    projectedSales: number;
    confidence: number;
    demandLevel: number;
    riskLevel: 'low' | 'medium' | 'high';
  }> {
    const query = `
      SELECT 
        COUNT(CASE WHEN created_at >= datetime('now', '-7 days') THEN 1 END) as recent_activity,
        COUNT(CASE WHEN created_at >= datetime('now', '-14 days') AND created_at < datetime('now', '-7 days') THEN 1 END) as previous_activity,
        COUNT(*) as total_activity
      FROM purchases 
      WHERE model_id = ?
    `;

    const result = await this.db.prepare(query).bind(modelId).first<any>();
    
    const recent = result.recent_activity || 0;
    const previous = result.previous_activity || 1;
    const total = result.total_activity || 0;

    const growthRate = (recent - previous) / previous;
    const isSignificant = growthRate > 0.5 && recent >= 3; // мінімум 50% зростання та 3 покупки

    return {
      isSignificant,
      multiplier: Math.min(growthRate * 0.2, 0.5), // максимум 50% підвищення ціни
      projectedSales: recent * 2, // прогнозуємо подвоєння продажів
      confidence: Math.min(total / 20, 1.0), // впевненість на основі історії
      demandLevel: Math.min(recent / 10, 1.0),
      riskLevel: growthRate > 2 ? 'high' : growthRate > 1 ? 'medium' : 'low'
    };
  }

  private async findTrendBasedOpportunities(category: string, marketData: any): Promise<ArbitrageOpportunity[]> {
    const query = `
      SELECT 
        id as model_id,
        user_id as seller_id,
        name,
        price as current_price,
        download_count,
        view_count
      FROM models 
      WHERE category = ? AND price > 0 AND price < ?
      ORDER BY (view_count * download_count) DESC
      LIMIT 20
    `;

    const maxPrice = marketData.averagePrice * 1.5; // шукаємо недооцінені моделі
    const models = await this.db.prepare(query).bind(category, maxPrice).all<any>();
    const opportunities: ArbitrageOpportunity[] = [];

    for (const model of models) {
      const suggestedPrice = model.current_price * (1 + marketData.growthRate / 100);
      const potentialProfit = (suggestedPrice - model.current_price) * (model.download_count / 30);

      if (potentialProfit > 5) {
        opportunities.push({
          id: this.generateOpportunityId(),
          modelId: model.model_id,
          sellerId: model.seller_id,
          opportunityType: 'trend_prediction',
          currentPrice: model.current_price,
          suggestedPrice,
          potentialProfit,
          confidenceScore: marketData.aiConfidence,
          marketDemand: marketData.demandLevel,
          competitionLevel: marketData.competitionIndex,
          riskLevel: 'medium',
          status: 'active',
          expiresAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 днів
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
    }

    return opportunities;
  }

  private async getCompetitionLevel(category: string): Promise<number> {
    const query = `
      SELECT COUNT(*) as count FROM models WHERE category = ?
    `;
    const result = await this.db.prepare(query).bind(category).first<{ count: number }>();
    return Math.min((result?.count || 0) / 100, 1.0);
  }

  private async getAllCategories(): Promise<string[]> {
    const query = `SELECT DISTINCT category FROM models WHERE category IS NOT NULL`;
    const results = await this.db.prepare(query).all<{ category: string }>();
    return results.map(r => r.category);
  }

  private matchesFilters(opportunity: ArbitrageOpportunity, filters?: OpportunityFilters): boolean {
    if (!filters) return true;

    if (filters.minProfit && opportunity.potentialProfit < filters.minProfit) return false;
    if (filters.maxRisk && this.getRiskLevel(filters.maxRisk) < this.getRiskLevel(opportunity.riskLevel)) return false;
    if (filters.minConfidence && opportunity.confidenceScore < filters.minConfidence) return false;
    if (filters.priceRange) {
      if (opportunity.currentPrice < filters.priceRange.min || opportunity.currentPrice > filters.priceRange.max) {
        return false;
      }
    }

    return true;
  }

  private getRiskLevel(risk: 'low' | 'medium' | 'high'): number {
    switch (risk) {
      case 'low': return 1;
      case 'medium': return 2;
      case 'high': return 3;
      default: return 2;
    }
  }

  private calculateOverallConfidence(opportunities: ArbitrageOpportunity[]): number {
    if (opportunities.length === 0) return 0;
    const avgConfidence = opportunities.reduce((sum, op) => sum + op.confidenceScore, 0) / opportunities.length;
    return avgConfidence;
  }

  private generateOpportunityId(): string {
    return `opp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async saveOpportunity(opportunity: ArbitrageOpportunity): Promise<void> {
    const query = `
      INSERT INTO arbitrage_opportunities (
        id, model_id, seller_id, opportunity_type, current_price, suggested_price,
        potential_profit, confidence_score, market_demand, competition_level,
        risk_level, status, expires_at, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await this.db.prepare(query).bind(
      opportunity.id,
      opportunity.modelId,
      opportunity.sellerId,
      opportunity.opportunityType,
      opportunity.currentPrice,
      opportunity.suggestedPrice,
      opportunity.potentialProfit,
      opportunity.confidenceScore,
      opportunity.marketDemand,
      opportunity.competitionLevel,
      opportunity.riskLevel,
      opportunity.status,
      opportunity.expiresAt.toISOString(),
      opportunity.createdAt.toISOString(),
      opportunity.updatedAt.toISOString()
    ).run();
  }
}
