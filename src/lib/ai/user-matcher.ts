/**
 * User Matcher - AI сервіс для з'єднання покупців і продавців
 */

import { getDb } from '@/lib/db';
import { ArbitrageOpportunity, UserPreferences, MatchingCriteria, AIAnalysisResult } from './types';

export interface UserMatch {
  userId: string;
  opportunityId: string;
  compatibilityScore: number;
  matchReasons: string[];
  recommendedAction: 'buy' | 'sell' | 'watch';
  confidence: number;
}

export interface MatchingResult {
  matches: UserMatch[];
  totalOpportunities: number;
  averageCompatibility: number;
}

export class UserMatcher {
  private db = getDb();

  /**
   * Знаходить підходящі можливості для користувача
   */
  async findMatchesForUser(userId: string): Promise<AIAnalysageResult> {
    try {
      const userPreferences = await this.getUserPreferences(userId);
      if (!userPreferences) {
        throw new Error('User preferences not found');
      }

      const activeOpportunities = await this.getActiveOpportunities();
      const matches: UserMatch[] = [];

      for (const opportunity of activeOpportunities) {
        const compatibility = await this.calculateCompatibility(userPreferences, opportunity);
        
        if (compatibility.score >= 0.6) { // мінімальний поріг сумісності
          matches.push({
            userId,
            opportunityId: opportunity.id,
            compatibilityScore: compatibility.score,
            matchReasons: compatibility.reasons,
            recommendedAction: this.determineRecommendedAction(userPreferences, opportunity),
            confidence: compatibility.confidence
          });
        }
      }

      // Сортуємо за сумісністю
      matches.sort((a, b) => b.compatibilityScore - a.compatibilityScore);

      // Обмежуємо кількість рекомендацій
      const maxRecommendations = userPreferences.autoTradingEnabled ? 20 : 10;
      const topMatches = matches.slice(0, maxRecommendations);

      const result: MatchingResult = {
        matches: topMatches,
        totalOpportunities: activeOpportunities.length,
        averageCompatibility: matches.length > 0 ? 
          matches.reduce((sum, m) => sum + m.compatibilityScore, 0) / matches.length : 0
      };

      return {
        success: true,
        data: result,
        confidence: this.calculateOverallConfidence(topMatches),
        timestamp: new Date()
      };
    } catch (error) {
      console.error('User matching error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        confidence: 0,
        timestamp: new Date()
      };
    }
  }

  /**
   * Знаходить потенційних покупців для можливості
   */
  async findBuyersForOpportunity(opportunityId: string): Promise<AIAnalysisResult> {
    try {
      const opportunity = await this.getOpportunity(opportunityId);
      if (!opportunity) {
        throw new Error('Opportunity not found');
      }

      const potentialBuyers = await this.getPotentialBuyers(opportunity);
      const matches: UserMatch[] = [];

      for (const buyer of potentialBuyers) {
        const compatibility = await this.calculateBuyerCompatibility(buyer, opportunity);
        
        if (compatibility.score >= 0.5) {
          matches.push({
            userId: buyer.userId,
            opportunityId,
            compatibilityScore: compatibility.score,
            matchReasons: compatibility.reasons,
            recommendedAction: 'buy',
            confidence: compatibility.confidence
          });
        }
      }

      matches.sort((a, b) => b.compatibilityScore - a.compatibilityScore);

      return {
        success: true,
        data: {
          matches: matches.slice(0, 15), // топ 15 покупців
          totalOpportunities: 1,
          averageCompatibility: matches.length > 0 ? 
            matches.reduce((sum, m) => sum + m.compatibilityScore, 0) / matches.length : 0
        },
        confidence: this.calculateOverallConfidence(matches),
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Buyer matching error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        confidence: 0,
        timestamp: new Date()
      };
    }
  }

  /**
   * Створює автоматичне з'єднання між користувачами
   */
  async createAutoMatch(opportunityId: string): Promise<AIAnalysisResult> {
    try {
      const opportunity = await this.getOpportunity(opportunityId);
      if (!opportunity) {
        throw new Error('Opportunity not found');
      }

      // Знаходимо найкращого покупця
      const buyersResult = await this.findBuyersForOpportunity(opportunityId);
      if (!buyersResult.success || !buyersResult.data.matches.length) {
        throw new Error('No suitable buyers found');
      }

      const bestBuyer = buyersResult.data.matches[0];
      
      // Перевіряємо, чи покупець дозволив автоматичну торгівлю
      const buyerPreferences = await this.getUserPreferences(bestBuyer.userId);
      if (!buyerPreferences?.autoTradingEnabled) {
        throw new Error('Buyer has not enabled auto-trading');
      }

      // Створюємо торгову сесію
      const tradingSessionId = await this.createTradingSession(opportunity, bestBuyer.userId);

      return {
        success: true,
        data: {
          tradingSessionId,
          buyerId: bestBuyer.userId,
          sellerId: opportunity.sellerId,
          compatibilityScore: bestBuyer.compatibilityScore,
          estimatedProfit: opportunity.potentialProfit
        },
        confidence: bestBuyer.confidence,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Auto matching error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        confidence: 0,
        timestamp: new Date()
      };
    }
  }

  /**
   * Розраховує сумісність користувача з можливістю
   */
  private async calculateCompatibility(
    preferences: UserPreferences, 
    opportunity: ArbitrageOpportunity
  ): Promise<{ score: number; reasons: string[]; confidence: number }> {
    const reasons: string[] = [];
    let score = 0;
    let factors = 0;

    // Перевіряємо категорії
    const model = await this.getModelInfo(opportunity.modelId);
    if (model && preferences.preferredCategories.includes(model.category)) {
      score += 0.3;
      reasons.push(`Відповідає вашим улюбленим категоріям: ${model.category}`);
    }
    factors++;

    // Перевіряємо ціновий діапазон
    if (opportunity.currentPrice >= preferences.priceRangeMin && 
        opportunity.currentPrice <= preferences.priceRangeMax) {
      score += 0.25;
      reasons.push(`Ціна в межах вашого бюджету: $${opportunity.currentPrice}`);
    }
    factors++;

    // Перевіряємо толерантність до ризику
    const riskCompatibility = this.calculateRiskCompatibility(preferences.riskTolerance, opportunity.riskLevel);
    score += riskCompatibility.score;
    if (riskCompatibility.score > 0) {
      reasons.push(riskCompatibility.reason);
    }
    factors++;

    // Перевіряємо потенційний прибуток
    const profitScore = Math.min(opportunity.potentialProfit / 100, 0.2); // максимум 0.2 за прибуток
    score += profitScore;
    if (opportunity.potentialProfit >= preferences.profitTarget * opportunity.currentPrice) {
      reasons.push(`Високий потенційний прибуток: $${opportunity.potentialProfit.toFixed(2)}`);
    }
    factors++;

    // Перевіряємо впевненість AI
    const confidenceScore = opportunity.confidenceScore * 0.15;
    score += confidenceScore;
    if (opportunity.confidenceScore > 0.8) {
      reasons.push(`Висока впевненість AI: ${(opportunity.confidenceScore * 100).toFixed(0)}%`);
    }
    factors++;

    // Нормалізуємо оцінку
    const normalizedScore = Math.min(score, 1.0);
    const confidence = Math.min(factors / 5, 1.0); // впевненість на основі кількості факторів

    return {
      score: normalizedScore,
      reasons,
      confidence
    };
  }

  /**
   * Розраховує сумісність покупця з можливістю
   */
  private async calculateBuyerCompatibility(
    buyer: UserPreferences,
    opportunity: ArbitrageOpportunity
  ): Promise<{ score: number; reasons: string[]; confidence: number }> {
    const reasons: string[] = [];
    let score = 0;

    // Перевіряємо бюджет
    if (buyer.investmentBudget >= opportunity.currentPrice) {
      score += 0.3;
      reasons.push('Достатній бюджет для покупки');
    }

    // Перевіряємо категорії
    const model = await this.getModelInfo(opportunity.modelId);
    if (model && buyer.preferredCategories.includes(model.category)) {
      score += 0.25;
      reasons.push(`Цікавиться категорією: ${model.category}`);
    }

    // Перевіряємо ризик
    const riskCompatibility = this.calculateRiskCompatibility(buyer.riskTolerance, opportunity.riskLevel);
    score += riskCompatibility.score;
    reasons.push(riskCompatibility.reason);

    // Перевіряємо історію покупок
    const purchaseHistory = await this.getBuyerHistory(buyer.userId);
    if (purchaseHistory.relevantPurchases > 0) {
      score += 0.15;
      reasons.push('Має досвід покупок в цій категорії');
    }

    return {
      score: Math.min(score, 1.0),
      reasons,
      confidence: 0.8
    };
  }

  /**
   * Допоміжні методи
   */
  private calculateRiskCompatibility(
    userTolerance: 'low' | 'medium' | 'high',
    opportunityRisk: 'low' | 'medium' | 'high'
  ): { score: number; reason: string } {
    const riskLevels = { low: 1, medium: 2, high: 3 };
    const userLevel = riskLevels[userTolerance];
    const oppLevel = riskLevels[opportunityRisk];

    if (userLevel >= oppLevel) {
      return {
        score: 0.2,
        reason: `Рівень ризику (${opportunityRisk}) відповідає вашій толерантності`
      };
    } else {
      return {
        score: 0,
        reason: `Рівень ризику (${opportunityRisk}) перевищує вашу толерантність`
      };
    }
  }

  private determineRecommendedAction(
    preferences: UserPreferences,
    opportunity: ArbitrageOpportunity
  ): 'buy' | 'sell' | 'watch' {
    if (opportunity.confidenceScore > 0.8 && opportunity.potentialProfit > preferences.profitTarget * opportunity.currentPrice) {
      return 'buy';
    } else if (opportunity.confidenceScore > 0.6) {
      return 'watch';
    } else {
      return 'watch';
    }
  }

  private calculateOverallConfidence(matches: UserMatch[]): number {
    if (matches.length === 0) return 0;
    return matches.reduce((sum, m) => sum + m.confidence, 0) / matches.length;
  }

  /**
   * Методи для роботи з базою даних
   */
  private async getUserPreferences(userId: string): Promise<UserPreferences | null> {
    const query = `
      SELECT * FROM user_preferences WHERE user_id = ?
    `;
    const result = await this.db.prepare(query).bind(userId).first<any>();
    
    if (!result) return null;

    return {
      id: result.id,
      userId: result.user_id,
      preferredCategories: JSON.parse(result.preferred_categories || '[]'),
      priceRangeMin: result.price_range_min,
      priceRangeMax: result.price_range_max,
      riskTolerance: result.risk_tolerance,
      investmentBudget: result.investment_budget,
      autoTradingEnabled: result.auto_trading_enabled,
      notificationPreferences: JSON.parse(result.notification_preferences || '{}'),
      tradingFrequency: result.trading_frequency,
      profitTarget: result.profit_target,
      createdAt: new Date(result.created_at),
      updatedAt: new Date(result.updated_at)
    };
  }

  private async getActiveOpportunities(): Promise<ArbitrageOpportunity[]> {
    const query = `
      SELECT * FROM arbitrage_opportunities 
      WHERE status = 'active' AND expires_at > datetime('now')
      ORDER BY potential_profit DESC
      LIMIT 100
    `;
    
    const results = await this.db.prepare(query).all<any>();
    return results.map(this.mapOpportunityFromDb);
  }

  private async getOpportunity(opportunityId: string): Promise<ArbitrageOpportunity | null> {
    const query = `
      SELECT * FROM arbitrage_opportunities WHERE id = ?
    `;
    
    const result = await this.db.prepare(query).bind(opportunityId).first<any>();
    return result ? this.mapOpportunityFromDb(result) : null;
  }

  private async getPotentialBuyers(opportunity: ArbitrageOpportunity): Promise<UserPreferences[]> {
    const query = `
      SELECT * FROM user_preferences 
      WHERE price_range_min <= ? AND price_range_max >= ?
      AND investment_budget >= ?
      ORDER BY investment_budget DESC
      LIMIT 50
    `;
    
    const results = await this.db.prepare(query)
      .bind(opportunity.currentPrice, opportunity.currentPrice, opportunity.currentPrice)
      .all<any>();
    
    return results.map(r => ({
      id: r.id,
      userId: r.user_id,
      preferredCategories: JSON.parse(r.preferred_categories || '[]'),
      priceRangeMin: r.price_range_min,
      priceRangeMax: r.price_range_max,
      riskTolerance: r.risk_tolerance,
      investmentBudget: r.investment_budget,
      autoTradingEnabled: r.auto_trading_enabled,
      notificationPreferences: JSON.parse(r.notification_preferences || '{}'),
      tradingFrequency: r.trading_frequency,
      profitTarget: r.profit_target,
      createdAt: new Date(r.created_at),
      updatedAt: new Date(r.updated_at)
    }));
  }

  private async getModelInfo(modelId: string): Promise<{ category: string } | null> {
    const query = `SELECT category FROM models WHERE id = ?`;
    return await this.db.prepare(query).bind(modelId).first<{ category: string }>();
  }

  private async getBuyerHistory(userId: string): Promise<{ relevantPurchases: number }> {
    const query = `
      SELECT COUNT(*) as count FROM purchases WHERE user_id = ?
    `;
    const result = await this.db.prepare(query).bind(userId).first<{ count: number }>();
    return { relevantPurchases: result?.count || 0 };
  }

  private async createTradingSession(opportunity: ArbitrageOpportunity, buyerId: string): Promise<string> {
    const sessionId = `ts_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const query = `
      INSERT INTO trading_sessions (
        id, opportunity_id, seller_id, buyer_id, model_id, initial_price, status, ai_mediated
      ) VALUES (?, ?, ?, ?, ?, ?, 'initiated', true)
    `;

    await this.db.prepare(query).bind(
      sessionId,
      opportunity.id,
      opportunity.sellerId,
      buyerId,
      opportunity.modelId,
      opportunity.currentPrice
    ).run();

    return sessionId;
  }

  private mapOpportunityFromDb(row: any): ArbitrageOpportunity {
    return {
      id: row.id,
      modelId: row.model_id,
      sellerId: row.seller_id,
      buyerId: row.buyer_id,
      opportunityType: row.opportunity_type,
      currentPrice: row.current_price,
      suggestedPrice: row.suggested_price,
      potentialProfit: row.potential_profit,
      confidenceScore: row.confidence_score,
      marketDemand: row.market_demand,
      competitionLevel: row.competition_level,
      riskLevel: row.risk_level,
      status: row.status,
      expiresAt: new Date(row.expires_at),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }
}
