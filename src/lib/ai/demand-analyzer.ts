/**
 * Demand Analyzer - AI сервіс для аналізу попиту на 3D моделі
 */

import { getDb } from '@/lib/db';
import { AIAnalysisResult, DemandPattern } from './types';
import { movingAverage, classifyTrend, calculateSeasonalIndex } from './utils';

export interface DemandAnalysis {
  category: string;
  currentDemandLevel: number; // 0-1
  demandTrend: 'rising' | 'falling' | 'stable';
  seasonalFactors: Record<number, number>;
  peakHours: number[];
  peakDays: number[];
  volatility: number;
  predictedDemand: number;
  confidence: number;
}

export class DemandAnalyzer {
  private db = getDb();

  /**
   * Аналізує попит для конкретної категорії
   */
  async analyzeCategoryDemand(category: string): Promise<AIAnalysisResult> {
    try {
      const demandData = await this.collectDemandData(category);
      const patterns = await this.identifyDemandPatterns(category);
      const seasonality = await this.analyzeSeasonality(category);
      const trends = this.analyzeDemandTrends(demandData);

      const analysis: DemandAnalysis = {
        category,
        currentDemandLevel: this.calculateCurrentDemandLevel(demandData),
        demandTrend: trends.direction,
        seasonalFactors: seasonality.factors,
        peakHours: patterns.peakHours,
        peakDays: patterns.peakDays,
        volatility: trends.volatility,
        predictedDemand: await this.predictFutureDemand(category, demandData, trends),
        confidence: this.calculateConfidence(demandData, patterns, seasonality)
      };

      // Зберігаємо паттерни попиту
      await this.saveDemandPatterns(analysis);

      return {
        success: true,
        data: analysis,
        confidence: analysis.confidence,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Demand analysis error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        confidence: 0,
        timestamp: new Date()
      };
    }
  }

  /**
   * Аналізує попит для конкретної моделі
   */
  async analyzeModelDemand(modelId: string): Promise<AIAnalysisResult> {
    try {
      const modelData = await this.getModelData(modelId);
      if (!modelData) {
        throw new Error('Model not found');
      }

      const viewsData = await this.getModelViewsData(modelId);
      const purchasesData = await this.getModelPurchasesData(modelId);
      const competitorData = await this.getCompetitorData(modelData.category, modelId);

      const demandMetrics = {
        viewToPurchaseRatio: purchasesData.length > 0 ? viewsData.length / purchasesData.length : 0,
        recentActivity: this.calculateRecentActivity(viewsData, purchasesData),
        competitivePosition: this.calculateCompetitivePosition(modelData, competitorData),
        trendDirection: this.analyzeTrendDirection(viewsData, purchasesData)
      };

      const demandLevel = this.calculateModelDemandLevel(demandMetrics);
      const prediction = await this.predictModelDemand(modelId, demandMetrics);

      return {
        success: true,
        data: {
          modelId,
          currentDemandLevel: demandLevel,
          demandTrend: demandMetrics.trendDirection,
          viewToPurchaseRatio: demandMetrics.viewToPurchaseRatio,
          competitivePosition: demandMetrics.competitivePosition,
          predictedDemand: prediction.level,
          confidence: prediction.confidence,
          recommendations: this.generateDemandRecommendations(demandMetrics)
        },
        confidence: prediction.confidence,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Model demand analysis error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        confidence: 0,
        timestamp: new Date()
      };
    }
  }

  /**
   * Збирає дані про попит
   */
  private async collectDemandData(category: string): Promise<Array<{
    date: Date;
    views: number;
    purchases: number;
    downloads: number;
  }>> {
    const query = `
      SELECT 
        DATE(p.created_at) as date,
        COUNT(p.id) as purchases,
        SUM(m.view_count) as views,
        SUM(m.download_count) as downloads
      FROM purchases p
      JOIN models m ON p.model_id = m.id
      WHERE m.category = ? AND p.created_at >= datetime('now', '-90 days')
      GROUP BY DATE(p.created_at)
      ORDER BY date
    `;

    const results = await this.db.prepare(query).bind(category).all<any>();
    return results.map(r => ({
      date: new Date(r.date),
      views: r.views || 0,
      purchases: r.purchases || 0,
      downloads: r.downloads || 0
    }));
  }

  /**
   * Ідентифікує паттерни попиту
   */
  private async identifyDemandPatterns(category: string): Promise<{
    peakHours: number[];
    peakDays: number[];
    patterns: DemandPattern[];
  }> {
    // Аналіз по годинах
    const hourlyQuery = `
      SELECT 
        strftime('%H', p.created_at) as hour,
        COUNT(*) as activity_count
      FROM purchases p
      JOIN models m ON p.model_id = m.id
      WHERE m.category = ? AND p.created_at >= datetime('now', '-30 days')
      GROUP BY hour
      ORDER BY activity_count DESC
    `;

    const hourlyData = await this.db.prepare(hourlyQuery).bind(category).all<any>();
    const peakHours = hourlyData.slice(0, 3).map(h => parseInt(h.hour));

    // Аналіз по днях тижня
    const dailyQuery = `
      SELECT 
        strftime('%w', p.created_at) as day_of_week,
        COUNT(*) as activity_count
      FROM purchases p
      JOIN models m ON p.model_id = m.id
      WHERE m.category = ? AND p.created_at >= datetime('now', '-30 days')
      GROUP BY day_of_week
      ORDER BY activity_count DESC
    `;

    const dailyData = await this.db.prepare(dailyQuery).bind(category).all<any>();
    const peakDays = dailyData.slice(0, 3).map(d => parseInt(d.day_of_week));

    // Створюємо паттерни для збереження
    const patterns: DemandPattern[] = [];

    hourlyData.forEach(h => {
      patterns.push({
        id: `${category}_hourly_${h.hour}`,
        category,
        timePeriod: 'hourly',
        periodValue: parseInt(h.hour),
        demandMultiplier: h.activity_count / (hourlyData.reduce((sum, item) => sum + item.activity_count, 0) / hourlyData.length),
        confidenceLevel: Math.min(h.activity_count / 10, 1.0),
        sampleSize: h.activity_count,
        lastUpdated: new Date()
      });
    });

    return { peakHours, peakDays, patterns };
  }

  /**
   * Аналізує сезонність
   */
  private async analyzeSeasonality(category: string): Promise<{
    factors: Record<number, number>;
    isSignificant: boolean;
  }> {
    const query = `
      SELECT 
        strftime('%m', p.created_at) as month,
        COUNT(*) as activity_count
      FROM purchases p
      JOIN models m ON p.model_id = m.id
      WHERE m.category = ? AND p.created_at >= datetime('now', '-365 days')
      GROUP BY month
    `;

    const monthlyData = await this.db.prepare(query).bind(category).all<any>();
    
    if (monthlyData.length < 6) {
      return { factors: {}, isSignificant: false };
    }

    const seasonalData = monthlyData.map(m => ({
      value: m.activity_count,
      period: parseInt(m.month)
    }));

    const factors = calculateSeasonalIndex(seasonalData);
    
    // Перевіряємо значущість сезонності
    const factorValues = Object.values(factors);
    const maxFactor = Math.max(...factorValues);
    const minFactor = Math.min(...factorValues);
    const isSignificant = (maxFactor - minFactor) > 0.3; // 30% різниця

    return { factors, isSignificant };
  }

  /**
   * Аналізує тренди попиту
   */
  private analyzeDemandTrends(demandData: Array<{
    date: Date;
    views: number;
    purchases: number;
    downloads: number;
  }>): {
    direction: 'rising' | 'falling' | 'stable';
    volatility: number;
    strength: number;
  } {
    if (demandData.length < 7) {
      return { direction: 'stable', volatility: 0, strength: 0 };
    }

    const purchases = demandData.map(d => d.purchases);
    const views = demandData.map(d => d.views);

    // Використовуємо ковзне середнє для згладжування
    const smoothedPurchases = movingAverage(purchases, 7);
    const smoothedViews = movingAverage(views, 7);

    const purchaseTrend = classifyTrend(smoothedPurchases);
    const viewTrend = classifyTrend(smoothedViews);

    // Комбінуємо тренди
    let direction: 'rising' | 'falling' | 'stable';
    if (purchaseTrend === 'rising' && viewTrend === 'rising') {
      direction = 'rising';
    } else if (purchaseTrend === 'falling' && viewTrend === 'falling') {
      direction = 'falling';
    } else {
      direction = 'stable';
    }

    // Розраховуємо волатильність
    const purchaseVolatility = this.calculateVolatility(purchases);
    const viewVolatility = this.calculateVolatility(views);
    const volatility = (purchaseVolatility + viewVolatility) / 2;

    // Розраховуємо силу тренду
    const recentPurchases = purchases.slice(-7);
    const olderPurchases = purchases.slice(0, 7);
    const recentAvg = recentPurchases.reduce((a, b) => a + b, 0) / recentPurchases.length;
    const olderAvg = olderPurchases.reduce((a, b) => a + b, 0) / olderPurchases.length;
    const strength = Math.abs((recentAvg - olderAvg) / olderAvg);

    return { direction, volatility, strength };
  }

  /**
   * Розраховує поточний рівень попиту
   */
  private calculateCurrentDemandLevel(demandData: Array<{
    date: Date;
    views: number;
    purchases: number;
    downloads: number;
  }>): number {
    if (demandData.length === 0) return 0;

    const recentData = demandData.slice(-7); // останні 7 днів
    const avgPurchases = recentData.reduce((sum, d) => sum + d.purchases, 0) / recentData.length;
    const avgViews = recentData.reduce((sum, d) => sum + d.views, 0) / recentData.length;

    // Нормалізуємо до 0-1
    const purchaseLevel = Math.min(avgPurchases / 20, 1.0); // 20 покупок = максимум
    const viewLevel = Math.min(avgViews / 1000, 1.0); // 1000 переглядів = максимум

    return (purchaseLevel + viewLevel) / 2;
  }

  /**
   * Прогнозує майбутній попит
   */
  private async predictFutureDemand(
    category: string,
    demandData: Array<any>,
    trends: any
  ): Promise<number> {
    const currentLevel = this.calculateCurrentDemandLevel(demandData);
    
    // Простий прогноз на основі тренду
    let prediction = currentLevel;
    
    if (trends.direction === 'rising') {
      prediction = Math.min(currentLevel * (1 + trends.strength), 1.0);
    } else if (trends.direction === 'falling') {
      prediction = Math.max(currentLevel * (1 - trends.strength), 0.0);
    }

    return prediction;
  }

  /**
   * Допоміжні методи
   */
  private calculateVolatility(data: number[]): number {
    if (data.length < 2) return 0;

    const mean = data.reduce((a, b) => a + b, 0) / data.length;
    const variance = data.reduce((sum, x) => sum + Math.pow(x - mean, 2), 0) / data.length;
    
    return Math.sqrt(variance) / mean;
  }

  private calculateConfidence(demandData: any[], patterns: any, seasonality: any): number {
    const dataQuality = Math.min(demandData.length / 30, 1.0); // 30 днів = повна впевненість
    const patternStrength = patterns.patterns.length > 0 ? 0.8 : 0.4;
    const seasonalityStrength = seasonality.isSignificant ? 0.9 : 0.6;

    return (dataQuality + patternStrength + seasonalityStrength) / 3;
  }

  private async getModelData(modelId: string): Promise<any> {
    const query = `SELECT * FROM models WHERE id = ?`;
    return await this.db.prepare(query).bind(modelId).first();
  }

  private async getModelViewsData(modelId: string): Promise<any[]> {
    // Спрощена реалізація - в реальності тут би була таблиця переглядів
    return [];
  }

  private async getModelPurchasesData(modelId: string): Promise<any[]> {
    const query = `
      SELECT * FROM purchases 
      WHERE model_id = ? AND created_at >= datetime('now', '-30 days')
      ORDER BY created_at
    `;
    return await this.db.prepare(query).bind(modelId).all();
  }

  private async getCompetitorData(category: string, excludeModelId: string): Promise<any[]> {
    const query = `
      SELECT * FROM models 
      WHERE category = ? AND id != ?
      ORDER BY download_count DESC
      LIMIT 10
    `;
    return await this.db.prepare(query).bind(category, excludeModelId).all();
  }

  private calculateRecentActivity(viewsData: any[], purchasesData: any[]): number {
    const recentViews = viewsData.filter(v => 
      new Date(v.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    ).length;
    
    const recentPurchases = purchasesData.filter(p => 
      new Date(p.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    ).length;

    return (recentViews + recentPurchases * 5) / 10; // покупки важать більше
  }

  private calculateCompetitivePosition(modelData: any, competitorData: any[]): number {
    if (competitorData.length === 0) return 0.5;

    const avgDownloads = competitorData.reduce((sum, c) => sum + c.download_count, 0) / competitorData.length;
    return Math.min(modelData.download_count / avgDownloads, 2.0) / 2.0; // нормалізуємо до 0-1
  }

  private analyzeTrendDirection(viewsData: any[], purchasesData: any[]): 'rising' | 'falling' | 'stable' {
    // Спрощена логіка аналізу тренду
    const recentActivity = viewsData.length + purchasesData.length;
    if (recentActivity > 10) return 'rising';
    if (recentActivity < 3) return 'falling';
    return 'stable';
  }

  private calculateModelDemandLevel(metrics: any): number {
    const factors = [
      Math.min(metrics.viewToPurchaseRatio / 10, 1.0),
      Math.min(metrics.recentActivity / 20, 1.0),
      metrics.competitivePosition
    ];

    return factors.reduce((sum, f) => sum + f, 0) / factors.length;
  }

  private async predictModelDemand(modelId: string, metrics: any): Promise<{
    level: number;
    confidence: number;
  }> {
    // Спрощений прогноз
    const currentLevel = this.calculateModelDemandLevel(metrics);
    const trendMultiplier = metrics.trendDirection === 'rising' ? 1.2 : 
                           metrics.trendDirection === 'falling' ? 0.8 : 1.0;

    return {
      level: Math.min(currentLevel * trendMultiplier, 1.0),
      confidence: 0.7
    };
  }

  private generateDemandRecommendations(metrics: any): string[] {
    const recommendations: string[] = [];

    if (metrics.viewToPurchaseRatio < 5) {
      recommendations.push('Низька конверсія переглядів у покупки');
    }

    if (metrics.competitivePosition < 0.3) {
      recommendations.push('Слабка позиція відносно конкурентів');
    }

    if (metrics.trendDirection === 'rising') {
      recommendations.push('Зростаючий попит - хороший час для підвищення ціни');
    }

    return recommendations;
  }

  private async saveDemandPatterns(analysis: DemandAnalysis): Promise<void> {
    // Зберігаємо паттерни в базу даних
    for (const pattern of analysis.seasonalFactors ? Object.entries(analysis.seasonalFactors) : []) {
      const [period, multiplier] = pattern;
      
      const query = `
        INSERT OR REPLACE INTO demand_patterns (
          id, category, time_period, period_value, demand_multiplier,
          confidence_level, sample_size, last_updated
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const id = `${analysis.category}_monthly_${period}`;
      await this.db.prepare(query).bind(
        id,
        analysis.category,
        'monthly',
        parseInt(period),
        multiplier,
        analysis.confidence,
        10, // sample size
        new Date().toISOString()
      ).run();
    }
  }
}
