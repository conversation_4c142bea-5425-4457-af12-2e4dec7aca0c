/**
 * Market Analyzer - AI сервіс для аналізу ринку 3D моделей
 */

import { getDb } from '@/lib/db';
import { MarketAnalysis, MarketMetrics, AIAnalysisResult, DemandPattern } from './types';

export class MarketAnalyzer {
  private db = getDb();

  /**
   * Аналізує ринок для конкретної категорії
   */
  async analyzeCategory(category: string): Promise<AIAnalysisResult> {
    try {
      const metrics = await this.calculateMarketMetrics(category);
      const trends = await this.analyzePriceTrends(category);
      const demand = await this.analyzeDemandPatterns(category);
      const competition = await this.analyzeCompetition(category);

      const analysis: Omit<MarketAnalysis, 'id' | 'createdAt'> = {
        category,
        analysisDate: new Date(),
        averagePrice: metrics.averagePrice,
        medianPrice: await this.calculateMedianPrice(category),
        priceTrend: trends.direction,
        demandLevel: demand.level,
        supplyLevel: await this.calculateSupplyLevel(category),
        competitionIndex: competition.index,
        growthRate: trends.growthRate,
        seasonalFactor: demand.seasonalFactor,
        marketSaturation: competition.saturation,
        aiConfidence: this.calculateConfidence(metrics, trends, demand, competition)
      };

      // Зберігаємо аналіз в базу даних
      await this.saveMarketAnalysis(analysis);

      return {
        success: true,
        data: analysis,
        confidence: analysis.aiConfidence,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Market analysis error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        confidence: 0,
        timestamp: new Date()
      };
    }
  }

  /**
   * Розраховує базові метрики ринку
   */
  private async calculateMarketMetrics(category: string): Promise<MarketMetrics> {
    const query = `
      SELECT 
        COUNT(*) as total_models,
        AVG(price) as avg_price,
        SUM(download_count) as total_downloads,
        SUM(view_count) as total_views,
        AVG(view_count) as avg_views
      FROM models 
      WHERE category = ? AND created_at >= datetime('now', '-30 days')
    `;

    const result = await this.db.prepare(query).bind(category).first<any>();

    const priceHistory = await this.getPriceHistory(category, 30);
    const volatility = this.calculateVolatility(priceHistory);

    return {
      totalVolume: result.total_downloads || 0,
      averagePrice: result.avg_price || 0,
      priceVolatility: volatility,
      demandTrend: this.calculateDemandTrend(result.total_views, result.total_downloads),
      supplyTrend: this.calculateSupplyTrend(result.total_models),
      competitionLevel: await this.calculateCompetitionLevel(category),
      growthRate: await this.calculateGrowthRate(category)
    };
  }

  /**
   * Аналізує тренди цін
   */
  private async analyzePriceTrends(category: string): Promise<{
    direction: 'rising' | 'falling' | 'stable';
    growthRate: number;
  }> {
    const priceHistory = await this.getPriceHistory(category, 30);
    
    if (priceHistory.length < 2) {
      return { direction: 'stable', growthRate: 0 };
    }

    const recentPrices = priceHistory.slice(-7); // останні 7 днів
    const olderPrices = priceHistory.slice(0, 7); // перші 7 днів

    const recentAvg = recentPrices.reduce((sum, p) => sum + p.price, 0) / recentPrices.length;
    const olderAvg = olderPrices.reduce((sum, p) => sum + p.price, 0) / olderPrices.length;

    const growthRate = ((recentAvg - olderAvg) / olderAvg) * 100;

    let direction: 'rising' | 'falling' | 'stable';
    if (Math.abs(growthRate) < 2) {
      direction = 'stable';
    } else if (growthRate > 0) {
      direction = 'rising';
    } else {
      direction = 'falling';
    }

    return { direction, growthRate };
  }

  /**
   * Аналізує паттерни попиту
   */
  private async analyzeDemandPatterns(category: string): Promise<{
    level: number;
    seasonalFactor: number;
  }> {
    const query = `
      SELECT 
        strftime('%w', created_at) as day_of_week,
        strftime('%H', created_at) as hour_of_day,
        COUNT(*) as activity_count
      FROM purchases p
      JOIN models m ON p.model_id = m.id
      WHERE m.category = ? AND p.created_at >= datetime('now', '-30 days')
      GROUP BY day_of_week, hour_of_day
    `;

    const patterns = await this.db.prepare(query).bind(category).all<any>();

    // Розраховуємо рівень попиту
    const totalActivity = patterns.reduce((sum, p) => sum + p.activity_count, 0);
    const demandLevel = Math.min(totalActivity / 1000, 1.0); // нормалізуємо до 0-1

    // Розраховуємо сезонний фактор
    const weekdayActivity = patterns
      .filter(p => p.day_of_week >= 1 && p.day_of_week <= 5)
      .reduce((sum, p) => sum + p.activity_count, 0);
    
    const weekendActivity = patterns
      .filter(p => p.day_of_week === 0 || p.day_of_week === 6)
      .reduce((sum, p) => sum + p.activity_count, 0);

    const seasonalFactor = weekdayActivity > 0 ? weekendActivity / weekdayActivity : 1;

    return { level: demandLevel, seasonalFactor };
  }

  /**
   * Аналізує конкуренцію
   */
  private async analyzeCompetition(category: string): Promise<{
    index: number;
    saturation: number;
  }> {
    const query = `
      SELECT 
        COUNT(*) as model_count,
        COUNT(DISTINCT user_id) as seller_count,
        AVG(price) as avg_price,
        MIN(price) as min_price,
        MAX(price) as max_price
      FROM models 
      WHERE category = ? AND created_at >= datetime('now', '-30 days')
    `;

    const result = await this.db.prepare(query).bind(category).first<any>();

    const modelCount = result.model_count || 0;
    const sellerCount = result.seller_count || 1;
    const priceRange = (result.max_price || 0) - (result.min_price || 0);

    // Індекс конкуренції: більше моделей на продавця = більша конкуренція
    const competitionIndex = Math.min(modelCount / sellerCount / 10, 1.0);

    // Насиченість ринку: більший діапазон цін = менша насиченість
    const saturation = priceRange > 0 ? Math.min(1 / (priceRange / (result.avg_price || 1)), 1.0) : 1.0;

    return { index: competitionIndex, saturation };
  }

  /**
   * Допоміжні методи
   */
  private async getPriceHistory(category: string, days: number): Promise<Array<{ price: number; date: Date }>> {
    const query = `
      SELECT ph.price, ph.recorded_at
      FROM price_history ph
      JOIN models m ON ph.model_id = m.id
      WHERE m.category = ? AND ph.recorded_at >= datetime('now', '-${days} days')
      ORDER BY ph.recorded_at
    `;

    const results = await this.db.prepare(query).bind(category).all<any>();
    return results.map(r => ({ price: r.price, date: new Date(r.recorded_at) }));
  }

  private calculateVolatility(priceHistory: Array<{ price: number; date: Date }>): number {
    if (priceHistory.length < 2) return 0;

    const prices = priceHistory.map(p => p.price);
    const mean = prices.reduce((sum, p) => sum + p, 0) / prices.length;
    const variance = prices.reduce((sum, p) => sum + Math.pow(p - mean, 2), 0) / prices.length;
    
    return Math.sqrt(variance) / mean; // коефіцієнт варіації
  }

  private calculateDemandTrend(totalViews: number, totalDownloads: number): number {
    if (totalViews === 0) return 0;
    return totalDownloads / totalViews; // конверсія переглядів в завантаження
  }

  private calculateSupplyTrend(totalModels: number): number {
    // Простий розрахунок тренду пропозиції
    return Math.min(totalModels / 100, 1.0);
  }

  private async calculateMedianPrice(category: string): Promise<number> {
    const query = `
      SELECT price FROM models 
      WHERE category = ? AND price > 0
      ORDER BY price
    `;

    const prices = await this.db.prepare(query).bind(category).all<{ price: number }>();
    
    if (prices.length === 0) return 0;

    const middle = Math.floor(prices.length / 2);
    if (prices.length % 2 === 0) {
      return (prices[middle - 1].price + prices[middle].price) / 2;
    } else {
      return prices[middle].price;
    }
  }

  private async calculateSupplyLevel(category: string): Promise<number> {
    const query = `
      SELECT COUNT(*) as count FROM models 
      WHERE category = ? AND created_at >= datetime('now', '-7 days')
    `;

    const result = await this.db.prepare(query).bind(category).first<{ count: number }>();
    return Math.min((result?.count || 0) / 50, 1.0); // нормалізуємо до 0-1
  }

  private async calculateCompetitionLevel(category: string): Promise<number> {
    const query = `
      SELECT 
        COUNT(*) as model_count,
        COUNT(DISTINCT user_id) as seller_count
      FROM models 
      WHERE category = ?
    `;

    const result = await this.db.prepare(query).bind(category).first<any>();
    const modelsPerSeller = (result.model_count || 0) / (result.seller_count || 1);
    
    return Math.min(modelsPerSeller / 10, 1.0);
  }

  private async calculateGrowthRate(category: string): Promise<number> {
    const query = `
      SELECT 
        COUNT(CASE WHEN created_at >= datetime('now', '-7 days') THEN 1 END) as recent,
        COUNT(CASE WHEN created_at >= datetime('now', '-14 days') AND created_at < datetime('now', '-7 days') THEN 1 END) as previous
      FROM models 
      WHERE category = ?
    `;

    const result = await this.db.prepare(query).bind(category).first<any>();
    const recent = result.recent || 0;
    const previous = result.previous || 1;

    return ((recent - previous) / previous) * 100;
  }

  private calculateConfidence(
    metrics: MarketMetrics,
    trends: any,
    demand: any,
    competition: any
  ): number {
    // Розраховуємо впевненість на основі кількості даних та їх якості
    const dataQuality = Math.min(metrics.totalVolume / 100, 1.0);
    const trendStability = 1 - Math.abs(trends.growthRate) / 100;
    const demandConsistency = demand.level;
    
    return (dataQuality + trendStability + demandConsistency) / 3;
  }

  private async saveMarketAnalysis(analysis: Omit<MarketAnalysis, 'id' | 'createdAt'>): Promise<void> {
    const id = `ma_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const query = `
      INSERT INTO market_analysis (
        id, category, analysis_date, average_price, median_price, price_trend,
        demand_level, supply_level, competition_index, growth_rate,
        seasonal_factor, market_saturation, ai_confidence
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await this.db.prepare(query).bind(
      id,
      analysis.category,
      analysis.analysisDate.toISOString(),
      analysis.averagePrice,
      analysis.medianPrice,
      analysis.priceTrend,
      analysis.demandLevel,
      analysis.supplyLevel,
      analysis.competitionIndex,
      analysis.growthRate,
      analysis.seasonalFactor,
      analysis.marketSaturation,
      analysis.aiConfidence
    ).run();
  }
}
