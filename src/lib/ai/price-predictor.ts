/**
 * Price Predictor - AI сервіс для прогнозування цін на 3D моделі
 */

import { getDb } from '@/lib/db';
import { AIAnalysisResult, AIPrediction, PriceHistory } from './types';

export class PricePredictor {
  private db = getDb();

  /**
   * Прогнозує оптимальну ціну для моделі
   */
  async predictOptimalPrice(modelId: string): Promise<AIAnalysisResult> {
    try {
      const model = await this.getModelData(modelId);
      if (!model) {
        throw new Error('Model not found');
      }

      const priceHistory = await this.getPriceHistory(modelId);
      const marketData = await this.getMarketData(model.category);
      const demandData = await this.getDemandData(modelId);

      // Різні методи прогнозування
      const trendBasedPrice = this.calculateTrendBasedPrice(priceHistory);
      const marketBasedPrice = this.calculateMarketBasedPrice(model, marketData);
      const demandBasedPrice = this.calculateDemandBasedPrice(model, demandData);
      const competitionBasedPrice = await this.calculateCompetitionBasedPrice(model);

      // Зважена комбінація прогнозів
      const weights = this.calculatePredictionWeights(priceHistory, marketData, demandData);
      const optimalPrice = this.combinePredictions([
        { price: trendBasedPrice, weight: weights.trend },
        { price: marketBasedPrice, weight: weights.market },
        { price: demandBasedPrice, weight: weights.demand },
        { price: competitionBasedPrice, weight: weights.competition }
      ]);

      const confidence = this.calculateConfidence(priceHistory, marketData, demandData);

      // Зберігаємо прогноз
      await this.savePrediction({
        modelId,
        predictionType: 'price',
        currentValue: model.price,
        predictedValue: optimalPrice,
        predictionHorizon: 30, // 30 днів
        confidenceScore: confidence,
        factors: this.getInfluencingFactors(model, marketData, demandData),
        algorithmVersion: '1.0'
      });

      return {
        success: true,
        data: {
          currentPrice: model.price,
          optimalPrice,
          priceChange: optimalPrice - model.price,
          priceChangePercentage: ((optimalPrice - model.price) / model.price) * 100,
          confidence,
          factors: this.getInfluencingFactors(model, marketData, demandData)
        },
        confidence,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Price prediction error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        confidence: 0,
        timestamp: new Date()
      };
    }
  }

  /**
   * Прогнозує тренд цін для категорії
   */
  async predictCategoryPriceTrend(category: string, horizon: number = 30): Promise<AIAnalysisResult> {
    try {
      const marketData = await this.getMarketData(category);
      const historicalTrends = await this.getCategoryPriceHistory(category, 90);
      
      const trendAnalysis = this.analyzePriceTrend(historicalTrends);
      const seasonalFactors = await this.getSeasonalFactors(category);
      
      const predictedTrend = this.calculateFutureTrend(trendAnalysis, seasonalFactors, horizon);

      return {
        success: true,
        data: {
          category,
          currentAveragePrice: marketData.averagePrice,
          predictedAveragePrice: predictedTrend.averagePrice,
          trendDirection: predictedTrend.direction,
          volatility: predictedTrend.volatility,
          confidence: predictedTrend.confidence
        },
        confidence: predictedTrend.confidence,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Category price trend prediction error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        confidence: 0,
        timestamp: new Date()
      };
    }
  }

  /**
   * Методи розрахунку цін
   */
  private calculateTrendBasedPrice(priceHistory: PriceHistory[]): number {
    if (priceHistory.length < 3) return 0;

    // Лінійна регресія для тренду
    const prices = priceHistory.map(p => p.price);
    const n = prices.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = prices;

    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    // Прогноз на наступний період
    return intercept + slope * n;
  }

  private calculateMarketBasedPrice(model: any, marketData: any): number {
    // Базуємося на середній ціні ринку з корекцією на якість моделі
    const qualityMultiplier = this.calculateQualityMultiplier(model);
    return marketData.averagePrice * qualityMultiplier;
  }

  private calculateDemandBasedPrice(model: any, demandData: any): number {
    // Ціна на основі попиту
    const basePrice = model.price;
    const demandMultiplier = 1 + (demandData.demandLevel - 0.5) * 0.4; // ±20% корекція
    return basePrice * demandMultiplier;
  }

  private async calculateCompetitionBasedPrice(model: any): Promise<number> {
    const query = `
      SELECT AVG(price) as avg_price, COUNT(*) as competitor_count
      FROM models 
      WHERE category = ? AND id != ? AND price > 0
    `;

    const result = await this.db.prepare(query).bind(model.category, model.id).first<any>();
    
    if (!result || result.competitor_count === 0) return model.price;

    // Корекція на основі конкуренції
    const competitionFactor = Math.max(0.8, 1 - (result.competitor_count / 100));
    return result.avg_price * competitionFactor;
  }

  private calculateQualityMultiplier(model: any): number {
    // Оцінюємо якість моделі на основі метрик
    const downloadScore = Math.min(model.download_count / 100, 2); // максимум 2x
    const viewScore = Math.min(model.view_count / 1000, 1.5); // максимум 1.5x
    const likeScore = Math.min(model.like_count / 50, 1.3); // максимум 1.3x

    return (downloadScore + viewScore + likeScore) / 3;
  }

  private combinePredictions(predictions: Array<{ price: number; weight: number }>): number {
    const totalWeight = predictions.reduce((sum, p) => sum + p.weight, 0);
    if (totalWeight === 0) return 0;

    return predictions.reduce((sum, p) => sum + (p.price * p.weight), 0) / totalWeight;
  }

  private calculatePredictionWeights(priceHistory: PriceHistory[], marketData: any, demandData: any): {
    trend: number;
    market: number;
    demand: number;
    competition: number;
  } {
    // Ваги на основі доступності та якості даних
    const historyWeight = Math.min(priceHistory.length / 10, 1);
    const marketWeight = marketData ? 0.8 : 0.2;
    const demandWeight = demandData ? 0.7 : 0.3;
    const competitionWeight = 0.6;

    return {
      trend: historyWeight,
      market: marketWeight,
      demand: demandWeight,
      competition: competitionWeight
    };
  }

  private calculateConfidence(priceHistory: PriceHistory[], marketData: any, demandData: any): number {
    // Впевненість на основі кількості та якості даних
    const historyConfidence = Math.min(priceHistory.length / 20, 1);
    const marketConfidence = marketData?.aiConfidence || 0.5;
    const demandConfidence = demandData ? 0.8 : 0.4;

    return (historyConfidence + marketConfidence + demandConfidence) / 3;
  }

  private getInfluencingFactors(model: any, marketData: any, demandData: any): string[] {
    const factors: string[] = [];

    if (marketData?.priceTrend === 'rising') factors.push('Зростаючий ринковий тренд');
    if (marketData?.priceTrend === 'falling') factors.push('Спадний ринковий тренд');
    if (demandData?.demandLevel > 0.7) factors.push('Високий попит');
    if (demandData?.demandLevel < 0.3) factors.push('Низький попит');
    if (model.download_count > 100) factors.push('Популярна модель');
    if (marketData?.competitionIndex > 0.7) factors.push('Висока конкуренція');
    if (marketData?.competitionIndex < 0.3) factors.push('Низька конкуренція');

    return factors;
  }

  /**
   * Допоміжні методи для отримання даних
   */
  private async getModelData(modelId: string): Promise<any> {
    const query = `
      SELECT id, name, price, category, download_count, view_count, like_count, created_at
      FROM models WHERE id = ?
    `;
    return await this.db.prepare(query).bind(modelId).first();
  }

  private async getPriceHistory(modelId: string): Promise<PriceHistory[]> {
    const query = `
      SELECT price, recorded_at, source
      FROM price_history 
      WHERE model_id = ? 
      ORDER BY recorded_at DESC 
      LIMIT 30
    `;

    const results = await this.db.prepare(query).bind(modelId).all<any>();
    return results.map(r => ({
      id: '',
      modelId,
      price: r.price,
      currency: 'USD',
      source: r.source,
      recordedAt: new Date(r.recorded_at)
    }));
  }

  private async getMarketData(category: string): Promise<any> {
    const query = `
      SELECT * FROM market_analysis 
      WHERE category = ? 
      ORDER BY analysis_date DESC 
      LIMIT 1
    `;
    return await this.db.prepare(query).bind(category).first();
  }

  private async getDemandData(modelId: string): Promise<any> {
    const query = `
      SELECT 
        COUNT(CASE WHEN created_at >= datetime('now', '-7 days') THEN 1 END) as recent_purchases,
        COUNT(CASE WHEN created_at >= datetime('now', '-30 days') THEN 1 END) as monthly_purchases
      FROM purchases 
      WHERE model_id = ?
    `;

    const result = await this.db.prepare(query).bind(modelId).first<any>();
    
    return {
      demandLevel: Math.min((result?.recent_purchases || 0) / 10, 1),
      monthlyDemand: result?.monthly_purchases || 0
    };
  }

  private async getCategoryPriceHistory(category: string, days: number): Promise<Array<{ date: Date; averagePrice: number }>> {
    const query = `
      SELECT 
        DATE(ph.recorded_at) as date,
        AVG(ph.price) as average_price
      FROM price_history ph
      JOIN models m ON ph.model_id = m.id
      WHERE m.category = ? AND ph.recorded_at >= datetime('now', '-${days} days')
      GROUP BY DATE(ph.recorded_at)
      ORDER BY date
    `;

    const results = await this.db.prepare(query).bind(category).all<any>();
    return results.map(r => ({
      date: new Date(r.date),
      averagePrice: r.average_price
    }));
  }

  private analyzePriceTrend(history: Array<{ date: Date; averagePrice: number }>): any {
    if (history.length < 3) return { direction: 'stable', volatility: 0 };

    const prices = history.map(h => h.averagePrice);
    const recentPrices = prices.slice(-7);
    const olderPrices = prices.slice(0, 7);

    const recentAvg = recentPrices.reduce((a, b) => a + b, 0) / recentPrices.length;
    const olderAvg = olderPrices.reduce((a, b) => a + b, 0) / olderPrices.length;

    const direction = recentAvg > olderAvg ? 'rising' : recentAvg < olderAvg ? 'falling' : 'stable';
    const volatility = this.calculateVolatility(prices);

    return { direction, volatility };
  }

  private calculateVolatility(prices: number[]): number {
    if (prices.length < 2) return 0;

    const mean = prices.reduce((a, b) => a + b, 0) / prices.length;
    const variance = prices.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / prices.length;
    
    return Math.sqrt(variance) / mean;
  }

  private async getSeasonalFactors(category: string): Promise<any> {
    // Спрощений розрахунок сезонних факторів
    return { factor: 1.0, confidence: 0.5 };
  }

  private calculateFutureTrend(trendAnalysis: any, seasonalFactors: any, horizon: number): any {
    // Спрощений прогноз майбутнього тренду
    return {
      averagePrice: 0,
      direction: trendAnalysis.direction,
      volatility: trendAnalysis.volatility,
      confidence: 0.6
    };
  }

  private async savePrediction(prediction: Omit<AIPrediction, 'id' | 'createdAt' | 'validUntil'>): Promise<void> {
    const id = `pred_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const validUntil = new Date(Date.now() + prediction.predictionHorizon * 24 * 60 * 60 * 1000);

    const query = `
      INSERT INTO ai_predictions (
        id, model_id, category, prediction_type, current_value, predicted_value,
        prediction_horizon, confidence_score, factors, algorithm_version, valid_until
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await this.db.prepare(query).bind(
      id,
      prediction.modelId,
      null, // category буде null для прогнозів конкретних моделей
      prediction.predictionType,
      prediction.currentValue,
      prediction.predictedValue,
      prediction.predictionHorizon,
      prediction.confidenceScore,
      JSON.stringify(prediction.factors),
      prediction.algorithmVersion,
      validUntil.toISOString()
    ).run();
  }
}
