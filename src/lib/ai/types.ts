/**
 * Типи для AI Arbitrage системи
 */

export interface ArbitrageOpportunity {
  id: string;
  modelId: string;
  sellerId: string;
  buyerId?: string;
  opportunityType: 'price_gap' | 'demand_spike' | 'trend_prediction';
  currentPrice: number;
  suggestedPrice: number;
  potentialProfit: number;
  confidenceScore: number; // 0.0 to 1.0
  marketDemand: number;
  competitionLevel: number;
  riskLevel: 'low' | 'medium' | 'high';
  status: 'active' | 'matched' | 'completed' | 'expired';
  expiresAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface MarketAnalysis {
  id: string;
  category: string;
  analysisDate: Date;
  averagePrice: number;
  medianPrice: number;
  priceTrend: 'rising' | 'falling' | 'stable';
  demandLevel: number; // 0.0 to 1.0
  supplyLevel: number; // 0.0 to 1.0
  competitionIndex: number; // 0.0 to 1.0
  growthRate: number; // percentage
  seasonalFactor: number;
  marketSaturation: number;
  aiConfidence: number;
  createdAt: Date;
}

export interface UserPreferences {
  id: string;
  userId: string;
  preferredCategories: string[];
  priceRangeMin: number;
  priceRangeMax: number;
  riskTolerance: 'low' | 'medium' | 'high';
  investmentBudget: number;
  autoTradingEnabled: boolean;
  notificationPreferences: NotificationPreferences;
  tradingFrequency: 'daily' | 'weekly' | 'monthly';
  profitTarget: number; // percentage
  createdAt: Date;
  updatedAt: Date;
}

export interface NotificationPreferences {
  email: boolean;
  push: boolean;
  sms: boolean;
  opportunities: boolean;
  priceAlerts: boolean;
  marketUpdates: boolean;
}

export interface AIPrediction {
  id: string;
  modelId?: string;
  category?: string;
  predictionType: 'price' | 'demand' | 'trend' | 'popularity';
  currentValue: number;
  predictedValue: number;
  predictionHorizon: number; // days
  confidenceScore: number;
  factors: string[]; // factors influencing prediction
  algorithmVersion: string;
  createdAt: Date;
  validUntil: Date;
}

export interface TradingSession {
  id: string;
  opportunityId: string;
  sellerId: string;
  buyerId: string;
  modelId: string;
  initialPrice: number;
  agreedPrice?: number;
  negotiationRounds: number;
  status: 'initiated' | 'negotiating' | 'agreed' | 'completed' | 'cancelled';
  aiMediated: boolean;
  commissionRate: number;
  platformFee: number;
  sellerProfit: number;
  buyerSavings: number;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface PriceHistory {
  id: string;
  modelId: string;
  price: number;
  currency: string;
  source: 'marketplace' | 'external' | 'ai_suggested';
  recordedAt: Date;
}

export interface DemandPattern {
  id: string;
  category: string;
  timePeriod: 'hourly' | 'daily' | 'weekly' | 'monthly';
  periodValue: number; // hour of day, day of week, etc.
  demandMultiplier: number;
  confidenceLevel: number;
  sampleSize: number;
  lastUpdated: Date;
}

export interface AIAnalysisResult {
  success: boolean;
  data?: any;
  error?: string;
  confidence: number;
  timestamp: Date;
}

export interface MarketMetrics {
  totalVolume: number;
  averagePrice: number;
  priceVolatility: number;
  demandTrend: number;
  supplyTrend: number;
  competitionLevel: number;
  growthRate: number;
}

export interface OpportunityFilters {
  category?: string;
  minProfit?: number;
  maxRisk?: 'low' | 'medium' | 'high';
  minConfidence?: number;
  priceRange?: {
    min: number;
    max: number;
  };
}

export interface MatchingCriteria {
  userId: string;
  preferences: UserPreferences;
  budget: number;
  riskTolerance: 'low' | 'medium' | 'high';
  categories: string[];
}

export interface AIConfig {
  priceAnalysis: {
    lookbackDays: number;
    confidenceThreshold: number;
    volatilityWeight: number;
  };
  demandPrediction: {
    seasonalityWeight: number;
    trendWeight: number;
    competitionWeight: number;
  };
  riskAssessment: {
    marketVolatilityThreshold: number;
    competitionThreshold: number;
    liquidityThreshold: number;
  };
  matching: {
    compatibilityThreshold: number;
    maxRecommendations: number;
    diversificationFactor: number;
  };
}
