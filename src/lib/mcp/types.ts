/**
 * MCP (Model Context Protocol) Types
 * Типи для інтеграції з Cloudflare та Bright Data MCP
 */

// Базові MCP типи
export interface MCPConfig {
  enabled: boolean;
  apiToken?: string;
  endpoint?: string;
  timeout: number;
  retryAttempts: number;
  enableFallback: boolean;
}

export interface MCPResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: string;
  executionTime: number;
}

export interface MCPCallOptions {
  timeout?: number;
  retryAttempts?: number;
  priority?: 'low' | 'normal' | 'high';
  metadata?: Record<string, any>;
}

// Cloudflare MCP типи
export interface CloudflareMCPConfig extends MCPConfig {
  accountId?: string;
  zoneId?: string;
  services: {
    r2: boolean;
    d1: boolean;
    kv: boolean;
    analytics: boolean;
    durableObjects: boolean;
    queues: boolean;
  };
}

export interface CloudflareR2Operation {
  action: 'put' | 'get' | 'delete' | 'list';
  key: string;
  data?: ArrayBuffer | string;
  metadata?: Record<string, string>;
  options?: {
    prefix?: string;
    limit?: number;
    cursor?: string;
  };
}

export interface CloudflareD1Operation {
  action: 'query' | 'execute' | 'batch';
  sql: string;
  params?: any[];
  database?: string;
}

export interface CloudflareKVOperation {
  action: 'put' | 'get' | 'delete' | 'list';
  key: string;
  value?: string;
  options?: {
    ttl?: number;
    metadata?: Record<string, any>;
    prefix?: string;
    limit?: number;
  };
}

export interface CloudflareAnalyticsEvent {
  timestamp?: string;
  event: string;
  properties: Record<string, any>;
  userId?: string;
  sessionId?: string;
}

// Bright Data MCP типи
export interface BrightDataMCPConfig extends MCPConfig {
  zone?: string;
  country?: string;
  sessionId?: string;
  platforms: {
    printables: boolean;
    makerworld: boolean;
    thangs: boolean;
    thingiverse: boolean;
  };
}

export interface ScrapingTarget {
  platform: 'printables' | 'makerworld' | 'thangs' | 'thingiverse';
  url: string;
  type: 'model' | 'search' | 'category' | 'user';
  parameters?: Record<string, any>;
}

export interface ScrapingResult {
  id: string;
  platform: string;
  url: string;
  title: string;
  description?: string;
  author?: string;
  images: string[];
  downloadUrl?: string;
  fileType?: string;
  fileSize?: number;
  tags: string[];
  category?: string;
  likes?: number;
  downloads?: number;
  createdAt?: string;
  updatedAt?: string;
  metadata: Record<string, any>;
}

export interface BrightDataOperation {
  action: 'scrape' | 'search' | 'download';
  target: ScrapingTarget;
  options?: {
    maxResults?: number;
    includeImages?: boolean;
    includeFiles?: boolean;
    timeout?: number;
  };
}

// Service Coordinator типи
export interface ServiceCoordinatorConfig {
  cloudflare: CloudflareMCPConfig;
  brightData: BrightDataMCPConfig;
  coordination: {
    maxConcurrentJobs: number;
    queueTimeout: number;
    retryDelay: number;
    enableAutoRetry: boolean;
  };
}

export interface WorkflowJob {
  id: string;
  type: 'scrape_and_store' | 'download_model' | 'analyze_data';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'retrying';
  priority: 'low' | 'normal' | 'high';
  createdAt: string;
  updatedAt: string;
  startedAt?: string;
  completedAt?: string;
  retryCount: number;
  maxRetries: number;
  error?: string;
  progress: number;
  metadata: Record<string, any>;
}

export interface WorkflowStep {
  id: string;
  jobId: string;
  service: 'cloudflare' | 'brightdata';
  operation: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  input: any;
  output?: any;
  error?: string;
  executionTime?: number;
  timestamp: string;
}

// Health Check типи
export interface ServiceHealth {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  lastCheck: string;
  error?: string;
  details?: Record<string, any>;
}

export interface MCPHealthCheck {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  services: ServiceHealth[];
  timestamp: string;
  uptime: number;
}

// Error типи
export interface MCPError {
  code: string;
  message: string;
  service: string;
  operation: string;
  timestamp: string;
  details?: Record<string, any>;
  retryable: boolean;
}

// Metrics типи
export interface MCPMetrics {
  service: string;
  operation: string;
  count: number;
  successRate: number;
  averageResponseTime: number;
  errorRate: number;
  lastHour: {
    requests: number;
    errors: number;
    averageTime: number;
  };
  lastDay: {
    requests: number;
    errors: number;
    averageTime: number;
  };
}

// Event типи для координації
export interface MCPEvent {
  id: string;
  type: 'job_created' | 'job_started' | 'job_completed' | 'job_failed' | 'service_error';
  service: string;
  jobId?: string;
  data: any;
  timestamp: string;
}

// Utility типи
export type MCPServiceType = 'cloudflare' | 'brightdata';
export type MCPOperationType = string;
export type MCPPriority = 'low' | 'normal' | 'high';
export type MCPStatus = 'pending' | 'running' | 'completed' | 'failed' | 'retrying';

// Export all types
export * from './types';
