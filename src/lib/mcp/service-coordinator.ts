/**
 * MCP Service Coordinator
 * Координація між Cloudflare та Bright Data MCP сервісами
 */

import { CloudflareMCPClient } from '../cloudflare/real-mcp-client';
import { BrightDataMCPClient } from '../bright-data/real-mcp-client';
import {
  ServiceCoordinatorConfig,
  WorkflowJob,
  WorkflowStep,
  MCPEvent,
  MCPHealthCheck,
  ScrapingTarget,
  ScrapingResult,
  MCPResponse
} from './types';

export class MCPServiceCoordinator {
  private config: ServiceCoordinatorConfig;
  private cloudflareClient: CloudflareMCPClient;
  private brightDataClient: BrightDataMCPClient;
  private jobs: Map<string, WorkflowJob> = new Map();
  private steps: Map<string, WorkflowStep[]> = new Map();
  private eventQueue: MCPEvent[] = [];
  private isProcessing = false;

  constructor(config: ServiceCoordinatorConfig) {
    this.config = config;
    this.cloudflareClient = new CloudflareMCPClient(config.cloudflare);
    this.brightDataClient = new BrightDataMCPClient(config.brightData);
  }

  /**
   * Ініціалізація координатора
   */
  async initialize(cloudflareBindings?: any): Promise<void> {
    console.log('🎯 Ініціалізація MCP Service Coordinator');

    // Ініціалізуємо клієнти
    if (cloudflareBindings) {
      await this.cloudflareClient.initialize(cloudflareBindings);
    }
    await this.brightDataClient.initialize();

    // Запускаємо обробку черги
    this.startEventProcessing();

    console.log('✅ MCP Service Coordinator готовий до роботи');
  }

  /**
   * Створення нового workflow job
   */
  async createJob(
    type: 'scrape_and_store' | 'download_model' | 'analyze_data',
    metadata: Record<string, any>,
    priority: 'low' | 'normal' | 'high' = 'normal'
  ): Promise<string> {
    const jobId = `job-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const job: WorkflowJob = {
      id: jobId,
      type,
      status: 'pending',
      priority,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      retryCount: 0,
      maxRetries: this.config.coordination.enableAutoRetry ? 3 : 0,
      progress: 0,
      metadata
    };

    this.jobs.set(jobId, job);
    this.steps.set(jobId, []);

    // Додаємо подію до черги
    this.addEvent({
      id: `event-${Date.now()}`,
      type: 'job_created',
      service: 'coordinator',
      jobId,
      data: { type, priority, metadata },
      timestamp: new Date().toISOString()
    });

    console.log(`📋 Створено job ${jobId} типу ${type}`);
    return jobId;
  }

  /**
   * Виконання scrape and store workflow
   */
  async executeScrapeAndStoreWorkflow(jobId: string): Promise<MCPResponse> {
    const job = this.jobs.get(jobId);
    if (!job) {
      throw new Error(`Job ${jobId} не знайдено`);
    }

    try {
      this.updateJobStatus(jobId, 'running');
      console.log(`🚀 Запуск scrape and store workflow для job ${jobId}`);

      const target = job.metadata.target as ScrapingTarget;
      if (!target) {
        throw new Error('Target не вказано в metadata');
      }

      // Крок 1: Скрапінг даних
      const scrapingStep = await this.executeStep(jobId, 'brightdata', 'scraping', {
        action: 'scrape',
        target,
        options: job.metadata.scrapingOptions
      });

      if (!scrapingStep.output?.success) {
        throw new Error(`Помилка скрапінгу: ${scrapingStep.error}`);
      }

      const scrapedData = scrapingStep.output.data as ScrapingResult[];
      this.updateJobProgress(jobId, 33);

      // Крок 2: Завантаження файлів на R2
      const storageResults = [];
      for (const item of scrapedData) {
        if (item.downloadUrl) {
          const storageStep = await this.executeStep(jobId, 'cloudflare', 'r2_storage', {
            action: 'put',
            key: `models/${item.platform}/${item.id}/${item.title.replace(/[^a-zA-Z0-9]/g, '_')}.stl`,
            data: await this.downloadFile(item.downloadUrl),
            metadata: {
              'model-id': item.id,
              'platform': item.platform,
              'title': item.title,
              'author': item.author || '',
              'original-url': item.url,
              'uploaded-at': new Date().toISOString()
            }
          });

          if (storageStep.output?.success) {
            storageResults.push({
              modelId: item.id,
              r2Key: storageStep.input.key,
              success: true
            });
          }
        }
      }

      this.updateJobProgress(jobId, 66);

      // Крок 3: Збереження метаданих в D1
      const metadataStep = await this.executeStep(jobId, 'cloudflare', 'd1_storage', {
        action: 'execute',
        sql: `INSERT INTO scraped_models (id, platform, title, description, author, url, images, tags, metadata, created_at) 
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        params: scrapedData.map(item => [
          item.id,
          item.platform,
          item.title,
          item.description || '',
          item.author || '',
          item.url,
          JSON.stringify(item.images),
          JSON.stringify(item.tags),
          JSON.stringify(item.metadata),
          new Date().toISOString()
        ])
      });

      this.updateJobProgress(jobId, 100);

      // Крок 4: Аналітика
      await this.executeStep(jobId, 'cloudflare', 'analytics', {
        event: 'models_scraped',
        properties: {
          platform: target.platform,
          count: scrapedData.length,
          successful_downloads: storageResults.filter(r => r.success).length,
          job_id: jobId
        }
      });

      this.updateJobStatus(jobId, 'completed');

      return {
        success: true,
        data: {
          scrapedModels: scrapedData.length,
          storedFiles: storageResults.filter(r => r.success).length,
          storageResults
        },
        timestamp: new Date().toISOString(),
        executionTime: Date.now() - new Date(job.createdAt).getTime()
      };

    } catch (error) {
      console.error(`❌ Помилка workflow ${jobId}:`, error);
      this.updateJobStatus(jobId, 'failed', error instanceof Error ? error.message : 'Невідома помилка');
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Невідома помилка workflow',
        timestamp: new Date().toISOString(),
        executionTime: Date.now() - new Date(job.createdAt).getTime()
      };
    }
  }

  /**
   * Виконання кроку workflow
   */
  private async executeStep(
    jobId: string,
    service: 'cloudflare' | 'brightdata',
    operation: string,
    input: any
  ): Promise<WorkflowStep> {
    const stepId = `step-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const startTime = Date.now();

    const step: WorkflowStep = {
      id: stepId,
      jobId,
      service,
      operation,
      status: 'running',
      input,
      timestamp: new Date().toISOString()
    };

    // Додаємо крок до списку
    const jobSteps = this.steps.get(jobId) || [];
    jobSteps.push(step);
    this.steps.set(jobId, jobSteps);

    try {
      console.log(`⚙️ Виконання кроку ${operation} для job ${jobId}`);

      let result: MCPResponse;

      if (service === 'cloudflare') {
        result = await this.executeCloudflareOperation(operation, input);
      } else {
        result = await this.executeBrightDataOperation(operation, input);
      }

      step.status = result.success ? 'completed' : 'failed';
      step.output = result;
      step.executionTime = Date.now() - startTime;

      if (!result.success) {
        step.error = result.error;
      }

    } catch (error) {
      step.status = 'failed';
      step.error = error instanceof Error ? error.message : 'Невідома помилка кроку';
      step.executionTime = Date.now() - startTime;
    }

    return step;
  }

  /**
   * Виконання Cloudflare операції
   */
  private async executeCloudflareOperation(operation: string, input: any): Promise<MCPResponse> {
    switch (operation) {
      case 'r2_storage':
        return await this.cloudflareClient.r2Operation(input);
      
      case 'd1_storage':
        return await this.cloudflareClient.d1Operation(input);
      
      case 'kv_storage':
        return await this.cloudflareClient.kvOperation(input);
      
      case 'analytics':
        return await this.cloudflareClient.sendAnalyticsEvent(input);
      
      default:
        throw new Error(`Непідтримувана Cloudflare операція: ${operation}`);
    }
  }

  /**
   * Виконання Bright Data операції
   */
  private async executeBrightDataOperation(operation: string, input: any): Promise<MCPResponse> {
    switch (operation) {
      case 'scraping':
        return await this.brightDataClient.scrapingOperation(input);
      
      default:
        throw new Error(`Непідтримувана Bright Data операція: ${operation}`);
    }
  }

  /**
   * Завантаження файлу
   */
  private async downloadFile(url: string): Promise<ArrayBuffer> {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.arrayBuffer();
    } catch (error) {
      console.error('❌ Помилка завантаження файлу:', error);
      throw error;
    }
  }

  /**
   * Оновлення статусу job
   */
  private updateJobStatus(jobId: string, status: WorkflowJob['status'], error?: string): void {
    const job = this.jobs.get(jobId);
    if (job) {
      job.status = status;
      job.updatedAt = new Date().toISOString();
      
      if (status === 'completed') {
        job.completedAt = new Date().toISOString();
      } else if (status === 'running' && !job.startedAt) {
        job.startedAt = new Date().toISOString();
      }
      
      if (error) {
        job.error = error;
      }

      this.jobs.set(jobId, job);
    }
  }

  /**
   * Оновлення прогресу job
   */
  private updateJobProgress(jobId: string, progress: number): void {
    const job = this.jobs.get(jobId);
    if (job) {
      job.progress = Math.min(100, Math.max(0, progress));
      job.updatedAt = new Date().toISOString();
      this.jobs.set(jobId, job);
    }
  }

  /**
   * Додавання події до черги
   */
  private addEvent(event: MCPEvent): void {
    this.eventQueue.push(event);
  }

  /**
   * Запуск обробки подій
   */
  private startEventProcessing(): void {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    
    setInterval(async () => {
      if (this.eventQueue.length > 0) {
        const event = this.eventQueue.shift();
        if (event) {
          await this.processEvent(event);
        }
      }
    }, 1000);
  }

  /**
   * Обробка події
   */
  private async processEvent(event: MCPEvent): Promise<void> {
    try {
      console.log(`📨 Обробка події ${event.type} для job ${event.jobId}`);

      switch (event.type) {
        case 'job_created':
          if (event.jobId) {
            const job = this.jobs.get(event.jobId);
            if (job && job.type === 'scrape_and_store') {
              await this.executeScrapeAndStoreWorkflow(event.jobId);
            }
          }
          break;
        
        // Додаткові типи подій можна обробляти тут
      }

    } catch (error) {
      console.error('❌ Помилка обробки події:', error);
    }
  }

  /**
   * Health check всіх сервісів
   */
  async healthCheck(): Promise<MCPHealthCheck> {
    const cloudflareHealth = await this.cloudflareClient.healthCheck();
    const brightDataHealth = await this.brightDataClient.healthCheck();
    
    const allServices = [...cloudflareHealth, ...brightDataHealth];
    const unhealthyServices = allServices.filter(s => s.status === 'unhealthy');
    const degradedServices = allServices.filter(s => s.status === 'degraded');

    let overall: 'healthy' | 'degraded' | 'unhealthy';
    if (unhealthyServices.length > 0) {
      overall = 'unhealthy';
    } else if (degradedServices.length > 0) {
      overall = 'degraded';
    } else {
      overall = 'healthy';
    }

    return {
      overall,
      services: allServices,
      timestamp: new Date().toISOString(),
      uptime: Date.now() - new Date().getTime() // Це потрібно буде покращити
    };
  }

  /**
   * Отримання інформації про job
   */
  getJob(jobId: string): WorkflowJob | undefined {
    return this.jobs.get(jobId);
  }

  /**
   * Отримання кроків job
   */
  getJobSteps(jobId: string): WorkflowStep[] {
    return this.steps.get(jobId) || [];
  }

  /**
   * Отримання всіх jobs
   */
  getAllJobs(): WorkflowJob[] {
    return Array.from(this.jobs.values());
  }

  /**
   * Отримання метрик
   */
  getMetrics(): any {
    return {
      cloudflare: this.cloudflareClient.getMetrics(),
      brightData: this.brightDataClient.getMetrics(),
      jobs: {
        total: this.jobs.size,
        pending: Array.from(this.jobs.values()).filter(j => j.status === 'pending').length,
        running: Array.from(this.jobs.values()).filter(j => j.status === 'running').length,
        completed: Array.from(this.jobs.values()).filter(j => j.status === 'completed').length,
        failed: Array.from(this.jobs.values()).filter(j => j.status === 'failed').length
      }
    };
  }
}
