/**
 * Real-time Alert Engine for Market Changes
 */

import { PriceChangeEvent, MarketOpportunity } from '@/lib/database/competitor-db';

export interface AlertRule {
  id: string;
  userId: string;
  name: string;
  description?: string;
  ruleType: 'price_change' | 'new_competitor' | 'trend_shift' | 'opportunity' | 'performance';
  platform?: string;
  category?: string;
  conditions: AlertConditions;
  notificationChannels: ('email' | 'websocket' | 'webhook' | 'push')[];
  frequency: 'immediate' | 'daily' | 'weekly';
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  lastTriggered?: string;
  triggerCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface AlertConditions {
  // Price change conditions
  priceChangeThreshold?: number; // Percentage
  priceChangeDirection?: 'increase' | 'decrease' | 'any';
  minPriceChange?: number; // Absolute value
  
  // Market conditions
  competitorCountThreshold?: number;
  marketShareThreshold?: number;
  opportunityScoreThreshold?: number;
  
  // Performance conditions
  downloadThreshold?: number;
  ratingThreshold?: number;
  revenueThreshold?: number;
  
  // Time-based conditions
  timeWindow?: string; // '1hour', '1day', '1week'
  consecutivePeriods?: number;
}

export interface AlertNotification {
  id: string;
  alertRuleId: string;
  userId: string;
  title: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  channel: 'email' | 'websocket' | 'webhook' | 'push';
  data: Record<string, any>;
  status: 'sent' | 'delivered' | 'failed' | 'acknowledged';
  sentAt: string;
  acknowledgedAt?: string;
}

export interface AlertTriggerEvent {
  type: 'price_change' | 'new_competitor' | 'trend_shift' | 'opportunity' | 'performance';
  platform: string;
  category?: string;
  data: any;
  timestamp: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export class AlertEngine {
  private alertRules: Map<string, AlertRule> = new Map();
  private notificationQueue: AlertNotification[] = [];
  private websocketConnections: Map<string, WebSocket> = new Map();

  /**
   * Add or update an alert rule
   */
  addAlertRule(rule: AlertRule): void {
    this.alertRules.set(rule.id, rule);
    console.log(`🚨 Added alert rule: ${rule.name} (${rule.ruleType})`);
  }

  /**
   * Remove an alert rule
   */
  removeAlertRule(ruleId: string): boolean {
    const removed = this.alertRules.delete(ruleId);
    if (removed) {
      console.log(`🗑️ Removed alert rule: ${ruleId}`);
    }
    return removed;
  }

  /**
   * Process price change events and trigger alerts
   */
  async processPriceChangeEvents(events: PriceChangeEvent[]): Promise<AlertNotification[]> {
    const notifications: AlertNotification[] = [];

    for (const event of events) {
      const relevantRules = Array.from(this.alertRules.values()).filter(rule => 
        rule.enabled && 
        rule.ruleType === 'price_change' &&
        this.matchesPlatformCategory(rule, event.platform, event.category)
      );

      for (const rule of relevantRules) {
        if (this.evaluatePriceChangeRule(rule, event)) {
          const notification = await this.createPriceChangeNotification(rule, event);
          notifications.push(notification);
          await this.sendNotification(notification);
        }
      }
    }

    return notifications;
  }

  /**
   * Process market opportunities and trigger alerts
   */
  async processMarketOpportunities(opportunities: MarketOpportunity[]): Promise<AlertNotification[]> {
    const notifications: AlertNotification[] = [];

    for (const opportunity of opportunities) {
      const relevantRules = Array.from(this.alertRules.values()).filter(rule => 
        rule.enabled && 
        rule.ruleType === 'opportunity' &&
        this.matchesPlatformCategory(rule, opportunity.platform, opportunity.category)
      );

      for (const rule of relevantRules) {
        if (this.evaluateOpportunityRule(rule, opportunity)) {
          const notification = await this.createOpportunityNotification(rule, opportunity);
          notifications.push(notification);
          await this.sendNotification(notification);
        }
      }
    }

    return notifications;
  }

  /**
   * Process trend shift events
   */
  async processTrendShifts(trendEvents: AlertTriggerEvent[]): Promise<AlertNotification[]> {
    const notifications: AlertNotification[] = [];

    for (const event of trendEvents) {
      const relevantRules = Array.from(this.alertRules.values()).filter(rule => 
        rule.enabled && 
        rule.ruleType === 'trend_shift' &&
        this.matchesPlatformCategory(rule, event.platform, event.category)
      );

      for (const rule of relevantRules) {
        const notification = await this.createTrendShiftNotification(rule, event);
        notifications.push(notification);
        await this.sendNotification(notification);
      }
    }

    return notifications;
  }

  /**
   * Register WebSocket connection for real-time notifications
   */
  registerWebSocketConnection(userId: string, ws: WebSocket): void {
    this.websocketConnections.set(userId, ws);
    
    ws.on('close', () => {
      this.websocketConnections.delete(userId);
    });

    console.log(`🔌 WebSocket connected for user: ${userId}`);
  }

  /**
   * Send notification through appropriate channels
   */
  private async sendNotification(notification: AlertNotification): Promise<void> {
    try {
      switch (notification.channel) {
        case 'websocket':
          await this.sendWebSocketNotification(notification);
          break;
        case 'email':
          await this.sendEmailNotification(notification);
          break;
        case 'webhook':
          await this.sendWebhookNotification(notification);
          break;
        case 'push':
          await this.sendPushNotification(notification);
          break;
      }

      notification.status = 'delivered';
      console.log(`📤 Sent ${notification.channel} notification: ${notification.title}`);

    } catch (error) {
      notification.status = 'failed';
      console.error(`❌ Failed to send notification:`, error);
    }
  }

  /**
   * Send WebSocket notification for real-time updates
   */
  private async sendWebSocketNotification(notification: AlertNotification): Promise<void> {
    const ws = this.websocketConnections.get(notification.userId);
    
    if (ws && ws.readyState === WebSocket.OPEN) {
      const message = {
        type: 'alert',
        notification: {
          id: notification.id,
          title: notification.title,
          message: notification.message,
          severity: notification.severity,
          data: notification.data,
          timestamp: notification.sentAt
        }
      };

      ws.send(JSON.stringify(message));
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(notification: AlertNotification): Promise<void> {
    // In a real implementation, this would integrate with an email service
    console.log(`📧 Email notification: ${notification.title}`);
    console.log(`   To: User ${notification.userId}`);
    console.log(`   Message: ${notification.message}`);
    
    // Simulate email sending
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Send webhook notification
   */
  private async sendWebhookNotification(notification: AlertNotification): Promise<void> {
    // In a real implementation, this would make HTTP requests to configured webhooks
    console.log(`🪝 Webhook notification: ${notification.title}`);
    
    const webhookPayload = {
      alert_id: notification.id,
      title: notification.title,
      message: notification.message,
      severity: notification.severity,
      data: notification.data,
      timestamp: notification.sentAt
    };

    // Simulate webhook call
    await new Promise(resolve => setTimeout(resolve, 200));
  }

  /**
   * Send push notification
   */
  private async sendPushNotification(notification: AlertNotification): Promise<void> {
    // In a real implementation, this would integrate with push notification services
    console.log(`📱 Push notification: ${notification.title}`);
    
    // Simulate push notification
    await new Promise(resolve => setTimeout(resolve, 150));
  }

  /**
   * Evaluate if a price change event matches rule conditions
   */
  private evaluatePriceChangeRule(rule: AlertRule, event: PriceChangeEvent): boolean {
    const conditions = rule.conditions;

    // Check price change threshold
    if (conditions.priceChangeThreshold) {
      if (Math.abs(event.priceChangePercentage) < conditions.priceChangeThreshold) {
        return false;
      }
    }

    // Check price change direction
    if (conditions.priceChangeDirection && conditions.priceChangeDirection !== 'any') {
      if (conditions.priceChangeDirection !== event.changeType) {
        return false;
      }
    }

    // Check minimum absolute price change
    if (conditions.minPriceChange) {
      const absoluteChange = Math.abs(event.newPrice - event.oldPrice);
      if (absoluteChange < conditions.minPriceChange) {
        return false;
      }
    }

    // Check frequency limits
    if (rule.frequency !== 'immediate' && rule.lastTriggered) {
      const timeSinceLastTrigger = Date.now() - new Date(rule.lastTriggered).getTime();
      const frequencyMs = rule.frequency === 'daily' ? 24 * 60 * 60 * 1000 : 7 * 24 * 60 * 60 * 1000;
      
      if (timeSinceLastTrigger < frequencyMs) {
        return false;
      }
    }

    return true;
  }

  /**
   * Evaluate if a market opportunity matches rule conditions
   */
  private evaluateOpportunityRule(rule: AlertRule, opportunity: MarketOpportunity): boolean {
    const conditions = rule.conditions;

    // Check opportunity score threshold
    if (conditions.opportunityScoreThreshold) {
      if (opportunity.score < conditions.opportunityScoreThreshold) {
        return false;
      }
    }

    return true;
  }

  /**
   * Check if rule matches platform and category
   */
  private matchesPlatformCategory(rule: AlertRule, platform: string, category?: string): boolean {
    if (rule.platform && rule.platform !== platform) {
      return false;
    }

    if (rule.category && category && rule.category !== category) {
      return false;
    }

    return true;
  }

  /**
   * Create price change notification
   */
  private async createPriceChangeNotification(rule: AlertRule, event: PriceChangeEvent): Promise<AlertNotification> {
    const changeDirection = event.changeType === 'increase' ? '📈' : '📉';
    const title = `${changeDirection} Price ${event.changeType} Alert`;
    
    const message = `${event.creatorName || 'Model'} on ${event.platform} changed price by ${event.priceChangePercentage.toFixed(1)}% (${event.oldPrice} → ${event.newPrice})`;

    const notification: AlertNotification = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      alertRuleId: rule.id,
      userId: rule.userId,
      title,
      message,
      severity: this.calculatePriceChangeSeverity(event),
      channel: rule.notificationChannels[0], // Use first channel for now
      data: {
        event,
        platform: event.platform,
        category: event.category,
        priceChange: event.priceChangePercentage
      },
      status: 'sent',
      sentAt: new Date().toISOString()
    };

    // Update rule trigger info
    rule.lastTriggered = notification.sentAt;
    rule.triggerCount++;

    return notification;
  }

  /**
   * Create opportunity notification
   */
  private async createOpportunityNotification(rule: AlertRule, opportunity: MarketOpportunity): Promise<AlertNotification> {
    const title = `🎯 Market Opportunity Alert`;
    const message = `New ${opportunity.opportunityType.replace('_', ' ')} opportunity in ${opportunity.category} on ${opportunity.platform} (Score: ${opportunity.score}/100)`;

    const notification: AlertNotification = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      alertRuleId: rule.id,
      userId: rule.userId,
      title,
      message,
      severity: opportunity.score > 80 ? 'high' : opportunity.score > 60 ? 'medium' : 'low',
      channel: rule.notificationChannels[0],
      data: {
        opportunity,
        platform: opportunity.platform,
        category: opportunity.category,
        score: opportunity.score
      },
      status: 'sent',
      sentAt: new Date().toISOString()
    };

    rule.lastTriggered = notification.sentAt;
    rule.triggerCount++;

    return notification;
  }

  /**
   * Create trend shift notification
   */
  private async createTrendShiftNotification(rule: AlertRule, event: AlertTriggerEvent): Promise<AlertNotification> {
    const title = `📊 Market Trend Shift Alert`;
    const message = `Trend shift detected in ${event.category} on ${event.platform}`;

    const notification: AlertNotification = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      alertRuleId: rule.id,
      userId: rule.userId,
      title,
      message,
      severity: event.severity,
      channel: rule.notificationChannels[0],
      data: {
        event,
        platform: event.platform,
        category: event.category
      },
      status: 'sent',
      sentAt: new Date().toISOString()
    };

    rule.lastTriggered = notification.sentAt;
    rule.triggerCount++;

    return notification;
  }

  /**
   * Calculate severity based on price change magnitude
   */
  private calculatePriceChangeSeverity(event: PriceChangeEvent): 'low' | 'medium' | 'high' | 'critical' {
    const changePercent = Math.abs(event.priceChangePercentage);
    
    if (changePercent >= 50) return 'critical';
    if (changePercent >= 25) return 'high';
    if (changePercent >= 10) return 'medium';
    return 'low';
  }

  /**
   * Get alert statistics
   */
  getAlertStatistics(): {
    totalRules: number;
    activeRules: number;
    totalNotifications: number;
    notificationsByChannel: Record<string, number>;
    notificationsBySeverity: Record<string, number>;
  } {
    const activeRules = Array.from(this.alertRules.values()).filter(rule => rule.enabled).length;
    
    const notificationsByChannel: Record<string, number> = {};
    const notificationsBySeverity: Record<string, number> = {};

    this.notificationQueue.forEach(notification => {
      notificationsByChannel[notification.channel] = (notificationsByChannel[notification.channel] || 0) + 1;
      notificationsBySeverity[notification.severity] = (notificationsBySeverity[notification.severity] || 0) + 1;
    });

    return {
      totalRules: this.alertRules.size,
      activeRules,
      totalNotifications: this.notificationQueue.length,
      notificationsByChannel,
      notificationsBySeverity
    };
  }

  /**
   * Get user's alert rules
   */
  getUserAlertRules(userId: string): AlertRule[] {
    return Array.from(this.alertRules.values()).filter(rule => rule.userId === userId);
  }

  /**
   * Get user's recent notifications
   */
  getUserNotifications(userId: string, limit: number = 50): AlertNotification[] {
    return this.notificationQueue
      .filter(notification => notification.userId === userId)
      .sort((a, b) => new Date(b.sentAt).getTime() - new Date(a.sentAt).getTime())
      .slice(0, limit);
  }
}
