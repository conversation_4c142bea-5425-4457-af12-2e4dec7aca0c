/**
 * Printables.com real web scraping integration
 */

import { BaseScraper, ScrapingError } from '@/lib/scraping/base-scraper';
import { ScrapedModel, ScrapedImage, ScrapedFile, ScrapedDesigner } from '@/types/models';

export class PrintablesScraper extends BaseScraper {
  constructor() {
    super('printables', {
      userAgent: 'Mozilla/5.0 (compatible; 3DMarketplace-Printables/1.0)',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      rateLimit: 10,
    });
  }

  /**
   * Validate if URL is from Printables
   */
  validateUrl(url: string): boolean {
    return /^https?:\/\/(www\.)?printables\.com\/model\/\d+/.test(url);
  }

  /**
   * Extract model ID from Printables URL
   */
  extractModelId(url: string): string | null {
    const match = url.match(/printables\.com\/model\/(\d+)/);
    return match ? match[1] : null;
  }

  /**
   * Scrape model data from Printables.com
   */
  async scrapeModel(url: string): Promise<ScrapedModel> {
    if (!this.validateUrl(url)) {
      throw new ScrapingError('INVALID_URL', 'Invalid Printables URL format', this.platform, url);
    }

    const modelId = this.extractModelId(url);
    if (!modelId) {
      throw new ScrapingError('INVALID_MODEL_ID', 'Could not extract model ID from URL', this.platform, url);
    }

    try {
      // Fetch the HTML content
      const html = await this.fetchHtml(url);
      const $ = this.parseHtml(html);

      // Extract basic information
      const title = this.extractTitle($);
      const description = this.extractDescription($);
      const images = this.extractImages($);
      const files = this.extractFiles($, url);
      const designer = this.extractDesigner($);
      const stats = this.extractStats($);
      const printSettings = this.extractPrintSettings($);
      const tags = this.extractTags($);
      const category = this.extractCategory($);
      const license = this.detectLicense($.html());

      return {
        title,
        description,
        summary: this.extractSummary($),
        images,
        thumbnail: images.length > 0 ? images[0].url : '',
        files,
        fileFormats: this.extractFileFormats(files),
        totalSize: this.calculateTotalSize(files),
        designer,
        tags,
        category,
        license,
        stats,
        platform: 'printables',
        originalId: modelId,
        originalUrl: url,
        scrapedAt: new Date().toISOString(),
        printSettings,
        isFree: true, // Printables models are typically free
      };
    } catch (error) {
      if (error instanceof ScrapingError) {
        throw error;
      }
      throw new ScrapingError(
        'SCRAPING_FAILED',
        `Failed to scrape Printables model: ${error instanceof Error ? error.message : 'Unknown error'}`,
        this.platform,
        url
      );
    }
  }

  /**
   * Extract model title
   */
  private extractTitle($: any): string {
    const selectors = [
      'h1[data-testid="model-title"]',
      'h1.model-title',
      '.model-header h1',
      'h1',
    ];

    for (const selector of selectors) {
      const title = this.extractText($, selector);
      if (title) return title;
    }

    throw new ScrapingError('MISSING_TITLE', 'Could not extract model title', this.platform);
  }

  /**
   * Extract model description
   */
  private extractDescription($: any): string {
    const selectors = [
      '[data-testid="model-description"]',
      '.model-description',
      '.description-content',
      '.model-summary',
    ];

    for (const selector of selectors) {
      const description = $(selector).html();
      if (description) {
        return description.trim();
      }
    }

    return '';
  }

  /**
   * Extract model summary
   */
  private extractSummary($: any): string {
    const selectors = [
      '.model-summary',
      '.model-excerpt',
      '.short-description',
    ];

    for (const selector of selectors) {
      const summary = this.extractText($, selector);
      if (summary) return summary;
    }

    return '';
  }

  /**
   * Extract images
   */
  private extractImages($: any): ScrapedImage[] {
    const images: ScrapedImage[] = [];
    const selectors = [
      '.model-gallery img',
      '.image-gallery img',
      '.model-images img',
      '.gallery-item img',
    ];

    selectors.forEach(selector => {
      $(selector).each((index: number, element: any) => {
        const $img = $(element);
        const src = $img.attr('src') || $img.attr('data-src') || $img.attr('data-lazy');
        const alt = $img.attr('alt') || '';

        if (src && this.isValidImageUrl(src)) {
          images.push({
            id: `img_${index}`,
            url: this.normalizeUrl(src),
            alt: alt || undefined,
          });
        }
      });
    });

    return this.deduplicateImages(images);
  }

  /**
   * Extract files information
   */
  private extractFiles($: any, baseUrl: string): ScrapedFile[] {
    const files: ScrapedFile[] = [];
    const selectors = [
      '.file-list .file-item',
      '.download-files .file',
      '.model-files .file-entry',
    ];

    selectors.forEach(selector => {
      $(selector).each((index: number, element: any) => {
        const $file = $(element);
        const name = this.extractText($file, '.file-name, .filename, .name');
        const sizeText = this.extractText($file, '.file-size, .size');
        const downloadLink = $file.find('a[href*="download"]').attr('href');

        if (name && downloadLink) {
          const format = this.extractFileFormat(name);
          const size = this.parseFileSize(sizeText);

          files.push({
            id: `file_${index}`,
            name,
            url: this.normalizeUrl(downloadLink, baseUrl),
            downloadUrl: this.normalizeUrl(downloadLink, baseUrl),
            size,
            format,
          });
        }
      });
    });

    return files;
  }

  /**
   * Extract designer information
   */
  private extractDesigner($: any): ScrapedDesigner {
    const nameSelectors = [
      '[data-testid="designer-name"]',
      '.designer-name',
      '.author-name',
      '.creator-name',
    ];

    const avatarSelectors = [
      '.designer-avatar img',
      '.author-avatar img',
      '.creator-avatar img',
    ];

    let name = 'Unknown Designer';
    for (const selector of nameSelectors) {
      const extractedName = this.extractText($, selector);
      if (extractedName) {
        name = extractedName;
        break;
      }
    }

    let avatar: string | undefined;
    for (const selector of avatarSelectors) {
      const avatarSrc = $(selector).attr('src');
      if (avatarSrc && this.isValidImageUrl(avatarSrc)) {
        avatar = this.normalizeUrl(avatarSrc);
        break;
      }
    }

    return {
      id: `printables_${name.toLowerCase().replace(/\s+/g, '_')}`,
      name,
      avatar,
    };
  }

  /**
   * Extract statistics
   */
  private extractStats($: any) {
    const extractStat = (selectors: string[]): number => {
      for (const selector of selectors) {
        const text = this.extractText($, selector);
        if (text) {
          return this.extractNumber(text);
        }
      }
      return 0;
    };

    return {
      views: extractStat(['.views-count', '.view-count', '[data-testid="views"]']),
      downloads: extractStat(['.downloads-count', '.download-count', '[data-testid="downloads"]']),
      likes: extractStat(['.likes-count', '.like-count', '[data-testid="likes"]']),
      comments: extractStat(['.comments-count', '.comment-count', '[data-testid="comments"]']),
    };
  }

  /**
   * Extract print settings
   */
  private extractPrintSettings($: any) {
    const extractSetting = (label: string): string => {
      const $setting = $(`*:contains("${label}")`).closest('.setting, .print-setting, .parameter');
      return $setting.find('.value, .setting-value').text().trim() || 'Unknown';
    };

    return {
      material: extractSetting('Material') || 'PLA',
      layerHeight: extractSetting('Layer Height') || '0.2mm',
      infill: extractSetting('Infill') || '20%',
      supports: extractSetting('Supports') || 'No',
      rafts: extractSetting('Rafts') || 'No',
      printTime: extractSetting('Print Time') || 'Unknown',
    };
  }

  /**
   * Extract tags
   */
  private extractTags($: any): string[] {
    const tags: string[] = [];
    const selectors = [
      '.tags .tag',
      '.model-tags .tag',
      '.tag-list .tag',
    ];

    selectors.forEach(selector => {
      $(selector).each((_: number, element: any) => {
        const tag = $(element).text().trim();
        if (tag && !tags.includes(tag)) {
          tags.push(tag);
        }
      });
    });

    return tags;
  }

  /**
   * Extract category
   */
  private extractCategory($: any): string {
    const selectors = [
      '.category-name',
      '.model-category',
      '.breadcrumb .category',
    ];

    for (const selector of selectors) {
      const category = this.extractText($, selector);
      if (category) return category;
    }

    return 'Other';
  }

  /**
   * Helper methods
   */
  private extractFileFormat(filename: string): string {
    const extension = filename.split('.').pop()?.toUpperCase();
    return extension || 'UNKNOWN';
  }

  private parseFileSize(sizeText: string): number {
    if (!sizeText) return 0;

    const match = sizeText.match(/([\d.]+)\s*(B|KB|MB|GB)/i);
    if (!match) return 0;

    const value = parseFloat(match[1]);
    const unit = match[2].toUpperCase();

    const multipliers = {
      'B': 1,
      'KB': 1024,
      'MB': 1024 * 1024,
      'GB': 1024 * 1024 * 1024,
    };

    return Math.round(value * (multipliers[unit as keyof typeof multipliers] || 1));
  }

  private extractFileFormats(files: ScrapedFile[]): string[] {
    const formats = new Set<string>();
    files.forEach(file => {
      if (file.format && file.format !== 'UNKNOWN') {
        formats.add(file.format);
      }
    });
    return Array.from(formats);
  }

  private calculateTotalSize(files: ScrapedFile[]): number {
    return files.reduce((total, file) => total + file.size, 0);
  }
}

// Export singleton instance
export const printablesScraper = new PrintablesScraper();

// Legacy functions for backward compatibility
export function extractPrintablesModelId(url: string): string | null {
  return printablesScraper.extractModelId(url);
}

export function isPrintablesUrl(url: string): boolean {
  return printablesScraper.validateUrl(url);
}

export async function importFromPrintables(url: string): Promise<{ success: boolean; model?: ScrapedModel; error?: string }> {
  try {
    const model = await printablesScraper.scrapeModel(url);
    return { success: true, model };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}
