/**
 * MakerWorld.com (Bambu Lab) web scraping integration
 */

import { BaseScraper, ScrapingError } from '@/lib/scraping/base-scraper';
import { ScrapedModel, ScrapedImage, ScrapedFile, ScrapedDesigner, MaterialInfo } from '@/types/models';

export class MakerWorldScraper extends BaseScraper {
  constructor() {
    super('makerworld', {
      userAgent: 'Mozilla/5.0 (compatible; 3DMarketplace-MakerWorld/1.0)',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      rateLimit: 15,
    });
  }

  /**
   * Validate if URL is from MakerWorld
   */
  validateUrl(url: string): boolean {
    return /^https?:\/\/(www\.)?makerworld\.com\/(en\/)?models\/\d+/.test(url);
  }

  /**
   * Extract model ID from MakerWorld URL
   */
  extractModelId(url: string): string | null {
    const match = url.match(/makerworld\.com\/(?:en\/)?models\/(\d+)/);
    return match ? match[1] : null;
  }

  /**
   * Scrape model data from MakerWorld.com
   */
  async scrapeModel(url: string): Promise<ScrapedModel> {
    if (!this.validateUrl(url)) {
      throw new ScrapingError('INVALID_URL', 'Invalid MakerWorld URL format', this.platform, url);
    }

    const modelId = this.extractModelId(url);
    if (!modelId) {
      throw new ScrapingError('INVALID_MODEL_ID', 'Could not extract model ID from URL', this.platform, url);
    }

    try {
      // Fetch the HTML content
      const html = await this.fetchHtml(url);
      const $ = this.parseHtml(html);

      // Extract basic information
      const title = this.extractTitle($);
      const description = this.extractDescription($);
      const images = this.extractImages($);
      const files = this.extractFiles($, url);
      const designer = this.extractDesigner($);
      const stats = this.extractStats($);
      const printSettings = this.extractPrintSettings($);
      const materialInfo = this.extractMaterialInfo($);
      const tags = this.extractTags($);
      const category = this.extractCategory($);
      const license = this.detectLicense($.html());

      return {
        title,
        description,
        summary: this.extractSummary($),
        images,
        thumbnail: images.length > 0 ? images[0].url : '',
        files,
        fileFormats: this.extractFileFormats(files),
        totalSize: this.calculateTotalSize(files),
        designer,
        tags,
        category,
        license,
        stats,
        platform: 'makerworld',
        originalId: modelId,
        originalUrl: url,
        scrapedAt: new Date().toISOString(),
        printSettings,
        materialInfo,
        isFree: this.checkIfFree($),
      };
    } catch (error) {
      if (error instanceof ScrapingError) {
        throw error;
      }
      throw new ScrapingError(
        'SCRAPING_FAILED',
        `Failed to scrape MakerWorld model: ${error instanceof Error ? error.message : 'Unknown error'}`,
        this.platform,
        url
      );
    }
  }

  /**
   * Extract model title
   */
  private extractTitle($: any): string {
    const selectors = [
      'h1[data-testid="model-title"]',
      'h1.model-title',
      '.model-header h1',
      '.title-section h1',
      'h1',
    ];

    for (const selector of selectors) {
      const title = this.extractText($, selector);
      if (title) return title;
    }

    throw new ScrapingError('MISSING_TITLE', 'Could not extract model title', this.platform);
  }

  /**
   * Extract model description
   */
  private extractDescription($: any): string {
    const selectors = [
      '[data-testid="model-description"]',
      '.model-description',
      '.description-content',
      '.model-details .description',
    ];

    for (const selector of selectors) {
      const description = $(selector).html();
      if (description) {
        return description.trim();
      }
    }

    return '';
  }

  /**
   * Extract model summary
   */
  private extractSummary($: any): string {
    const selectors = [
      '.model-summary',
      '.model-excerpt',
      '.short-description',
      '.model-intro',
    ];

    for (const selector of selectors) {
      const summary = this.extractText($, selector);
      if (summary) return summary;
    }

    return '';
  }

  /**
   * Extract images
   */
  private extractImages($: any): ScrapedImage[] {
    const images: ScrapedImage[] = [];
    const selectors = [
      '.model-gallery img',
      '.image-gallery img',
      '.model-images img',
      '.gallery-item img',
      '.carousel-item img',
    ];

    selectors.forEach(selector => {
      $(selector).each((index: number, element: any) => {
        const $img = $(element);
        const src = $img.attr('src') || $img.attr('data-src') || $img.attr('data-lazy');
        const alt = $img.attr('alt') || '';

        if (src && this.isValidImageUrl(src)) {
          images.push({
            id: `img_${index}`,
            url: this.normalizeUrl(src),
            alt: alt || undefined,
          });
        }
      });
    });

    return this.deduplicateImages(images);
  }

  /**
   * Extract files information
   */
  private extractFiles($: any, baseUrl: string): ScrapedFile[] {
    const files: ScrapedFile[] = [];
    const selectors = [
      '.file-list .file-item',
      '.download-files .file',
      '.model-files .file-entry',
      '.attachments .file',
    ];

    selectors.forEach(selector => {
      $(selector).each((index: number, element: any) => {
        const $file = $(element);
        const name = this.extractText($file, '.file-name, .filename, .name');
        const sizeText = this.extractText($file, '.file-size, .size');
        const downloadLink = $file.find('a[href*="download"], a[href*="file"]').attr('href');

        if (name && downloadLink) {
          const format = this.extractFileFormat(name);
          const size = this.parseFileSize(sizeText);

          files.push({
            id: `file_${index}`,
            name,
            url: this.normalizeUrl(downloadLink, baseUrl),
            downloadUrl: this.normalizeUrl(downloadLink, baseUrl),
            size,
            format,
          });
        }
      });
    });

    return files;
  }

  /**
   * Extract designer information
   */
  private extractDesigner($: any): ScrapedDesigner {
    const nameSelectors = [
      '[data-testid="designer-name"]',
      '.designer-name',
      '.author-name',
      '.creator-name',
      '.user-name',
    ];

    const avatarSelectors = [
      '.designer-avatar img',
      '.author-avatar img',
      '.creator-avatar img',
      '.user-avatar img',
    ];

    let name = 'Unknown Designer';
    for (const selector of nameSelectors) {
      const extractedName = this.extractText($, selector);
      if (extractedName) {
        name = extractedName;
        break;
      }
    }

    let avatar: string | undefined;
    for (const selector of avatarSelectors) {
      const avatarSrc = $(selector).attr('src');
      if (avatarSrc && this.isValidImageUrl(avatarSrc)) {
        avatar = this.normalizeUrl(avatarSrc);
        break;
      }
    }

    return {
      id: `makerworld_${name.toLowerCase().replace(/\s+/g, '_')}`,
      name,
      avatar,
    };
  }

  /**
   * Extract statistics
   */
  private extractStats($: any) {
    const extractStat = (selectors: string[]): number => {
      for (const selector of selectors) {
        const text = this.extractText($, selector);
        if (text) {
          return this.extractNumber(text);
        }
      }
      return 0;
    };

    return {
      views: extractStat(['.views-count', '.view-count', '[data-testid="views"]']),
      downloads: extractStat(['.downloads-count', '.download-count', '[data-testid="downloads"]']),
      likes: extractStat(['.likes-count', '.like-count', '[data-testid="likes"]', '.heart-count']),
      comments: extractStat(['.comments-count', '.comment-count', '[data-testid="comments"]']),
    };
  }

  /**
   * Extract print settings
   */
  private extractPrintSettings($: any) {
    const extractSetting = (label: string): string => {
      const $setting = $(`*:contains("${label}")`).closest('.setting, .print-setting, .parameter, .spec-item');
      return $setting.find('.value, .setting-value, .spec-value').text().trim() || 'Unknown';
    };

    return {
      material: extractSetting('Material') || extractSetting('Filament') || 'PLA',
      layerHeight: extractSetting('Layer Height') || extractSetting('Layer') || '0.2mm',
      infill: extractSetting('Infill') || extractSetting('Density') || '20%',
      supports: extractSetting('Supports') || extractSetting('Support') || 'No',
      rafts: extractSetting('Rafts') || extractSetting('Raft') || 'No',
      printTime: extractSetting('Print Time') || extractSetting('Duration') || 'Unknown',
    };
  }

  /**
   * Extract material information (Bambu Lab specific)
   */
  private extractMaterialInfo($: any): MaterialInfo {
    const recommended: string[] = [];
    const tested: string[] = [];

    // Extract recommended materials
    $('.recommended-materials .material, .material-list .material').each((_: number, element: any) => {
      const material = $(element).text().trim();
      if (material) recommended.push(material);
    });

    // Extract tested materials
    $('.tested-materials .material, .compatible-materials .material').each((_: number, element: any) => {
      const material = $(element).text().trim();
      if (material) tested.push(material);
    });

    return {
      recommended: recommended.length > 0 ? recommended : ['PLA', 'PETG'],
      tested: tested.length > 0 ? tested : ['PLA'],
      settings: {},
    };
  }

  /**
   * Extract tags
   */
  private extractTags($: any): string[] {
    const tags: string[] = [];
    const selectors = [
      '.tags .tag',
      '.model-tags .tag',
      '.tag-list .tag',
      '.labels .label',
    ];

    selectors.forEach(selector => {
      $(selector).each((_: number, element: any) => {
        const tag = $(element).text().trim();
        if (tag && !tags.includes(tag)) {
          tags.push(tag);
        }
      });
    });

    return tags;
  }

  /**
   * Extract category
   */
  private extractCategory($: any): string {
    const selectors = [
      '.category-name',
      '.model-category',
      '.breadcrumb .category',
      '.category-tag',
    ];

    for (const selector of selectors) {
      const category = this.extractText($, selector);
      if (category) return category;
    }

    return 'Other';
  }

  /**
   * Check if model is free
   */
  private checkIfFree($: any): boolean {
    const priceSelectors = [
      '.price',
      '.model-price',
      '.cost',
    ];

    for (const selector of priceSelectors) {
      const priceText = this.extractText($, selector);
      if (priceText && (priceText.includes('$') || priceText.includes('€') || priceText.includes('£'))) {
        return false;
      }
    }

    // Check for "Free" indicators
    const freeIndicators = [
      '.free-badge',
      '.free-label',
      '*:contains("Free")',
      '*:contains("免费")', // Chinese for free
    ];

    for (const selector of freeIndicators) {
      if ($(selector).length > 0) {
        return true;
      }
    }

    return true; // Default to free if no price found
  }

  /**
   * Helper methods
   */
  private extractFileFormat(filename: string): string {
    const extension = filename.split('.').pop()?.toUpperCase();
    return extension || 'UNKNOWN';
  }

  private parseFileSize(sizeText: string): number {
    if (!sizeText) return 0;

    const match = sizeText.match(/([\d.]+)\s*(B|KB|MB|GB)/i);
    if (!match) return 0;

    const value = parseFloat(match[1]);
    const unit = match[2].toUpperCase();

    const multipliers = {
      'B': 1,
      'KB': 1024,
      'MB': 1024 * 1024,
      'GB': 1024 * 1024 * 1024,
    };

    return Math.round(value * (multipliers[unit as keyof typeof multipliers] || 1));
  }

  private extractFileFormats(files: ScrapedFile[]): string[] {
    const formats = new Set<string>();
    files.forEach(file => {
      if (file.format && file.format !== 'UNKNOWN') {
        formats.add(file.format);
      }
    });
    return Array.from(formats);
  }

  private calculateTotalSize(files: ScrapedFile[]): number {
    return files.reduce((total, file) => total + file.size, 0);
  }
}

// Export singleton instance
export const makerWorldScraper = new MakerWorldScraper();

// Utility functions
export function extractMakerWorldModelId(url: string): string | null {
  return makerWorldScraper.extractModelId(url);
}

export function isMakerWorldUrl(url: string): boolean {
  return makerWorldScraper.validateUrl(url);
}

export async function importFromMakerWorld(url: string): Promise<{ success: boolean; model?: ScrapedModel; error?: string }> {
  try {
    const model = await makerWorldScraper.scrapeModel(url);
    return { success: true, model };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}
