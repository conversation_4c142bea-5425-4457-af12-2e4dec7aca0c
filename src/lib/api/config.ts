/**
 * API Configuration for 3D Marketplace
 * Connects frontend to the deployed Cloudflare Worker API
 */

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp?: string;
  cached?: boolean;
}

export interface TrendingModelsResponse {
  models: any[];
  platform: string;
  total: number;
  timeframe: string;
  fallback?: boolean;
}

export interface PopularModelsResponse {
  models: any[];
  total: number;
}

export interface HealthResponse {
  status: string;
  timestamp: string;
  environment: string;
}

export interface StatsResponse {
  totalModels: number;
  totalDownloads: number;
  totalUsers: number;
  platformStats: Record<string, number>;
  timestamp: string;
}

// Environment-aware API configuration
const getApiBaseUrl = (): string => {
  // Check if we're in browser environment
  if (typeof window !== 'undefined') {
    // Client-side: use environment variables or fallback
    return process.env.NEXT_PUBLIC_API_WORKER_URL ||
           process.env.NEXT_PUBLIC_APP_URL + '/api' ||
           '/api';
  }

  // Server-side: use environment variables or fallback
  return process.env.API_WORKER_URL ||
         process.env.NEXT_PUBLIC_API_WORKER_URL ||
         'https://3d-marketplace-api.gcp-inspiration.workers.dev';
};

// API Base URLs
export const API_CONFIG = {
  // Dynamic API Worker URL based on environment
  BASE_URL: getApiBaseUrl(),

  // Fallback URLs for different environments
  FALLBACK_URLS: [
    '/api', // Local Next.js API routes
    'https://3d-marketplace-api.gcp-inspiration.workers.dev', // Production worker
    'https://marketplace-worker.gcp-inspiration.workers.dev', // Alternative worker
  ],

  // Timeout settings
  TIMEOUT: 30000, // 30 seconds

  // Retry settings
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second

  // Environment detection
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production',
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  // Health check
  HEALTH: '/health',
  
  // Bright Data endpoints
  BRIGHT_DATA: {
    TRENDING: '/api/bright-data/trending',
    POPULAR: '/api/bright-data/popular',
    RECENT: '/api/bright-data/recent',
  },
  
  // Models endpoints
  MODELS: '/api/models',
  
  // Scraping endpoints
  SCRAPING: {
    POPULAR: '/api/scraping/popular-models',
    AUTOMATED: '/api/scraping/automated',
    HEALTH: '/api/scraping/health',
  },
  
  // Stats endpoint
  STATS: '/api/stats',
} as const;

// API Client with error handling and retries
export class ApiClient {
  private baseUrl: string;
  private fallbackUrls: string[];
  private timeout: number;
  private maxRetries: number;
  private retryDelay: number;
  private currentUrlIndex: number = 0;

  constructor() {
    this.baseUrl = API_CONFIG.BASE_URL;
    this.fallbackUrls = [...API_CONFIG.FALLBACK_URLS];
    this.timeout = API_CONFIG.TIMEOUT;
    this.maxRetries = API_CONFIG.MAX_RETRIES;
    this.retryDelay = API_CONFIG.RETRY_DELAY;
  }

  private getCurrentBaseUrl(): string {
    if (this.currentUrlIndex === 0) {
      return this.baseUrl;
    }
    return this.fallbackUrls[this.currentUrlIndex - 1] || this.baseUrl;
  }

  private async tryNextUrl(): Promise<boolean> {
    if (this.currentUrlIndex < this.fallbackUrls.length) {
      this.currentUrlIndex++;
      console.warn(`🔄 Switching to fallback URL: ${this.getCurrentBaseUrl()}`);
      return true;
    }
    return false;
  }

  private resetUrlIndex(): void {
    this.currentUrlIndex = 0;
  }

  private async fetchWithRetry(url: string, options: RequestInit = {}, retries = 0): Promise<Response> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Reset URL index on successful request
      this.resetUrlIndex();
      return response;
    } catch (error) {
      // Try fallback URLs first before retrying
      if (await this.tryNextUrl()) {
        const newUrl = url.replace(this.baseUrl, this.getCurrentBaseUrl());
        return this.fetchWithRetry(newUrl, options, retries);
      }

      // If no more fallback URLs, try retrying with current URL
      if (retries < this.maxRetries) {
        console.warn(`API request failed, retrying... (${retries + 1}/${this.maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * (retries + 1)));
        return this.fetchWithRetry(url, options, retries + 1);
      }

      throw error;
    }
  }

  async get<T>(endpoint: string, params?: Record<string, string>): Promise<T> {
    const url = new URL(endpoint, this.getCurrentBaseUrl());

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }

    const response = await this.fetchWithRetry(url.toString());
    return response.json();
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    const url = new URL(endpoint, this.getCurrentBaseUrl());

    const response = await this.fetchWithRetry(url.toString(), {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });

    return response.json();
  }

  // Health check
  async healthCheck(): Promise<HealthResponse> {
    return this.get<HealthResponse>(API_ENDPOINTS.HEALTH);
  }

  // Bright Data endpoints
  async getTrendingModels(platform = 'printables', limit = 12): Promise<ApiResponse<TrendingModelsResponse>> {
    return this.get<ApiResponse<TrendingModelsResponse>>(API_ENDPOINTS.BRIGHT_DATA.TRENDING, {
      platform,
      limit: limit.toString(),
    });
  }

  async getPopularModels(): Promise<ApiResponse<PopularModelsResponse>> {
    return this.get<ApiResponse<PopularModelsResponse>>(API_ENDPOINTS.BRIGHT_DATA.POPULAR);
  }

  async getRecentModels(): Promise<ApiResponse<any[]>> {
    return this.get<ApiResponse<any[]>>(API_ENDPOINTS.BRIGHT_DATA.RECENT);
  }

  // Models endpoints
  async getModels(): Promise<ApiResponse<any[]>> {
    return this.get<ApiResponse<any[]>>(API_ENDPOINTS.MODELS);
  }

  // Stats endpoint
  async getStats(): Promise<ApiResponse<StatsResponse>> {
    return this.get<ApiResponse<StatsResponse>>(API_ENDPOINTS.STATS);
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Helper function to check if API is available
export async function checkApiHealth(): Promise<boolean> {
  try {
    const health = await apiClient.healthCheck();
    return health.status === 'healthy';
  } catch (error) {
    console.error('API health check failed:', error);
    return false;
  }
}

// Helper function to get trending models with fallback
export async function getTrendingModelsWithFallback(platform = 'printables', limit = 12): Promise<TrendingModelsResponse> {
  try {
    const response = await apiClient.getTrendingModels(platform, limit);
    if (response.success && response.data) {
      return response.data;
    }
    throw new Error('API returned unsuccessful response');
  } catch (error) {
    console.error('Failed to fetch trending models from API, using fallback:', error);

    // Return fallback data
    return {
      models: generateFallbackModels(limit),
      platform,
      total: limit,
      timeframe: 'week',
      fallback: true,
    };
  }
}

// Fallback data generator
function generateFallbackModels(limit: number) {
  const models = [];
  
  for (let i = 0; i < limit; i++) {
    models.push({
      id: `fallback_${Date.now()}_${i}`,
      title: `3D Model ${i + 1}`,
      description: 'High-quality 3D model for printing',
      thumbnail: `https://picsum.photos/400/300?random=${i}`,
      designer: {
        name: 'Designer',
        avatar: `https://picsum.photos/100/100?random=${i + 100}`
      },
      stats: {
        views: Math.floor(Math.random() * 10000) + 1000,
        downloads: Math.floor(Math.random() * 1000) + 100,
        likes: Math.floor(Math.random() * 500) + 50,
        trending_score: Math.floor(Math.random() * 100) + 50
      },
      platform: 'printables',
      tags: ['3d-printing', 'model'],
      category: 'General',
      isFree: true,
      isNew: false,
      publishedAt: new Date().toISOString()
    });
  }
  
  return models;
}
