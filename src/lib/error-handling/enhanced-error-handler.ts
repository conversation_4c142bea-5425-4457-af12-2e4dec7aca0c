/**
 * Enhanced error handling system for 3D Marketplace
 * Provides comprehensive error management, logging, and recovery
 */

import { ModelSource } from '@/types/models';
import { cloudflareMonitoring } from '@/lib/observability/cloudflare-monitoring';

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ErrorCategory {
  NETWORK = 'network',
  PARSING = 'parsing',
  VALIDATION = 'validation',
  RATE_LIMIT = 'rate_limit',
  AUTHENTICATION = 'authentication',
  PERMISSION = 'permission',
  PLATFORM = 'platform',
  SYSTEM = 'system',
  USER_INPUT = 'user_input'
}

export interface ErrorContext {
  platform?: ModelSource;
  url?: string;
  userId?: string;
  sessionId?: string;
  userAgent?: string;
  timestamp: string;
  requestId?: string;
  additionalData?: Record<string, any>;
}

export interface ErrorRecoveryAction {
  type: 'retry' | 'fallback' | 'skip' | 'manual' | 'redirect';
  description: string;
  automated: boolean;
  maxAttempts?: number;
  delay?: number;
  fallbackUrl?: string;
  redirectUrl?: string;
}

export class EnhancedError extends Error {
  public readonly code: string;
  public readonly category: ErrorCategory;
  public readonly severity: ErrorSeverity;
  public readonly context: ErrorContext;
  public readonly recoveryActions: ErrorRecoveryAction[];
  public readonly userMessage: string;
  public readonly technicalMessage: string;
  public readonly retryable: boolean;
  public readonly reportable: boolean;

  constructor(
    code: string,
    message: string,
    category: ErrorCategory,
    severity: ErrorSeverity,
    context: Partial<ErrorContext> = {},
    options: {
      userMessage?: string;
      retryable?: boolean;
      reportable?: boolean;
      recoveryActions?: ErrorRecoveryAction[];
    } = {}
  ) {
    super(message);
    this.name = 'EnhancedError';
    this.code = code;
    this.category = category;
    this.severity = severity;
    this.context = {
      timestamp: new Date().toISOString(),
      ...context
    };
    this.technicalMessage = message;
    this.userMessage = options.userMessage || this.generateUserMessage();
    this.retryable = options.retryable ?? this.isRetryableByDefault();
    this.reportable = options.reportable ?? this.isReportableByDefault();
    this.recoveryActions = options.recoveryActions || this.generateRecoveryActions();
  }

  private generateUserMessage(): string {
    switch (this.category) {
      case ErrorCategory.NETWORK:
        return 'Проблема з підключенням до інтернету. Перевірте з\'єднання та спробуйте знову.';
      case ErrorCategory.RATE_LIMIT:
        return 'Занадто багато запитів. Зачекайте кілька хвилин та спробуйте знову.';
      case ErrorCategory.PARSING:
        return 'Не вдалося обробити дані з веб-сайту. Можливо, сайт змінив свою структуру.';
      case ErrorCategory.VALIDATION:
        return 'Дані не пройшли перевірку. Перевірте правильність введеної інформації.';
      case ErrorCategory.AUTHENTICATION:
        return 'Проблема з автентифікацією. Увійдіть в систему знову.';
      case ErrorCategory.PERMISSION:
        return 'Недостатньо прав для виконання цієї дії.';
      case ErrorCategory.PLATFORM:
        return 'Проблема з платформою. Спробуйте пізніше або оберіть іншу платформу.';
      default:
        return 'Виникла технічна помилка. Спробуйте пізніше або зверніться до підтримки.';
    }
  }

  private isRetryableByDefault(): boolean {
    return [
      ErrorCategory.NETWORK,
      ErrorCategory.RATE_LIMIT,
      ErrorCategory.PLATFORM
    ].includes(this.category);
  }

  private isReportableByDefault(): boolean {
    return [
      ErrorSeverity.HIGH,
      ErrorSeverity.CRITICAL
    ].includes(this.severity);
  }

  private generateRecoveryActions(): ErrorRecoveryAction[] {
    const actions: ErrorRecoveryAction[] = [];

    switch (this.category) {
      case ErrorCategory.NETWORK:
        actions.push({
          type: 'retry',
          description: 'Повторити запит через 5 секунд',
          automated: true,
          maxAttempts: 3,
          delay: 5000
        });
        break;

      case ErrorCategory.RATE_LIMIT:
        actions.push({
          type: 'retry',
          description: 'Повторити запит через 60 секунд',
          automated: true,
          maxAttempts: 2,
          delay: 60000
        });
        break;

      case ErrorCategory.PARSING:
        actions.push({
          type: 'fallback',
          description: 'Спробувати альтернативний метод парсингу',
          automated: true
        });
        actions.push({
          type: 'manual',
          description: 'Повідомити розробників про зміни на сайті',
          automated: false
        });
        break;

      case ErrorCategory.PLATFORM:
        actions.push({
          type: 'fallback',
          description: 'Спробувати іншу платформу',
          automated: false
        });
        break;

      case ErrorCategory.AUTHENTICATION:
        actions.push({
          type: 'redirect',
          description: 'Перенаправити на сторінку входу',
          automated: true,
          redirectUrl: '/auth/signin'
        });
        break;
    }

    return actions;
  }

  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      userMessage: this.userMessage,
      category: this.category,
      severity: this.severity,
      context: this.context,
      retryable: this.retryable,
      reportable: this.reportable,
      recoveryActions: this.recoveryActions,
      stack: this.stack
    };
  }
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorLog: EnhancedError[] = [];
  private maxLogSize = 1000;

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Handle and process an error
   */
  async handleError(error: Error | EnhancedError, context: Partial<ErrorContext> = {}): Promise<EnhancedError> {
    let enhancedError: EnhancedError;

    if (error instanceof EnhancedError) {
      enhancedError = error;
    } else {
      enhancedError = this.enhanceError(error, context);
    }

    // Log the error
    this.logError(enhancedError);

    // Report if necessary
    if (enhancedError.reportable) {
      await this.reportError(enhancedError);
    }

    // Execute automated recovery actions
    await this.executeRecoveryActions(enhancedError);

    return enhancedError;
  }

  /**
   * Enhance a regular error into an EnhancedError
   */
  private enhanceError(error: Error, context: Partial<ErrorContext>): EnhancedError {
    const { category, severity, code } = this.categorizeError(error);
    
    return new EnhancedError(
      code,
      error.message,
      category,
      severity,
      context,
      {
        retryable: this.isRetryable(error),
        reportable: severity === ErrorSeverity.HIGH || severity === ErrorSeverity.CRITICAL
      }
    );
  }

  /**
   * Categorize error based on message and type
   */
  private categorizeError(error: Error): { category: ErrorCategory; severity: ErrorSeverity; code: string } {
    const message = error.message.toLowerCase();

    // Network errors
    if (message.includes('network') || message.includes('fetch') || message.includes('timeout')) {
      return { category: ErrorCategory.NETWORK, severity: ErrorSeverity.MEDIUM, code: 'NETWORK_ERROR' };
    }

    // Rate limit errors
    if (message.includes('rate limit') || message.includes('too many requests')) {
      return { category: ErrorCategory.RATE_LIMIT, severity: ErrorSeverity.LOW, code: 'RATE_LIMIT_EXCEEDED' };
    }

    // Parsing errors
    if (message.includes('parse') || message.includes('invalid html') || message.includes('selector')) {
      return { category: ErrorCategory.PARSING, severity: ErrorSeverity.MEDIUM, code: 'PARSING_ERROR' };
    }

    // Validation errors
    if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
      return { category: ErrorCategory.VALIDATION, severity: ErrorSeverity.LOW, code: 'VALIDATION_ERROR' };
    }

    // Authentication errors
    if (message.includes('auth') || message.includes('unauthorized') || message.includes('forbidden')) {
      return { category: ErrorCategory.AUTHENTICATION, severity: ErrorSeverity.MEDIUM, code: 'AUTH_ERROR' };
    }

    // Default to system error
    return { category: ErrorCategory.SYSTEM, severity: ErrorSeverity.HIGH, code: 'SYSTEM_ERROR' };
  }

  /**
   * Check if error is retryable
   */
  private isRetryable(error: Error): boolean {
    const message = error.message.toLowerCase();
    return message.includes('network') || 
           message.includes('timeout') || 
           message.includes('rate limit') ||
           message.includes('temporary');
  }

  /**
   * Log error to internal storage
   */
  private logError(error: EnhancedError): void {
    this.errorLog.push(error);
    
    // Maintain log size
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(-this.maxLogSize);
    }

    // Console logging based on severity
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        console.error('🚨 CRITICAL ERROR:', error.toJSON());
        break;
      case ErrorSeverity.HIGH:
        console.error('❌ HIGH SEVERITY ERROR:', error.toJSON());
        break;
      case ErrorSeverity.MEDIUM:
        console.warn('⚠️ MEDIUM SEVERITY ERROR:', error.toJSON());
        break;
      case ErrorSeverity.LOW:
        console.info('ℹ️ LOW SEVERITY ERROR:', error.toJSON());
        break;
    }
  }

  /**
   * Report error to external service
   */
  private async reportError(error: EnhancedError): Promise<void> {
    try {
      // Відправка в Cloudflare Observability
      await cloudflareMonitoring.logEvent({
        level: error.severity === ErrorSeverity.CRITICAL ? 'fatal' :
               error.severity === ErrorSeverity.HIGH ? 'error' : 'warn',
        message: error.technicalMessage,
        service: 'error-handler',
        platform: error.context.platform,
        userId: error.context.userId,
        sessionId: error.context.sessionId,
        requestId: error.context.requestId,
        metadata: {
          code: error.code,
          category: error.category,
          severity: error.severity,
          retryable: error.retryable,
          recoveryActions: error.recoveryActions,
          context: error.context
        }
      });

      console.log('📊 Error reported to Cloudflare Observability:', error.code);
    } catch (reportingError) {
      console.error('Failed to report error to Cloudflare:', reportingError);
    }
  }

  /**
   * Execute automated recovery actions
   */
  private async executeRecoveryActions(error: EnhancedError): Promise<void> {
    for (const action of error.recoveryActions) {
      if (action.automated) {
        try {
          await this.executeAction(action, error);
        } catch (actionError) {
          console.error('Failed to execute recovery action:', actionError);
        }
      }
    }
  }

  /**
   * Execute a specific recovery action
   */
  private async executeAction(action: ErrorRecoveryAction, error: EnhancedError): Promise<void> {
    switch (action.type) {
      case 'retry':
        if (action.delay) {
          await new Promise(resolve => setTimeout(resolve, action.delay));
        }
        console.log(`🔄 Automated retry scheduled for error: ${error.code}`);
        break;
        
      case 'fallback':
        console.log(`🔀 Fallback action triggered for error: ${error.code}`);
        break;
        
      case 'redirect':
        if (action.redirectUrl && typeof window !== 'undefined') {
          console.log(`↗️ Redirecting to: ${action.redirectUrl}`);
          window.location.href = action.redirectUrl;
        }
        break;
    }
  }

  /**
   * Get error statistics
   */
  getErrorStats() {
    const stats = {
      total: this.errorLog.length,
      byCategory: {} as Record<ErrorCategory, number>,
      bySeverity: {} as Record<ErrorSeverity, number>,
      byPlatform: {} as Record<string, number>,
      recent: this.errorLog.slice(-10)
    };

    this.errorLog.forEach(error => {
      stats.byCategory[error.category] = (stats.byCategory[error.category] || 0) + 1;
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
      if (error.context.platform) {
        stats.byPlatform[error.context.platform] = (stats.byPlatform[error.context.platform] || 0) + 1;
      }
    });

    return stats;
  }

  /**
   * Clear error log
   */
  clearErrorLog(): void {
    this.errorLog = [];
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();
