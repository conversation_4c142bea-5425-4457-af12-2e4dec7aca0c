/**
 * Batch Processing System for 3D Models
 * Processes multiple models simultaneously with queue management
 */

import { ModelFile, ScrapedModelWithFiles } from '@/lib/bright-data/model-file-scraper';
import { FormatConverter, ConversionOptions, ConversionResult } from './format-converter';
import { ThumbnailGenerator, ThumbnailOptions, ThumbnailResult } from './thumbnail-generator';
import { ModelAnalyzer, ModelAnalysis } from './model-analyzer';
import { R2ModelStorage } from '@/lib/storage/r2-model-storage';

export interface BatchJob {
  id: string;
  type: 'conversion' | 'thumbnail' | 'analysis' | 'full_processing';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  progress: number; // 0-100
  currentStage: string;
  files: { file: ModelFile; buffer: ArrayBuffer }[];
  options: any;
  results?: any[];
  error?: string;
  estimatedDuration?: number; // in seconds
  actualDuration?: number;
}

export interface BatchProcessingOptions {
  maxConcurrentJobs: number;
  maxConcurrentFilesPerJob: number;
  retryAttempts: number;
  timeoutMinutes: number;
  enablePrioritization: boolean;
  enableProgressTracking: boolean;
  enableResultCaching: boolean;
}

export interface ProcessingProgress {
  jobId: string;
  fileIndex: number;
  fileName: string;
  stage: string;
  progress: number;
  message: string;
  estimatedTimeRemaining?: number;
}

export interface BatchProcessingResult {
  jobId: string;
  success: boolean;
  totalFiles: number;
  processedFiles: number;
  failedFiles: number;
  results: any[];
  errors: string[];
  duration: number;
  averageTimePerFile: number;
}

export class BatchProcessor {
  private jobs: Map<string, BatchJob> = new Map();
  private activeJobs: Set<string> = new Set();
  private queue: string[] = [];
  private options: BatchProcessingOptions;
  private formatConverter: FormatConverter;
  private thumbnailGenerator: ThumbnailGenerator;
  private modelAnalyzer: ModelAnalyzer;
  private storage: R2ModelStorage;

  constructor(options?: Partial<BatchProcessingOptions>) {
    this.options = {
      maxConcurrentJobs: 3,
      maxConcurrentFilesPerJob: 2,
      retryAttempts: 2,
      timeoutMinutes: 30,
      enablePrioritization: true,
      enableProgressTracking: true,
      enableResultCaching: true,
      ...options
    };

    this.formatConverter = new FormatConverter();
    this.thumbnailGenerator = new ThumbnailGenerator();
    this.modelAnalyzer = new ModelAnalyzer();
    this.storage = new R2ModelStorage();

    console.log('✅ Batch processor initialized with options:', this.options);
  }

  /**
   * Add a batch conversion job
   */
  async addConversionJob(
    files: { file: ModelFile; buffer: ArrayBuffer }[],
    options: ConversionOptions,
    priority: BatchJob['priority'] = 'normal'
  ): Promise<string> {
    const jobId = this.generateJobId('conversion');
    
    const job: BatchJob = {
      id: jobId,
      type: 'conversion',
      status: 'pending',
      priority,
      createdAt: new Date(),
      progress: 0,
      currentStage: 'queued',
      files,
      options,
      estimatedDuration: this.estimateConversionTime(files)
    };

    this.jobs.set(jobId, job);
    this.addToQueue(jobId);

    console.log(`📋 Added conversion job ${jobId} with ${files.length} files (priority: ${priority})`);
    return jobId;
  }

  /**
   * Add a batch thumbnail generation job
   */
  async addThumbnailJob(
    files: { file: ModelFile; buffer: ArrayBuffer }[],
    options: ThumbnailOptions,
    priority: BatchJob['priority'] = 'normal'
  ): Promise<string> {
    const jobId = this.generateJobId('thumbnail');
    
    const job: BatchJob = {
      id: jobId,
      type: 'thumbnail',
      status: 'pending',
      priority,
      createdAt: new Date(),
      progress: 0,
      currentStage: 'queued',
      files,
      options,
      estimatedDuration: this.estimateThumbnailTime(files)
    };

    this.jobs.set(jobId, job);
    this.addToQueue(jobId);

    console.log(`📋 Added thumbnail job ${jobId} with ${files.length} files (priority: ${priority})`);
    return jobId;
  }

  /**
   * Add a batch analysis job
   */
  async addAnalysisJob(
    files: { file: ModelFile; buffer: ArrayBuffer }[],
    priority: BatchJob['priority'] = 'normal'
  ): Promise<string> {
    const jobId = this.generateJobId('analysis');
    
    const job: BatchJob = {
      id: jobId,
      type: 'analysis',
      status: 'pending',
      priority,
      createdAt: new Date(),
      progress: 0,
      currentStage: 'queued',
      files,
      options: {},
      estimatedDuration: this.estimateAnalysisTime(files)
    };

    this.jobs.set(jobId, job);
    this.addToQueue(jobId);

    console.log(`📋 Added analysis job ${jobId} with ${files.length} files (priority: ${priority})`);
    return jobId;
  }

  /**
   * Add a full processing job (conversion + thumbnails + analysis)
   */
  async addFullProcessingJob(
    files: { file: ModelFile; buffer: ArrayBuffer }[],
    conversionOptions: ConversionOptions,
    thumbnailOptions: ThumbnailOptions,
    priority: BatchJob['priority'] = 'normal'
  ): Promise<string> {
    const jobId = this.generateJobId('full');
    
    const job: BatchJob = {
      id: jobId,
      type: 'full_processing',
      status: 'pending',
      priority,
      createdAt: new Date(),
      progress: 0,
      currentStage: 'queued',
      files,
      options: { conversion: conversionOptions, thumbnail: thumbnailOptions },
      estimatedDuration: this.estimateFullProcessingTime(files)
    };

    this.jobs.set(jobId, job);
    this.addToQueue(jobId);

    console.log(`📋 Added full processing job ${jobId} with ${files.length} files (priority: ${priority})`);
    return jobId;
  }

  /**
   * Get job status
   */
  getJobStatus(jobId: string): BatchJob | null {
    return this.jobs.get(jobId) || null;
  }

  /**
   * Get all jobs
   */
  getAllJobs(): BatchJob[] {
    return Array.from(this.jobs.values());
  }

  /**
   * Cancel a job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    const job = this.jobs.get(jobId);
    if (!job) return false;

    if (job.status === 'processing') {
      // Mark for cancellation - the processing loop will handle it
      job.status = 'cancelled';
      console.log(`🚫 Cancelling job ${jobId}`);
    } else if (job.status === 'pending') {
      // Remove from queue
      job.status = 'cancelled';
      this.removeFromQueue(jobId);
      console.log(`🚫 Cancelled pending job ${jobId}`);
    }

    return true;
  }

  /**
   * Start processing queue
   */
  async startProcessing(): Promise<void> {
    console.log('🚀 Starting batch processing queue');
    
    // Process jobs continuously
    while (true) {
      await this.processNextJob();
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between checks
    }
  }

  /**
   * Process next job in queue
   */
  private async processNextJob(): Promise<void> {
    // Check if we can start a new job
    if (this.activeJobs.size >= this.options.maxConcurrentJobs) {
      return;
    }

    // Get next job from queue
    const jobId = this.getNextJobFromQueue();
    if (!jobId) {
      return;
    }

    const job = this.jobs.get(jobId);
    if (!job || job.status !== 'pending') {
      return;
    }

    // Start processing the job
    this.activeJobs.add(jobId);
    job.status = 'processing';
    job.startedAt = new Date();

    console.log(`🔄 Starting job ${jobId} (${job.type})`);

    try {
      const result = await this.processJob(job);
      
      job.status = 'completed';
      job.completedAt = new Date();
      job.progress = 100;
      job.results = result.results;
      job.actualDuration = Date.now() - job.startedAt.getTime();

      console.log(`✅ Completed job ${jobId} in ${job.actualDuration}ms`);

    } catch (error) {
      job.status = 'failed';
      job.completedAt = new Date();
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.actualDuration = Date.now() - (job.startedAt?.getTime() || Date.now());

      console.error(`❌ Failed job ${jobId}:`, error);
    } finally {
      this.activeJobs.delete(jobId);
    }
  }

  /**
   * Process a specific job
   */
  private async processJob(job: BatchJob): Promise<BatchProcessingResult> {
    const startTime = Date.now();
    const results: any[] = [];
    const errors: string[] = [];

    const onProgress = (fileIndex: number, progress: any) => {
      if (this.options.enableProgressTracking) {
        const overallProgress = ((fileIndex + progress.progress / 100) / job.files.length) * 100;
        job.progress = Math.round(overallProgress);
        job.currentStage = `${progress.stage} (${fileIndex + 1}/${job.files.length})`;
      }
    };

    try {
      switch (job.type) {
        case 'conversion':
          const conversionResults = await this.formatConverter.convertFiles(
            job.files,
            job.options as ConversionOptions,
            onProgress
          );
          results.push(...conversionResults);
          break;

        case 'thumbnail':
          const thumbnailResults = await this.thumbnailGenerator.generateThumbnails(
            job.files,
            job.options as ThumbnailOptions,
            onProgress
          );
          results.push(...thumbnailResults);
          break;

        case 'analysis':
          const analysisResults = await this.modelAnalyzer.analyzeModels(
            job.files,
            onProgress
          );
          results.push(...analysisResults);
          break;

        case 'full_processing':
          // Process conversion, thumbnails, and analysis
          const fullResults = await this.processFullJob(job, onProgress);
          results.push(...fullResults);
          break;

        default:
          throw new Error(`Unknown job type: ${job.type}`);
      }

      const duration = Date.now() - startTime;
      const processedFiles = results.filter(r => r.success).length;
      const failedFiles = results.length - processedFiles;

      return {
        jobId: job.id,
        success: failedFiles === 0,
        totalFiles: job.files.length,
        processedFiles,
        failedFiles,
        results,
        errors,
        duration,
        averageTimePerFile: duration / job.files.length
      };

    } catch (error) {
      throw error;
    }
  }

  /**
   * Process full processing job
   */
  private async processFullJob(
    job: BatchJob,
    onProgress: (fileIndex: number, progress: any) => void
  ): Promise<any[]> {
    const results: any[] = [];

    // Stage 1: Conversion
    job.currentStage = 'Converting files';
    const conversionResults = await this.formatConverter.convertFiles(
      job.files,
      job.options.conversion,
      (fileIndex, progress) => {
        onProgress(fileIndex, { ...progress, stage: `conversion: ${progress.stage}` });
      }
    );

    // Stage 2: Thumbnails
    job.currentStage = 'Generating thumbnails';
    const thumbnailResults = await this.thumbnailGenerator.generateThumbnails(
      job.files,
      job.options.thumbnail,
      (fileIndex, progress) => {
        onProgress(fileIndex, { ...progress, stage: `thumbnail: ${progress.stage}` });
      }
    );

    // Stage 3: Analysis
    job.currentStage = 'Analyzing models';
    const analysisResults = await this.modelAnalyzer.analyzeModels(
      job.files,
      (fileIndex, progress) => {
        onProgress(fileIndex, { ...progress, stage: `analysis: ${progress.stage}` });
      }
    );

    // Combine results
    for (let i = 0; i < job.files.length; i++) {
      results.push({
        file: job.files[i].file,
        conversion: conversionResults[i] || null,
        thumbnail: thumbnailResults[i] || null,
        analysis: analysisResults[i] || null
      });
    }

    return results;
  }

  /**
   * Queue management methods
   */
  private addToQueue(jobId: string): void {
    if (this.options.enablePrioritization) {
      // Insert based on priority
      const job = this.jobs.get(jobId);
      if (!job) return;

      const priorityOrder = { urgent: 0, high: 1, normal: 2, low: 3 };
      const jobPriority = priorityOrder[job.priority];

      let insertIndex = this.queue.length;
      for (let i = 0; i < this.queue.length; i++) {
        const queuedJob = this.jobs.get(this.queue[i]);
        if (queuedJob && priorityOrder[queuedJob.priority] > jobPriority) {
          insertIndex = i;
          break;
        }
      }

      this.queue.splice(insertIndex, 0, jobId);
    } else {
      this.queue.push(jobId);
    }
  }

  private removeFromQueue(jobId: string): void {
    const index = this.queue.indexOf(jobId);
    if (index > -1) {
      this.queue.splice(index, 1);
    }
  }

  private getNextJobFromQueue(): string | null {
    return this.queue.shift() || null;
  }

  /**
   * Utility methods
   */
  private generateJobId(type: string): string {
    return `${type}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }

  private estimateConversionTime(files: { file: ModelFile; buffer: ArrayBuffer }[]): number {
    // Estimate 30 seconds per file + 5 seconds per MB
    return files.reduce((total, { buffer }) => {
      return total + 30 + (buffer.byteLength / (1024 * 1024)) * 5;
    }, 0);
  }

  private estimateThumbnailTime(files: { file: ModelFile; buffer: ArrayBuffer }[]): number {
    // Estimate 15 seconds per file
    return files.length * 15;
  }

  private estimateAnalysisTime(files: { file: ModelFile; buffer: ArrayBuffer }[]): number {
    // Estimate 10 seconds per file
    return files.length * 10;
  }

  private estimateFullProcessingTime(files: { file: ModelFile; buffer: ArrayBuffer }[]): number {
    return this.estimateConversionTime(files) + 
           this.estimateThumbnailTime(files) + 
           this.estimateAnalysisTime(files);
  }

  /**
   * Get processing statistics
   */
  getProcessingStats(): {
    totalJobs: number;
    activeJobs: number;
    queuedJobs: number;
    completedJobs: number;
    failedJobs: number;
    averageProcessingTime: number;
  } {
    const jobs = Array.from(this.jobs.values());
    
    return {
      totalJobs: jobs.length,
      activeJobs: this.activeJobs.size,
      queuedJobs: this.queue.length,
      completedJobs: jobs.filter(j => j.status === 'completed').length,
      failedJobs: jobs.filter(j => j.status === 'failed').length,
      averageProcessingTime: jobs
        .filter(j => j.actualDuration)
        .reduce((sum, j) => sum + (j.actualDuration || 0), 0) / 
        Math.max(1, jobs.filter(j => j.actualDuration).length)
    };
  }
}
