/**
 * 3D Model Analysis System
 * Extracts dimensions, polygon count, and other metadata from 3D models
 */

import { ModelFile } from '@/lib/bright-data/model-file-scraper';

export interface ModelAnalysis {
  dimensions: {
    width: number;
    height: number;
    depth: number;
    units: string;
    boundingBox: {
      min: [number, number, number];
      max: [number, number, number];
      center: [number, number, number];
    };
  };
  geometry: {
    vertexCount: number;
    faceCount: number;
    triangleCount: number;
    edgeCount: number;
    surfaceArea: number;
    volume: number;
  };
  quality: {
    meshQuality: 'excellent' | 'good' | 'fair' | 'poor';
    manifoldness: boolean;
    watertight: boolean;
    hasNormals: boolean;
    hasTextures: boolean;
    hasColors: boolean;
  };
  printability: {
    printable: boolean;
    supportRequired: boolean;
    overhangs: number;
    bridgeLength: number;
    minWallThickness: number;
    issues: string[];
    recommendations: string[];
  };
  complexity: {
    level: 'simple' | 'moderate' | 'complex' | 'expert';
    score: number; // 0-100
    factors: {
      geometryComplexity: number;
      detailLevel: number;
      supportComplexity: number;
    };
  };
  materials: {
    recommended: string[];
    difficulty: {
      PLA: 'easy' | 'medium' | 'hard';
      ABS: 'easy' | 'medium' | 'hard';
      PETG: 'easy' | 'medium' | 'hard';
      TPU: 'easy' | 'medium' | 'hard';
    };
  };
  estimatedPrintTime: {
    fast: number; // minutes
    normal: number;
    high: number;
  };
  fileInfo: {
    format: string;
    size: number;
    compression: number;
    version?: string;
  };
}

export interface AnalysisProgress {
  stage: 'parsing' | 'geometry' | 'quality' | 'printability' | 'complexity' | 'complete';
  progress: number; // 0-100
  message: string;
}

export class ModelAnalyzer {
  /**
   * Analyze a 3D model file
   */
  async analyzeModel(
    file: ModelFile,
    buffer: ArrayBuffer,
    onProgress?: (progress: AnalysisProgress) => void
  ): Promise<ModelAnalysis> {
    try {
      console.log(`🔍 Analyzing model: ${file.name}`);

      // Stage 1: Parse the model
      onProgress?.({
        stage: 'parsing',
        progress: 10,
        message: 'Parsing 3D model...'
      });

      const parsedModel = await this.parseModel(file, buffer);

      // Stage 2: Analyze geometry
      onProgress?.({
        stage: 'geometry',
        progress: 30,
        message: 'Analyzing geometry...'
      });

      const geometryAnalysis = this.analyzeGeometry(parsedModel);
      const dimensionsAnalysis = this.analyzeDimensions(parsedModel);

      // Stage 3: Assess quality
      onProgress?.({
        stage: 'quality',
        progress: 50,
        message: 'Assessing mesh quality...'
      });

      const qualityAnalysis = this.analyzeQuality(parsedModel);

      // Stage 4: Evaluate printability
      onProgress?.({
        stage: 'printability',
        progress: 70,
        message: 'Evaluating 3D printability...'
      });

      const printabilityAnalysis = this.analyzePrintability(parsedModel, dimensionsAnalysis);

      // Stage 5: Calculate complexity
      onProgress?.({
        stage: 'complexity',
        progress: 90,
        message: 'Calculating complexity...'
      });

      const complexityAnalysis = this.analyzeComplexity(parsedModel, geometryAnalysis, printabilityAnalysis);
      const materialsAnalysis = this.analyzeMaterials(complexityAnalysis, printabilityAnalysis);
      const printTimeAnalysis = this.estimatePrintTime(dimensionsAnalysis, complexityAnalysis);

      onProgress?.({
        stage: 'complete',
        progress: 100,
        message: 'Analysis complete!'
      });

      const analysis: ModelAnalysis = {
        dimensions: dimensionsAnalysis,
        geometry: geometryAnalysis,
        quality: qualityAnalysis,
        printability: printabilityAnalysis,
        complexity: complexityAnalysis,
        materials: materialsAnalysis,
        estimatedPrintTime: printTimeAnalysis,
        fileInfo: {
          format: file.format,
          size: buffer.byteLength,
          compression: this.calculateCompressionRatio(file.format, buffer.byteLength, geometryAnalysis.vertexCount)
        }
      };

      console.log(`✅ Model analysis completed for ${file.name}`);
      console.log(`📊 Complexity: ${analysis.complexity.level} (${analysis.complexity.score}/100)`);
      console.log(`📐 Dimensions: ${analysis.dimensions.width.toFixed(1)} × ${analysis.dimensions.height.toFixed(1)} × ${analysis.dimensions.depth.toFixed(1)} ${analysis.dimensions.units}`);

      return analysis;

    } catch (error) {
      console.error(`❌ Model analysis failed for ${file.name}:`, error);
      throw error;
    }
  }

  /**
   * Analyze multiple models in batch
   */
  async analyzeModels(
    files: { file: ModelFile; buffer: ArrayBuffer }[],
    onProgress?: (fileIndex: number, progress: AnalysisProgress) => void
  ): Promise<ModelAnalysis[]> {
    const results: ModelAnalysis[] = [];

    console.log(`🔍 Starting batch analysis of ${files.length} models`);

    for (let i = 0; i < files.length; i++) {
      const { file, buffer } = files[i];

      try {
        const analysis = await this.analyzeModel(file, buffer, (progress) => {
          onProgress?.(i, progress);
        });

        results.push(analysis);
      } catch (error) {
        console.error(`❌ Failed to analyze model ${file.name}:`, error);
        // Continue with other files
      }
    }

    console.log(`✅ Batch analysis completed: ${results.length}/${files.length} successful`);
    return results;
  }

  /**
   * Parse model using format converter
   */
  private async parseModel(file: ModelFile, buffer: ArrayBuffer): Promise<any> {
    const { FormatConverter } = await import('./format-converter');
    const converter = new FormatConverter();
    return await (converter as any).parseInputFile(file, buffer);
  }

  /**
   * Analyze model geometry
   */
  private analyzeGeometry(parsedModel: any): ModelAnalysis['geometry'] {
    const vertexCount = parsedModel.vertexCount || 0;
    const faceCount = parsedModel.faceCount || 0;
    
    // Calculate additional metrics
    const triangleCount = faceCount; // Assuming triangulated mesh
    const edgeCount = this.estimateEdgeCount(vertexCount, faceCount);
    const surfaceArea = this.calculateSurfaceArea(parsedModel.vertices, parsedModel.faces);
    const volume = this.calculateVolume(parsedModel.vertices, parsedModel.faces);

    return {
      vertexCount,
      faceCount,
      triangleCount,
      edgeCount,
      surfaceArea,
      volume
    };
  }

  /**
   * Analyze model dimensions
   */
  private analyzeDimensions(parsedModel: any): ModelAnalysis['dimensions'] {
    const vertices = parsedModel.vertices || [];
    
    if (vertices.length === 0) {
      return {
        width: 0,
        height: 0,
        depth: 0,
        units: 'mm',
        boundingBox: {
          min: [0, 0, 0],
          max: [0, 0, 0],
          center: [0, 0, 0]
        }
      };
    }

    let minX = Infinity, minY = Infinity, minZ = Infinity;
    let maxX = -Infinity, maxY = -Infinity, maxZ = -Infinity;

    for (let i = 0; i < vertices.length; i += 3) {
      const x = vertices[i];
      const y = vertices[i + 1];
      const z = vertices[i + 2];

      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      minZ = Math.min(minZ, z);
      maxX = Math.max(maxX, x);
      maxY = Math.max(maxY, y);
      maxZ = Math.max(maxZ, z);
    }

    const width = maxX - minX;
    const height = maxY - minY;
    const depth = maxZ - minZ;

    const center: [number, number, number] = [
      (minX + maxX) / 2,
      (minY + maxY) / 2,
      (minZ + maxZ) / 2
    ];

    // Determine units based on scale
    const maxDimension = Math.max(width, height, depth);
    const units = maxDimension > 1000 ? 'mm' : maxDimension > 1 ? 'cm' : 'm';

    return {
      width,
      height,
      depth,
      units,
      boundingBox: {
        min: [minX, minY, minZ],
        max: [maxX, maxY, maxZ],
        center
      }
    };
  }

  /**
   * Analyze mesh quality
   */
  private analyzeQuality(parsedModel: any): ModelAnalysis['quality'] {
    const vertexCount = parsedModel.vertexCount || 0;
    const faceCount = parsedModel.faceCount || 0;
    
    // Determine mesh quality based on vertex/face ratio and other factors
    const ratio = vertexCount > 0 ? faceCount / vertexCount : 0;
    let meshQuality: 'excellent' | 'good' | 'fair' | 'poor';

    if (ratio > 1.8 && vertexCount > 1000) {
      meshQuality = 'excellent';
    } else if (ratio > 1.5 && vertexCount > 500) {
      meshQuality = 'good';
    } else if (ratio > 1.0 && vertexCount > 100) {
      meshQuality = 'fair';
    } else {
      meshQuality = 'poor';
    }

    return {
      meshQuality,
      manifoldness: this.checkManifoldness(parsedModel),
      watertight: this.checkWatertight(parsedModel),
      hasNormals: !!parsedModel.normals && parsedModel.normals.length > 0,
      hasTextures: !!parsedModel.textures && parsedModel.textures.length > 0,
      hasColors: !!parsedModel.colors && parsedModel.colors.length > 0
    };
  }

  /**
   * Analyze 3D printability
   */
  private analyzePrintability(
    parsedModel: any, 
    dimensions: ModelAnalysis['dimensions']
  ): ModelAnalysis['printability'] {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check dimensions
    const maxDimension = Math.max(dimensions.width, dimensions.height, dimensions.depth);
    if (maxDimension > 200) {
      issues.push('Model may be too large for standard 3D printers');
      recommendations.push('Consider scaling down or splitting into parts');
    }

    if (dimensions.width < 1 || dimensions.height < 1 || dimensions.depth < 1) {
      issues.push('Model may be too small for reliable printing');
      recommendations.push('Consider scaling up or checking units');
    }

    // Analyze overhangs and supports
    const overhangs = this.analyzeOverhangs(parsedModel);
    const supportRequired = overhangs > 45; // degrees

    if (supportRequired) {
      recommendations.push('Support structures recommended for overhangs');
    }

    // Check wall thickness
    const minWallThickness = this.calculateMinWallThickness(parsedModel);
    if (minWallThickness < 0.8) {
      issues.push('Thin walls detected - may not print reliably');
      recommendations.push('Increase wall thickness to at least 0.8mm');
    }

    // Calculate bridge length
    const bridgeLength = this.calculateBridgeLength(parsedModel);

    const printable = issues.length === 0;

    return {
      printable,
      supportRequired,
      overhangs,
      bridgeLength,
      minWallThickness,
      issues,
      recommendations
    };
  }

  /**
   * Analyze model complexity
   */
  private analyzeComplexity(
    parsedModel: any,
    geometry: ModelAnalysis['geometry'],
    printability: ModelAnalysis['printability']
  ): ModelAnalysis['complexity'] {
    // Calculate complexity factors
    const geometryComplexity = Math.min(100, (geometry.vertexCount / 10000) * 100);
    const detailLevel = Math.min(100, (geometry.faceCount / geometry.vertexCount) * 50);
    const supportComplexity = printability.supportRequired ? 
      Math.min(100, printability.overhangs / 90 * 100) : 0;

    // Overall complexity score
    const score = Math.round(
      (geometryComplexity * 0.4) + 
      (detailLevel * 0.3) + 
      (supportComplexity * 0.3)
    );

    // Determine complexity level
    let level: 'simple' | 'moderate' | 'complex' | 'expert';
    if (score < 25) level = 'simple';
    else if (score < 50) level = 'moderate';
    else if (score < 75) level = 'complex';
    else level = 'expert';

    return {
      level,
      score,
      factors: {
        geometryComplexity,
        detailLevel,
        supportComplexity
      }
    };
  }

  /**
   * Analyze material recommendations
   */
  private analyzeMaterials(
    complexity: ModelAnalysis['complexity'],
    printability: ModelAnalysis['printability']
  ): ModelAnalysis['materials'] {
    const recommended: string[] = [];
    
    // Base recommendations
    if (complexity.level === 'simple') {
      recommended.push('PLA', 'PETG');
    } else if (complexity.level === 'moderate') {
      recommended.push('PLA', 'PETG', 'ABS');
    } else {
      recommended.push('PETG', 'ABS', 'ASA');
    }

    // Adjust based on printability
    if (printability.supportRequired) {
      recommended.push('PVA (for supports)');
    }

    // Material difficulty assessment
    const difficulty = {
      PLA: complexity.level === 'expert' ? 'medium' as const : 'easy' as const,
      ABS: complexity.level === 'simple' ? 'medium' as const : 'hard' as const,
      PETG: complexity.level === 'expert' ? 'hard' as const : 'medium' as const,
      TPU: 'hard' as const // Always hard due to flexibility
    };

    return {
      recommended,
      difficulty
    };
  }

  /**
   * Estimate print time
   */
  private estimatePrintTime(
    dimensions: ModelAnalysis['dimensions'],
    complexity: ModelAnalysis['complexity']
  ): ModelAnalysis['estimatedPrintTime'] {
    const volume = dimensions.width * dimensions.height * dimensions.depth;
    const baseTime = Math.max(30, volume / 1000 * 60); // Base time in minutes

    const complexityMultiplier = 1 + (complexity.score / 100);

    return {
      fast: Math.round(baseTime * 0.7 * complexityMultiplier),
      normal: Math.round(baseTime * complexityMultiplier),
      high: Math.round(baseTime * 1.5 * complexityMultiplier)
    };
  }

  /**
   * Helper methods for analysis
   */
  private estimateEdgeCount(vertexCount: number, faceCount: number): number {
    // Euler's formula: V - E + F = 2 for a polyhedron
    // E = V + F - 2
    return Math.max(0, vertexCount + faceCount - 2);
  }

  private calculateSurfaceArea(vertices: number[], faces: number[]): number {
    // Simplified surface area calculation
    return faces ? faces.length * 0.1 : 0;
  }

  private calculateVolume(vertices: number[], faces: number[]): number {
    // Simplified volume calculation
    return faces ? faces.length * 0.01 : 0;
  }

  private checkManifoldness(parsedModel: any): boolean {
    // Simplified manifoldness check
    return parsedModel.vertexCount > 0 && parsedModel.faceCount > 0;
  }

  private checkWatertight(parsedModel: any): boolean {
    // Simplified watertight check
    return this.checkManifoldness(parsedModel);
  }

  private analyzeOverhangs(parsedModel: any): number {
    // Simplified overhang analysis - return average overhang angle
    return Math.random() * 90; // Placeholder
  }

  private calculateMinWallThickness(parsedModel: any): number {
    // Simplified wall thickness calculation
    return 0.8 + Math.random() * 1.2; // Placeholder
  }

  private calculateBridgeLength(parsedModel: any): number {
    // Simplified bridge length calculation
    return Math.random() * 20; // Placeholder
  }

  private calculateCompressionRatio(format: string, fileSize: number, vertexCount: number): number {
    // Estimate compression ratio based on format and data
    const uncompressedSize = vertexCount * 12; // 3 floats per vertex
    return uncompressedSize > 0 ? fileSize / uncompressedSize : 1;
  }
}
