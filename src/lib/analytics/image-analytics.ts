/**
 * Image Analytics - Аналітика та моніторинг використання зображень
 * Інтеграція з Cloudflare Analytics та власними метриками
 */

export interface ImageAnalyticsEvent {
  type: 'view' | 'load' | 'error' | 'download' | 'zoom' | 'share';
  imageId: string;
  imageUrl: string;
  modelId?: string;
  platform?: string;
  userId?: string;
  sessionId: string;
  timestamp: string;
  metadata?: {
    loadTime?: number;
    errorMessage?: string;
    zoomLevel?: number;
    shareMethod?: string;
    deviceType?: string;
    browserType?: string;
    connectionType?: string;
    imageSize?: number;
    imageFormat?: string;
    cacheHit?: boolean;
  };
}

export interface ImagePerformanceMetrics {
  imageId: string;
  imageUrl: string;
  averageLoadTime: number;
  totalViews: number;
  totalDownloads: number;
  errorRate: number;
  cacheHitRate: number;
  popularityScore: number;
  lastUpdated: string;
}

export interface ImageAnalyticsSummary {
  totalImages: number;
  totalViews: number;
  totalDownloads: number;
  averageLoadTime: number;
  errorRate: number;
  cacheHitRate: number;
  bandwidthUsed: number;
  topImages: ImagePerformanceMetrics[];
  platformBreakdown: Record<string, number>;
  deviceBreakdown: Record<string, number>;
  timeRange: {
    start: string;
    end: string;
  };
}

export class ImageAnalytics {
  private sessionId: string;
  private userId?: string;
  private isEnabled: boolean;
  private eventQueue: ImageAnalyticsEvent[] = [];
  private flushInterval: number = 30000; // 30 seconds
  private maxQueueSize: number = 100;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.isEnabled = this.checkAnalyticsEnabled();
    
    if (this.isEnabled) {
      this.startPeriodicFlush();
      this.setupBeforeUnloadHandler();
    }
  }

  /**
   * Генерація унікального ID сесії
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Перевірка чи увімкнена аналітика
   */
  private checkAnalyticsEnabled(): boolean {
    // Перевіряємо налаштування користувача та GDPR
    if (typeof window === 'undefined') return false;
    
    const consent = localStorage.getItem('analytics_consent');
    return consent === 'true';
  }

  /**
   * Встановлення користувача
   */
  setUser(userId: string): void {
    this.userId = userId;
  }

  /**
   * Відстеження перегляду зображення
   */
  trackImageView(imageId: string, imageUrl: string, metadata?: Partial<ImageAnalyticsEvent['metadata']>): void {
    this.trackEvent('view', imageId, imageUrl, metadata);
  }

  /**
   * Відстеження завантаження зображення
   */
  trackImageLoad(
    imageId: string, 
    imageUrl: string, 
    loadTime: number, 
    metadata?: Partial<ImageAnalyticsEvent['metadata']>
  ): void {
    this.trackEvent('load', imageId, imageUrl, {
      ...metadata,
      loadTime,
      cacheHit: this.detectCacheHit(loadTime),
    });
  }

  /**
   * Відстеження помилки завантаження
   */
  trackImageError(
    imageId: string, 
    imageUrl: string, 
    errorMessage: string, 
    metadata?: Partial<ImageAnalyticsEvent['metadata']>
  ): void {
    this.trackEvent('error', imageId, imageUrl, {
      ...metadata,
      errorMessage,
    });
  }

  /**
   * Відстеження завантаження зображення користувачем
   */
  trackImageDownload(imageId: string, imageUrl: string, metadata?: Partial<ImageAnalyticsEvent['metadata']>): void {
    this.trackEvent('download', imageId, imageUrl, metadata);
  }

  /**
   * Відстеження зуму зображення
   */
  trackImageZoom(
    imageId: string, 
    imageUrl: string, 
    zoomLevel: number, 
    metadata?: Partial<ImageAnalyticsEvent['metadata']>
  ): void {
    this.trackEvent('zoom', imageId, imageUrl, {
      ...metadata,
      zoomLevel,
    });
  }

  /**
   * Відстеження поділу зображення
   */
  trackImageShare(
    imageId: string, 
    imageUrl: string, 
    shareMethod: string, 
    metadata?: Partial<ImageAnalyticsEvent['metadata']>
  ): void {
    this.trackEvent('share', imageId, imageUrl, {
      ...metadata,
      shareMethod,
    });
  }

  /**
   * Загальний метод відстеження подій
   */
  private trackEvent(
    type: ImageAnalyticsEvent['type'],
    imageId: string,
    imageUrl: string,
    metadata?: Partial<ImageAnalyticsEvent['metadata']>
  ): void {
    if (!this.isEnabled) return;

    const event: ImageAnalyticsEvent = {
      type,
      imageId,
      imageUrl,
      userId: this.userId,
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
      metadata: {
        ...this.getDeviceInfo(),
        ...metadata,
      },
    };

    this.eventQueue.push(event);

    // Flush якщо черга переповнена
    if (this.eventQueue.length >= this.maxQueueSize) {
      this.flushEvents();
    }
  }

  /**
   * Отримання інформації про пристрій
   */
  private getDeviceInfo(): Partial<ImageAnalyticsEvent['metadata']> {
    if (typeof window === 'undefined') return {};

    return {
      deviceType: this.getDeviceType(),
      browserType: this.getBrowserType(),
      connectionType: this.getConnectionType(),
    };
  }

  /**
   * Визначення типу пристрою
   */
  private getDeviceType(): string {
    if (typeof window === 'undefined') return 'unknown';
    
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (/mobile|android|iphone|ipad|phone/i.test(userAgent)) {
      return 'mobile';
    } else if (/tablet|ipad/i.test(userAgent)) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }

  /**
   * Визначення типу браузера
   */
  private getBrowserType(): string {
    if (typeof window === 'undefined') return 'unknown';
    
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (userAgent.includes('chrome')) return 'chrome';
    if (userAgent.includes('firefox')) return 'firefox';
    if (userAgent.includes('safari')) return 'safari';
    if (userAgent.includes('edge')) return 'edge';
    
    return 'other';
  }

  /**
   * Визначення типу з'єднання
   */
  private getConnectionType(): string {
    if (typeof window === 'undefined') return 'unknown';
    
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    
    if (connection) {
      return connection.effectiveType || connection.type || 'unknown';
    }
    
    return 'unknown';
  }

  /**
   * Визначення cache hit
   */
  private detectCacheHit(loadTime: number): boolean {
    // Якщо завантаження дуже швидке, ймовірно це cache hit
    return loadTime < 100;
  }

  /**
   * Відправка подій на сервер
   */
  private async flushEvents(): Promise<void> {
    if (this.eventQueue.length === 0) return;

    const events = [...this.eventQueue];
    this.eventQueue = [];

    try {
      await this.sendEventsToServer(events);
      console.log(`📊 Відправлено ${events.length} аналітичних подій`);
    } catch (error) {
      console.error('Помилка відправки аналітичних подій:', error);
      // Повертаємо події назад у чергу
      this.eventQueue.unshift(...events);
    }
  }

  /**
   * Відправка подій на сервер
   */
  private async sendEventsToServer(events: ImageAnalyticsEvent[]): Promise<void> {
    try {
      const response = await fetch('/api/analytics/images', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ events }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      // Fallback до Cloudflare Analytics
      await this.sendToCloudflareAnalytics(events);
    }
  }

  /**
   * Відправка до Cloudflare Analytics
   */
  private async sendToCloudflareAnalytics(events: ImageAnalyticsEvent[]): Promise<void> {
    try {
      // Використовуємо Cloudflare Web Analytics API
      const analyticsData = events.map(event => ({
        name: `image_${event.type}`,
        value: 1,
        dimensions: {
          imageId: event.imageId,
          platform: event.metadata?.platform || 'unknown',
          deviceType: event.metadata?.deviceType || 'unknown',
          browserType: event.metadata?.browserType || 'unknown',
        },
        timestamp: event.timestamp,
      }));

      // В реальному проекті тут би був виклик Cloudflare Analytics API
      console.log('📈 Відправка до Cloudflare Analytics:', analyticsData);
    } catch (error) {
      console.error('Помилка відправки до Cloudflare Analytics:', error);
    }
  }

  /**
   * Періодичне відправлення подій
   */
  private startPeriodicFlush(): void {
    setInterval(() => {
      this.flushEvents();
    }, this.flushInterval);
  }

  /**
   * Обробник перед закриттям сторінки
   */
  private setupBeforeUnloadHandler(): void {
    if (typeof window === 'undefined') return;

    window.addEventListener('beforeunload', () => {
      // Синхронна відправка перед закриттям
      if (this.eventQueue.length > 0) {
        navigator.sendBeacon('/api/analytics/images', JSON.stringify({
          events: this.eventQueue
        }));
      }
    });
  }

  /**
   * Отримання метрик продуктивності зображень
   */
  async getImagePerformanceMetrics(imageId: string): Promise<ImagePerformanceMetrics | null> {
    try {
      const response = await fetch(`/api/analytics/images/${imageId}/metrics`);
      if (!response.ok) return null;
      
      return await response.json();
    } catch (error) {
      console.error('Помилка отримання метрик зображення:', error);
      return null;
    }
  }

  /**
   * Отримання загального звіту по аналітиці
   */
  async getAnalyticsSummary(timeRange?: { start: string; end: string }): Promise<ImageAnalyticsSummary | null> {
    try {
      const params = new URLSearchParams();
      if (timeRange) {
        params.append('start', timeRange.start);
        params.append('end', timeRange.end);
      }

      const response = await fetch(`/api/analytics/images/summary?${params}`);
      if (!response.ok) return null;
      
      return await response.json();
    } catch (error) {
      console.error('Помилка отримання звіту аналітики:', error);
      return null;
    }
  }

  /**
   * Увімкнення/вимкнення аналітики
   */
  setAnalyticsEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    
    if (typeof window !== 'undefined') {
      localStorage.setItem('analytics_consent', enabled.toString());
    }

    if (enabled && this.eventQueue.length === 0) {
      this.startPeriodicFlush();
      this.setupBeforeUnloadHandler();
    }
  }

  /**
   * Очищення черги подій
   */
  clearEventQueue(): void {
    this.eventQueue = [];
  }

  /**
   * Отримання поточного розміру черги
   */
  getQueueSize(): number {
    return this.eventQueue.length;
  }
}

// Експорт глобального екземпляра
export const imageAnalytics = new ImageAnalytics();
