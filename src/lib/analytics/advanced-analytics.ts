/**
 * Advanced Analytics Engine for ROI Optimization and Market Analysis
 */

import { PricingTrend } from '@/types/competitor-models';

export interface ROIAnalysis {
  sellerId: string;
  platform: string;
  category: string;
  period: {
    start: string;
    end: string;
  };
  revenue: {
    total: number;
    average: number;
    growth: number;
    growthPercentage: number;
  };
  costs: {
    development: number;
    platform: number;
    marketing: number;
    total: number;
  };
  roi: {
    percentage: number;
    absolute: number;
    trend: 'improving' | 'declining' | 'stable';
  };
  performance: {
    modelsCount: number;
    averagePrice: number;
    totalDownloads: number;
    conversionRate: number;
  };
  recommendations: ROIRecommendation[];
}

export interface ROIRecommendation {
  type: 'pricing' | 'portfolio' | 'marketing' | 'timing';
  title: string;
  description: string;
  expectedImpact: string;
  priority: 'high' | 'medium' | 'low';
  implementation: string;
  confidence: number;
}

export interface MarketShareAnalysis {
  platform: string;
  category: string;
  period: {
    start: string;
    end: string;
  };
  totalMarketSize: {
    models: number;
    revenue: number;
    downloads: number;
  };
  topPerformers: {
    sellerId: string;
    marketShare: number;
    revenue: number;
    modelsCount: number;
    averagePrice: number;
  }[];
  marketConcentration: {
    hhi: number; // Herfindahl-Hirschman Index
    topNShare: number; // Top N sellers' combined share
    competitionLevel: 'low' | 'medium' | 'high';
  };
  trends: {
    growthRate: number;
    priceEvolution: number;
    newEntrants: number;
    marketMaturity: 'emerging' | 'growing' | 'mature' | 'declining';
  };
}

export interface PerformanceBenchmark {
  sellerId: string;
  platform: string;
  category: string;
  metrics: {
    revenuePerModel: number;
    downloadsPerModel: number;
    priceOptimization: number;
    marketPenetration: number;
    customerSatisfaction: number;
  };
  benchmarks: {
    industryAverage: number;
    topQuartile: number;
    percentileRank: number;
  };
  gaps: {
    metric: string;
    currentValue: number;
    targetValue: number;
    improvementPotential: number;
  }[];
}

export interface PredictiveInsight {
  type: 'demand_forecast' | 'price_optimization' | 'market_timing' | 'risk_assessment';
  title: string;
  description: string;
  prediction: {
    value: number;
    confidence: number;
    timeframe: string;
    factors: string[];
  };
  actionable: boolean;
  recommendation: string;
  impact: 'high' | 'medium' | 'low';
}

export class AdvancedAnalyticsEngine {
  /**
   * Calculate comprehensive ROI analysis for a seller
   */
  calculateROIAnalysis(
    sellerId: string,
    platform: string,
    category: string,
    salesData: any[],
    costData: any,
    period: { start: string; end: string }
  ): ROIAnalysis {
    // Calculate revenue metrics
    const totalRevenue = salesData.reduce((sum, sale) => sum + sale.amount, 0);
    const averageRevenue = totalRevenue / Math.max(salesData.length, 1);
    const previousPeriodRevenue = this.calculatePreviousPeriodRevenue(salesData, period);
    const revenueGrowth = totalRevenue - previousPeriodRevenue;
    const revenueGrowthPercentage = previousPeriodRevenue > 0 ? (revenueGrowth / previousPeriodRevenue) * 100 : 0;

    // Calculate costs
    const totalCosts = costData.development + costData.platform + costData.marketing;

    // Calculate ROI
    const roiPercentage = totalCosts > 0 ? ((totalRevenue - totalCosts) / totalCosts) * 100 : 0;
    const roiAbsolute = totalRevenue - totalCosts;
    const roiTrend = this.determineROITrend(salesData, costData);

    // Calculate performance metrics
    const modelsCount = new Set(salesData.map(sale => sale.modelId)).size;
    const averagePrice = salesData.length > 0 ? totalRevenue / salesData.length : 0;
    const totalDownloads = salesData.reduce((sum, sale) => sum + (sale.downloads || 1), 0);
    const conversionRate = totalDownloads > 0 ? (salesData.length / totalDownloads) * 100 : 0;

    // Generate recommendations
    const recommendations = this.generateROIRecommendations({
      roiPercentage,
      revenueGrowthPercentage,
      averagePrice,
      conversionRate,
      modelsCount
    });

    return {
      sellerId,
      platform,
      category,
      period,
      revenue: {
        total: totalRevenue,
        average: averageRevenue,
        growth: revenueGrowth,
        growthPercentage: revenueGrowthPercentage
      },
      costs: {
        development: costData.development,
        platform: costData.platform,
        marketing: costData.marketing,
        total: totalCosts
      },
      roi: {
        percentage: roiPercentage,
        absolute: roiAbsolute,
        trend: roiTrend
      },
      performance: {
        modelsCount,
        averagePrice,
        totalDownloads,
        conversionRate
      },
      recommendations
    };
  }

  /**
   * Analyze market share and competitive positioning
   */
  analyzeMarketShare(
    platform: string,
    category: string,
    marketData: any[],
    period: { start: string; end: string }
  ): MarketShareAnalysis {
    // Calculate total market size
    const totalModels = marketData.length;
    const totalRevenue = marketData.reduce((sum, item) => sum + (item.revenue || 0), 0);
    const totalDownloads = marketData.reduce((sum, item) => sum + (item.downloads || 0), 0);

    // Group by seller and calculate market shares
    const sellerData = new Map<string, any>();
    marketData.forEach(item => {
      const sellerId = item.sellerId;
      if (!sellerData.has(sellerId)) {
        sellerData.set(sellerId, {
          sellerId,
          revenue: 0,
          modelsCount: 0,
          downloads: 0,
          prices: []
        });
      }
      
      const seller = sellerData.get(sellerId);
      seller.revenue += item.revenue || 0;
      seller.modelsCount += 1;
      seller.downloads += item.downloads || 0;
      seller.prices.push(item.price || 0);
    });

    // Calculate top performers
    const topPerformers = Array.from(sellerData.values())
      .map(seller => ({
        sellerId: seller.sellerId,
        marketShare: totalRevenue > 0 ? (seller.revenue / totalRevenue) * 100 : 0,
        revenue: seller.revenue,
        modelsCount: seller.modelsCount,
        averagePrice: seller.prices.reduce((sum: number, price: number) => sum + price, 0) / seller.prices.length
      }))
      .sort((a, b) => b.marketShare - a.marketShare)
      .slice(0, 10);

    // Calculate market concentration (HHI)
    const marketShares = topPerformers.map(p => p.marketShare);
    const hhi = marketShares.reduce((sum, share) => sum + Math.pow(share, 2), 0);
    const topNShare = marketShares.slice(0, 5).reduce((sum, share) => sum + share, 0);
    
    const competitionLevel = hhi > 2500 ? 'low' : hhi > 1500 ? 'medium' : 'high';

    // Calculate market trends
    const growthRate = this.calculateMarketGrowthRate(marketData, period);
    const priceEvolution = this.calculatePriceEvolution(marketData, period);
    const newEntrants = this.countNewEntrants(marketData, period);
    const marketMaturity = this.determineMarketMaturity(growthRate, newEntrants, hhi);

    return {
      platform,
      category,
      period,
      totalMarketSize: {
        models: totalModels,
        revenue: totalRevenue,
        downloads: totalDownloads
      },
      topPerformers,
      marketConcentration: {
        hhi,
        topNShare,
        competitionLevel
      },
      trends: {
        growthRate,
        priceEvolution,
        newEntrants,
        marketMaturity
      }
    };
  }

  /**
   * Generate performance benchmarks against industry standards
   */
  generatePerformanceBenchmarks(
    sellerId: string,
    platform: string,
    category: string,
    sellerData: any,
    industryData: any[]
  ): PerformanceBenchmark {
    // Calculate seller metrics
    const revenuePerModel = sellerData.totalRevenue / Math.max(sellerData.modelsCount, 1);
    const downloadsPerModel = sellerData.totalDownloads / Math.max(sellerData.modelsCount, 1);
    const priceOptimization = this.calculatePriceOptimizationScore(sellerData, industryData);
    const marketPenetration = this.calculateMarketPenetration(sellerData, industryData);
    const customerSatisfaction = sellerData.averageRating || 0;

    // Calculate industry benchmarks
    const industryMetrics = this.calculateIndustryMetrics(industryData);
    const percentileRank = this.calculatePercentileRank(revenuePerModel, industryData.map(d => d.revenuePerModel));

    // Identify improvement gaps
    const gaps = [
      {
        metric: 'Revenue per Model',
        currentValue: revenuePerModel,
        targetValue: industryMetrics.topQuartileRevenue,
        improvementPotential: Math.max(0, industryMetrics.topQuartileRevenue - revenuePerModel)
      },
      {
        metric: 'Downloads per Model',
        currentValue: downloadsPerModel,
        targetValue: industryMetrics.topQuartileDownloads,
        improvementPotential: Math.max(0, industryMetrics.topQuartileDownloads - downloadsPerModel)
      },
      {
        metric: 'Price Optimization',
        currentValue: priceOptimization,
        targetValue: 100,
        improvementPotential: Math.max(0, 100 - priceOptimization)
      }
    ].filter(gap => gap.improvementPotential > 0);

    return {
      sellerId,
      platform,
      category,
      metrics: {
        revenuePerModel,
        downloadsPerModel,
        priceOptimization,
        marketPenetration,
        customerSatisfaction
      },
      benchmarks: {
        industryAverage: industryMetrics.averageRevenue,
        topQuartile: industryMetrics.topQuartileRevenue,
        percentileRank
      },
      gaps
    };
  }

  /**
   * Generate predictive insights using historical data
   */
  generatePredictiveInsights(
    platform: string,
    category: string,
    historicalData: any[],
    currentTrends: PricingTrend[]
  ): PredictiveInsight[] {
    const insights: PredictiveInsight[] = [];

    // Demand forecast
    const demandForecast = this.forecastDemand(historicalData);
    insights.push({
      type: 'demand_forecast',
      title: 'Demand Forecast',
      description: `Predicted demand for ${category} models on ${platform}`,
      prediction: {
        value: demandForecast.value,
        confidence: demandForecast.confidence,
        timeframe: '3 months',
        factors: demandForecast.factors
      },
      actionable: true,
      recommendation: demandForecast.recommendation,
      impact: demandForecast.impact
    });

    // Price optimization
    const priceOptimization = this.optimizePrice(currentTrends);
    insights.push({
      type: 'price_optimization',
      title: 'Price Optimization',
      description: `Optimal pricing strategy for maximum ROI`,
      prediction: {
        value: priceOptimization.optimalPrice,
        confidence: priceOptimization.confidence,
        timeframe: '1 month',
        factors: priceOptimization.factors
      },
      actionable: true,
      recommendation: priceOptimization.recommendation,
      impact: priceOptimization.impact
    });

    // Market timing
    const marketTiming = this.analyzeMarketTiming(historicalData);
    insights.push({
      type: 'market_timing',
      title: 'Market Timing',
      description: `Best time to launch new models`,
      prediction: {
        value: marketTiming.score,
        confidence: marketTiming.confidence,
        timeframe: marketTiming.timeframe,
        factors: marketTiming.factors
      },
      actionable: true,
      recommendation: marketTiming.recommendation,
      impact: marketTiming.impact
    });

    return insights.sort((a, b) => b.prediction.confidence - a.prediction.confidence);
  }

  // Private helper methods

  private calculatePreviousPeriodRevenue(salesData: any[], period: { start: string; end: string }): number {
    const periodLength = new Date(period.end).getTime() - new Date(period.start).getTime();
    const previousStart = new Date(new Date(period.start).getTime() - periodLength);
    const previousEnd = new Date(period.start);

    return salesData
      .filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate >= previousStart && saleDate < previousEnd;
      })
      .reduce((sum, sale) => sum + sale.amount, 0);
  }

  private determineROITrend(salesData: any[], costData: any): 'improving' | 'declining' | 'stable' {
    // Simplified trend analysis - would use more sophisticated methods in production
    const recentROI = this.calculateRecentROI(salesData, costData);
    const historicalROI = this.calculateHistoricalROI(salesData, costData);

    if (recentROI > historicalROI * 1.1) return 'improving';
    if (recentROI < historicalROI * 0.9) return 'declining';
    return 'stable';
  }

  private calculateRecentROI(salesData: any[], costData: any): number {
    const recentSales = salesData.slice(-Math.floor(salesData.length / 3));
    const revenue = recentSales.reduce((sum, sale) => sum + sale.amount, 0);
    const costs = costData.total / 3; // Approximate recent costs
    return costs > 0 ? ((revenue - costs) / costs) * 100 : 0;
  }

  private calculateHistoricalROI(salesData: any[], costData: any): number {
    const historicalSales = salesData.slice(0, Math.floor(salesData.length * 2 / 3));
    const revenue = historicalSales.reduce((sum, sale) => sum + sale.amount, 0);
    const costs = costData.total * 2 / 3; // Approximate historical costs
    return costs > 0 ? ((revenue - costs) / costs) * 100 : 0;
  }

  private generateROIRecommendations(metrics: any): ROIRecommendation[] {
    const recommendations: ROIRecommendation[] = [];

    if (metrics.roiPercentage < 20) {
      recommendations.push({
        type: 'pricing',
        title: 'Increase Pricing',
        description: 'Current ROI is below industry standards. Consider increasing prices.',
        expectedImpact: `Potential ${Math.round((30 - metrics.roiPercentage) * 0.8)}% ROI improvement`,
        priority: 'high',
        implementation: 'Gradually increase prices by 10-15% and monitor market response',
        confidence: 0.8
      });
    }

    if (metrics.conversionRate < 5) {
      recommendations.push({
        type: 'marketing',
        title: 'Improve Conversion Rate',
        description: 'Low conversion rate suggests marketing or presentation issues.',
        expectedImpact: 'Potential 20-30% revenue increase',
        priority: 'medium',
        implementation: 'Improve model descriptions, thumbnails, and SEO optimization',
        confidence: 0.7
      });
    }

    if (metrics.modelsCount < 10) {
      recommendations.push({
        type: 'portfolio',
        title: 'Expand Portfolio',
        description: 'Limited model count restricts revenue potential.',
        expectedImpact: 'Linear revenue growth with portfolio expansion',
        priority: 'medium',
        implementation: 'Focus on high-demand categories and quality over quantity',
        confidence: 0.9
      });
    }

    return recommendations;
  }

  private calculateMarketGrowthRate(marketData: any[], period: { start: string; end: string }): number {
    // Simplified growth calculation
    const periodLength = new Date(period.end).getTime() - new Date(period.start).getTime();
    const midPoint = new Date(new Date(period.start).getTime() + periodLength / 2);

    const firstHalf = marketData.filter(item => new Date(item.date) < midPoint);
    const secondHalf = marketData.filter(item => new Date(item.date) >= midPoint);

    const firstHalfRevenue = firstHalf.reduce((sum, item) => sum + (item.revenue || 0), 0);
    const secondHalfRevenue = secondHalf.reduce((sum, item) => sum + (item.revenue || 0), 0);

    return firstHalfRevenue > 0 ? ((secondHalfRevenue - firstHalfRevenue) / firstHalfRevenue) * 100 : 0;
  }

  private calculatePriceEvolution(marketData: any[], period: { start: string; end: string }): number {
    const prices = marketData.map(item => item.price || 0).filter(price => price > 0);
    if (prices.length < 2) return 0;

    const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    const firstQuarter = prices.slice(0, Math.floor(prices.length / 4));
    const lastQuarter = prices.slice(-Math.floor(prices.length / 4));

    const firstAvg = firstQuarter.reduce((sum, price) => sum + price, 0) / firstQuarter.length;
    const lastAvg = lastQuarter.reduce((sum, price) => sum + price, 0) / lastQuarter.length;

    return firstAvg > 0 ? ((lastAvg - firstAvg) / firstAvg) * 100 : 0;
  }

  private countNewEntrants(marketData: any[], period: { start: string; end: string }): number {
    const sellers = new Set(marketData.map(item => item.sellerId));
    const periodStart = new Date(period.start);
    
    const newSellers = new Set();
    marketData.forEach(item => {
      if (new Date(item.firstSaleDate) >= periodStart) {
        newSellers.add(item.sellerId);
      }
    });

    return newSellers.size;
  }

  private determineMarketMaturity(growthRate: number, newEntrants: number, hhi: number): 'emerging' | 'growing' | 'mature' | 'declining' {
    if (growthRate > 20 && newEntrants > 10) return 'emerging';
    if (growthRate > 10 && newEntrants > 5) return 'growing';
    if (growthRate < -5) return 'declining';
    return 'mature';
  }

  private calculatePriceOptimizationScore(sellerData: any, industryData: any[]): number {
    // Simplified price optimization score
    const sellerAvgPrice = sellerData.averagePrice || 0;
    const industryAvgPrice = industryData.reduce((sum, d) => sum + (d.averagePrice || 0), 0) / industryData.length;
    
    if (industryAvgPrice === 0) return 50;
    
    const priceRatio = sellerAvgPrice / industryAvgPrice;
    
    // Optimal range is 0.8 to 1.2 of industry average
    if (priceRatio >= 0.8 && priceRatio <= 1.2) return 100;
    if (priceRatio >= 0.6 && priceRatio <= 1.5) return 75;
    if (priceRatio >= 0.4 && priceRatio <= 2.0) return 50;
    return 25;
  }

  private calculateMarketPenetration(sellerData: any, industryData: any[]): number {
    const sellerMarketShare = sellerData.marketShare || 0;
    return Math.min(sellerMarketShare * 10, 100); // Scale to 0-100
  }

  private calculateIndustryMetrics(industryData: any[]): any {
    const revenues = industryData.map(d => d.revenuePerModel || 0).sort((a, b) => a - b);
    const downloads = industryData.map(d => d.downloadsPerModel || 0).sort((a, b) => a - b);

    return {
      averageRevenue: revenues.reduce((sum, r) => sum + r, 0) / revenues.length,
      topQuartileRevenue: revenues[Math.floor(revenues.length * 0.75)],
      topQuartileDownloads: downloads[Math.floor(downloads.length * 0.75)]
    };
  }

  private calculatePercentileRank(value: number, dataset: number[]): number {
    const sorted = dataset.sort((a, b) => a - b);
    const rank = sorted.findIndex(v => v >= value);
    return rank >= 0 ? (rank / sorted.length) * 100 : 0;
  }

  private forecastDemand(historicalData: any[]): any {
    // Simplified demand forecasting
    const recentTrend = historicalData.slice(-6); // Last 6 periods
    const avgGrowth = recentTrend.length > 1 ? 
      (recentTrend[recentTrend.length - 1].demand - recentTrend[0].demand) / recentTrend.length : 0;

    const currentDemand = historicalData[historicalData.length - 1]?.demand || 0;
    const forecastedDemand = currentDemand + (avgGrowth * 3); // 3 periods ahead

    return {
      value: Math.max(0, forecastedDemand),
      confidence: Math.min(0.9, recentTrend.length / 10),
      factors: ['Historical growth trend', 'Seasonal patterns', 'Market conditions'],
      recommendation: avgGrowth > 0 ? 'Increase production capacity' : 'Focus on market penetration',
      impact: avgGrowth > 0.1 ? 'high' : avgGrowth > 0.05 ? 'medium' : 'low'
    };
  }

  private optimizePrice(currentTrends: PricingTrend[]): any {
    if (currentTrends.length === 0) {
      return {
        optimalPrice: 25,
        confidence: 0.3,
        factors: ['Default pricing strategy'],
        recommendation: 'Gather more market data for better pricing',
        impact: 'medium'
      };
    }

    const latestTrend = currentTrends[0];
    const optimalPrice = (latestTrend.pricePercentiles.p50 + latestTrend.pricePercentiles.p75) / 2;

    return {
      optimalPrice,
      confidence: 0.8,
      factors: ['Market median pricing', 'Competition analysis', 'Demand elasticity'],
      recommendation: `Price between $${latestTrend.pricePercentiles.p50} and $${latestTrend.pricePercentiles.p75}`,
      impact: 'high'
    };
  }

  private analyzeMarketTiming(historicalData: any[]): any {
    // Simplified market timing analysis
    const seasonalPatterns = this.identifySeasonalPatterns(historicalData);
    const currentMonth = new Date().getMonth();
    const score = seasonalPatterns[currentMonth] || 50;

    return {
      score,
      confidence: 0.7,
      timeframe: 'Next 30 days',
      factors: ['Seasonal demand patterns', 'Competition cycles', 'Market trends'],
      recommendation: score > 70 ? 'Excellent time to launch' : score > 50 ? 'Good timing' : 'Consider waiting',
      impact: score > 70 ? 'high' : 'medium'
    };
  }

  private identifySeasonalPatterns(historicalData: any[]): number[] {
    // Simplified seasonal analysis - returns scores for each month (0-11)
    const monthlyScores = new Array(12).fill(50);
    
    // Example seasonal patterns (would be calculated from actual data)
    monthlyScores[0] = 60; // January
    monthlyScores[1] = 55; // February
    monthlyScores[2] = 70; // March
    monthlyScores[3] = 75; // April
    monthlyScores[4] = 80; // May
    monthlyScores[5] = 65; // June
    monthlyScores[6] = 60; // July
    monthlyScores[7] = 65; // August
    monthlyScores[8] = 75; // September
    monthlyScores[9] = 80; // October
    monthlyScores[10] = 85; // November
    monthlyScores[11] = 90; // December

    return monthlyScores;
  }
}
