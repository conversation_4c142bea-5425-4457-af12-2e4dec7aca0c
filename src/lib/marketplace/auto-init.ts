/**
 * Automatic marketplace initialization
 * Starts trending models loading on app startup
 */

import { initializeModelsDatabase } from '@/lib/db/client';
import { getTrendingModelsLoader } from './trending-models-loader';

let isInitialized = false;

/**
 * Initializes marketplace with automatic trending models loading
 */
export async function initializeMarketplace(): Promise<void> {
  if (isInitialized) {
    console.log('⚠️ Marketplace already initialized');
    return;
  }

  try {
    console.log('🚀 Initializing real marketplace...');

    // 1. Initialize database
    await initializeModelsDatabase();
    console.log('✅ Database initialized');

    // 2. Set up automatic trending models loading
    const loader = getTrendingModelsLoader({
      platforms: ['printables', 'makerworld', 'thangs'],
      modelsPerPlatform: 20,
      updateInterval: 180, // 3 hours
      enableAI: true
    });

    // 3. Start automatic loading
    await loader.startAutoLoading();
    console.log('✅ Automatic trending models loading started');

    isInitialized = true;
    console.log('🎉 Real marketplace successfully initialized!');

  } catch (error) {
    console.error('❌ Marketplace initialization error:', error);

    // Don't block the app if initialization fails
    // User can start manually through admin panel
  }
}

/**
 * Checks if marketplace is initialized
 */
export function isMarketplaceInitialized(): boolean {
  return isInitialized;
}

/**
 * Force initialization (even if already initialized)
 */
export async function forceInitializeMarketplace(): Promise<void> {
  isInitialized = false;
  await initializeMarketplace();
}

// Automatic initialization on module import (server-side only)
if (typeof window === 'undefined') {
  // Delay to give the app time to fully load
  setTimeout(() => {
    initializeMarketplace().catch(error => {
      console.error('❌ Automatic initialization failed:', error);
    });
  }, 5000); // 5 seconds delay
}
