'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import type { AdvancedModelViewerProps, ARVRViewerProps, ModelComparisonProps } from '@/types/components';
import {
    ArrowLeftRight,
    BarChart3,
    BookOpen,
    Download,
    Eye,
    Heart,
    Layers,
    MessageCircle,
    Monitor,
    Settings,
    Share2,
    Smartphone,
    Star
} from 'lucide-react';
import dynamic from 'next/dynamic';
import { useState } from 'react';

const AdvancedModelViewer = dynamic(() => import('@/components/3d-viewer/AdvancedModelViewer'), {
  ssr: false,
  loading: () => <div className="h-96 bg-muted animate-pulse rounded-lg" />
}) as React.ComponentType<AdvancedModelViewerProps>;

const ARVRViewer = dynamic(() => import('@/components/3d-viewer/ARVRViewer'), {
  ssr: false,
  loading: () => <div className="h-96 bg-muted animate-pulse rounded-lg" />
}) as React.ComponentType<ARVRViewerProps>;

const ModelComparison = dynamic(() => import('@/components/3d-viewer/ModelComparison'), {
  ssr: false,
  loading: () => <div className="h-96 bg-muted animate-pulse rounded-lg" />
}) as React.ComponentType<ModelComparisonProps>;

const DEMO_MODELS = [
  {
    id: 'model1',
    name: 'Articulated Dragon',
    url: 'https://threejs.org/examples/models/gltf/DamagedHelmet/glTF/DamagedHelmet.gltf',
    description: 'Високодеталізований дракон з рухомими частинами',
    price: 15.99,
    creator: 'DragonMaster3D',
    rating: 4.8,
    downloads: 1250
  },
  {
    id: 'model2',
    name: 'Sci-Fi Helmet',
    url: 'https://threejs.org/examples/models/gltf/DamagedHelmet/glTF/DamagedHelmet.gltf',
    description: 'Футуристичний шолом з деталізованими текстурами',
    price: 12.50,
    creator: 'SciFiDesigner',
    rating: 4.6,
    downloads: 890
  },
  {
    id: 'model3',
    name: 'Vintage Car',
    url: 'https://threejs.org/examples/models/gltf/DamagedHelmet/glTF/DamagedHelmet.gltf',
    description: 'Класичний автомобіль 1960-х років',
    price: 25.00,
    creator: 'VintageModels',
    rating: 4.9,
    downloads: 2100
  },
  {
    id: 'model4',
    name: 'Modern Chair',
    url: 'https://threejs.org/examples/models/gltf/DamagedHelmet/glTF/DamagedHelmet.gltf',
    description: 'Сучасний дизайнерський стілець',
    price: 8.99,
    creator: 'FurnitureDesign',
    rating: 4.4,
    downloads: 650
  }
];

export default function ThreeDShowcase() {
  const [activeTab, setActiveTab] = useState('advanced');
  const [selectedModel, setSelectedModel] = useState(DEMO_MODELS[0]);
  const [comparisonModels, setComparisonModels] = useState([DEMO_MODELS[0], DEMO_MODELS[1]]);

  const handleModelSelect = (model: typeof DEMO_MODELS[0]) => {
    setSelectedModel(model);
  };

  const addToComparison = (model: typeof DEMO_MODELS[0]) => {
    if (comparisonModels.length < 4 && !comparisonModels.find(m => m.id === model.id)) {
      setComparisonModels([...comparisonModels, model]);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">3D Showcase</h1>
              <p className="text-muted-foreground mt-1">
                Демонстрація розширених можливостей 3D-перегляду
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="flex items-center gap-1">
                <Eye className="w-3 h-3" />
                Advanced Viewer
              </Badge>
              <Badge variant="secondary" className="flex items-center gap-1">
                <ArrowLeftRight className="w-3 h-3" />
                Model Comparison
              </Badge>
              <Badge variant="secondary" className="flex items-center gap-1">
                <Smartphone className="w-3 h-3" />
                AR/VR Ready
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5 mb-8">
            <TabsTrigger value="advanced" className="flex items-center gap-2">
              <Layers className="w-4 h-4" />
              Advanced Viewer
            </TabsTrigger>
            <TabsTrigger value="comparison" className="flex items-center gap-2">
              <ArrowLeftRight className="w-4 h-4" />
              Model Comparison
            </TabsTrigger>
            <TabsTrigger value="arvr" className="flex items-center gap-2">
              <Smartphone className="w-4 h-4" />
              AR/VR Viewer
            </TabsTrigger>
            <TabsTrigger value="tutorial" className="flex items-center gap-2">
              <BookOpen className="w-4 h-4" />
              3D Tutorial
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Analytics
            </TabsTrigger>
          </TabsList>

          {/* Advanced Viewer Tab */}
          <TabsContent value="advanced" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Model Selection */}
              <Card className="p-4">
                <h3 className="font-semibold mb-4">Вибрати модель</h3>
                <div className="space-y-3">
                  {DEMO_MODELS.map((model) => (
                    <div
                      key={model.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedModel.id === model.id
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => handleModelSelect(model)}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-sm">{model.name}</h4>
                        <Badge variant="outline" className="text-xs">
                          ${model.price}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground mb-2">
                        {model.description}
                      </p>
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-muted-foreground">by {model.creator}</span>
                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-1">
                            <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                            <span>{model.rating}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Download className="w-3 h-3" />
                            <span>{model.downloads}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              {/* Advanced Viewer */}
              <Card className="lg:col-span-2 p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold">Advanced 3D Viewer</h3>
                  <div className="flex items-center gap-2">
                    <Button size="sm" variant="outline">
                      <Heart className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <MessageCircle className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <Share2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                <AdvancedModelViewer
                  modelUrl={selectedModel.url}
                  title={selectedModel.name}
                  description={selectedModel.description}
                  autoRotate={true}
                  showControls={true}
                  showStats={false}
                  enableAnimations={true}
                  enableWireframe={true}
                  enableFullscreen={true}
                  enableMeasurements={true}
                  className="h-96"
                />

                {/* Model Details */}
                <div className="mt-4 p-4 bg-muted/50 rounded-lg">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Ціна:</span>
                      <p className="font-semibold">${selectedModel.price}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Рейтинг:</span>
                      <p className="font-semibold flex items-center gap-1">
                        <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                        {selectedModel.rating}
                      </p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Завантаження:</span>
                      <p className="font-semibold">{selectedModel.downloads}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Автор:</span>
                      <p className="font-semibold">{selectedModel.creator}</p>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </TabsContent>

          {/* Model Comparison Tab */}
          <TabsContent value="comparison" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* Available Models */}
              <Card className="p-4">
                <h3 className="font-semibold mb-4">Доступні моделі</h3>
                <div className="space-y-3">
                  {DEMO_MODELS.map((model) => (
                    <div
                      key={model.id}
                      className="p-3 rounded-lg border hover:border-primary/50 cursor-pointer transition-colors"
                      onClick={() => addToComparison(model)}
                    >
                      <h4 className="font-medium text-sm mb-1">{model.name}</h4>
                      <p className="text-xs text-muted-foreground mb-2">
                        ${model.price}
                      </p>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="w-full text-xs"
                        disabled={comparisonModels.find(m => m.id === model.id) !== undefined}
                      >
                        {comparisonModels.find(m => m.id === model.id) ? 'Додано' : 'Додати'}
                      </Button>
                    </div>
                  ))}
                </div>
              </Card>

              {/* Model Comparison */}
              <Card className="lg:col-span-3 p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold">Порівняння моделей</h3>
                  <Badge variant="secondary">
                    {comparisonModels.length} / 4 моделей
                  </Badge>
                </div>
                
                <ModelComparison
                  models={comparisonModels}
                  maxModels={4}
                  showControls={true}
                  enableSync={true}
                  className="h-96"
                />
              </Card>
            </div>
          </TabsContent>

          {/* AR/VR Viewer Tab */}
          <TabsContent value="arvr" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* AR/VR Info */}
              <Card className="p-4">
                <h3 className="font-semibold mb-4">AR/VR Можливості</h3>
                <div className="space-y-4">
                  <div className="p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Smartphone className="w-4 h-4 text-primary" />
                      <span className="font-medium text-sm">Augmented Reality</span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Переглядайте 3D моделі у реальному світі через камеру вашого пристрою
                    </p>
                  </div>

                  <div className="p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Monitor className="w-4 h-4 text-primary" />
                      <span className="font-medium text-sm">Virtual Reality</span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Повне занурення у віртуальне середовище з 3D моделями
                    </p>
                  </div>

                  <div className="p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Settings className="w-4 h-4 text-primary" />
                      <span className="font-medium text-sm">Налаштування</span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Повний контроль над масштабом, позицією та освітленням
                    </p>
                  </div>
                </div>
              </Card>

              {/* AR/VR Viewer */}
              <Card className="lg:col-span-2 p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold">AR/VR Viewer</h3>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      WebXR Ready
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      Camera Access
                    </Badge>
                  </div>
                </div>
                
                <ARVRViewer
                  modelUrl={selectedModel.url}
                  title={selectedModel.name}
                  description={selectedModel.description}
                  enableAR={true}
                  enableVR={true}
                  showControls={true}
                  className="h-96"
                  onARStart={() => console.log('AR started')}
                  onVRStart={() => console.log('VR started')}
                  onError={(error: Error) => console.error('AR/VR error:', error)}
                />
              </Card>
            </div>
          </TabsContent>

          {/* 3D Tutorial Tab */}
          <TabsContent value="tutorial" className="space-y-6">
            <Card className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold">Інтерактивне навчання 3D</h3>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    Інтерактивний
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    Покроковий
                  </Badge>
                </div>
              </div>

              <div className="h-96 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <BookOpen className="w-16 h-16 mx-auto mb-4 text-blue-500" />
                  <h4 className="text-lg font-semibold mb-2">3D Tutorial</h4>
                  <p className="text-muted-foreground mb-4">
                    Інтерактивне навчання основам 3D-моделювання
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Основні форми</span>
                    </div>
                    <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span>Матеріали та текстури</span>
                    </div>
                    <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      <span>Освітлення та анімація</span>
                    </div>
                  </div>
                  <Button className="mt-4" variant="outline">
                    Почати навчання
                  </Button>
                </div>
              </div>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <Card className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold">Аналітика моделі</h3>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    Реальний час
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    Детальна статистика
                  </Badge>
                </div>
              </div>

              <div className="h-96 bg-gradient-to-br from-green-50 to-blue-50 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="w-16 h-16 mx-auto mb-4 text-green-500" />
                  <h4 className="text-lg font-semibold mb-2">Model Analytics</h4>
                  <p className="text-muted-foreground mb-4">
                    Детальна аналітика переглядів, завантажень та взаємодії
                  </p>
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">1,250</div>
                      <div className="text-xs text-muted-foreground">Переглядів</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">340</div>
                      <div className="text-xs text-muted-foreground">Завантажень</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">89</div>
                      <div className="text-xs text-muted-foreground">Лайків</div>
                    </div>
                  </div>
                  <Button variant="outline">
                    Детальна аналітика
                  </Button>
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
