import { NextRequest, NextResponse } from 'next/server';
import { FilamentScraper } from '@/lib/bright-data/filament-scraper';
import { FilamentAnalytics } from '@/lib/cloudflare/filament-analytics';

// Note: Edge runtime disabled for OpenNext compatibility
// export const runtime = 'edge';

interface ScrapeRequest {
  keyword?: string;
  maxResults?: number;
  source?: 'amazon' | 'aliexpress' | 'all';
  useCache?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body: ScrapeRequest = await request.json();
    const { 
      keyword = 'PLA filament 1.75mm', 
      maxResults = 20, 
      source = 'amazon',
      useCache = true 
    } = body;

    console.log(`🔍 API: Початок скрапінгу філаментів`, { keyword, maxResults, source });

    // Ініціалізуємо скрапер та аналітику
    const scraper = new FilamentScraper();
    const analytics = new FilamentAnalytics();

    // Перевіряємо кеш якщо потрібно
    if (useCache) {
      try {
        // Тут би була перевірка кешу в KV
        console.log('📦 Перевіряємо кеш...');
      } catch (error) {
        console.log('⚠️ Кеш недоступний, продовжуємо скрапінг');
      }
    }

    let result;

    // Виконуємо скрапінг залежно від джерела
    switch (source) {
      case 'amazon':
        result = await scraper.scrapeAmazonFilaments(keyword, maxResults);
        break;
      case 'all':
        result = await scraper.scrapePopularFilaments(maxResults);
        break;
      default:
        result = await scraper.scrapeAmazonFilaments(keyword, maxResults);
    }

    // Записуємо аналітику
    try {
      await analytics.trackEvent({
        eventType: 'search',
        searchQuery: keyword,
        sessionId: request.headers.get('x-session-id') || 'anonymous',
        userId: request.headers.get('x-user-id') || undefined,
        timestamp: new Date(),
        metadata: {
          source,
          maxResults,
          resultCount: result.products.length
        }
      });
    } catch (error) {
      console.error('❌ Помилка запису аналітики:', error);
    }

    // Зберігаємо результат в кеш якщо потрібно
    if (useCache && result.success) {
      try {
        // Тут би було збереження в KV кеш
        console.log('💾 Зберігаємо результат в кеш');
      } catch (error) {
        console.log('⚠️ Не вдалося зберегти в кеш:', error);
      }
    }

    return NextResponse.json({
      success: result.success,
      data: {
        products: result.products,
        totalFound: result.totalFound,
        scrapedAt: result.scrapedAt,
        source: result.source
      },
      errors: result.errors,
      metadata: {
        keyword,
        maxResults,
        source,
        cached: false
      }
    });

  } catch (error) {
    console.error('❌ API помилка скрапінгу філаментів:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to scrape filaments',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const keyword = searchParams.get('keyword') || 'PLA filament 1.75mm';
    const maxResults = parseInt(searchParams.get('maxResults') || '20');
    const source = (searchParams.get('source') || 'amazon') as 'amazon' | 'aliexpress' | 'all';
    const useCache = searchParams.get('useCache') !== 'false';

    console.log(`🔍 API GET: Скрапінг філаментів`, { keyword, maxResults, source });

    const scraper = new FilamentScraper();
    const analytics = new FilamentAnalytics();

    let result;

    switch (source) {
      case 'amazon':
        result = await scraper.scrapeAmazonFilaments(keyword, maxResults);
        break;
      case 'all':
        result = await scraper.scrapePopularFilaments(maxResults);
        break;
      default:
        result = await scraper.scrapeAmazonFilaments(keyword, maxResults);
    }

    // Записуємо аналітику
    try {
      await analytics.trackEvent({
        eventType: 'search',
        searchQuery: keyword,
        sessionId: request.headers.get('x-session-id') || 'anonymous',
        userId: request.headers.get('x-user-id') || undefined,
        timestamp: new Date(),
        metadata: {
          source,
          maxResults,
          resultCount: result.products.length,
          method: 'GET'
        }
      });
    } catch (error) {
      console.error('❌ Помилка запису аналітики:', error);
    }

    return NextResponse.json({
      success: result.success,
      data: {
        products: result.products,
        totalFound: result.totalFound,
        scrapedAt: result.scrapedAt,
        source: result.source
      },
      errors: result.errors,
      metadata: {
        keyword,
        maxResults,
        source,
        cached: false
      }
    });

  } catch (error) {
    console.error('❌ API GET помилка скрапінгу філаментів:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to scrape filaments',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Ендпоінт для отримання трендових філаментів
export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');

    console.log(`📈 API: Отримання трендових філаментів`, { limit });

    const scraper = new FilamentScraper();
    const analytics = new FilamentAnalytics();

    // Отримуємо трендові філаменти
    const trendingFilaments = await scraper.getTrendingFilaments(limit);

    // Отримуємо популярні філаменти з аналітики
    const popularMetrics = await analytics.getPopularFilaments(limit);

    // Записуємо аналітику
    try {
      await analytics.trackEvent({
        eventType: 'view',
        sessionId: request.headers.get('x-session-id') || 'anonymous',
        userId: request.headers.get('x-user-id') || undefined,
        timestamp: new Date(),
        metadata: {
          action: 'trending_filaments',
          limit
        }
      });
    } catch (error) {
      console.error('❌ Помилка запису аналітики:', error);
    }

    return NextResponse.json({
      success: true,
      data: {
        trending: trendingFilaments,
        popular: popularMetrics,
        generatedAt: new Date()
      },
      metadata: {
        limit,
        trendingCount: trendingFilaments.length,
        popularCount: popularMetrics.length
      }
    });

  } catch (error) {
    console.error('❌ API помилка отримання трендових філаментів:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to get trending filaments',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
