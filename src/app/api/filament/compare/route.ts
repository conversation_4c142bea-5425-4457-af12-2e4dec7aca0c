import { FilamentScraper } from '@/lib/bright-data/filament-scraper';
import { FilamentAnalytics } from '@/lib/cloudflare/filament-analytics';
import { NextRequest, NextResponse } from 'next/server';

// Note: Edge runtime disabled for OpenNext compatibility
// export const runtime = 'edge';

interface CompareRequest {
  filamentIds: string[];
  includeAnalytics?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body: CompareRequest = await request.json();
    const { filamentIds, includeAnalytics = true } = body;

    if (!filamentIds || filamentIds.length < 2) {
      return NextResponse.json({
        success: false,
        error: 'At least 2 filament IDs are required for comparison'
      }, { status: 400 });
    }

    if (filamentIds.length > 4) {
      return NextResponse.json({
        success: false,
        error: 'Maximum 4 filaments can be compared at once'
      }, { status: 400 });
    }

    console.log(`🔍 API: Порівняння філаментів`, { filamentIds, includeAnalytics });

    const scraper = new FilamentScraper();
    const analytics = new FilamentAnalytics();

    // Отримуємо дані філаментів (в реальному проекті з бази даних)
    // Поки що використовуємо демо-дані
    const allFilaments = await scraper.scrapePopularFilaments(100);
    const filamentsToCompare = allFilaments.products.filter(f => 
      filamentIds.includes(f.id)
    );

    if (filamentsToCompare.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No filaments found with provided IDs'
      }, { status: 404 });
    }

    // Аналіз порівняння
    const comparison = {
      filaments: filamentsToCompare,
      analysis: {
        priceRange: {
          min: Math.min(...filamentsToCompare.map(f => f.price)),
          max: Math.max(...filamentsToCompare.map(f => f.price)),
          average: filamentsToCompare.reduce((sum, f) => sum + f.price, 0) / filamentsToCompare.length
        },
        ratingRange: {
          min: Math.min(...filamentsToCompare.map(f => f.rating)),
          max: Math.max(...filamentsToCompare.map(f => f.rating)),
          average: filamentsToCompare.reduce((sum, f) => sum + f.rating, 0) / filamentsToCompare.length
        },
        bestValue: filamentsToCompare.reduce((best, current) => 
          (current.rating / current.price) > (best.rating / best.price) ? current : best
        ),
        mostPopular: filamentsToCompare.reduce((most, current) => 
          current.likes > most.likes ? current : most
        ),
        categories: [...new Set(filamentsToCompare.map(f => f.category).filter(Boolean))],
        manufacturers: [...new Set(filamentsToCompare.map(f => f.manufacturer))],
        commonFeatures: getCommonFeatures(filamentsToCompare),
        differences: getDifferences(filamentsToCompare)
      }
    };

    // Записуємо аналітику
    if (includeAnalytics) {
      try {
        await analytics.trackEvent({
          eventType: 'compare',
          sessionId: request.headers.get('x-session-id') || 'anonymous',
          userId: request.headers.get('x-user-id') || undefined,
          timestamp: new Date(),
          metadata: {
            filamentIds,
            comparisonCount: filamentsToCompare.length,
            categories: comparison.analysis.categories,
            manufacturers: comparison.analysis.manufacturers
          }
        });
      } catch (error) {
        console.error('❌ Помилка запису аналітики:', error);
      }
    }

    return NextResponse.json({
      success: true,
      data: comparison,
      metadata: {
        comparedCount: filamentsToCompare.length,
        requestedCount: filamentIds.length,
        generatedAt: new Date()
      }
    });

  } catch (error) {
    console.error('❌ API помилка порівняння філаментів:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to compare filaments',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Допоміжні функції
function getCommonFeatures(filaments: any[]): string[] {
  if (filaments.length === 0) return [];
  
  const firstFilament = filaments[0];
  const commonFeatures: string[] = [];
  
  // Перевіряємо спільні особливості
  firstFilament.features?.forEach((feature: string) => {
    if (filaments.every(f => f.features?.includes(feature))) {
      commonFeatures.push(feature);
    }
  });
  
  return commonFeatures;
}

function getDifferences(filaments: any[]): Record<string, any> {
  const differences: Record<string, any> = {};
  
  // Порівнюємо основні характеристики
  const specs = ['material', 'diameter', 'color', 'printingTemperature', 'bedTemperature'];
  
  specs.forEach(spec => {
    const values = filaments.map(f => f.specifications?.[spec]).filter(Boolean);
    const uniqueValues = [...new Set(values)];
    
    if (uniqueValues.length > 1) {
      differences[spec] = uniqueValues;
    }
  });
  
  // Порівнюємо ціни
  const prices = filaments.map(f => f.price);
  if (Math.max(...prices) - Math.min(...prices) > 0) {
    differences.priceVariation = {
      range: Math.max(...prices) - Math.min(...prices),
      cheapest: Math.min(...prices),
      mostExpensive: Math.max(...prices)
    };
  }
  
  // Порівнюємо рейтинги
  const ratings = filaments.map(f => f.rating);
  if (Math.max(...ratings) - Math.min(...ratings) > 0) {
    differences.ratingVariation = {
      range: Math.max(...ratings) - Math.min(...ratings),
      lowest: Math.min(...ratings),
      highest: Math.max(...ratings)
    };
  }
  
  return differences;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const filamentIds = searchParams.get('ids')?.split(',') || [];
    const includeAnalytics = searchParams.get('analytics') !== 'false';

    if (filamentIds.length < 2) {
      return NextResponse.json({
        success: false,
        error: 'At least 2 filament IDs are required. Use ?ids=id1,id2,id3'
      }, { status: 400 });
    }

    // Перенаправляємо на POST метод
    return await POST(new NextRequest(request.url, {
      method: 'POST',
      headers: request.headers,
      body: JSON.stringify({ filamentIds, includeAnalytics })
    }));

  } catch (error) {
    console.error('❌ API GET помилка порівняння філаментів:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to compare filaments',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
