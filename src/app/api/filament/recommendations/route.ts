import { NextRequest, NextResponse } from 'next/server';
import { FilamentScraper } from '@/lib/bright-data/filament-scraper';
import { FilamentAnalytics } from '@/lib/cloudflare/filament-analytics';

// Note: Edge runtime disabled for OpenNext compatibility
// export const runtime = 'edge';

interface RecommendationsRequest {
  userId?: string;
  sessionId?: string;
  basedOn?: 'viewed' | 'purchased' | 'compared' | 'similar';
  filamentId?: string;
  preferences?: {
    priceRange?: [number, number];
    materials?: string[];
    brands?: string[];
    features?: string[];
  };
  limit?: number;
}

export async function POST(request: NextRequest) {
  try {
    const body: RecommendationsRequest = await request.json();
    const { 
      userId, 
      sessionId = 'anonymous', 
      basedOn = 'similar', 
      filamentId,
      preferences = {},
      limit = 10 
    } = body;

    console.log(`🎯 API: Генерація рекомендацій філаментів`, { 
      userId, sessionId, basedOn, filamentId, limit 
    });

    const scraper = new FilamentScraper();
    const analytics = new FilamentAnalytics();

    // Отримуємо всі доступні філаменти
    const allFilaments = await scraper.scrapePopularFilaments(100);
    let recommendations = allFilaments.products;

    // Фільтруємо за вподобаннями користувача
    if (preferences.priceRange) {
      recommendations = recommendations.filter(f => 
        f.price >= preferences.priceRange![0] && f.price <= preferences.priceRange![1]
      );
    }

    if (preferences.materials && preferences.materials.length > 0) {
      recommendations = recommendations.filter(f => 
        preferences.materials!.some(material => 
          f.specifications.material.toLowerCase().includes(material.toLowerCase())
        )
      );
    }

    if (preferences.brands && preferences.brands.length > 0) {
      recommendations = recommendations.filter(f => 
        preferences.brands!.some(brand => 
          f.manufacturer.toLowerCase().includes(brand.toLowerCase())
        )
      );
    }

    if (preferences.features && preferences.features.length > 0) {
      recommendations = recommendations.filter(f => 
        preferences.features!.some(feature => 
          f.features.some(fFeature => 
            fFeature.toLowerCase().includes(feature.toLowerCase())
          )
        )
      );
    }

    // Генеруємо рекомендації залежно від типу
    let scoredRecommendations;
    
    switch (basedOn) {
      case 'viewed':
        scoredRecommendations = await generateViewBasedRecommendations(
          recommendations, userId, sessionId, analytics
        );
        break;
      case 'purchased':
        scoredRecommendations = await generatePurchaseBasedRecommendations(
          recommendations, userId, sessionId, analytics
        );
        break;
      case 'compared':
        scoredRecommendations = await generateComparisonBasedRecommendations(
          recommendations, userId, sessionId, analytics
        );
        break;
      case 'similar':
      default:
        scoredRecommendations = await generateSimilarityBasedRecommendations(
          recommendations, filamentId, scraper
        );
        break;
    }

    // Сортуємо за скором та обмежуємо кількість
    const finalRecommendations = scoredRecommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(({ filament, score, reason }) => ({
        ...filament,
        recommendationScore: score,
        recommendationReason: reason
      }));

    // Записуємо аналітику
    try {
      await analytics.trackEvent({
        eventType: 'view',
        sessionId,
        userId,
        timestamp: new Date(),
        metadata: {
          action: 'recommendations_generated',
          basedOn,
          filamentId,
          recommendationsCount: finalRecommendations.length,
          preferences
        }
      });
    } catch (error) {
      console.error('❌ Помилка запису аналітики:', error);
    }

    return NextResponse.json({
      success: true,
      data: {
        recommendations: finalRecommendations,
        basedOn,
        preferences,
        generatedAt: new Date()
      },
      metadata: {
        totalAvailable: recommendations.length,
        returned: finalRecommendations.length,
        algorithm: basedOn,
        userId: userId || 'anonymous'
      }
    });

  } catch (error) {
    console.error('❌ API помилка генерації рекомендацій:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to generate recommendations',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Рекомендації на основі переглядів
async function generateViewBasedRecommendations(
  filaments: any[], 
  userId?: string, 
  sessionId?: string, 
  analytics?: any
): Promise<Array<{filament: any, score: number, reason: string}>> {
  // Симуляція аналізу переглядів користувача
  const userBehavior = await analytics?.analyzeUserBehavior(userId || sessionId, sessionId);
  
  return filaments.map(filament => {
    let score = filament.rating * 20; // Базовий скор
    let reason = 'Популярний філамент';

    // Бонус за відповідність інтересам користувача
    if (userBehavior?.interests.includes(filament.category)) {
      score += 30;
      reason = `Відповідає вашим інтересам: ${filament.category}`;
    }

    // Бонус за ціновий діапазон
    if (userBehavior?.priceRange && 
        filament.price >= userBehavior.priceRange[0] && 
        filament.price <= userBehavior.priceRange[1]) {
      score += 20;
      reason += ' та ціновому діапазону';
    }

    return { filament, score, reason };
  });
}

// Рекомендації на основі покупок
async function generatePurchaseBasedRecommendations(
  filaments: any[], 
  userId?: string, 
  sessionId?: string, 
  analytics?: any
): Promise<Array<{filament: any, score: number, reason: string}>> {
  return filaments.map(filament => {
    let score = filament.sales * 0.1 + filament.rating * 15;
    let reason = 'Часто купують разом';

    // Бонус за високі продажі
    if (filament.sales > 1000) {
      score += 25;
      reason = 'Бестселер серед користувачів';
    }

    return { filament, score, reason };
  });
}

// Рекомендації на основі порівнянь
async function generateComparisonBasedRecommendations(
  filaments: any[], 
  userId?: string, 
  sessionId?: string, 
  analytics?: any
): Promise<Array<{filament: any, score: number, reason: string}>> {
  return filaments.map(filament => {
    let score = filament.rating * 18 + (filament.likes * 0.05);
    let reason = 'Часто порівнюють з іншими';

    // Бонус за високий рейтинг
    if (filament.rating >= 4.5) {
      score += 30;
      reason = 'Високо оцінений користувачами';
    }

    return { filament, score, reason };
  });
}

// Рекомендації на основі схожості
async function generateSimilarityBasedRecommendations(
  filaments: any[], 
  baseFilamentId?: string, 
  scraper?: any
): Promise<Array<{filament: any, score: number, reason: string}>> {
  if (!baseFilamentId) {
    // Якщо немає базового філаменту, повертаємо популярні
    return filaments.map(filament => ({
      filament,
      score: filament.likes * 0.1 + filament.rating * 20,
      reason: 'Популярний вибір'
    }));
  }

  const baseFilament = filaments.find(f => f.id === baseFilamentId);
  if (!baseFilament) {
    return filaments.map(filament => ({
      filament,
      score: filament.rating * 15,
      reason: 'Рекомендований філамент'
    }));
  }

  return filaments
    .filter(f => f.id !== baseFilamentId)
    .map(filament => {
      let score = 0;
      let reasons: string[] = [];

      // Схожість за матеріалом
      if (filament.specifications.material === baseFilament.specifications.material) {
        score += 40;
        reasons.push('той же матеріал');
      }

      // Схожість за діаметром
      if (filament.specifications.diameter === baseFilament.specifications.diameter) {
        score += 30;
        reasons.push('той же діаметр');
      }

      // Схожість за виробником
      if (filament.manufacturer === baseFilament.manufacturer) {
        score += 25;
        reasons.push('той же виробник');
      }

      // Схожість за ціною (±20%)
      const priceDiff = Math.abs(filament.price - baseFilament.price) / baseFilament.price;
      if (priceDiff <= 0.2) {
        score += 20;
        reasons.push('схожа ціна');
      }

      // Схожість за категорією
      if (filament.category === baseFilament.category) {
        score += 15;
        reasons.push('та ж категорія');
      }

      // Базовий скор за рейтинг
      score += filament.rating * 10;

      const reason = reasons.length > 0 
        ? `Схожий: ${reasons.join(', ')}` 
        : 'Може вас зацікавити';

      return { filament, score, reason };
    });
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || undefined;
    const sessionId = searchParams.get('sessionId') || 'anonymous';
    const basedOn = (searchParams.get('basedOn') as any) || 'similar';
    const filamentId = searchParams.get('filamentId') || undefined;
    const limit = parseInt(searchParams.get('limit') || '10');

    // Парсимо preferences з query параметрів
    const preferences: any = {};
    
    const priceMin = searchParams.get('priceMin');
    const priceMax = searchParams.get('priceMax');
    if (priceMin && priceMax) {
      preferences.priceRange = [parseFloat(priceMin), parseFloat(priceMax)];
    }

    const materials = searchParams.get('materials');
    if (materials) {
      preferences.materials = materials.split(',');
    }

    const brands = searchParams.get('brands');
    if (brands) {
      preferences.brands = brands.split(',');
    }

    // Перенаправляємо на POST метод
    return await POST(new NextRequest(request.url, {
      method: 'POST',
      headers: request.headers,
      body: JSON.stringify({ 
        userId, 
        sessionId, 
        basedOn, 
        filamentId, 
        preferences, 
        limit 
      })
    }));

  } catch (error) {
    console.error('❌ API GET помилка рекомендацій:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to generate recommendations',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
