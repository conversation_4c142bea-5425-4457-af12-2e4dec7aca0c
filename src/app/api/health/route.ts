import { NextRequest, NextResponse } from 'next/server';
import { apiClient } from '@/lib/api/config';

/**
 * Health check route - connects to unified worker
 * This provides a health check for both the Next.js app and the worker
 */

export async function GET(request: NextRequest) {
  try {
    console.log(`🏥 Health check request`);

    // Check Next.js app health
    const nextjsHealth = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: '1.0.0'
    };

    // Try to check worker health
    let workerHealth = null;
    try {
      workerHealth = await apiClient.healthCheck();
    } catch (error) {
      console.warn('Worker health check failed:', error);
      workerHealth = {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    const overallStatus = workerHealth?.status === 'healthy' ? 'healthy' : 'degraded';

    return NextResponse.json({
      success: true,
      status: overallStatus,
      services: {
        nextjs: nextjsHealth,
        worker: workerHealth
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Health check error:', error);
    
    return NextResponse.json({
      success: false,
      status: 'unhealthy',
      error: 'Health check failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
