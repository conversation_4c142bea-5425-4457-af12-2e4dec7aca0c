import { NextRequest, NextResponse } from 'next/server';

interface Env {
  PRINTER_MANAGER: DurableObjectNamespace;
  DB: D1Database;
  R2_BUCKET: R2Bucket;
  ANALYTICS: AnalyticsEngineDataset;
  BACKGROUND_QUEUE: Queue;
}

function getCloudflareEnv(request: NextRequest): Env | null {
  const env = (request as any).env || (globalThis as any).env;
  return env;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { printerId: string } }
) {
  try {
    const env = getCloudflareEnv(request);
    if (!env?.PRINTER_MANAGER) {
      return NextResponse.json(
        { error: 'Cloudflare bindings not available' },
        { status: 500 }
      );
    }

    const printerId = params.printerId;
    const url = new URL(request.url);
    
    // Get Durable Object instance for this printer
    const id = env.PRINTER_MANAGER.idFromName(printerId);
    const printerManager = env.PRINTER_MANAGER.get(id);

    // Forward request to Durable Object
    const response = await printerManager.fetch(new Request(url.origin + '/status?' + url.searchParams.toString(), {
      method: 'GET',
      headers: request.headers,
    }));

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Printer manager error:', error);
    return NextResponse.json(
      { error: 'Failed to get printer status' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { printerId: string } }
) {
  try {
    const env = getCloudflareEnv(request);
    if (!env?.PRINTER_MANAGER) {
      return NextResponse.json(
        { error: 'Cloudflare bindings not available' },
        { status: 500 }
      );
    }

    const printerId = params.printerId;
    const body = await request.json();
    const url = new URL(request.url);
    const action = url.searchParams.get('action') || 'register';
    
    // Get Durable Object instance
    const id = env.PRINTER_MANAGER.idFromName(printerId);
    const printerManager = env.PRINTER_MANAGER.get(id);

    let endpoint = '/register';
    switch (action) {
      case 'command':
        endpoint = '/command';
        break;
      case 'job':
        endpoint = '/job';
        break;
      case 'sensors':
        endpoint = '/sensors';
        break;
    }

    // Forward request to Durable Object
    const response = await printerManager.fetch(new Request(url.origin + endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    }));

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Printer manager error:', error);
    return NextResponse.json(
      { error: 'Failed to process printer request' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { printerId: string } }
) {
  try {
    const env = getCloudflareEnv(request);
    if (!env?.PRINTER_MANAGER) {
      return NextResponse.json(
        { error: 'Cloudflare bindings not available' },
        { status: 500 }
      );
    }

    const printerId = params.printerId;
    const body = await request.json();
    
    // Get Durable Object instance
    const id = env.PRINTER_MANAGER.idFromName(printerId);
    const printerManager = env.PRINTER_MANAGER.get(id);

    // Update printer status
    const response = await printerManager.fetch(new Request(request.url + '/sensors', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        printerId,
        sensors: body,
      }),
    }));

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Printer update error:', error);
    return NextResponse.json(
      { error: 'Failed to update printer' },
      { status: 500 }
    );
  }
}
