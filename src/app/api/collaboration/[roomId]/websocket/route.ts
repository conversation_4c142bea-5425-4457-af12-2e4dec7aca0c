import { NextRequest, NextResponse } from 'next/server';

interface Env {
  COLLABORATION_ROOM: DurableObjectNamespace;
}

function getCloudflareEnv(request: NextRequest): Env | null {
  const env = (request as any).env || (globalThis as any).env;
  return env;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { roomId: string } }
) {
  try {
    const env = getCloudflareEnv(request);
    if (!env?.COLLABORATION_ROOM) {
      return NextResponse.json(
        { error: 'Cloudflare bindings not available' },
        { status: 500 }
      );
    }

    const roomId = params.roomId;
    const url = new URL(request.url);
    
    // Get Durable Object instance
    const id = env.COLLABORATION_ROOM.idFromName(roomId);
    const room = env.COLLABORATION_ROOM.get(id);

    // Forward WebSocket upgrade request to Durable Object
    const response = await room.fetch(new Request(url.origin + '/websocket?' + url.searchParams.toString(), {
      method: 'GET',
      headers: request.headers,
    }));

    return response;

  } catch (error) {
    console.error('WebSocket collaboration error:', error);
    return NextResponse.json(
      { error: 'Failed to establish WebSocket connection' },
      { status: 500 }
    );
  }
}
