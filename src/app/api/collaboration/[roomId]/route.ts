import { NextRequest, NextResponse } from 'next/server';

interface Env {
  COLLABORATION_ROOM: DurableObjectNamespace;
  DB: D1Database;
  R2_BUCKET: R2Bucket;
  ANALYTICS: AnalyticsEngineDataset;
}

// Get Cloudflare bindings from the request context
function getCloudflareEnv(request: NextRequest): Env | null {
  // In Cloudflare Pages, bindings are available on the request object
  const env = (request as any).env || (globalThis as any).env;
  return env;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { roomId: string } }
) {
  try {
    const env = getCloudflareEnv(request);
    if (!env?.COLLABORATION_ROOM) {
      return NextResponse.json(
        { error: 'Cloudflare bindings not available' },
        { status: 500 }
      );
    }

    const roomId = params.roomId;
    const url = new URL(request.url);
    
    // Get Durable Object instance
    const id = env.COLLABORATION_ROOM.idFromName(roomId);
    const room = env.COLLABORATION_ROOM.get(id);

    // Forward request to Durable Object
    const response = await room.fetch(new Request(url.toString() + '/state', {
      method: 'GET',
      headers: request.headers,
    }));

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Collaboration room error:', error);
    return NextResponse.json(
      { error: 'Failed to get room state' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { roomId: string } }
) {
  try {
    const env = getCloudflareEnv(request);
    if (!env?.COLLABORATION_ROOM) {
      return NextResponse.json(
        { error: 'Cloudflare bindings not available' },
        { status: 500 }
      );
    }

    const roomId = params.roomId;
    const body = await request.json();
    
    // Get Durable Object instance
    const id = env.COLLABORATION_ROOM.idFromName(roomId);
    const room = env.COLLABORATION_ROOM.get(id);

    // Forward request to Durable Object
    const response = await room.fetch(new Request(request.url + '/join', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    }));

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Collaboration room error:', error);
    return NextResponse.json(
      { error: 'Failed to join room' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { roomId: string } }
) {
  try {
    const env = getCloudflareEnv(request);
    if (!env?.COLLABORATION_ROOM) {
      return NextResponse.json(
        { error: 'Cloudflare bindings not available' },
        { status: 500 }
      );
    }

    const roomId = params.roomId;
    const body = await request.json();
    
    // Get Durable Object instance
    const id = env.COLLABORATION_ROOM.idFromName(roomId);
    const room = env.COLLABORATION_ROOM.get(id);

    // Forward request to Durable Object
    const response = await room.fetch(new Request(request.url + '/leave', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    }));

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Collaboration room error:', error);
    return NextResponse.json(
      { error: 'Failed to leave room' },
      { status: 500 }
    );
  }
}
