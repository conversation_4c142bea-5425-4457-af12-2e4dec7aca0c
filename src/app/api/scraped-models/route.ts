// import { CloudflareObservabilityContext } from '@/lib/cloudflare/middleware';
import {
    getAllModels,
    getModelsByCategory,
    searchModels
} from '@/lib/db';
import { NextRequest, NextResponse } from 'next/server';

// Note: Edge runtime disabled for OpenNext compatibility
// export const runtime = 'edge';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const sort = searchParams.get('sort') || 'popular';
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    let models: any[] = [];

    // Трекінг пошукового запиту (тимчасово відключено)
    // if (search) {
    //   await context.trackEvent('search', {
    //     query: search,
    //     category,
    //     sort
    //   });
    // }

    // Отримуємо моделі залежно від параметрів
    if (search) {
      // Пошук моделей
      models = await searchModels(search);
    } else if (category && category !== 'all') {
      // Фільтрація за категорією
      models = await getModelsByCategory(category);
    } else {
      // Отримуємо всі моделі
      models = await getAllModels();
    }

    // Якщо моделей немає, повертаємо порожній результат
    if (models.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          models: [],
          pagination: {
            total: 0,
            page: Math.floor(offset / limit) + 1,
            limit,
            pages: 0
          }
        },
        message: 'Моделі не знайдено. Спробуйте запустити скрапінг через /api/scrape'
      });
    }

    // Сортування
    switch (sort) {
      case 'newest':
        models.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
      case 'popular':
        models.sort((a, b) => (b.download_count + b.like_count) - (a.download_count + a.like_count));
        break;
      case 'liked':
        models.sort((a, b) => b.like_count - a.like_count);
        break;
      case 'downloads':
        models.sort((a, b) => b.download_count - a.download_count);
        break;
      case 'price_low':
        models.sort((a, b) => a.price - b.price);
        break;
      case 'price_high':
        models.sort((a, b) => b.price - a.price);
        break;
      case 'free':
        models = models.filter(model => model.is_free);
        break;
    }

    // Пагінація
    const totalModels = models.length;
    const paginatedModels = models.slice(offset, offset + limit);
    const hasMore = offset + limit < totalModels;

    // Трекінг результатів пошуку (тимчасово відключено)
    // if (search) {
    //   await context.trackEvent('search_results', {
    //     query: search,
    //     resultsCount: totalModels,
    //     category,
    //     sort
    //   });
    // }

    // Трекінг продуктивності (тимчасово відключено)
    // await context.trackPerformance('models_fetch_time', Date.now() - Date.now());

    // Повертаємо дані в форматі, який очікує клієнт
    return NextResponse.json({
      success: true,
      data: {
        models: paginatedModels,
        pagination: {
          total: totalModels,
          page: Math.floor(offset / limit) + 1,
          limit,
          pages: Math.ceil(totalModels / limit)
        }
      }
    });
  } catch (error) {
    console.error('❌ Помилка отримання моделей:', error);

    // Трекінг помилки (тимчасово відключено)
    // await context.trackError(error as Error, 'scraped_models_api');

    return NextResponse.json({
      success: false,
      error: 'Помилка отримання моделей',
      data: {
        models: [],
        pagination: {
          total: 0,
          page: 1,
          limit: 20,
          pages: 0
        }
      }
    }, { status: 500 });
  }
}
