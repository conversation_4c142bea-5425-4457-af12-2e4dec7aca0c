/**
 * API для Cloudflare Bindings MCP
 * Прямий доступ до Cloudflare сервісів через bindings
 */

import { NextRequest, NextResponse } from 'next/server';
import { CloudflareBindingsMCPClient, CloudflareBindings } from '../../../../lib/cloudflare/bindings-mcp-client';
import { CloudflareEnv } from '../../../../lib/cloudflare/types';

interface CloudflareRequest extends NextRequest {
  env?: CloudflareEnv;
}

// Глобальний екземпляр bindings client
let bindingsClientInstance: CloudflareBindingsMCPClient | null = null;

function getBindingsClient(env: CloudflareEnv): CloudflareBindingsMCPClient {
  if (!bindingsClientInstance) {
    bindingsClientInstance = new CloudflareBindingsMCPClient();
    bindingsClientInstance.initialize(env as CloudflareBindings);
  }
  return bindingsClientInstance;
}

/**
 * GET /api/cloudflare/bindings
 * Отримання даних з Cloudflare сервісів
 */
export async function GET(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const service = searchParams.get('service'); // r2, d1, kv, analytics
    const action = searchParams.get('action'); // get, list, query, etc.
    const key = searchParams.get('key');
    const prefix = searchParams.get('prefix');
    const limit = searchParams.get('limit');

    const env = request.env;
    if (!env) {
      return NextResponse.json({
        error: 'Cloudflare environment не налаштований'
      }, { status: 500 });
    }

    const bindingsClient = getBindingsClient(env);

    switch (service) {
      case 'r2':
        switch (action) {
          case 'get':
            if (!key) {
              return NextResponse.json({
                error: 'Параметр key є обов\'язковим для R2 get'
              }, { status: 400 });
            }
            
            const r2GetResult = await bindingsClient.r2Get(key);
            return NextResponse.json({
              success: r2GetResult.success,
              data: r2GetResult.success ? {
                exists: true,
                metadata: r2GetResult.metadata
              } : null,
              error: r2GetResult.error
            });

          case 'list':
            const r2ListResult = await bindingsClient.r2List(
              prefix || undefined,
              limit ? parseInt(limit) : undefined
            );
            return NextResponse.json({
              success: r2ListResult.success,
              data: r2ListResult.data,
              error: r2ListResult.error
            });

          default:
            return NextResponse.json({
              error: `Непідтримувана дія для R2: ${action}`
            }, { status: 400 });
        }

      case 'kv':
        switch (action) {
          case 'get':
            if (!key) {
              return NextResponse.json({
                error: 'Параметр key є обов\'язковим для KV get'
              }, { status: 400 });
            }
            
            const type = searchParams.get('type') as 'text' | 'json' | 'arrayBuffer' || 'text';
            const kvGetResult = await bindingsClient.kvGet(key, type);
            return NextResponse.json({
              success: kvGetResult.success,
              data: kvGetResult.data,
              metadata: kvGetResult.metadata,
              error: kvGetResult.error
            });

          default:
            return NextResponse.json({
              error: `Непідтримувана дія для KV: ${action}`
            }, { status: 400 });
        }

      case 'd1':
        switch (action) {
          case 'query':
            const sql = searchParams.get('sql');
            if (!sql) {
              return NextResponse.json({
                error: 'Параметр sql є обов\'язковим для D1 query'
              }, { status: 400 });
            }
            
            const d1Result = await bindingsClient.d1Query(sql);
            return NextResponse.json({
              success: d1Result.success,
              data: d1Result.data,
              meta: d1Result.meta,
              error: d1Result.error
            });

          default:
            return NextResponse.json({
              error: `Непідтримувана дія для D1: ${action}`
            }, { status: 400 });
        }

      case 'health':
        const healthCheck = await bindingsClient.healthCheck();
        return NextResponse.json({
          success: true,
          data: healthCheck
        });

      case 'usage':
        const usageStats = await bindingsClient.getUsageStats();
        return NextResponse.json({
          success: true,
          data: usageStats
        });

      default:
        return NextResponse.json({
          error: `Непідтримуваний сервіс: ${service}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Помилка GET /api/cloudflare/bindings:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * POST /api/cloudflare/bindings
 * Запис даних до Cloudflare сервісів
 */
export async function POST(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const service = searchParams.get('service');
    const action = searchParams.get('action');
    const body = await request.json();

    const env = request.env;
    if (!env) {
      return NextResponse.json({
        error: 'Cloudflare environment не налаштований'
      }, { status: 500 });
    }

    const bindingsClient = getBindingsClient(env);

    switch (service) {
      case 'r2':
        switch (action) {
          case 'put':
            const { key, data, metadata } = body;
            if (!key || !data) {
              return NextResponse.json({
                error: 'Поля key та data є обов\'язковими для R2 put'
              }, { status: 400 });
            }

            // Конвертуємо data в ArrayBuffer якщо це строка
            let arrayBuffer: ArrayBuffer;
            if (typeof data === 'string') {
              arrayBuffer = new TextEncoder().encode(data).buffer;
            } else {
              arrayBuffer = data;
            }

            const r2PutResult = await bindingsClient.r2Put(key, arrayBuffer, metadata);
            return NextResponse.json({
              success: r2PutResult.success,
              metadata: r2PutResult.metadata,
              error: r2PutResult.error
            });

          default:
            return NextResponse.json({
              error: `Непідтримувана дія для R2: ${action}`
            }, { status: 400 });
        }

      case 'kv':
        switch (action) {
          case 'put':
            const { key, value, options } = body;
            if (!key || value === undefined) {
              return NextResponse.json({
                error: 'Поля key та value є обов\'язковими для KV put'
              }, { status: 400 });
            }

            const kvPutResult = await bindingsClient.kvPut(key, value, options);
            return NextResponse.json({
              success: kvPutResult.success,
              metadata: kvPutResult.metadata,
              error: kvPutResult.error
            });

          default:
            return NextResponse.json({
              error: `Непідтримувана дія для KV: ${action}`
            }, { status: 400 });
        }

      case 'd1':
        switch (action) {
          case 'execute':
            const { sql, params } = body;
            if (!sql) {
              return NextResponse.json({
                error: 'Поле sql є обов\'язковим для D1 execute'
              }, { status: 400 });
            }

            const d1Result = await bindingsClient.d1Execute(sql, params);
            return NextResponse.json({
              success: d1Result.success,
              data: d1Result.data,
              meta: d1Result.meta,
              error: d1Result.error
            });

          default:
            return NextResponse.json({
              error: `Непідтримувана дія для D1: ${action}`
            }, { status: 400 });
        }

      case 'analytics':
        switch (action) {
          case 'write':
            const { dataPoint } = body;
            if (!dataPoint) {
              return NextResponse.json({
                error: 'Поле dataPoint є обов\'язковим для Analytics write'
              }, { status: 400 });
            }

            const analyticsResult = await bindingsClient.analyticsWrite(dataPoint);
            return NextResponse.json({
              success: analyticsResult.success,
              metadata: analyticsResult.metadata,
              error: analyticsResult.error
            });

          default:
            return NextResponse.json({
              error: `Непідтримувана дія для Analytics: ${action}`
            }, { status: 400 });
        }

      default:
        return NextResponse.json({
          error: `Непідтримуваний сервіс: ${service}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Помилка POST /api/cloudflare/bindings:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/cloudflare/bindings
 * Видалення даних з Cloudflare сервісів
 */
export async function DELETE(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const service = searchParams.get('service');
    const key = searchParams.get('key');

    const env = request.env;
    if (!env) {
      return NextResponse.json({
        error: 'Cloudflare environment не налаштований'
      }, { status: 500 });
    }

    const bindingsClient = getBindingsClient(env);

    switch (service) {
      case 'r2':
        if (!key) {
          return NextResponse.json({
            error: 'Параметр key є обов\'язковим для R2 delete'
          }, { status: 400 });
        }

        const r2DeleteResult = await bindingsClient.r2Delete(key);
        return NextResponse.json({
          success: r2DeleteResult.success,
          error: r2DeleteResult.error
        });

      case 'kv':
        if (!key) {
          return NextResponse.json({
            error: 'Параметр key є обов\'язковим для KV delete'
          }, { status: 400 });
        }

        const kvDeleteResult = await bindingsClient.kvDelete(key);
        return NextResponse.json({
          success: kvDeleteResult.success,
          error: kvDeleteResult.error
        });

      default:
        return NextResponse.json({
          error: `Непідтримуваний сервіс для видалення: ${service}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Помилка DELETE /api/cloudflare/bindings:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}
