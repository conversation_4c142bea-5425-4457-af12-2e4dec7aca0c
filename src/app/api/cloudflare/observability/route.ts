/**
 * API для Cloudflare Observability
 * Інтеграція з cloudflare-observability MCP та cloudflare-bindings MCP
 */

import { NextRequest, NextResponse } from 'next/server';
import { CloudflareObservability } from '../../../../lib/cloudflare/observability';
import { CloudflareEnv } from '../../../../lib/cloudflare/types';

// Тимчасовий інтерфейс для CloudflareBindings
interface CloudflareBindings {
  ANALYTICS?: any;
  CACHE_KV?: any;
  R2_BUCKET?: any;
  DB?: any;
  DOWNLOAD_MANAGER?: any;
  SCRAPING_COORDINATOR?: any;
}

// Тимчасовий клас для CloudflareBindingsMCPClient
class CloudflareBindingsMCPClient {
  private bindings?: CloudflareBindings;

  initialize(bindings: CloudflareBindings) {
    this.bindings = bindings;
  }

  async healthCheck() {
    return {
      analytics: !!this.bindings?.ANALYTICS,
      kv: !!this.bindings?.CACHE_KV,
      r2: !!this.bindings?.R2_BUCKET,
      d1: !!this.bindings?.DB,
      durableObjects: !!this.bindings?.DOWNLOAD_MANAGER
    };
  }
}

interface CloudflareRequest extends NextRequest {
  env?: CloudflareEnv;
}

// Глобальні екземпляри (в реальному додатку це буде в контексті worker)
let observabilityInstance: CloudflareObservability | null = null;
let bindingsClientInstance: CloudflareBindingsMCPClient | null = null;

function getObservability(env: CloudflareEnv): CloudflareObservability {
  if (!observabilityInstance) {
    observabilityInstance = new CloudflareObservability(env);
  }
  return observabilityInstance;
}

function getBindingsClient(env: CloudflareEnv): CloudflareBindingsMCPClient {
  if (!bindingsClientInstance) {
    bindingsClientInstance = new CloudflareBindingsMCPClient();
    bindingsClientInstance.initialize(env as CloudflareBindings);
  }
  return bindingsClientInstance;
}

/**
 * GET /api/cloudflare/observability
 * Отримання метрик, логів або статистики
 */
export async function GET(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'metrics';
    const startTime = searchParams.get('startTime');
    const endTime = searchParams.get('endTime');
    const filter = searchParams.get('filter');

    const env = request.env;
    if (!env) {
      return NextResponse.json({
        error: 'Cloudflare environment не налаштований'
      }, { status: 500 });
    }

    const observability = getObservability(env);

    switch (type) {
      case 'metrics':
        const metrics = observability.getMetrics(
          startTime ? parseInt(startTime) : undefined,
          endTime ? parseInt(endTime) : undefined,
          filter || undefined
        );
        return NextResponse.json({
          success: true,
          data: metrics,
          count: metrics.length
        });

      case 'logs':
        const levelFilter = filter as any;
        const logs = observability.getLogs(
          startTime ? parseInt(startTime) : undefined,
          endTime ? parseInt(endTime) : undefined,
          levelFilter
        );
        return NextResponse.json({
          success: true,
          data: logs,
          count: logs.length
        });

      case 'export':
        const exportData = observability.exportData();
        return NextResponse.json({
          success: true,
          data: exportData
        });

      case 'health':
        const bindingsClient = getBindingsClient(env);
        const healthCheck = await bindingsClient.healthCheck();

        return NextResponse.json({
          success: true,
          data: {
            status: Object.values(healthCheck).every(Boolean) ? 'healthy' : 'degraded',
            timestamp: Date.now(),
            services: {
              analytics: healthCheck.analytics,
              kv: healthCheck.kv,
              r2: healthCheck.r2,
              d1: healthCheck.d1,
              durableObjects: healthCheck.durableObjects
            },
            bindings: {
              analytics: !!env.ANALYTICS,
              kv: !!env.CACHE_KV,
              r2: !!env.R2_BUCKET,
              d1: !!env.DB,
              modelDownloadManager: !!env.DOWNLOAD_MANAGER,
              scrapingCoordinator: !!(env as any).SCRAPING_COORDINATOR
            }
          }
        });

      default:
        return NextResponse.json({
          error: `Непідтримуваний тип: ${type}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Помилка GET /api/cloudflare/observability:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * POST /api/cloudflare/observability
 * Запис метрик, логів або початок трейсів
 */
export async function POST(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'metric';
    const body = await request.json() as any;

    const env = request.env;
    if (!env) {
      return NextResponse.json({
        error: 'Cloudflare environment не налаштований'
      }, { status: 500 });
    }

    const observability = getObservability(env);

    switch (type) {
      case 'metric':
        const { name, value, labels, unit } = body;
        if (!name || value === undefined) {
          return NextResponse.json({
            error: 'Поля name та value є обов\'язковими'
          }, { status: 400 });
        }

        await observability.recordMetric(name, value, labels, unit);
        return NextResponse.json({
          success: true,
          message: 'Метрика записана'
        });

      case 'log':
        const { level, message, context, traceId, spanId } = body;
        if (!level || !message) {
          return NextResponse.json({
            error: 'Поля level та message є обов\'язковими'
          }, { status: 400 });
        }

        await observability.log(level, message, context, traceId, spanId);
        return NextResponse.json({
          success: true,
          message: 'Лог записаний'
        });

      case 'span':
        const { operationName, parentSpanId, tags } = body;
        if (!operationName) {
          return NextResponse.json({
            error: 'Поле operationName є обов\'язковим'
          }, { status: 400 });
        }

        const span = observability.startSpan(operationName, parentSpanId, tags);
        return NextResponse.json({
          success: true,
          data: {
            spanId: span.spanId,
            traceId: span.traceId
          }
        });

      case 'error':
        const { error: errorData, context: errorContext, spanId: errorSpanId } = body;
        if (!errorData) {
          return NextResponse.json({
            error: 'Поле error є обов\'язковим'
          }, { status: 400 });
        }

        const error = new Error(errorData.message || 'Невідома помилка');
        error.name = errorData.name || 'Error';
        error.stack = errorData.stack;

        await observability.recordError(error, errorContext, errorSpanId);
        return NextResponse.json({
          success: true,
          message: 'Помилка записана'
        });

      case 'scraping':
        const { platform, success, duration, modelsFound } = body;
        if (!platform || success === undefined || !duration) {
          return NextResponse.json({
            error: 'Поля platform, success та duration є обов\'язковими'
          }, { status: 400 });
        }

        await observability.recordScrapingMetrics(platform, success, duration, modelsFound || 0);
        return NextResponse.json({
          success: true,
          message: 'Метрики скрапінгу записані'
        });

      case 'download':
        const { platform: dlPlatform, success: dlSuccess, fileSize, duration: dlDuration } = body;
        if (!dlPlatform || dlSuccess === undefined || !dlDuration) {
          return NextResponse.json({
            error: 'Поля platform, success та duration є обов\'язковими'
          }, { status: 400 });
        }

        await observability.recordDownloadMetrics(dlPlatform, dlSuccess, fileSize || 0, dlDuration);
        return NextResponse.json({
          success: true,
          message: 'Метрики завантаження записані'
        });

      case 'api':
        const { endpoint, method, statusCode, duration: apiDuration } = body;
        if (!endpoint || !method || !statusCode || !apiDuration) {
          return NextResponse.json({
            error: 'Поля endpoint, method, statusCode та duration є обов\'язковими'
          }, { status: 400 });
        }

        await observability.recordAPIMetrics(endpoint, method, statusCode, apiDuration);
        return NextResponse.json({
          success: true,
          message: 'Метрики API записані'
        });

      default:
        return NextResponse.json({
          error: `Непідтримуваний тип: ${type}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Помилка POST /api/cloudflare/observability:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * PATCH /api/cloudflare/observability
 * Завершення span або оновлення конфігурації
 */
export async function PATCH(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const body = await request.json() as any;

    const env = request.env;
    if (!env) {
      return NextResponse.json({
        error: 'Cloudflare environment не налаштований'
      }, { status: 500 });
    }

    const observability = getObservability(env);

    switch (action) {
      case 'finish-span':
        const { spanId, status } = body;
        if (!spanId) {
          return NextResponse.json({
            error: 'Поле spanId є обов\'язковим'
          }, { status: 400 });
        }

        observability.finishSpan(spanId, status);
        return NextResponse.json({
          success: true,
          message: 'Span завершено'
        });

      case 'add-span-log':
        const { spanId: logSpanId, level, message, context } = body;
        if (!logSpanId || !level || !message) {
          return NextResponse.json({
            error: 'Поля spanId, level та message є обов\'язковими'
          }, { status: 400 });
        }

        observability.addSpanLog(logSpanId, level, message, context);
        return NextResponse.json({
          success: true,
          message: 'Лог додано до span'
        });

      case 'cleanup':
        const { maxAge } = body;
        observability.cleanup(maxAge);
        return NextResponse.json({
          success: true,
          message: 'Очищення виконано'
        });

      default:
        return NextResponse.json({
          error: `Непідтримувана дія: ${action}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Помилка PATCH /api/cloudflare/observability:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/cloudflare/observability
 * Очистка даних
 */
export async function DELETE(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'all';

    const env = request.env;
    if (!env) {
      return NextResponse.json({
        error: 'Cloudflare environment не налаштований'
      }, { status: 500 });
    }

    const observability = getObservability(env);

    switch (type) {
      case 'all':
        observability.cleanup(0); // Очистити все
        return NextResponse.json({
          success: true,
          message: 'Всі дані очищено'
        });

      case 'old':
        observability.cleanup(); // Очистити старі дані (24 години)
        return NextResponse.json({
          success: true,
          message: 'Старі дані очищено'
        });

      default:
        return NextResponse.json({
          error: `Непідтримуваний тип: ${type}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Помилка DELETE /api/cloudflare/observability:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}
