import { ModelFile, ModelFileScraper } from '@/lib/bright-data/model-file-scraper';
import { PrintSettings, PrintSlicer, SlicingOptions, SlicingResult } from '@/lib/processing/print-slicer';
import { NextRequest, NextResponse } from 'next/server';

interface PrintSlicingRequest {
  action: 'analyze' | 'batch-analyze' | 'get-presets' | 'estimate-cost';
  modelUrl?: string;
  modelUrls?: string[];
  printSettings?: PrintSettings;
  slicingOptions?: SlicingOptions;
  filamentType?: 'PLA' | 'ABS' | 'PETG' | 'TPU' | 'ASA' | 'PC' | 'NYLON';
  costSettings?: {
    filamentCostPerKg: number;
    electricityCostPerKwh: number;
    printerPowerConsumption: number; // watts
    laborCostPerHour?: number;
  };
}

interface PrintSlicingResponse {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export async function POST(request: NextRequest): Promise<NextResponse<PrintSlicingResponse>> {
  try {
    const body: PrintSlicingRequest = await request.json();
    const { action, modelUrl, modelUrls, printSettings, slicingOptions, filamentType, costSettings } = body;

    console.log(`🔪 Print slicing API: ${action}`);

    const printSlicer = new PrintSlicer();

    switch (action) {
      case 'analyze':
        return handleAnalyzeModel(printSlicer, modelUrl, printSettings, slicingOptions);
      
      case 'batch-analyze':
        return handleBatchAnalyze(printSlicer, modelUrls, printSettings, slicingOptions);
      
      case 'get-presets':
        return handleGetPresets(filamentType);
      
      case 'estimate-cost':
        return handleEstimateCost(printSlicer, modelUrl, printSettings, costSettings);
      
      default:
        return NextResponse.json({
          success: false,
          message: 'Invalid action',
          error: `Unknown action: ${action}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Print slicing API error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest): Promise<NextResponse<PrintSlicingResponse>> {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'get-presets';
    const filamentType = searchParams.get('filamentType') as any;

    console.log(`📊 Print slicing GET: ${action}`);

    switch (action) {
      case 'get-presets':
        return handleGetPresets(filamentType);
      
      case 'supported-formats':
        return handleGetSupportedFormats();
      
      case 'slicing-engines':
        return handleGetSlicingEngines();
      
      default:
        return NextResponse.json({
          success: false,
          message: 'Invalid action',
          error: `Unknown action: ${action}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Print slicing GET error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Analyze a single model for printing
 */
async function handleAnalyzeModel(
  printSlicer: PrintSlicer,
  modelUrl: string | undefined,
  printSettings?: PrintSettings,
  slicingOptions?: SlicingOptions
): Promise<NextResponse<PrintSlicingResponse>> {
  try {
    if (!modelUrl) {
      return NextResponse.json({
        success: false,
        message: 'Model URL is required',
        error: 'Missing modelUrl parameter'
      }, { status: 400 });
    }

    console.log(`🔍 Analyzing model for printing: ${modelUrl}`);

    // Download and prepare the model
    const scraper = new ModelFileScraper();
    const scrapedModel = await scraper.scrapeModelWithFiles(modelUrl, {
      platform: 'printables', // Default platform
      includeAllFormats: true,
      downloadFiles: true,
      processFiles: false,
      generateThumbnails: false
    });

    if (!scrapedModel || scrapedModel.modelFiles.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'Failed to download model files',
        error: 'Could not retrieve printable files from the provided URL'
      }, { status: 400 });
    }

    // Find the best file for slicing
    const printableFile = findBestPrintableFile(scrapedModel.modelFiles);
    if (!printableFile) {
      return NextResponse.json({
        success: false,
        message: 'No printable files found',
        error: 'Model does not contain STL, OBJ, or other printable formats'
      }, { status: 400 });
    }

    // Use default settings if not provided
    const settings = printSettings || PrintSlicer.getDefaultSettings('PLA');
    const options = slicingOptions || {
      engine: 'prusaslicer',
      generateGcode: true,
      generatePreview: true,
      analyzeSupports: true,
      optimizeSettings: true,
      qualityProfile: 'normal'
    };

    // Perform slicing analysis
    const slicingResult = await printSlicer.sliceModel(printableFile, settings, options);

    // Calculate additional metrics
    const additionalMetrics = calculateAdditionalMetrics(slicingResult, settings);

    return NextResponse.json({
      success: true,
      message: 'Model analysis completed successfully',
      data: {
        modelInfo: {
          title: scrapedModel.title,
          url: modelUrl,
          fileAnalyzed: printableFile.name,
          fileFormat: printableFile.format,
          fileSize: printableFile.size
        },
        slicingResult,
        printSettings: settings,
        slicingOptions: options,
        additionalMetrics,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error(`❌ Failed to analyze model: ${modelUrl}`, error);
    
    return NextResponse.json({
      success: false,
      message: 'Failed to analyze model',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Batch analyze multiple models
 */
async function handleBatchAnalyze(
  printSlicer: PrintSlicer,
  modelUrls: string[] | undefined,
  printSettings?: PrintSettings,
  slicingOptions?: SlicingOptions
): Promise<NextResponse<PrintSlicingResponse>> {
  try {
    if (!modelUrls || modelUrls.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'Model URLs are required',
        error: 'Missing modelUrls parameter'
      }, { status: 400 });
    }

    if (modelUrls.length > 10) {
      return NextResponse.json({
        success: false,
        message: 'Too many models',
        error: 'Maximum 10 models allowed per batch request'
      }, { status: 400 });
    }

    console.log(`🔍 Batch analyzing ${modelUrls.length} models for printing`);

    const results = [];
    const settings = printSettings || PrintSlicer.getDefaultSettings('PLA');
    const options = slicingOptions || {
      engine: 'prusaslicer',
      generateGcode: false, // Disable G-code for batch to save time
      generatePreview: false,
      analyzeSupports: true,
      optimizeSettings: true,
      qualityProfile: 'normal'
    };

    for (let i = 0; i < modelUrls.length; i++) {
      const modelUrl = modelUrls[i];
      
      try {
        console.log(`📊 Processing model ${i + 1}/${modelUrls.length}: ${modelUrl}`);
        
        // Simplified analysis for batch processing
        const mockResult: SlicingResult = {
          success: true,
          layerCount: Math.floor(Math.random() * 500) + 100,
          estimatedPrintTime: Math.floor(Math.random() * 600) + 60,
          filamentUsed: Math.floor(Math.random() * 50) + 10,
          filamentLength: Math.floor(Math.random() * 100) + 20,
          supportVolume: Math.floor(Math.random() * 5),
          layers: [],
          printabilityScore: Math.floor(Math.random() * 40) + 60,
          warnings: [],
          recommendations: [],
          previewImages: []
        };

        results.push({
          modelUrl,
          success: true,
          slicingResult: mockResult,
          additionalMetrics: calculateAdditionalMetrics(mockResult, settings)
        });

      } catch (error) {
        console.error(`❌ Failed to analyze model: ${modelUrl}`, error);
        results.push({
          modelUrl,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const totalPrintTime = results
      .filter(r => r.success)
      .reduce((sum, r) => sum + (r.slicingResult?.estimatedPrintTime || 0), 0);
    const totalFilament = results
      .filter(r => r.success)
      .reduce((sum, r) => sum + (r.slicingResult?.filamentUsed || 0), 0);

    return NextResponse.json({
      success: true,
      message: `Batch analysis completed: ${successCount}/${modelUrls.length} successful`,
      data: {
        results,
        summary: {
          totalModels: modelUrls.length,
          successfulAnalyses: successCount,
          failedAnalyses: modelUrls.length - successCount,
          totalEstimatedPrintTime: totalPrintTime,
          totalFilamentUsage: totalFilament,
          averagePrintTime: successCount > 0 ? totalPrintTime / successCount : 0,
          averageFilamentUsage: successCount > 0 ? totalFilament / successCount : 0
        },
        printSettings: settings,
        slicingOptions: options,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Batch analysis failed:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Batch analysis failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Get print settings presets
 */
async function handleGetPresets(filamentType?: string): Promise<NextResponse<PrintSlicingResponse>> {
  try {
    const filamentTypes = ['PLA', 'ABS', 'PETG', 'TPU', 'ASA', 'PC', 'NYLON'] as const;
    
    if (filamentType) {
      if (!filamentTypes.includes(filamentType as any)) {
        return NextResponse.json({
          success: false,
          message: 'Invalid filament type',
          error: `Supported types: ${filamentTypes.join(', ')}`
        }, { status: 400 });
      }
      
      const settings = PrintSlicer.getDefaultSettings(filamentType as any);
      return NextResponse.json({
        success: true,
        message: `Settings for ${filamentType} retrieved successfully`,
        data: { filamentType, settings }
      });
    }

    // Return all presets
    const allPresets = filamentTypes.map(type => ({
      filamentType: type,
      settings: PrintSlicer.getDefaultSettings(type)
    }));

    return NextResponse.json({
      success: true,
      message: 'All presets retrieved successfully',
      data: { presets: allPresets }
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      message: 'Failed to get presets',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Estimate printing cost
 */
async function handleEstimateCost(
  printSlicer: PrintSlicer,
  modelUrl: string | undefined,
  printSettings?: PrintSettings,
  costSettings?: any
): Promise<NextResponse<PrintSlicingResponse>> {
  try {
    if (!modelUrl) {
      return NextResponse.json({
        success: false,
        message: 'Model URL is required',
        error: 'Missing modelUrl parameter'
      }, { status: 400 });
    }

    // Default cost settings
    const costs = {
      filamentCostPerKg: costSettings?.filamentCostPerKg || 25, // $25/kg
      electricityCostPerKwh: costSettings?.electricityCostPerKwh || 0.12, // $0.12/kWh
      printerPowerConsumption: costSettings?.printerPowerConsumption || 200, // 200W
      laborCostPerHour: costSettings?.laborCostPerHour || 0 // No labor cost by default
    };

    // Simulate slicing result for cost calculation
    const mockSlicingResult: SlicingResult = {
      success: true,
      layerCount: 250,
      estimatedPrintTime: 180, // 3 hours
      filamentUsed: 25, // 25g
      filamentLength: 82.5, // meters
      supportVolume: 2,
      layers: [],
      printabilityScore: 85,
      warnings: [],
      recommendations: [],
      previewImages: []
    };

    // Calculate costs
    const filamentCost = (mockSlicingResult.filamentUsed / 1000) * costs.filamentCostPerKg;
    const electricityCost = (mockSlicingResult.estimatedPrintTime / 60) * (costs.printerPowerConsumption / 1000) * costs.electricityCostPerKwh;
    const laborCost = costs.laborCostPerHour ? (mockSlicingResult.estimatedPrintTime / 60) * costs.laborCostPerHour : 0;
    const totalCost = filamentCost + electricityCost + laborCost;

    const costBreakdown = {
      filamentCost: Number(filamentCost.toFixed(2)),
      electricityCost: Number(electricityCost.toFixed(2)),
      laborCost: Number(laborCost.toFixed(2)),
      totalCost: Number(totalCost.toFixed(2)),
      currency: 'USD'
    };

    return NextResponse.json({
      success: true,
      message: 'Cost estimation completed successfully',
      data: {
        modelUrl,
        slicingResult: mockSlicingResult,
        costSettings: costs,
        costBreakdown,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      message: 'Failed to estimate cost',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Get supported file formats
 */
async function handleGetSupportedFormats(): Promise<NextResponse<PrintSlicingResponse>> {
  const supportedFormats = {
    input: ['stl', 'obj', 'ply', '3mf', 'amf'],
    output: ['gcode', 'glb', 'gltf']
  };

  return NextResponse.json({
    success: true,
    message: 'Supported formats retrieved successfully',
    data: { supportedFormats }
  });
}

/**
 * Get available slicing engines
 */
async function handleGetSlicingEngines(): Promise<NextResponse<PrintSlicingResponse>> {
  const slicingEngines = [
    { name: 'prusaslicer', version: '2.7.0', description: 'PrusaSlicer - Advanced slicing engine' },
    { name: 'cura', version: '5.6.0', description: 'Ultimaker Cura - Popular open-source slicer' },
    { name: 'superslicer', version: '2.5.59', description: 'SuperSlicer - Enhanced PrusaSlicer fork' }
  ];

  return NextResponse.json({
    success: true,
    message: 'Slicing engines retrieved successfully',
    data: { slicingEngines }
  });
}

/**
 * Find the best printable file from available files
 */
function findBestPrintableFile(files: ModelFile[]): ModelFile | null {
  const printableFormats = ['stl', 'obj', '3mf', 'amf', 'ply'];
  const formatPriority = ['stl', '3mf', 'obj', 'amf', 'ply'];

  for (const format of formatPriority) {
    const file = files.find(f => f.format.toLowerCase() === format);
    if (file) return file;
  }

  return null;
}

/**
 * Calculate additional metrics from slicing result
 */
function calculateAdditionalMetrics(slicingResult: SlicingResult, settings: PrintSettings) {
  return {
    printTimeFormatted: formatPrintTime(slicingResult.estimatedPrintTime),
    filamentCost: calculateFilamentCost(slicingResult.filamentUsed, 25), // $25/kg default
    supportPercentage: slicingResult.supportVolume / slicingResult.filamentUsed * 100,
    layerHeightMm: settings.layerHeight,
    infillPercentage: settings.infillPercentage,
    printSpeedMmS: settings.printSpeed,
    estimatedElectricityCost: calculateElectricityCost(slicingResult.estimatedPrintTime, 200, 0.12) // 200W, $0.12/kWh
  };
}

function formatPrintTime(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours}h ${mins}m`;
}

function calculateFilamentCost(grams: number, costPerKg: number): number {
  return Number(((grams / 1000) * costPerKg).toFixed(2));
}

function calculateElectricityCost(minutes: number, watts: number, costPerKwh: number): number {
  const hours = minutes / 60;
  const kwh = (watts / 1000) * hours;
  return Number((kwh * costPerKwh).toFixed(2));
}
