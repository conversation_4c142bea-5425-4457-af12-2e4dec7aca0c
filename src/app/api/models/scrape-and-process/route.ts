import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { ModelFileScraper, ModelFileScrapingOptions } from '@/lib/bright-data/model-file-scraper';
import { R2ModelStorage } from '@/lib/storage/r2-model-storage';
import { ModelSource } from '@/types/models';

interface ScrapeRequest {
  modelUrl: string;
  platform: ModelSource;
  includeAllFormats?: boolean;
  downloadFiles?: boolean;
  processFiles?: boolean;
  generateThumbnails?: boolean;
}

interface ScrapeResponse {
  success: boolean;
  modelId?: string;
  title?: string;
  files?: any[];
  previewUrl?: string;
  error?: string;
  details?: string;
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json() as ScrapeRequest;
    const { modelUrl, platform, includeAllFormats = true, downloadFiles = true, processFiles = true, generateThumbnails = true } = body;

    if (!modelUrl || !platform) {
      return NextResponse.json(
        { success: false, error: 'Model URL and platform are required' },
        { status: 400 }
      );
    }

    console.log(`🔍 Starting model scraping: ${modelUrl} (${platform})`);

    // Initialize scraper and storage
    const scraper = new ModelFileScraper();
    const storage = new R2ModelStorage();

    // Scraping options
    const options: ModelFileScrapingOptions = {
      platform,
      includeAllFormats,
      downloadFiles,
      processFiles,
      generateThumbnails
    };

    // Scrape the model with files
    const scrapedModel = await scraper.scrapeModelWithFiles(modelUrl, options);

    if (!scrapedModel) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to scrape model',
          details: 'Could not extract model data from the provided URL'
        },
        { status: 400 }
      );
    }

    console.log(`✅ Successfully scraped model: ${scrapedModel.title}`);
    console.log(`📁 Found ${scrapedModel.modelFiles.length} files`);

    // Download and store files if requested
    let storedFiles: any[] = [];
    if (downloadFiles && scrapedModel.originalFiles.length > 0) {
      console.log(`⬇️ Downloading ${scrapedModel.originalFiles.length} files...`);
      
      for (const file of scrapedModel.originalFiles) {
        try {
          // Download the file
          const response = await fetch(file.url);
          if (!response.ok) {
            console.warn(`⚠️ Failed to download ${file.name}: ${response.status}`);
            continue;
          }

          const buffer = await response.arrayBuffer();
          
          // Store in R2
          const storedFile = await storage.uploadModelFile(file, buffer, {
            folder: `models/${platform}`,
            metadata: {
              originalUrl: modelUrl,
              platform,
              scrapedAt: new Date().toISOString(),
              userId: session.user.id
            }
          });

          storedFiles.push({
            name: file.name,
            format: file.format,
            size: file.size,
            url: storedFile.url,
            publicUrl: storedFile.publicUrl
          });

          console.log(`✅ Stored file: ${file.name}`);

        } catch (error) {
          console.error(`❌ Error processing file ${file.name}:`, error);
        }
      }
    }

    // Generate model ID
    const modelId = `scraped_${platform}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

    // Prepare response
    const response: ScrapeResponse = {
      success: true,
      modelId,
      title: scrapedModel.title,
      files: storedFiles,
      previewUrl: scrapedModel.previewFile?.url || storedFiles[0]?.url
    };

    // Store model metadata in database (if available)
    try {
      await storeModelMetadata(modelId, scrapedModel, storedFiles, session.user.id);
    } catch (error) {
      console.warn(`⚠️ Failed to store model metadata:`, error);
      // Continue without failing the request
    }

    console.log(`🎉 Model scraping completed successfully: ${modelId}`);

    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ Error in scrape-and-process:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to scrape and process model',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const modelId = searchParams.get('modelId');

    if (!modelId) {
      return NextResponse.json(
        { success: false, error: 'Model ID is required' },
        { status: 400 }
      );
    }

    // Get model metadata from database
    const modelData = await getModelMetadata(modelId);

    if (!modelData) {
      return NextResponse.json(
        { success: false, error: 'Model not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      model: modelData
    });

  } catch (error) {
    console.error('❌ Error getting model data:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to get model data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Store model metadata in database
 */
async function storeModelMetadata(
  modelId: string,
  scrapedModel: any,
  storedFiles: any[],
  userId: string
): Promise<void> {
  try {
    // This would store the model metadata in your database
    // For now, just log the data
    console.log(`💾 Storing model metadata for ${modelId}:`, {
      id: modelId,
      title: scrapedModel.title,
      description: scrapedModel.description,
      platform: scrapedModel.platform,
      originalUrl: scrapedModel.originalUrl,
      files: storedFiles,
      userId,
      createdAt: new Date().toISOString()
    });

    // Example database storage (uncomment when database is available):
    /*
    const { execute } = await import('@/lib/db');
    
    await execute(
      `INSERT INTO scraped_models (
        id, title, description, platform, original_url, 
        files_data, user_id, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
      [
        modelId,
        scrapedModel.title,
        scrapedModel.description,
        scrapedModel.platform,
        scrapedModel.originalUrl,
        JSON.stringify(storedFiles),
        userId
      ]
    );
    */

  } catch (error) {
    console.error('❌ Error storing model metadata:', error);
    throw error;
  }
}

/**
 * Get model metadata from database
 */
async function getModelMetadata(modelId: string): Promise<any | null> {
  try {
    // This would retrieve the model metadata from your database
    // For now, return null
    console.log(`🔍 Getting model metadata for ${modelId}`);

    // Example database retrieval (uncomment when database is available):
    /*
    const { queryOne } = await import('@/lib/db');
    
    const model = await queryOne(
      'SELECT * FROM scraped_models WHERE id = ?',
      [modelId]
    );

    if (model) {
      return {
        ...model,
        files: JSON.parse(model.files_data || '[]')
      };
    }
    */

    return null;

  } catch (error) {
    console.error('❌ Error getting model metadata:', error);
    return null;
  }
}

/**
 * Validate model URL format
 */
function validateModelUrl(url: string, platform: ModelSource): boolean {
  const patterns = {
    printables: /^https:\/\/www\.printables\.com\/model\/\d+/,
    makerworld: /^https:\/\/makerworld\.com\/models\/\d+/,
    thangs: /^https:\/\/thangs\.com\/designer\/[^\/]+\/3d-model\/\d+/,
    thingiverse: /^https:\/\/www\.thingiverse\.com\/thing:\d+/,
    myminifactory: /^https:\/\/www\.myminifactory\.com\/object\/3d-print\/\d+/
  };

  const pattern = patterns[platform];
  return pattern ? pattern.test(url) : false;
}

/**
 * Estimate processing time based on file count and sizes
 */
function estimateProcessingTime(fileCount: number, totalSize: number): number {
  // Base time: 30 seconds
  // + 10 seconds per file
  // + 1 second per MB
  const baseTime = 30;
  const fileTime = fileCount * 10;
  const sizeTime = Math.floor(totalSize / (1024 * 1024));
  
  return baseTime + fileTime + sizeTime;
}
