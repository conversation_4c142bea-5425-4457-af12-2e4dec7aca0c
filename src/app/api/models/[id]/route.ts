import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { queryOne, execute } from '@/lib/db';

// GET handler for model details
export async function GET(
  _request: NextRequest,
  context: any
) {
  const { params } = context;
  try {
    const modelId = params.id;

    const model = await queryOne(
      `SELECT m.*, u.name as author_name, u.avatar_url as author_avatar
       FROM models m
       JOIN users u ON m.user_id = u.id
       WHERE m.id = ?`,
      [modelId]
    );

    if (!model) {
      return NextResponse.json(
        { success: false, error: 'Model not found' },
        { status: 404 }
      );
    }

    // Parse JSON fields
    const processedModel = {
      ...model,
      additional_files: (model as any).additional_files ? JSON.parse((model as any).additional_files) : [],
      additional_images: (model as any).additional_images ? JSON.parse((model as any).additional_images) : [],
      print_settings: (model as any).print_settings ? JSON.parse((model as any).print_settings) : {},
      tags: (model as any).tags ? (model as any).tags.split(',').map((tag: string) => tag.trim()) : [],
    };

    // Increment view count
    await execute(
      'UPDATE models SET view_count = view_count + 1 WHERE id = ?',
      [modelId]
    );

    return NextResponse.json({
      success: true,
      data: { model: processedModel }
    });
  } catch (error) {
    console.error('Error fetching model details:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch model details' },
      { status: 500 }
    );
  }
}

// DELETE handler for model deletion
export async function DELETE(
  _request: NextRequest,
  context: any
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { params } = context;
    const modelId = params.id;

    // Check if model exists and user owns it
    const model = await queryOne(
      'SELECT * FROM models WHERE id = ? AND user_id = ?',
      [modelId, session.user.id]
    );

    if (!model) {
      return NextResponse.json(
        { success: false, error: 'Model not found or access denied' },
        { status: 404 }
      );
    }

    // Delete the model
    await execute('DELETE FROM models WHERE id = ?', [modelId]);

    return NextResponse.json({
      success: true,
      message: 'Model deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting model:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete model' },
      { status: 500 }
    );
  }
}

// PUT handler for updating a model (placeholder)
export async function PUT(
  _request: NextRequest,
  context: any
) {
  try {
    // This will be the logic for updating the model
    // Currently just a placeholder
    return NextResponse.json(
      { success: false, error: 'Not implemented yet' },
      { status: 501 }
    );
  } catch (error) {
    console.error('Error updating model:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update model' },
      { status: 500 }
    );
  }
}
