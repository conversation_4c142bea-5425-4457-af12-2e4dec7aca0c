import { EnhancedMakerWorldScraper, MakerWorldModel } from '@/lib/bright-data/enhanced-makerworld-scraper';
import { NextRequest, NextResponse } from 'next/server';

interface TrendingResponse {
  success: boolean;
  data?: {
    models: MakerWorldModel[];
    total: number;
    platform: string;
    scrapedAt: string;
    processingTime: number;
  } | {
    hasCachedData: boolean;
    cacheAge: number;
    cacheExpiry: number;
    modelsCount: number;
  };
  error?: string;
  message: string;
}

// Cache for storing scraped data
let cachedData: {
  models: MakerWorldModel[];
  timestamp: number;
  expiresAt: number;
} | null = null;

const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

export async function GET(request: NextRequest): Promise<NextResponse<TrendingResponse>> {
  const startTime = Date.now();
  
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const forceRefresh = searchParams.get('refresh') === 'true';

    console.log(`🚀 MakerWorld trending API called with limit: ${limit}, forceRefresh: ${forceRefresh}`);

    // Check cache first
    if (!forceRefresh && cachedData && Date.now() < cachedData.expiresAt) {
      console.log('📋 Returning cached MakerWorld data');
      
      const models = cachedData.models.slice(0, limit);
      const processingTime = Date.now() - startTime;

      return NextResponse.json({
        success: true,
        data: {
          models,
          total: models.length,
          platform: 'makerworld',
          scrapedAt: new Date(cachedData.timestamp).toISOString(),
          processingTime
        },
        message: `Retrieved ${models.length} cached trending models from MakerWorld`
      });
    }

    // Scrape fresh data
    console.log('🔍 Scraping fresh MakerWorld data...');
    const scraper = new EnhancedMakerWorldScraper();
    const models = await scraper.scrapeTrendingModels(Math.max(limit, 20)); // Scrape at least 20 for caching

    if (models.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No models found',
        message: 'Failed to scrape any models from MakerWorld'
      }, { status: 404 });
    }

    // Update cache
    cachedData = {
      models,
      timestamp: Date.now(),
      expiresAt: Date.now() + CACHE_DURATION
    };

    const returnedModels = models.slice(0, limit);
    const processingTime = Date.now() - startTime;

    console.log(`✅ Successfully scraped ${models.length} models from MakerWorld in ${processingTime}ms`);

    return NextResponse.json({
      success: true,
      data: {
        models: returnedModels,
        total: returnedModels.length,
        platform: 'makerworld',
        scrapedAt: new Date().toISOString(),
        processingTime
      },
      message: `Successfully scraped ${returnedModels.length} trending models from MakerWorld`
    });

  } catch (error) {
    console.error('❌ MakerWorld trending API error:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to fetch trending models from MakerWorld'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest): Promise<NextResponse<TrendingResponse>> {
  try {
    const body = await request.json() as { action?: string; limit?: number };
    const { action, limit = 20 } = body;

    console.log(`🚀 MakerWorld trending POST API called with action: ${action}`);

    switch (action) {
      case 'refresh':
        // Force refresh the cache
        cachedData = null;
        
        // Create a new request with refresh=true
        const refreshUrl = new URL(request.url);
        refreshUrl.searchParams.set('refresh', 'true');
        refreshUrl.searchParams.set('limit', limit.toString());
        
        const refreshRequest = new NextRequest(refreshUrl.toString(), {
          method: 'GET',
          headers: request.headers
        });
        
        return await GET(refreshRequest);

      case 'clear-cache':
        cachedData = null;
        return NextResponse.json({
          success: true,
          message: 'Cache cleared successfully'
        });

      case 'cache-status':
        const cacheStatus = {
          hasCachedData: !!cachedData,
          cacheAge: cachedData ? Date.now() - cachedData.timestamp : 0,
          cacheExpiry: cachedData ? cachedData.expiresAt : 0,
          modelsCount: cachedData ? cachedData.models.length : 0
        };

        return NextResponse.json({
          success: true,
          data: cacheStatus,
          message: 'Cache status retrieved'
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action',
          message: `Unknown action: ${action}. Available actions: refresh, clear-cache, cache-status`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ MakerWorld trending POST API error:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      message: 'Failed to process POST request'
    }, { status: 500 });
  }
}

// Health check endpoint
export async function HEAD(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Cache-Control': 'no-cache',
      'X-API-Status': 'healthy',
      'X-Cache-Status': cachedData ? 'hit' : 'miss'
    }
  });
}
