import { ModelFileScraper, ModelFileScrapingOptions } from '@/lib/bright-data/model-file-scraper';
import { ScrapedModelsRepository } from '@/lib/database/scraped-models-repository';
import { BatchProcessor } from '@/lib/processing/batch-processor';
import { ThumbnailGenerator } from '@/lib/processing/thumbnail-generator';
import { ModelSource } from '@/types/models';
import { NextRequest, NextResponse } from 'next/server';

interface EnhancedPopularResponse {
  success: boolean;
  models: any[];
  platform: string;
  category?: string;
  timeframe: string;
  total: number;
  cached: boolean;
  processingJobId?: string;
  timestamp: string;
  error?: string;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform') as ModelSource || 'printables';
    const limit = parseInt(searchParams.get('limit') || '8');
    const category = searchParams.get('category') || undefined;
    const timeframe = searchParams.get('timeframe') as 'all' | 'year' | 'month' | 'week' || 'week';
    const processFiles = searchParams.get('processFiles') === 'true';
    const generateThumbnails = searchParams.get('generateThumbnails') === 'true';
    const forceRefresh = searchParams.get('forceRefresh') === 'true';

    console.log(`⭐ Getting enhanced popular models from ${platform}`);

    const repository = new ScrapedModelsRepository();

    // Check if we have cached models (unless force refresh)
    if (!forceRefresh) {
      const cachedModels = await repository.searchScrapedModels({
        filters: {
          platform,
          category,
          hasAnalysis: true
        },
        sortBy: 'popularity',
        limit
      });

      if (cachedModels.models.length > 0) {
        console.log(`✅ Returning ${cachedModels.models.length} cached models`);
        
        return NextResponse.json({
          success: true,
          models: cachedModels.models,
          platform,
          category,
          timeframe,
          total: cachedModels.total,
          cached: true,
          timestamp: new Date().toISOString()
        });
      }
    }

    // If no cached models or force refresh, scrape new ones
    console.log(`🔍 Scraping fresh models from ${platform}`);
    
    const scraper = new ModelFileScraper();
    const batchProcessor = new BatchProcessor();

    // Get platform URLs to scrape
    const urlsToScrape = await getPlatformUrls(platform, category, timeframe, limit);
    
    if (urlsToScrape.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No URLs found to scrape',
        models: [],
        platform,
        total: 0,
        cached: false,
        timestamp: new Date().toISOString()
      });
    }

    // Scrape models with files
    const scrapingOptions: ModelFileScrapingOptions = {
      platform,
      includeAllFormats: true,
      downloadFiles: processFiles,
      processFiles: processFiles,
      generateThumbnails: generateThumbnails
    };

    const scrapedModels: Array<any> = [];
    const filesToProcess = [];

    for (const url of urlsToScrape) {
      try {
        const scrapedModel = await scraper.scrapeModelWithFiles(url, scrapingOptions);

        if (scrapedModel) {
          // Store in database
          const modelId = await repository.storeScrapedModel(scrapedModel);

          // Create enhanced model with ID
          const enhancedModel = {
            ...scrapedModel,
            id: modelId,
            featured: false, // Default value
            publishedAt: new Date().toISOString() // Default to current time
          };

          scrapedModels.push(enhancedModel);
          
          // Collect files for batch processing
          if (processFiles && scrapedModel.originalFiles.length > 0) {
            for (const file of scrapedModel.originalFiles) {
              // Download file buffer (in real implementation)
              const buffer = new ArrayBuffer(1024); // Placeholder
              filesToProcess.push({ file, buffer });
            }
          }
        }
      } catch (error) {
        console.error(`❌ Failed to scrape ${url}:`, error);
      }
    }

    // Start batch processing if requested
    let processingJobId: string | undefined;
    
    if (processFiles && filesToProcess.length > 0) {
      console.log(`🔄 Starting batch processing for ${filesToProcess.length} files`);
      
      // Add conversion job
      const conversionJobId = await batchProcessor.addConversionJob(
        filesToProcess,
        {
          targetFormat: 'glb',
          optimize: true,
          compress: true,
          generateLOD: false,
          quality: 'medium'
        },
        'normal'
      );

      // Add thumbnail job if requested
      if (generateThumbnails) {
        await batchProcessor.addThumbnailJob(
          filesToProcess,
          ThumbnailGenerator.createPreset('card'),
          'normal'
        );
      }

      // Add analysis job
      await batchProcessor.addAnalysisJob(filesToProcess, 'normal');

      processingJobId = conversionJobId;
      
      // Start processing in background (don't wait)
      batchProcessor.startProcessing().catch(error => {
        console.error('❌ Batch processing error:', error);
      });
    }

    // Return scraped models
    const response: EnhancedPopularResponse = {
      success: true,
      models: scrapedModels.map(model => ({
        id: model.id,
        title: model.title,
        description: model.description,
        thumbnail: model.previewFile?.url || model.thumbnail,
        designer: model.designer,
        stats: model.stats,
        platform: model.platform,
        originalUrl: model.originalUrl,
        tags: model.tags,
        category: model.category,
        isFree: model.isFree,
        price: model.price,
        featured: model.featured,
        publishedAt: model.publishedAt,
        files: model.modelFiles.map((f: any) => ({
          name: f.name,
          format: f.format,
          size: f.size,
          isProcessed: f.isProcessed
        }))
      })),
      platform,
      category,
      timeframe,
      total: scrapedModels.length,
      cached: false,
      processingJobId,
      timestamp: new Date().toISOString()
    };

    console.log(`✅ Scraped ${scrapedModels.length} models from ${platform}`);
    
    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ Error in enhanced popular models API:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to get enhanced popular models',
      details: error instanceof Error ? error.message : 'Unknown error',
      models: [],
      platform: 'unknown',
      total: 0,
      cached: false,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // TODO: Parse body for advanced filtering options
    // const body = await request.json();

    // Handle POST request for more complex filtering and processing options
    const response = await GET(request);
    return response;

  } catch (error) {
    console.error('❌ Error in enhanced popular models POST:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to process enhanced popular models request',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Get URLs to scrape for a platform
 */
async function getPlatformUrls(
  platform: ModelSource,
  _category?: string,
  _timeframe?: string,
  limit: number = 8
): Promise<string[]> {
  // This would use the existing platform scrapers to get URLs
  // For now, return sample URLs

  const sampleUrls: Record<string, string[]> = {
    printables: [
      'https://www.printables.com/model/123456',
      'https://www.printables.com/model/123457',
      'https://www.printables.com/model/123458',
    ],
    makerworld: [
      'https://makerworld.com/models/123456',
      'https://makerworld.com/models/123457',
    ],
    thangs: [
      'https://thangs.com/designer/user/3d-model/123456',
    ],
    local: [] // Add support for local platform
  };

  return sampleUrls[platform]?.slice(0, limit) || [];
}

/**
 * Check processing job status
 */
export async function GET_JOB_STATUS(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json({
        success: false,
        error: 'Job ID is required'
      }, { status: 400 });
    }

    const batchProcessor = new BatchProcessor();
    const jobStatus = batchProcessor.getJobStatus(jobId);

    if (!jobStatus) {
      return NextResponse.json({
        success: false,
        error: 'Job not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      job: {
        id: jobStatus.id,
        type: jobStatus.type,
        status: jobStatus.status,
        progress: jobStatus.progress,
        currentStage: jobStatus.currentStage,
        estimatedDuration: jobStatus.estimatedDuration,
        actualDuration: jobStatus.actualDuration,
        error: jobStatus.error
      }
    });

  } catch (error) {
    console.error('❌ Error getting job status:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to get job status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
