import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { R2ModelStorage } from '@/lib/storage/r2-model-storage';

interface DownloadRequest {
  format?: string;
  includeAll?: boolean;
}

interface DownloadResponse {
  success: boolean;
  downloadUrl?: string;
  files?: Array<{
    name: string;
    url: string;
    format: string;
    size: number;
  }>;
  error?: string;
  details?: string;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id } = await params;

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Model ID is required' },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format');
    const includeAll = searchParams.get('includeAll') === 'true';

    console.log(`⬇️ Processing download request for model: ${id}`);

    // Get model data from database
    const modelData = await getModelData(id);
    
    if (!modelData) {
      return NextResponse.json(
        { success: false, error: 'Model not found' },
        { status: 404 }
      );
    }

    // Check download permissions
    const hasPermission = await checkDownloadPermission(id, session.user.id);
    
    if (!hasPermission) {
      return NextResponse.json(
        { success: false, error: 'Download permission denied' },
        { status: 403 }
      );
    }

    // Initialize storage
    const storage = new R2ModelStorage();

    // Filter files based on request
    let filesToDownload = modelData.files;
    
    if (format && !includeAll) {
      filesToDownload = modelData.files.filter((file: any) => 
        file.format.toLowerCase() === format.toLowerCase()
      );
      
      if (filesToDownload.length === 0) {
        return NextResponse.json(
          { success: false, error: `No files found in ${format} format` },
          { status: 404 }
        );
      }
    }

    // Generate download URLs
    const downloadFiles = await Promise.all(
      filesToDownload.map(async (file: any) => {
        try {
          const signedUrl = await storage.generateSignedUrl(file.storedName, 3600); // 1 hour
          
          return {
            name: file.name,
            url: signedUrl,
            format: file.format,
            size: file.size
          };
        } catch (error) {
          console.error(`❌ Error generating download URL for ${file.name}:`, error);
          return null;
        }
      })
    );

    // Filter out failed URLs
    const validDownloadFiles = downloadFiles.filter(file => file !== null);

    if (validDownloadFiles.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Failed to generate download URLs' },
        { status: 500 }
      );
    }

    // Log download activity
    await logDownloadActivity(id, session.user.id, validDownloadFiles.length);

    // If single file requested, return direct URL
    if (validDownloadFiles.length === 1 && !includeAll) {
      return NextResponse.json({
        success: true,
        downloadUrl: validDownloadFiles[0].url,
        files: validDownloadFiles
      });
    }

    // For multiple files, return array of URLs
    return NextResponse.json({
      success: true,
      files: validDownloadFiles
    });

  } catch (error) {
    console.error('❌ Error in real-download:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to process download request',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    const { id } = await params;

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json() as DownloadRequest;
    const { format, includeAll = false } = body;

    console.log(`📦 Creating download package for model: ${id}`);

    // Get model data
    const modelData = await getModelData(id);
    
    if (!modelData) {
      return NextResponse.json(
        { success: false, error: 'Model not found' },
        { status: 404 }
      );
    }

    // Check permissions
    const hasPermission = await checkDownloadPermission(id, session.user.id);
    
    if (!hasPermission) {
      return NextResponse.json(
        { success: false, error: 'Download permission denied' },
        { status: 403 }
      );
    }

    // Create download package (ZIP file)
    const packageUrl = await createDownloadPackage(modelData, format, includeAll);

    if (!packageUrl) {
      return NextResponse.json(
        { success: false, error: 'Failed to create download package' },
        { status: 500 }
      );
    }

    // Log download activity
    await logDownloadActivity(id, session.user.id, modelData.files.length);

    return NextResponse.json({
      success: true,
      downloadUrl: packageUrl,
      files: modelData.files
    });

  } catch (error) {
    console.error('❌ Error creating download package:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to create download package',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Get model data from database
 */
async function getModelData(modelId: string): Promise<any | null> {
  try {
    console.log(`🔍 Getting model data for: ${modelId}`);

    // This would query your database for the model
    // For now, return mock data
    
    // Example database query (uncomment when database is available):
    /*
    const { queryOne } = await import('@/lib/db');
    
    const model = await queryOne(
      'SELECT * FROM scraped_models WHERE id = ?',
      [modelId]
    );

    if (model) {
      return {
        ...model,
        files: JSON.parse(model.files_data || '[]')
      };
    }
    */

    // Mock data for testing
    return {
      id: modelId,
      title: 'Sample 3D Model',
      files: [
        {
          name: 'model.stl',
          storedName: 'models/sample/model.stl',
          format: 'stl',
          size: 1024 * 1024 * 5 // 5MB
        },
        {
          name: 'model.obj',
          storedName: 'models/sample/model.obj',
          format: 'obj',
          size: 1024 * 1024 * 3 // 3MB
        }
      ]
    };

  } catch (error) {
    console.error('❌ Error getting model data:', error);
    return null;
  }
}

/**
 * Check if user has permission to download the model
 */
async function checkDownloadPermission(modelId: string, userId: string): Promise<boolean> {
  try {
    console.log(`🔐 Checking download permission for user ${userId} and model ${modelId}`);

    // This would check your database for permissions
    // For now, allow all authenticated users
    
    // Example permission check (uncomment when database is available):
    /*
    const { queryOne } = await import('@/lib/db');
    
    // Check if user owns the model
    const ownership = await queryOne(
      'SELECT * FROM scraped_models WHERE id = ? AND user_id = ?',
      [modelId, userId]
    );

    if (ownership) {
      return true;
    }

    // Check if model is free
    const model = await queryOne(
      'SELECT is_free FROM scraped_models WHERE id = ?',
      [modelId]
    );

    if (model && model.is_free) {
      return true;
    }

    // Check if user has purchased the model
    const purchase = await queryOne(
      'SELECT * FROM user_purchases WHERE user_id = ? AND model_id = ?',
      [userId, modelId]
    );

    return !!purchase;
    */

    return true; // Allow all for testing

  } catch (error) {
    console.error('❌ Error checking download permission:', error);
    return false;
  }
}

/**
 * Log download activity
 */
async function logDownloadActivity(modelId: string, userId: string, fileCount: number): Promise<void> {
  try {
    console.log(`📊 Logging download activity: ${modelId} by ${userId} (${fileCount} files)`);

    // This would log to your database
    // For now, just console log
    
    // Example logging (uncomment when database is available):
    /*
    const { execute } = await import('@/lib/db');
    
    await execute(
      `INSERT INTO download_logs (
        model_id, user_id, file_count, downloaded_at
      ) VALUES (?, ?, ?, CURRENT_TIMESTAMP)`,
      [modelId, userId, fileCount]
    );

    // Update download count
    await execute(
      'UPDATE scraped_models SET download_count = download_count + 1 WHERE id = ?',
      [modelId]
    );
    */

  } catch (error) {
    console.error('❌ Error logging download activity:', error);
  }
}

/**
 * Create a download package (ZIP file) containing multiple files
 */
async function createDownloadPackage(
  modelData: any, 
  format?: string, 
  includeAll: boolean = false
): Promise<string | null> {
  try {
    console.log(`📦 Creating download package for model: ${modelData.id}`);

    // This would create a ZIP file containing the requested files
    // For now, return a mock URL
    
    // Filter files if format specified
    let filesToPackage = modelData.files;
    if (format && !includeAll) {
      filesToPackage = modelData.files.filter((file: any) => 
        file.format.toLowerCase() === format.toLowerCase()
      );
    }

    // In a real implementation, you would:
    // 1. Download all files from R2 storage
    // 2. Create a ZIP archive
    // 3. Upload the ZIP to R2 storage
    // 4. Return a signed URL for the ZIP

    const packageName = `${modelData.id}_${Date.now()}.zip`;
    const packageUrl = `https://downloads.3d-marketplace.com/packages/${packageName}`;

    console.log(`✅ Created download package: ${packageUrl}`);
    return packageUrl;

  } catch (error) {
    console.error('❌ Error creating download package:', error);
    return null;
  }
}
