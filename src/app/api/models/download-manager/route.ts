/**
 * API для управління завантаженням 3D моделей через ModelDownloadManager
 */

import { NextRequest, NextResponse } from 'next/server';
import { CloudflareEnv } from '../../../../lib/cloudflare/types';

interface CloudflareRequest extends NextRequest {
  env?: CloudflareEnv;
}

/**
 * GET /api/models/download-manager
 * Отримання списку завдань завантаження або статистики
 */
export async function GET(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'jobs';
    const status = searchParams.get('status');
    const platform = searchParams.get('platform');

    // Отримуємо Durable Object
    const env = request.env;
    if (!env?.MODEL_DOWNLOAD_MANAGER) {
      return NextResponse.json({
        error: 'MODEL_DOWNLOAD_MANAGER не налаштований'
      }, { status: 500 });
    }

    const id = env.MODEL_DOWNLOAD_MANAGER.idFromName('global');
    const stub = env.MODEL_DOWNLOAD_MANAGER.get(id);

    let url: string;
    switch (action) {
      case 'stats':
        url = 'https://fake-host/stats';
        break;
      case 'jobs':
      default:
        url = `https://fake-host/jobs?${new URLSearchParams({
          ...(status && { status }),
          ...(platform && { platform })
        })}`;
        break;
    }

    const response = await stub.fetch(url);
    const data = await response.json();

    return NextResponse.json(data);

  } catch (error) {
    console.error('❌ Помилка GET /api/models/download-manager:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * POST /api/models/download-manager
 * Додавання нового завдання завантаження або обробка черги
 */
export async function POST(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'download';

    // Отримуємо Durable Object
    const env = request.env;
    if (!env?.MODEL_DOWNLOAD_MANAGER) {
      return NextResponse.json({
        error: 'MODEL_DOWNLOAD_MANAGER не налаштований'
      }, { status: 500 });
    }

    const id = env.MODEL_DOWNLOAD_MANAGER.idFromName('global');
    const stub = env.MODEL_DOWNLOAD_MANAGER.get(id);

    let url: string;
    let body: any = null;

    switch (action) {
      case 'process':
        url = 'https://fake-host/process';
        break;
      case 'download':
      default:
        url = 'https://fake-host/download';
        body = await request.json();
        
        // Валідація даних
        const requiredFields = ['modelId', 'sourceUrl', 'fileName', 'fileType', 'platform'];
        for (const field of requiredFields) {
          if (!body[field]) {
            return NextResponse.json({
              error: `Поле ${field} є обов'язковим`
            }, { status: 400 });
          }
        }
        break;
    }

    const response = await stub.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: body ? JSON.stringify(body) : undefined
    });

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('❌ Помилка POST /api/models/download-manager:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/models/download-manager
 * Видалення завдання завантаження
 */
export async function DELETE(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json({
        error: 'Параметр jobId є обов\'язковим'
      }, { status: 400 });
    }

    // Отримуємо Durable Object
    const env = request.env;
    if (!env?.MODEL_DOWNLOAD_MANAGER) {
      return NextResponse.json({
        error: 'MODEL_DOWNLOAD_MANAGER не налаштований'
      }, { status: 500 });
    }

    const id = env.MODEL_DOWNLOAD_MANAGER.idFromName('global');
    const stub = env.MODEL_DOWNLOAD_MANAGER.get(id);

    const response = await stub.fetch(`https://fake-host/jobs/${jobId}`, {
      method: 'DELETE'
    });

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('❌ Помилка DELETE /api/models/download-manager:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * PATCH /api/models/download-manager
 * Повторна спроба завантаження
 */
export async function PATCH(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');
    const action = searchParams.get('action');

    if (!jobId) {
      return NextResponse.json({
        error: 'Параметр jobId є обов\'язковим'
      }, { status: 400 });
    }

    if (action !== 'retry') {
      return NextResponse.json({
        error: 'Підтримується тільки action=retry'
      }, { status: 400 });
    }

    // Отримуємо Durable Object
    const env = request.env;
    if (!env?.MODEL_DOWNLOAD_MANAGER) {
      return NextResponse.json({
        error: 'MODEL_DOWNLOAD_MANAGER не налаштований'
      }, { status: 500 });
    }

    const id = env.MODEL_DOWNLOAD_MANAGER.idFromName('global');
    const stub = env.MODEL_DOWNLOAD_MANAGER.get(id);

    const response = await stub.fetch(`https://fake-host/jobs/${jobId}/retry`, {
      method: 'POST'
    });

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('❌ Помилка PATCH /api/models/download-manager:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * Допоміжна функція для отримання інформації про завдання
 */
export async function getDownloadJobInfo(env: CloudflareEnv, jobId: string) {
  try {
    if (!env.MODEL_DOWNLOAD_MANAGER) {
      throw new Error('MODEL_DOWNLOAD_MANAGER не налаштований');
    }

    const id = env.MODEL_DOWNLOAD_MANAGER.idFromName('global');
    const stub = env.MODEL_DOWNLOAD_MANAGER.get(id);

    const response = await stub.fetch(`https://fake-host/jobs?jobId=${jobId}`);
    const data = await response.json();

    if (data.success && data.jobs.length > 0) {
      return data.jobs[0];
    }

    return null;
  } catch (error) {
    console.error('❌ Помилка отримання інформації про завдання:', error);
    return null;
  }
}

/**
 * Допоміжна функція для додавання завдання завантаження
 */
export async function addDownloadJob(
  env: CloudflareEnv,
  modelId: string,
  sourceUrl: string,
  fileName: string,
  fileType: string,
  platform: string
) {
  try {
    if (!env.MODEL_DOWNLOAD_MANAGER) {
      throw new Error('MODEL_DOWNLOAD_MANAGER не налаштований');
    }

    const id = env.MODEL_DOWNLOAD_MANAGER.idFromName('global');
    const stub = env.MODEL_DOWNLOAD_MANAGER.get(id);

    const response = await stub.fetch('https://fake-host/download', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        modelId,
        sourceUrl,
        fileName,
        fileType,
        platform
      })
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('❌ Помилка додавання завдання завантаження:', error);
    throw error;
  }
}

/**
 * Допоміжна функція для отримання статистики завантажень
 */
export async function getDownloadStats(env: CloudflareEnv) {
  try {
    if (!env.MODEL_DOWNLOAD_MANAGER) {
      throw new Error('MODEL_DOWNLOAD_MANAGER не налаштований');
    }

    const id = env.MODEL_DOWNLOAD_MANAGER.idFromName('global');
    const stub = env.MODEL_DOWNLOAD_MANAGER.get(id);

    const response = await stub.fetch('https://fake-host/stats');
    const data = await response.json();

    return data;
  } catch (error) {
    console.error('❌ Помилка отримання статистики завантажень:', error);
    throw error;
  }
}
