/**
 * API endpoint для пошуку арбітражних можливостей
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { OpportunityFinder } from '@/lib/ai/opportunity-finder';
import { OpportunityFilters } from '@/lib/ai/types';
import { logAIOperation, getCachedResult, setCachedResult } from '@/lib/ai/utils';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const filters: OpportunityFilters = {
      category: body.category,
      minProfit: body.minProfit,
      maxRisk: body.maxRisk,
      minConfidence: body.minConfidence,
      priceRange: body.priceRange
    };

    // Створюємо ключ кешу на основі фільтрів
    const cacheKey = `opportunities_${JSON.stringify(filters)}`;
    const cachedResult = getCachedResult(cacheKey);
    
    if (cachedResult) {
      logAIOperation('find_opportunities', { filters, cached: true }, true);
      return NextResponse.json({
        success: true,
        data: cachedResult,
        cached: true,
        timestamp: new Date()
      });
    }

    // Шукаємо можливості
    const finder = new OpportunityFinder();
    const result = await finder.findOpportunities(filters);

    if (result.success) {
      // Кешуємо результат на 15 хвилин (можливості швидко змінюються)
      setCachedResult(cacheKey, result.data, 15);
    }

    logAIOperation('find_opportunities', { 
      filters, 
      found: result.success ? result.data.length : 0,
      success: result.success 
    }, result.success);

    return NextResponse.json({
      success: result.success,
      data: result.data,
      error: result.error,
      confidence: result.confidence,
      cached: false,
      timestamp: result.timestamp
    });

  } catch (error) {
    console.error('Find opportunities API error:', error);
    logAIOperation('find_opportunities', { error: error.message }, false);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        timestamp: new Date()
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    const filters: OpportunityFilters = {
      category: searchParams.get('category') || undefined,
      minProfit: searchParams.get('minProfit') ? parseFloat(searchParams.get('minProfit')!) : undefined,
      maxRisk: searchParams.get('maxRisk') as 'low' | 'medium' | 'high' || undefined,
      minConfidence: searchParams.get('minConfidence') ? parseFloat(searchParams.get('minConfidence')!) : undefined,
      priceRange: searchParams.get('priceMin') && searchParams.get('priceMax') ? {
        min: parseFloat(searchParams.get('priceMin')!),
        max: parseFloat(searchParams.get('priceMax')!)
      } : undefined
    };

    const cacheKey = `opportunities_${JSON.stringify(filters)}`;
    const cachedResult = getCachedResult(cacheKey);
    
    if (cachedResult) {
      return NextResponse.json({
        success: true,
        data: cachedResult,
        cached: true,
        timestamp: new Date()
      });
    }

    const finder = new OpportunityFinder();
    const result = await finder.findOpportunities(filters);

    if (result.success) {
      setCachedResult(cacheKey, result.data, 15);
    }

    return NextResponse.json({
      success: result.success,
      data: result.data,
      error: result.error,
      confidence: result.confidence,
      cached: false,
      timestamp: result.timestamp
    });

  } catch (error) {
    console.error('Find opportunities GET API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        timestamp: new Date()
      },
      { status: 500 }
    );
  }
}
