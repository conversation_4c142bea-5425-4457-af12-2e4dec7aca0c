/**
 * API endpoint для прогнозування цін через AI
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { PricePredictor } from '@/lib/ai/price-predictor';
import { logAIOperation, getCachedResult, setCachedResult } from '@/lib/ai/utils';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { modelId, category, horizon } = body;

    const predictor = new PricePredictor();

    if (modelId) {
      // Прогнозуємо ціну для конкретної моделі
      const cacheKey = `price_prediction_${modelId}`;
      const cachedResult = getCachedResult(cacheKey);
      
      if (cachedResult) {
        logAIOperation('predict_model_price', { modelId, cached: true }, true);
        return NextResponse.json({
          success: true,
          data: cachedResult,
          cached: true,
          timestamp: new Date()
        });
      }

      const result = await predictor.predictOptimalPrice(modelId);

      if (result.success) {
        // Кешуємо результат на 60 хвилин
        setCachedResult(cacheKey, result.data, 60);
      }

      logAIOperation('predict_model_price', { 
        modelId,
        success: result.success 
      }, result.success);

      return NextResponse.json({
        success: result.success,
        data: result.data,
        error: result.error,
        confidence: result.confidence,
        cached: false,
        timestamp: result.timestamp
      });

    } else if (category) {
      // Прогнозуємо тренд цін для категорії
      const predictionHorizon = horizon || 30;
      const cacheKey = `category_price_trend_${category}_${predictionHorizon}`;
      const cachedResult = getCachedResult(cacheKey);
      
      if (cachedResult) {
        logAIOperation('predict_category_trend', { category, horizon: predictionHorizon, cached: true }, true);
        return NextResponse.json({
          success: true,
          data: cachedResult,
          cached: true,
          timestamp: new Date()
        });
      }

      const result = await predictor.predictCategoryPriceTrend(category, predictionHorizon);

      if (result.success) {
        // Кешуємо результат на 120 хвилин
        setCachedResult(cacheKey, result.data, 120);
      }

      logAIOperation('predict_category_trend', { 
        category,
        horizon: predictionHorizon,
        success: result.success 
      }, result.success);

      return NextResponse.json({
        success: result.success,
        data: result.data,
        error: result.error,
        confidence: result.confidence,
        cached: false,
        timestamp: result.timestamp
      });

    } else {
      return NextResponse.json(
        { success: false, error: 'Either modelId or category is required' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Price prediction API error:', error);
    logAIOperation('price_prediction', { error: error.message }, false);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        timestamp: new Date()
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const modelId = searchParams.get('modelId');
    const category = searchParams.get('category');
    const horizon = searchParams.get('horizon') ? parseInt(searchParams.get('horizon')!) : 30;

    const predictor = new PricePredictor();

    if (modelId) {
      // Прогноз для моделі
      const cacheKey = `price_prediction_${modelId}`;
      const cachedResult = getCachedResult(cacheKey);
      
      if (cachedResult) {
        return NextResponse.json({
          success: true,
          data: cachedResult,
          cached: true,
          timestamp: new Date()
        });
      }

      const result = await predictor.predictOptimalPrice(modelId);

      if (result.success) {
        setCachedResult(cacheKey, result.data, 60);
      }

      return NextResponse.json({
        success: result.success,
        data: result.data,
        error: result.error,
        confidence: result.confidence,
        cached: false,
        timestamp: result.timestamp
      });

    } else if (category) {
      // Прогноз для категорії
      const cacheKey = `category_price_trend_${category}_${horizon}`;
      const cachedResult = getCachedResult(cacheKey);
      
      if (cachedResult) {
        return NextResponse.json({
          success: true,
          data: cachedResult,
          cached: true,
          timestamp: new Date()
        });
      }

      const result = await predictor.predictCategoryPriceTrend(category, horizon);

      if (result.success) {
        setCachedResult(cacheKey, result.data, 120);
      }

      return NextResponse.json({
        success: result.success,
        data: result.data,
        error: result.error,
        confidence: result.confidence,
        cached: false,
        timestamp: result.timestamp
      });

    } else {
      return NextResponse.json(
        { success: false, error: 'Either modelId or category parameter is required' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Price prediction GET API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        timestamp: new Date()
      },
      { status: 500 }
    );
  }
}
