/**
 * API endpoint для аналізу ринку через AI
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { MarketAnalyzer } from '@/lib/ai/market-analyzer';
import { logAIOperation, getCachedResult, setCachedResult } from '@/lib/ai/utils';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { category } = body;

    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category is required' },
        { status: 400 }
      );
    }

    // Перевіряємо кеш
    const cacheKey = `market_analysis_${category}`;
    const cachedResult = getCachedResult(cacheKey);
    
    if (cachedResult) {
      logAIOperation('market_analysis', { category, cached: true }, true);
      return NextResponse.json({
        success: true,
        data: cachedResult,
        cached: true,
        timestamp: new Date()
      });
    }

    // Виконуємо аналіз
    const analyzer = new MarketAnalyzer();
    const result = await analyzer.analyzeCategory(category);

    if (result.success) {
      // Кешуємо результат на 30 хвилин
      setCachedResult(cacheKey, result.data, 30);
    }

    logAIOperation('market_analysis', { category, success: result.success }, result.success);

    return NextResponse.json({
      success: result.success,
      data: result.data,
      error: result.error,
      confidence: result.confidence,
      cached: false,
      timestamp: result.timestamp
    });

  } catch (error) {
    console.error('Market analysis API error:', error);
    logAIOperation('market_analysis', { error: error.message }, false);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        timestamp: new Date()
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category parameter is required' },
        { status: 400 }
      );
    }

    // Перевіряємо кеш
    const cacheKey = `market_analysis_${category}`;
    const cachedResult = getCachedResult(cacheKey);
    
    if (cachedResult) {
      return NextResponse.json({
        success: true,
        data: cachedResult,
        cached: true,
        timestamp: new Date()
      });
    }

    // Виконуємо аналіз
    const analyzer = new MarketAnalyzer();
    const result = await analyzer.analyzeCategory(category);

    if (result.success) {
      setCachedResult(cacheKey, result.data, 30);
    }

    return NextResponse.json({
      success: result.success,
      data: result.data,
      error: result.error,
      confidence: result.confidence,
      cached: false,
      timestamp: result.timestamp
    });

  } catch (error) {
    console.error('Market analysis GET API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        timestamp: new Date()
      },
      { status: 500 }
    );
  }
}
