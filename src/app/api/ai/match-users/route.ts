/**
 * API endpoint для з'єднання користувачів з можливостями
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { UserMatcher } from '@/lib/ai/user-matcher';
import { logAIOperation, getCachedResult, setCachedResult } from '@/lib/ai/utils';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, opportunityId } = body;

    const matcher = new UserMatcher();

    if (action === 'find_matches') {
      // Знаходимо підходящі можливості для користувача
      const cacheKey = `user_matches_${session.user.id}`;
      const cachedResult = getCachedResult(cacheKey);
      
      if (cachedResult) {
        logAIOperation('find_user_matches', { userId: session.user.id, cached: true }, true);
        return NextResponse.json({
          success: true,
          data: cachedResult,
          cached: true,
          timestamp: new Date()
        });
      }

      const result = await matcher.findMatchesForUser(session.user.id);

      if (result.success) {
        // Кешуємо результат на 10 хвилин
        setCachedResult(cacheKey, result.data, 10);
      }

      logAIOperation('find_user_matches', { 
        userId: session.user.id,
        matches: result.success ? result.data.matches.length : 0,
        success: result.success 
      }, result.success);

      return NextResponse.json({
        success: result.success,
        data: result.data,
        error: result.error,
        confidence: result.confidence,
        cached: false,
        timestamp: result.timestamp
      });

    } else if (action === 'find_buyers' && opportunityId) {
      // Знаходимо покупців для конкретної можливості
      const cacheKey = `opportunity_buyers_${opportunityId}`;
      const cachedResult = getCachedResult(cacheKey);
      
      if (cachedResult) {
        return NextResponse.json({
          success: true,
          data: cachedResult,
          cached: true,
          timestamp: new Date()
        });
      }

      const result = await matcher.findBuyersForOpportunity(opportunityId);

      if (result.success) {
        setCachedResult(cacheKey, result.data, 5); // 5 хвилин для покупців
      }

      logAIOperation('find_buyers', { 
        opportunityId,
        buyers: result.success ? result.data.matches.length : 0,
        success: result.success 
      }, result.success);

      return NextResponse.json({
        success: result.success,
        data: result.data,
        error: result.error,
        confidence: result.confidence,
        cached: false,
        timestamp: result.timestamp
      });

    } else if (action === 'auto_match' && opportunityId) {
      // Створюємо автоматичне з'єднання
      const result = await matcher.createAutoMatch(opportunityId);

      logAIOperation('auto_match', { 
        opportunityId,
        success: result.success 
      }, result.success);

      return NextResponse.json({
        success: result.success,
        data: result.data,
        error: result.error,
        confidence: result.confidence,
        timestamp: result.timestamp
      });

    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid action or missing parameters' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('User matching API error:', error);
    logAIOperation('user_matching', { error: error.message }, false);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        timestamp: new Date()
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const opportunityId = searchParams.get('opportunityId');

    const matcher = new UserMatcher();

    if (action === 'my_matches') {
      // Отримуємо підходящі можливості для поточного користувача
      const cacheKey = `user_matches_${session.user.id}`;
      const cachedResult = getCachedResult(cacheKey);
      
      if (cachedResult) {
        return NextResponse.json({
          success: true,
          data: cachedResult,
          cached: true,
          timestamp: new Date()
        });
      }

      const result = await matcher.findMatchesForUser(session.user.id);

      if (result.success) {
        setCachedResult(cacheKey, result.data, 10);
      }

      return NextResponse.json({
        success: result.success,
        data: result.data,
        error: result.error,
        confidence: result.confidence,
        cached: false,
        timestamp: result.timestamp
      });

    } else if (action === 'opportunity_buyers' && opportunityId) {
      // Отримуємо потенційних покупців для можливості
      const cacheKey = `opportunity_buyers_${opportunityId}`;
      const cachedResult = getCachedResult(cacheKey);
      
      if (cachedResult) {
        return NextResponse.json({
          success: true,
          data: cachedResult,
          cached: true,
          timestamp: new Date()
        });
      }

      const result = await matcher.findBuyersForOpportunity(opportunityId);

      if (result.success) {
        setCachedResult(cacheKey, result.data, 5);
      }

      return NextResponse.json({
        success: result.success,
        data: result.data,
        error: result.error,
        confidence: result.confidence,
        cached: false,
        timestamp: result.timestamp
      });

    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid action or missing parameters' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('User matching GET API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        timestamp: new Date()
      },
      { status: 500 }
    );
  }
}
