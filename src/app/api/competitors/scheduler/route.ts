/**
 * API endpoint for competitor scraping scheduler management
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { CompetitorScheduler } from '@/lib/bright-data/scheduler/competitor-scheduler';

// Global scheduler instance
let schedulerInstance: CompetitorScheduler | null = null;

function getScheduler(): CompetitorScheduler {
  if (!schedulerInstance) {
    schedulerInstance = new CompetitorScheduler();
  }
  return schedulerInstance;
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const taskId = searchParams.get('taskId');

    const scheduler = getScheduler();

    switch (action) {
      case 'status':
        const status = scheduler.getStatus();
        return NextResponse.json({
          success: true,
          data: status,
          timestamp: new Date().toISOString()
        });

      case 'tasks':
        const tasks = scheduler.getStatus().tasks;
        return NextResponse.json({
          success: true,
          data: tasks,
          timestamp: new Date().toISOString()
        });

      case 'task':
        if (!taskId) {
          return NextResponse.json(
            { success: false, error: 'Task ID parameter required' },
            { status: 400 }
          );
        }
        
        const task = scheduler.getTask(taskId);
        if (!task) {
          return NextResponse.json(
            { success: false, error: 'Task not found' },
            { status: 404 }
          );
        }
        
        return NextResponse.json({
          success: true,
          data: task,
          timestamp: new Date().toISOString()
        });

      case 'health':
        const health = {
          schedulerRunning: scheduler.getStatus().isRunning,
          activeTasks: scheduler.getStatus().activeTasks,
          totalTasks: scheduler.getStatus().totalTasks,
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
          timestamp: new Date().toISOString()
        };
        
        return NextResponse.json({
          success: true,
          data: health,
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('❌ Scheduler API GET error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, taskId, taskConfig } = body;

    const scheduler = getScheduler();

    switch (action) {
      case 'start':
        scheduler.start();
        return NextResponse.json({
          success: true,
          message: 'Scheduler started successfully',
          data: scheduler.getStatus(),
          timestamp: new Date().toISOString()
        });

      case 'stop':
        scheduler.stop();
        return NextResponse.json({
          success: true,
          message: 'Scheduler stopped successfully',
          data: scheduler.getStatus(),
          timestamp: new Date().toISOString()
        });

      case 'restart':
        scheduler.stop();
        // Wait a moment before restarting
        await new Promise(resolve => setTimeout(resolve, 1000));
        scheduler.start();
        
        return NextResponse.json({
          success: true,
          message: 'Scheduler restarted successfully',
          data: scheduler.getStatus(),
          timestamp: new Date().toISOString()
        });

      case 'add-task':
        if (!taskConfig) {
          return NextResponse.json(
            { success: false, error: 'Task configuration required' },
            { status: 400 }
          );
        }

        // Validate task configuration
        const requiredFields = ['id', 'name', 'description', 'cronExpression'];
        const missingFields = requiredFields.filter(field => !taskConfig[field]);
        
        if (missingFields.length > 0) {
          return NextResponse.json(
            { 
              success: false, 
              error: `Missing required fields: ${missingFields.join(', ')}` 
            },
            { status: 400 }
          );
        }

        // Create task function based on type
        let taskFunction: () => Promise<void>;
        
        switch (taskConfig.type) {
          case 'trending':
            taskFunction = async () => {
              console.log(`🔥 Custom trending task: ${taskConfig.name}`);
              // Implementation would go here
            };
            break;
            
          case 'category':
            taskFunction = async () => {
              console.log(`📂 Custom category task: ${taskConfig.name}`);
              // Implementation would go here
            };
            break;
            
          default:
            taskFunction = async () => {
              console.log(`⚙️ Custom task: ${taskConfig.name}`);
              // Default implementation
            };
        }

        scheduler.addTask({
          id: taskConfig.id,
          name: taskConfig.name,
          description: taskConfig.description,
          cronExpression: taskConfig.cronExpression,
          enabled: taskConfig.enabled ?? true,
          runCount: 0,
          successCount: 0,
          failureCount: 0,
          averageRunTime: 0,
          task: taskFunction
        });

        return NextResponse.json({
          success: true,
          message: `Task "${taskConfig.name}" added successfully`,
          taskId: taskConfig.id,
          timestamp: new Date().toISOString()
        });

      case 'update-task':
        if (!taskId) {
          return NextResponse.json(
            { success: false, error: 'Task ID required' },
            { status: 400 }
          );
        }

        const updated = scheduler.updateTask(taskId, taskConfig);
        
        if (!updated) {
          return NextResponse.json(
            { success: false, error: 'Task not found' },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          message: `Task "${taskId}" updated successfully`,
          timestamp: new Date().toISOString()
        });

      case 'execute-task':
        if (!taskId) {
          return NextResponse.json(
            { success: false, error: 'Task ID required' },
            { status: 400 }
          );
        }

        const task = scheduler.getTask(taskId);
        if (!task) {
          return NextResponse.json(
            { success: false, error: 'Task not found' },
            { status: 404 }
          );
        }

        // Execute task immediately (async)
        task.task().catch(error => {
          console.error(`❌ Manual task execution failed for ${taskId}:`, error);
        });

        return NextResponse.json({
          success: true,
          message: `Task "${taskId}" execution started`,
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('❌ Scheduler API POST error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const taskId = searchParams.get('taskId');

    if (!taskId) {
      return NextResponse.json(
        { success: false, error: 'Task ID parameter required' },
        { status: 400 }
      );
    }

    const scheduler = getScheduler();
    const removed = scheduler.removeTask(taskId);

    if (!removed) {
      return NextResponse.json(
        { success: false, error: 'Task not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Task "${taskId}" removed successfully`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Scheduler API DELETE error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, taskId, enabled } = body;

    const scheduler = getScheduler();

    switch (action) {
      case 'enable-task':
        if (!taskId) {
          return NextResponse.json(
            { success: false, error: 'Task ID required' },
            { status: 400 }
          );
        }

        const enableUpdated = scheduler.updateTask(taskId, { enabled: true });
        
        return NextResponse.json({
          success: enableUpdated,
          message: enableUpdated ? `Task "${taskId}" enabled` : 'Task not found',
          timestamp: new Date().toISOString()
        });

      case 'disable-task':
        if (!taskId) {
          return NextResponse.json(
            { success: false, error: 'Task ID required' },
            { status: 400 }
          );
        }

        const disableUpdated = scheduler.updateTask(taskId, { enabled: false });
        
        return NextResponse.json({
          success: disableUpdated,
          message: disableUpdated ? `Task "${taskId}" disabled` : 'Task not found',
          timestamp: new Date().toISOString()
        });

      case 'toggle-task':
        if (!taskId) {
          return NextResponse.json(
            { success: false, error: 'Task ID required' },
            { status: 400 }
          );
        }

        const task = scheduler.getTask(taskId);
        if (!task) {
          return NextResponse.json(
            { success: false, error: 'Task not found' },
            { status: 404 }
          );
        }

        const toggleUpdated = scheduler.updateTask(taskId, { enabled: !task.enabled });
        
        return NextResponse.json({
          success: toggleUpdated,
          message: `Task "${taskId}" ${!task.enabled ? 'enabled' : 'disabled'}`,
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('❌ Scheduler API PUT error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
