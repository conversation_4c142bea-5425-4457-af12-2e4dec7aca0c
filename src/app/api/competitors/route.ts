/**
 * API endpoint for competitor analysis and scraping
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { CompetitorScraperManager } from '@/lib/bright-data/competitor-scrapers/competitor-scraper-manager';

// Initialize the competitor scraper manager
const scraperManager = new CompetitorScraperManager({
  maxModelsPerPlatform: 50,
  rateLimitDelay: 2000,
  enableCaching: true
});

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const platform = searchParams.get('platform');
    const category = searchParams.get('category');
    const query = searchParams.get('query');
    const jobId = searchParams.get('jobId');

    switch (action) {
      case 'trending':
        console.log('🔥 API: Fetching trending models...');
        const trendingResults = await scraperManager.scrapeTrendingModels();
        return NextResponse.json({
          success: true,
          data: trendingResults,
          timestamp: new Date().toISOString(),
          source: 'bright-data'
        });

      case 'category':
        if (!category) {
          return NextResponse.json(
            { success: false, error: 'Category parameter required' },
            { status: 400 }
          );
        }
        
        console.log(`📂 API: Fetching ${category} models...`);
        const categoryResults = await scraperManager.scrapeCategoryModels(category);
        return NextResponse.json({
          success: true,
          data: categoryResults,
          category,
          timestamp: new Date().toISOString(),
          source: 'bright-data'
        });

      case 'search':
        if (!query) {
          return NextResponse.json(
            { success: false, error: 'Query parameter required' },
            { status: 400 }
          );
        }
        
        console.log(`🔍 API: Searching for "${query}"...`);
        const searchResults = await scraperManager.searchModels(query);
        return NextResponse.json({
          success: true,
          data: searchResults,
          query,
          timestamp: new Date().toISOString(),
          source: 'bright-data'
        });

      case 'metrics':
        const metrics = scraperManager.getPlatformMetrics();
        return NextResponse.json({
          success: true,
          data: metrics,
          timestamp: new Date().toISOString()
        });

      case 'jobs':
        const jobs = scraperManager.getActiveJobs();
        return NextResponse.json({
          success: true,
          data: jobs,
          timestamp: new Date().toISOString()
        });

      case 'job-status':
        if (!jobId) {
          return NextResponse.json(
            { success: false, error: 'Job ID parameter required' },
            { status: 400 }
          );
        }
        
        const job = scraperManager.getJob(jobId);
        if (!job) {
          return NextResponse.json(
            { success: false, error: 'Job not found' },
            { status: 404 }
          );
        }
        
        return NextResponse.json({
          success: true,
          data: job,
          timestamp: new Date().toISOString()
        });

      case 'platforms':
        const platformList = ['sketchfab', 'cgtrader', 'turbosquid'];
        return NextResponse.json({
          success: true,
          data: platformList,
          timestamp: new Date().toISOString()
        });

      case 'categories':
        const categories = [
          'characters', 'vehicles', 'architecture', 'furniture', 
          'electronics', 'weapons', 'animals', 'plants', 'food',
          'sports', 'medical', 'industrial', 'jewelry', 'toys'
        ];
        return NextResponse.json({
          success: true,
          data: categories,
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('❌ Competitor API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, platforms, category, query, maxModels } = body;

    // Update scraper configuration if provided
    if (maxModels) {
      // Note: In a real implementation, you'd want to create a new instance
      // or have a method to update configuration
      console.log(`📊 Updating max models per platform to: ${maxModels}`);
    }

    switch (action) {
      case 'start-trending-scrape':
        console.log('🚀 API: Starting trending models scrape...');
        
        // Start the scraping process asynchronously
        const trendingPromise = scraperManager.scrapeTrendingModels();
        
        return NextResponse.json({
          success: true,
          message: 'Trending models scraping started',
          estimatedTime: '5-10 minutes',
          timestamp: new Date().toISOString()
        });

      case 'start-category-scrape':
        if (!category) {
          return NextResponse.json(
            { success: false, error: 'Category parameter required' },
            { status: 400 }
          );
        }
        
        console.log(`🚀 API: Starting ${category} scrape...`);
        
        const categoryPromise = scraperManager.scrapeCategoryModels(category);
        
        return NextResponse.json({
          success: true,
          message: `Category "${category}" scraping started`,
          category,
          estimatedTime: '3-7 minutes',
          timestamp: new Date().toISOString()
        });

      case 'start-search-scrape':
        if (!query) {
          return NextResponse.json(
            { success: false, error: 'Query parameter required' },
            { status: 400 }
          );
        }
        
        console.log(`🚀 API: Starting search scrape for "${query}"...`);
        
        const searchPromise = scraperManager.searchModels(query);
        
        return NextResponse.json({
          success: true,
          message: `Search scraping for "${query}" started`,
          query,
          estimatedTime: '2-5 minutes',
          timestamp: new Date().toISOString()
        });

      case 'cancel-job':
        const { jobId } = body;
        if (!jobId) {
          return NextResponse.json(
            { success: false, error: 'Job ID required' },
            { status: 400 }
          );
        }
        
        const cancelled = scraperManager.cancelJob(jobId);
        
        return NextResponse.json({
          success: cancelled,
          message: cancelled ? 'Job cancelled successfully' : 'Job not found or already completed',
          jobId,
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('❌ Competitor API POST error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Job ID parameter required' },
        { status: 400 }
      );
    }

    const cancelled = scraperManager.cancelJob(jobId);

    return NextResponse.json({
      success: cancelled,
      message: cancelled ? 'Job cancelled successfully' : 'Job not found or already completed',
      jobId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Competitor API DELETE error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
