import { BrightDataScraperManager } from '@/lib/bright-data/scraper-manager';
import { ScraperManager } from '@/lib/scrapers/scraper-manager';
import { SimpleScraper } from '@/lib/scrapers/simple-scraper';
import { NextRequest, NextResponse } from 'next/server';

// Перевіряємо, чи ми в продакшні (Vercel)
const isProduction = process.env.NODE_ENV === 'production' || process.env.VERCEL === '1';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, count = 50 } = body;

    // Використовуємо простий скрапер в продакшні
    if (isProduction) {
      const simpleScraper = new SimpleScraper();

      switch (action) {
        case 'populate':
          console.log('🚀 Симуляція наповнення бази даних...');
          const allModels = await simpleScraper.simulateAllPlatforms(Math.floor(count / 3));
          const { saveModelsToDatabase } = await import('@/lib/db');
          await saveModelsToDatabase(allModels);
          return NextResponse.json({
            success: true,
            message: `База даних успішно наповнена ${allModels.length} моделями (симуляція)!`
          });

        case 'fake-data':
          console.log('🎭 Генерація високоякісних даних...');
          const fakeModels = await simpleScraper.generateFakeData(count);
          const { saveModelsToDatabase: saveFakeModels } = await import('@/lib/db');
          await saveFakeModels(fakeModels);
          return NextResponse.json({
            success: true,
            message: `Згенеровано ${fakeModels.length} високоякісних моделей!`
          });

        case 'scrape-printables':
          console.log('📦 Симуляція скрапінгу Printables...');
          const printablesModels = await simpleScraper.simulatePrintablesScraping(count);
          const { saveModelsToDatabase: savePrintables } = await import('@/lib/db');
          await savePrintables(printablesModels);
          return NextResponse.json({
            success: true,
            message: `Симуляція: отримано ${printablesModels.length} моделей з Printables`,
            data: printablesModels
          });

        case 'scrape-thangs':
          console.log('🎯 Симуляція скрапінгу Thangs...');
          const thangsModels = await simpleScraper.simulateThangsScraping(count);
          const { saveModelsToDatabase: saveThangs } = await import('@/lib/db');
          await saveThangs(thangsModels);
          return NextResponse.json({
            success: true,
            message: `Симуляція: отримано ${thangsModels.length} моделей з Thangs`,
            data: thangsModels
          });

        case 'bright-data-scrape':
          console.log('🌟 Скрапінг з Bright Data...');
          const brightDataManager = new BrightDataScraperManager();
          const brightDataModels = await brightDataManager.scrapeAllPlatforms({
            modelsPerPlatform: count,
            platforms: ['makerworld', 'printables', 'thangs']
          });
          const { saveModelsToDatabase: saveBrightData } = await import('@/lib/db');
          await saveBrightData(brightDataModels);
          return NextResponse.json({
            success: true,
            message: `Bright Data: отримано ${brightDataModels.length} моделей`,
            data: brightDataModels,
            stats: brightDataManager.getStats()
          });

        default:
          return NextResponse.json({
            success: false,
            message: 'Невідома дія. Доступні: populate, fake-data, scrape-printables, scrape-thangs, bright-data-scrape'
          }, { status: 400 });
      }
    }

    // Використовуємо повний скрапер в розробці
    const scraperManager = new ScraperManager();

    switch (action) {
      case 'populate':
        console.log('🚀 Початок наповнення бази даних...');
        await scraperManager.populateDatabase(count);
        return NextResponse.json({
          success: true,
          message: `База даних успішно наповнена ${count} моделями!`
        });

      case 'populate-categories':
        console.log('🗂️ Наповнення за категоріями...');
        await scraperManager.populateDatabaseByCategories();
        return NextResponse.json({
          success: true,
          message: 'База даних наповнена за категоріями!'
        });

      case 'fake-data':
        console.log('🎭 Генерація фейкових даних...');
        await scraperManager.populateWithFakeData(count);
        return NextResponse.json({
          success: true,
          message: `Згенеровано ${count} фейкових моделей!`
        });

      case 'scrape-printables':
        console.log('📦 Скрапінг з Printables...');
        try {
          const printablesScraper = new (await import('@/lib/scrapers/printables-scraper')).PrintablesScraper();
          const printablesModels = await printablesScraper.scrapePopularModels(count);
          await printablesScraper.close();
          return NextResponse.json({
            success: true,
            message: `Отримано ${printablesModels.length} моделей з Printables`,
            data: printablesModels
          });
        } catch (error) {
          return NextResponse.json({
            success: false,
            message: 'Помилка скрапінгу Printables',
            error: error instanceof Error ? error.message : 'Невідома помилка'
          }, { status: 500 });
        }

      case 'scrape-thangs':
        console.log('🎯 Скрапінг з Thangs...');
        try {
          const thangsScraper = new (await import('@/lib/scrapers/thangs-scraper')).ThangsScraper();
          const thangsModels = await thangsScraper.scrapePopularModels(count);
          await thangsScraper.close();
          return NextResponse.json({
            success: true,
            message: `Отримано ${thangsModels.length} моделей з Thangs`,
            data: thangsModels
          });
        } catch (error) {
          return NextResponse.json({
            success: false,
            message: 'Помилка скрапінгу Thangs',
            error: error instanceof Error ? error.message : 'Невідома помилка'
          }, { status: 500 });
        }

      case 'bright-data-scrape':
        console.log('🌟 Скрапінг з Bright Data (Production)...');
        try {
          const brightDataManager = new BrightDataScraperManager();
          const brightDataModels = await brightDataManager.scrapeAllPlatforms({
            modelsPerPlatform: count,
            platforms: ['makerworld', 'printables', 'thangs']
          });
          const { saveModelsToDatabase: saveBrightData } = await import('@/lib/db');
          await saveBrightData(brightDataModels);
          return NextResponse.json({
            success: true,
            message: `Bright Data: отримано ${brightDataModels.length} моделей`,
            data: brightDataModels,
            stats: brightDataManager.getStats()
          });
        } catch (error) {
          return NextResponse.json({
            success: false,
            message: 'Помилка Bright Data скрапінгу',
            error: error instanceof Error ? error.message : 'Невідома помилка'
          }, { status: 500 });
        }

      default:
        return NextResponse.json({
          success: false,
          message: 'Невідома дія. Доступні: populate, populate-categories, fake-data, scrape-printables, scrape-thangs, bright-data-scrape'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('❌ Помилка API скрапінгу:', error);
    return NextResponse.json({
      success: false,
      message: 'Помилка під час скрапінгу',
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'API скрапінгу 3D моделей',
    endpoints: {
      'POST /api/scrape': {
        description: 'Запуск скрапінгу',
        actions: [
          'populate - Наповнити базу даних з усіх платформ',
          'populate-categories - Наповнити за категоріями',
          'fake-data - Згенерувати фейкові дані',
          'scrape-printables - Скрапити тільки Printables',
          'scrape-thangs - Скрапити тільки Thangs',
          'bright-data-scrape - Скрапити з Bright Data (MakerWorld, Printables, Thangs)'
        ],
        example: {
          action: 'fake-data',
          count: 100
        }
      }
    }
  });
}
