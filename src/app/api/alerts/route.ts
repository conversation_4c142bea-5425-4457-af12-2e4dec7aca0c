/**
 * Real-time Alerts API for Market Changes and Notifications
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/config';
import { AlertEngine, AlertRule } from '@/lib/alerts/alert-engine';
import { CompetitorDatabase } from '@/lib/database/competitor-db';

// Global alert engine instance
let alertEngineInstance: AlertEngine | null = null;

function getAlertEngine(): AlertEngine {
  if (!alertEngineInstance) {
    alertEngineInstance = new AlertEngine();
  }
  return alertEngineInstance;
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const userId = session.user.id;

    const alertEngine = getAlertEngine();

    switch (action) {
      case 'rules':
        const userRules = alertEngine.getUserAlertRules(userId);
        return NextResponse.json({
          success: true,
          data: userRules,
          timestamp: new Date().toISOString()
        });

      case 'notifications':
        const limit = parseInt(searchParams.get('limit') || '50');
        const notifications = alertEngine.getUserNotifications(userId, limit);
        return NextResponse.json({
          success: true,
          data: notifications,
          timestamp: new Date().toISOString()
        });

      case 'statistics':
        const stats = alertEngine.getAlertStatistics();
        return NextResponse.json({
          success: true,
          data: stats,
          timestamp: new Date().toISOString()
        });

      case 'test-alerts':
        console.log('🧪 Testing alert system...');
        
        // Generate test price change events
        const testEvents = [
          {
            id: 'test_price_change_1',
            platform: 'sketchfab',
            modelId: 'test_model_123',
            oldPrice: 25.00,
            newPrice: 35.00,
            priceChangePercentage: 40.0,
            category: 'characters',
            creatorName: 'TestCreator',
            changeType: 'increase' as const,
            significance: 'major' as const,
            detectedAt: new Date().toISOString()
          }
        ];

        const testNotifications = await alertEngine.processPriceChangeEvents(testEvents);
        
        return NextResponse.json({
          success: true,
          message: 'Test alerts processed',
          data: {
            eventsProcessed: testEvents.length,
            notificationsGenerated: testNotifications.length,
            notifications: testNotifications
          },
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('❌ Alerts API GET error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, rule, ruleId } = body;
    const userId = session.user.id;

    const alertEngine = getAlertEngine();

    switch (action) {
      case 'create-rule':
        if (!rule) {
          return NextResponse.json(
            { success: false, error: 'Alert rule data required' },
            { status: 400 }
          );
        }

        const newRule: AlertRule = {
          id: `alert_rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          userId,
          name: rule.name,
          description: rule.description,
          ruleType: rule.ruleType,
          platform: rule.platform,
          category: rule.category,
          conditions: rule.conditions,
          notificationChannels: rule.notificationChannels || ['websocket'],
          frequency: rule.frequency || 'immediate',
          severity: rule.severity || 'medium',
          enabled: rule.enabled !== false,
          triggerCount: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        alertEngine.addAlertRule(newRule);

        return NextResponse.json({
          success: true,
          message: 'Alert rule created successfully',
          ruleId: newRule.id,
          rule: newRule,
          timestamp: new Date().toISOString()
        });

      case 'update-rule':
        if (!ruleId) {
          return NextResponse.json(
            { success: false, error: 'Rule ID required' },
            { status: 400 }
          );
        }

        // In a real implementation, you'd update the rule in the database
        // For now, we'll simulate the update
        const updatedRule = {
          ...rule,
          id: ruleId,
          userId,
          updatedAt: new Date().toISOString()
        };

        return NextResponse.json({
          success: true,
          message: 'Alert rule updated successfully',
          rule: updatedRule,
          timestamp: new Date().toISOString()
        });

      case 'test-rule':
        if (!ruleId) {
          return NextResponse.json(
            { success: false, error: 'Rule ID required' },
            { status: 400 }
          );
        }

        console.log(`🧪 Testing alert rule: ${ruleId}`);
        
        // Simulate rule testing
        const testResult = {
          ruleId,
          testPassed: true,
          simulatedTrigger: {
            type: 'price_change',
            data: {
              platform: 'sketchfab',
              category: 'characters',
              priceChange: 25.5
            },
            wouldTrigger: true,
            estimatedNotifications: 1
          }
        };

        return NextResponse.json({
          success: true,
          message: 'Alert rule test completed',
          testResult,
          timestamp: new Date().toISOString()
        });

      case 'bulk-create-rules':
        const { rules } = body;
        if (!rules || !Array.isArray(rules)) {
          return NextResponse.json(
            { success: false, error: 'Rules array required' },
            { status: 400 }
          );
        }

        const createdRules = rules.map(ruleData => {
          const rule: AlertRule = {
            id: `alert_rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            userId,
            name: ruleData.name,
            description: ruleData.description,
            ruleType: ruleData.ruleType,
            platform: ruleData.platform,
            category: ruleData.category,
            conditions: ruleData.conditions,
            notificationChannels: ruleData.notificationChannels || ['websocket'],
            frequency: ruleData.frequency || 'immediate',
            severity: ruleData.severity || 'medium',
            enabled: ruleData.enabled !== false,
            triggerCount: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };

          alertEngine.addAlertRule(rule);
          return rule;
        });

        return NextResponse.json({
          success: true,
          message: `${createdRules.length} alert rules created successfully`,
          rules: createdRules,
          timestamp: new Date().toISOString()
        });

      case 'acknowledge-notification':
        const { notificationId } = body;
        if (!notificationId) {
          return NextResponse.json(
            { success: false, error: 'Notification ID required' },
            { status: 400 }
          );
        }

        // In a real implementation, you'd update the notification status in the database
        console.log(`✅ Acknowledged notification: ${notificationId}`);

        return NextResponse.json({
          success: true,
          message: 'Notification acknowledged',
          notificationId,
          acknowledgedAt: new Date().toISOString(),
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('❌ Alerts API POST error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const ruleId = searchParams.get('ruleId');

    if (!ruleId) {
      return NextResponse.json(
        { success: false, error: 'Rule ID parameter required' },
        { status: 400 }
      );
    }

    const alertEngine = getAlertEngine();
    const removed = alertEngine.removeAlertRule(ruleId);

    if (!removed) {
      return NextResponse.json(
        { success: false, error: 'Alert rule not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Alert rule deleted successfully',
      ruleId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Alerts API DELETE error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, ruleId, enabled } = body;

    if (!ruleId) {
      return NextResponse.json(
        { success: false, error: 'Rule ID required' },
        { status: 400 }
      );
    }

    const alertEngine = getAlertEngine();

    switch (action) {
      case 'toggle-rule':
        // In a real implementation, you'd update the rule in the database
        console.log(`🔄 Toggling alert rule: ${ruleId}`);

        return NextResponse.json({
          success: true,
          message: `Alert rule ${enabled ? 'enabled' : 'disabled'}`,
          ruleId,
          enabled,
          timestamp: new Date().toISOString()
        });

      case 'reset-trigger-count':
        // Reset the trigger count for a rule
        console.log(`🔄 Resetting trigger count for rule: ${ruleId}`);

        return NextResponse.json({
          success: true,
          message: 'Trigger count reset successfully',
          ruleId,
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('❌ Alerts API PUT error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
