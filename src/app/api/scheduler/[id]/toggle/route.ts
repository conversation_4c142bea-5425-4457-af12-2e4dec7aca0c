/**
 * API для увімкнення/вимкнення завдань планувальника
 */

import { NextRequest, NextResponse } from 'next/server';
import { SchedulerManager } from '@/lib/scheduler/scheduler-manager';

// Глобальний екземпляр планувальника
let schedulerManager: SchedulerManager | null = null;

function getSchedulerManager(): SchedulerManager {
  if (!schedulerManager) {
    schedulerManager = new SchedulerManager();
  }
  return schedulerManager;
}

/**
 * POST /api/scheduler/[id]/toggle - Увімкнення/вимкнення завдання
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { enabled } = body;

    // Валідація параметра enabled
    if (typeof enabled !== 'boolean') {
      return NextResponse.json({
        success: false,
        message: 'Параметр "enabled" повинен бути boolean'
      }, { status: 400 });
    }

    const manager = getSchedulerManager();
    
    // Перевіряємо, чи існує завдання
    const job = manager.getJob(id);
    if (!job) {
      return NextResponse.json({
        success: false,
        message: `Завдання з ID ${id} не знайдено`
      }, { status: 404 });
    }

    // Оновлюємо статус завдання
    const updatedJob = await manager.toggleJob(id, enabled);

    const action = enabled ? 'увімкнено' : 'вимкнено';
    console.log(`🔄 Завдання "${updatedJob.name}" ${action}`);

    return NextResponse.json({
      success: true,
      message: `Завдання "${updatedJob.name}" ${action}`,
      job: {
        id: updatedJob.id,
        name: updatedJob.name,
        enabled: updatedJob.enabled,
        nextRun: updatedJob.nextRun
      }
    });

  } catch (error) {
    console.error('❌ Помилка зміни статусу завдання:', error);
    
    if (error instanceof Error && error.message.includes('не знайдено')) {
      return NextResponse.json({
        success: false,
        message: error.message
      }, { status: 404 });
    }

    return NextResponse.json({
      success: false,
      message: 'Помилка зміни статусу завдання',
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * GET /api/scheduler/[id]/toggle - Отримання поточного статусу завдання
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const manager = getSchedulerManager();
    const job = manager.getJob(id);

    if (!job) {
      return NextResponse.json({
        success: false,
        message: `Завдання з ID ${id} не знайдено`
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      job: {
        id: job.id,
        name: job.name,
        enabled: job.enabled,
        nextRun: job.nextRun,
        lastRun: job.lastRun
      }
    });

  } catch (error) {
    console.error('❌ Помилка отримання статусу завдання:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Помилка отримання статусу завдання',
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}
