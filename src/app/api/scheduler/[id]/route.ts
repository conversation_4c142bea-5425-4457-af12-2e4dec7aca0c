/**
 * API для управління окремими завданнями планувальника
 */

import { NextRequest, NextResponse } from 'next/server';
import { SchedulerManager } from '@/lib/scheduler/scheduler-manager';
import { UpdateSchedulerJobRequest } from '@/lib/scheduler/types';
import { validateCronExpression } from '@/lib/scheduler/cron-utils';

// Глобальний екземпляр планувальника
let schedulerManager: SchedulerManager | null = null;

function getSchedulerManager(): SchedulerManager {
  if (!schedulerManager) {
    schedulerManager = new SchedulerManager();
  }
  return schedulerManager;
}

/**
 * GET /api/scheduler/[id] - Отримання завдання за ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const includeStatus = searchParams.get('status') === 'true';
    const includeLogs = searchParams.get('logs') === 'true';

    const manager = getSchedulerManager();
    const job = manager.getJob(id);

    if (!job) {
      return NextResponse.json({
        success: false,
        message: `Завдання з ID ${id} не знайдено`
      }, { status: 404 });
    }

    let response: any = {
      success: true,
      job
    };

    if (includeStatus) {
      response.status = await manager.getJobStatus(id);
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('❌ Помилка отримання завдання планувальника:', error);
    return NextResponse.json({
      success: false,
      message: 'Помилка отримання завдання планувальника',
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * PUT /api/scheduler/[id] - Оновлення завдання
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body: UpdateSchedulerJobRequest = await request.json();

    // Валідація cron виразу, якщо він передається
    if (body.cronExpression && !validateCronExpression(body.cronExpression)) {
      return NextResponse.json({
        success: false,
        message: `Невірний cron вираз: ${body.cronExpression}`
      }, { status: 400 });
    }

    // Валідація платформ, якщо вони передаються
    if (body.platforms) {
      const validPlatforms = ['makerworld', 'printables', 'thangs'];
      const invalidPlatforms = body.platforms.filter(p => !validPlatforms.includes(p));
      if (invalidPlatforms.length > 0) {
        return NextResponse.json({
          success: false,
          message: `Невірні платформи: ${invalidPlatforms.join(', ')}`
        }, { status: 400 });
      }
    }

    // Валідація кількості моделей
    if (body.modelsPerPlatform && (body.modelsPerPlatform < 1 || body.modelsPerPlatform > 100)) {
      return NextResponse.json({
        success: false,
        message: 'Кількість моделей повинна бути від 1 до 100'
      }, { status: 400 });
    }

    const manager = getSchedulerManager();
    const updatedJob = await manager.updateJob(id, body);

    return NextResponse.json({
      success: true,
      message: `Завдання "${updatedJob.name}" оновлено успішно`,
      job: updatedJob
    });
  } catch (error) {
    console.error('❌ Помилка оновлення завдання планувальника:', error);
    
    if (error instanceof Error && error.message.includes('не знайдено')) {
      return NextResponse.json({
        success: false,
        message: error.message
      }, { status: 404 });
    }

    return NextResponse.json({
      success: false,
      message: 'Помилка оновлення завдання планувальника',
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/scheduler/[id] - Видалення завдання
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const manager = getSchedulerManager();
    await manager.deleteJob(id);

    return NextResponse.json({
      success: true,
      message: `Завдання з ID ${id} видалено успішно`
    });
  } catch (error) {
    console.error('❌ Помилка видалення завдання планувальника:', error);
    
    if (error instanceof Error && error.message.includes('не знайдено')) {
      return NextResponse.json({
        success: false,
        message: error.message
      }, { status: 404 });
    }

    return NextResponse.json({
      success: false,
      message: 'Помилка видалення завдання планувальника',
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}
