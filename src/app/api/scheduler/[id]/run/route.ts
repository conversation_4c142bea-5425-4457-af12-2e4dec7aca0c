/**
 * API для ручного запуску завдань планувальника
 */

import { NextRequest, NextResponse } from 'next/server';
import { SchedulerManager } from '@/lib/scheduler/scheduler-manager';

// Глобальний екземпляр планувальника
let schedulerManager: SchedulerManager | null = null;

function getSchedulerManager(): SchedulerManager {
  if (!schedulerManager) {
    schedulerManager = new SchedulerManager();
  }
  return schedulerManager;
}

/**
 * POST /api/scheduler/[id]/run - Ручний запуск завдання
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const manager = getSchedulerManager();
    
    // Перевіряємо, чи існує завдання
    const job = manager.getJob(id);
    if (!job) {
      return NextResponse.json({
        success: false,
        message: `Завдання з ID ${id} не знайдено`
      }, { status: 404 });
    }

    // Перевіряємо, чи не виконується вже завдання
    const status = await manager.getJobStatus(id);
    if (status.isRunning) {
      return NextResponse.json({
        success: false,
        message: `Завдання "${job.name}" вже виконується`
      }, { status: 409 });
    }

    console.log(`🚀 Ручний запуск завдання: ${job.name} (${id})`);

    // Запускаємо завдання асинхронно
    const logPromise = manager.runJobManually(id);

    // Повертаємо відповідь одразу, не чекаючи завершення
    return NextResponse.json({
      success: true,
      message: `Завдання "${job.name}" запущено вручну`,
      job: {
        id: job.id,
        name: job.name,
        status: 'running'
      }
    });

  } catch (error) {
    console.error('❌ Помилка ручного запуску завдання:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('не знайдено')) {
        return NextResponse.json({
          success: false,
          message: error.message
        }, { status: 404 });
      }
      
      if (error.message.includes('вже виконується')) {
        return NextResponse.json({
          success: false,
          message: error.message
        }, { status: 409 });
      }
    }

    return NextResponse.json({
      success: false,
      message: 'Помилка ручного запуску завдання',
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * GET /api/scheduler/[id]/run - Отримання статусу виконання завдання
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const manager = getSchedulerManager();
    const status = await manager.getJobStatus(id);

    return NextResponse.json({
      success: true,
      status: {
        isRunning: status.isRunning,
        lastRun: status.lastLog?.startTime,
        lastStatus: status.lastLog?.status,
        modelsScraped: status.lastLog?.modelsScraped || 0,
        executionTime: status.lastLog?.executionTime,
        errorMessage: status.lastLog?.errorMessage,
        nextRun: status.nextRunTime
      }
    });

  } catch (error) {
    console.error('❌ Помилка отримання статусу завдання:', error);
    
    if (error instanceof Error && error.message.includes('не знайдено')) {
      return NextResponse.json({
        success: false,
        message: error.message
      }, { status: 404 });
    }

    return NextResponse.json({
      success: false,
      message: 'Помилка отримання статусу завдання',
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}
