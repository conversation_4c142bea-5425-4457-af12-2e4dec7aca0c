/**
 * API для управління планувальником скрапінгу
 */

import { NextRequest, NextResponse } from 'next/server';
import { SchedulerManager } from '@/lib/scheduler/scheduler-manager';
import { CreateSchedulerJobRequest } from '@/lib/scheduler/types';
import { validateCronExpression } from '@/lib/scheduler/cron-utils';

// Глобальний екземпляр планувальника
let schedulerManager: SchedulerManager | null = null;

function getSchedulerManager(): SchedulerManager {
  if (!schedulerManager) {
    schedulerManager = new SchedulerManager();
  }
  return schedulerManager;
}

/**
 * GET /api/scheduler - Отримання всіх завдань та статистики
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeStats = searchParams.get('stats') === 'true';
    const includeStatus = searchParams.get('status') === 'true';

    const manager = getSchedulerManager();
    const jobs = manager.getAllJobs();

    let response: any = {
      success: true,
      jobs,
      total: jobs.length,
    };

    if (includeStats) {
      response.stats = await manager.getStats();
    }

    if (includeStatus) {
      const jobStatuses = await Promise.all(
        jobs.map(job => manager.getJobStatus(job.id))
      );
      response.jobStatuses = jobStatuses;
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('❌ Помилка отримання завдань планувальника:', error);
    return NextResponse.json({
      success: false,
      message: 'Помилка отримання завдань планувальника',
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * POST /api/scheduler - Створення нового завдання
 */
export async function POST(request: NextRequest) {
  try {
    const body: CreateSchedulerJobRequest = await request.json();

    // Валідація обов'язкових полів
    if (!body.name || !body.cronExpression || !body.platforms || body.platforms.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'Обов\'язкові поля: name, cronExpression, platforms'
      }, { status: 400 });
    }

    // Валідація cron виразу
    if (!validateCronExpression(body.cronExpression)) {
      return NextResponse.json({
        success: false,
        message: `Невірний cron вираз: ${body.cronExpression}`
      }, { status: 400 });
    }

    // Валідація платформ
    const validPlatforms = ['makerworld', 'printables', 'thangs'];
    const invalidPlatforms = body.platforms.filter(p => !validPlatforms.includes(p));
    if (invalidPlatforms.length > 0) {
      return NextResponse.json({
        success: false,
        message: `Невірні платформи: ${invalidPlatforms.join(', ')}`
      }, { status: 400 });
    }

    // Валідація кількості моделей
    if (body.modelsPerPlatform && (body.modelsPerPlatform < 1 || body.modelsPerPlatform > 100)) {
      return NextResponse.json({
        success: false,
        message: 'Кількість моделей повинна бути від 1 до 100'
      }, { status: 400 });
    }

    const manager = getSchedulerManager();
    const job = await manager.createJob(body);

    return NextResponse.json({
      success: true,
      message: `Завдання "${job.name}" створено успішно`,
      job
    });
  } catch (error) {
    console.error('❌ Помилка створення завдання планувальника:', error);
    return NextResponse.json({
      success: false,
      message: 'Помилка створення завдання планувальника',
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/scheduler - Видалення всіх завдань (тільки для розробки)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Тільки в режимі розробки
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({
        success: false,
        message: 'Операція недоступна в продакшні'
      }, { status: 403 });
    }

    const manager = getSchedulerManager();
    const jobs = manager.getAllJobs();

    for (const job of jobs) {
      await manager.deleteJob(job.id);
    }

    return NextResponse.json({
      success: true,
      message: `Видалено ${jobs.length} завдань`,
      deletedCount: jobs.length
    });
  } catch (error) {
    console.error('❌ Помилка видалення завдань планувальника:', error);
    return NextResponse.json({
      success: false,
      message: 'Помилка видалення завдань планувальника',
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}
