import { hashPassword } from '@/lib/auth';
import { execute, generateId, getDb, queryOne } from '@/lib/db';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Схема валідації для реєстрації
const registerSchema = z.object({
  name: z.string().min(2, { message: "Ім'я має містити щонайменше 2 символи" }),
  email: z.string().email({ message: 'Невірний формат email' }),
  password: z.string().min(8, { message: 'Пароль має містити щонайменше 8 символів' }),
  userType: z.enum(['buyer', 'seller', 'both']).default('buyer'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Валідація даних за допомогою Zod
    const result = registerSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { success: false, message: result.error.errors[0].message },
        { status: 400 }
      );
    }

    const { name, email, password, userType } = result.data;

    // Перевірка, чи доступна база даних
    const db = getDb();
    if (!db) {
      console.warn('База даних недоступна. Використовується тестовий режим реєстрації.');

      // В режимі розробки імітуємо успішну реєстрацію
      if (email === '<EMAIL>') {
        return NextResponse.json(
          { success: false, message: 'Користувач з таким email вже існує' },
          { status: 409 }
        );
      }

      const userId = generateId();

      return NextResponse.json({
        success: true,
        message: 'Користувач успішно зареєстрований (тестовий режим)',
        data: {
          id: userId,
          email,
          name,
          userType,
        }
      }, { status: 201 });
    }

    // Перевірка, чи існує користувач з такою електронною поштою
    const existingUser = await queryOne(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUser) {
      return NextResponse.json(
        { success: false, message: 'Користувач з таким email вже існує' },
        { status: 409 }
      );
    }

    // Хешування пароля
    const hashedPassword = await hashPassword(password);

    // Створення нового користувача
    const userId = generateId();

    await execute(
      `INSERT INTO users (
        id, email, name, password, user_type, email_verified, is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
      [
        userId,
        email,
        name,
        hashedPassword,
        userType,
        false,
        true
      ]
    );

    // Створення базового профілю користувача
    const profileId = generateId();
    await execute(
      `INSERT INTO user_profiles (
        id, user_id, created_at, updated_at
      ) VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
      [profileId, userId]
    );

    // Якщо користувач - продавець, створюємо профіль продавця
    let sellerProfileId = null;
    if (userType === 'seller' || userType === 'both') {
      sellerProfileId = generateId();
      await execute(
        `INSERT INTO seller_profiles (
          id, user_id, business_type, seller_rating, total_sales, total_models,
          total_downloads, total_revenue, commission_rate, seller_tier,
          is_featured, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        [
          sellerProfileId,
          userId,
          'individual',
          0.0,
          0,
          0,
          0,
          0.0,
          0.15,
          'bronze',
          false
        ]
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Користувач успішно зареєстрований',
      data: {
        id: userId,
        email,
        name,
        userType,
        profileId,
        sellerProfileId,
      }
    }, { status: 201 });
  } catch (error) {
    console.error('Помилка при реєстрації користувача:', error);
    return NextResponse.json(
      { success: false, message: 'Сталася помилка при реєстрації. Спробуйте ще раз.' },
      { status: 500 }
    );
  }
}
