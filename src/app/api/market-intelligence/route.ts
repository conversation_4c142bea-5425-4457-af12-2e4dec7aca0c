/**
 * API endpoint для Market Intelligence з використанням Bright Data
 */

import { authOptions } from '@/lib/auth/config';
import { MarketIntelligenceService } from '@/lib/bright-data/market-intelligence';
import { getServerSession } from 'next-auth';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const category = searchParams.get('category');

    const marketService = new MarketIntelligenceService();

    switch (action) {
      case 'competitors':
        const useRealData = searchParams.get('real') === 'true';
        const competitors = await marketService.analyzeCompetitors(useRealData);
        return NextResponse.json({
          success: true,
          data: competitors,
          timestamp: new Date(),
          dataSource: useRealData ? 'bright-data' : 'mock'
        });

      case 'trends':
        const trends = await marketService.analyzeMarketTrends();
        return NextResponse.json({
          success: true,
          data: trends,
          timestamp: new Date()
        });

      case 'pricing':
        if (!category) {
          return NextResponse.json(
            { success: false, error: 'Category parameter required for pricing insights' },
            { status: 400 }
          );
        }
        const pricingInsights = await marketService.generatePricingInsights(category);
        return NextResponse.json({
          success: true,
          data: pricingInsights,
          timestamp: new Date()
        });

      case 'keywords':
        const keywords = await marketService.getPopularKeywords(category || undefined);
        return NextResponse.json({
          success: true,
          data: keywords,
          timestamp: new Date()
        });

      case 'swot':
        const swotAnalysis = await marketService.analyzeCompetitiveAdvantages();
        return NextResponse.json({
          success: true,
          data: swotAnalysis,
          timestamp: new Date()
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Market intelligence API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        timestamp: new Date()
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, category, url } = body;

    const marketService = new MarketIntelligenceService();

    switch (action) {
      case 'analyze_competitor':
        // Тут би використовувався Bright Data для скрапінгу конкретного конкурента
        return NextResponse.json({
          success: true,
          data: {
            message: 'Competitor analysis initiated',
            url,
            estimatedTime: '5-10 minutes'
          },
          timestamp: new Date()
        });

      case 'track_prices':
        // Відстеження цін конкурентів
        return NextResponse.json({
          success: true,
          data: {
            message: 'Price tracking initiated',
            category,
            trackingInterval: '24 hours'
          },
          timestamp: new Date()
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Market intelligence POST API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        timestamp: new Date()
      },
      { status: 500 }
    );
  }
}
