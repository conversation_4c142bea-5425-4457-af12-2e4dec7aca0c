/**
 * API endpoint для тестування Bright Data MCP tools
 */

import { NextRequest, NextResponse } from 'next/server';
import { BrightDataScraper } from '@/lib/bright-data/scraper';
import { BrightDataScraperManager } from '@/lib/bright-data/scraper-manager';
import { MarketIntelligenceService } from '@/lib/bright-data/market-intelligence';
import { BrightDataTestSuite } from '@/lib/bright-data/test-integration';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action') || 'info';

  try {
    switch (action) {
      case 'info':
        return NextResponse.json({
          success: true,
          message: 'Bright Data MCP Test API',
          availableActions: [
            'test-scraper - Тестування базового скрапера',
            'test-manager - Тестування менеджера скраперів',
            'test-intelligence - Тестування Market Intelligence',
            'test-mcp-tools - Тестування MCP tools',
            'quick-test - Швидкий тест',
            'full-test - Повний тест'
          ],
          timestamp: new Date().toISOString()
        });

      case 'test-scraper':
        return await testBasicScraper();

      case 'test-manager':
        return await testScraperManager();

      case 'test-intelligence':
        return await testMarketIntelligence();

      case 'test-mcp-tools':
        return await testMCPTools();

      case 'quick-test':
        return await runQuickTest();

      case 'full-test':
        return await runFullTest();

      default:
        return NextResponse.json({
          success: false,
          error: 'Невідома дія',
          availableActions: ['test-scraper', 'test-manager', 'test-intelligence', 'test-mcp-tools', 'quick-test', 'full-test']
        }, { status: 400 });
    }
  } catch (error) {
    console.error('❌ Помилка Bright Data Test API:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Невідома помилка',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Тестування базового скрапера
 */
async function testBasicScraper() {
  const scraper = new BrightDataScraper();
  const results: any = {};

  try {
    console.log('🧪 Тестування базового Bright Data скрапера...');

    // Тест скрапінгу Markdown
    const markdownResult = await scraper.scrapeAsMarkdown('https://www.printables.com/model/123456');
    results.markdown = {
      success: markdownResult.success,
      hasData: !!markdownResult.data,
      platform: markdownResult.metadata?.platform
    };

    // Тест скрапінгу HTML
    const htmlResult = await scraper.scrapeAsHtml('https://makerworld.com/en/models/123456');
    results.html = {
      success: htmlResult.success,
      hasData: !!htmlResult.data,
      platform: htmlResult.metadata?.platform
    };

    // Тест пошуку
    const searchResult = await scraper.searchEngine('3D printing miniatures');
    results.search = {
      success: searchResult.success,
      hasData: !!searchResult.data,
      resultsCount: searchResult.data?.results?.length || 0
    };

    // Тест статистики
    const stats = await scraper.getSessionStats();
    results.stats = {
      hasData: !!stats,
      sessionId: stats?.session_id
    };

    return NextResponse.json({
      success: true,
      message: 'Тестування базового скрапера завершено',
      results,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Помилка тестування',
      results,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Тестування менеджера скраперів
 */
async function testScraperManager() {
  const manager = new BrightDataScraperManager();
  const results: any = {};

  try {
    console.log('🧪 Тестування менеджера скраперів...');

    // Тест скрапінгу популярних моделей
    const popularModels = await manager.scrapePopularModels({
      modelsPerPlatform: 2,
      platforms: ['printables', 'makerworld']
    });

    results.popularModels = {
      count: popularModels.length,
      platforms: [...new Set(popularModels.map(m => m.platform))]
    };

    // Тест пошуку
    const searchModels = await manager.searchAllPlatforms('miniature', {
      modelsPerPlatform: 1
    });

    results.search = {
      count: searchModels.length,
      platforms: [...new Set(searchModels.map(m => m.platform))]
    };

    // Статистика
    const stats = manager.getStats();
    results.stats = stats;

    return NextResponse.json({
      success: true,
      message: 'Тестування менеджера скраперів завершено',
      results,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Помилка тестування',
      results,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Тестування Market Intelligence
 */
async function testMarketIntelligence() {
  const intelligence = new MarketIntelligenceService();
  const results: any = {};

  try {
    console.log('🧪 Тестування Market Intelligence...');

    // Тест аналізу конкурентів (mock)
    const mockCompetitors = await intelligence.analyzeCompetitors(false);
    results.mockCompetitors = {
      count: mockCompetitors.length,
      dataSource: mockCompetitors[0]?.dataSource
    };

    // Тест аналізу конкурентів (реальні дані)
    const realCompetitors = await intelligence.analyzeCompetitors(true);
    results.realCompetitors = {
      count: realCompetitors.length,
      dataSource: realCompetitors[0]?.dataSource
    };

    // Тест трендів
    const trends = await intelligence.analyzeMarketTrends();
    results.trends = {
      count: trends.length
    };

    return NextResponse.json({
      success: true,
      message: 'Тестування Market Intelligence завершено',
      results,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Помилка тестування',
      results,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Тестування MCP tools
 */
async function testMCPTools() {
  const scraper = new BrightDataScraper();
  const results: any = {};

  try {
    console.log('🧪 Тестування MCP tools...');

    // Тест навігації браузером
    const browserResult = await scraper.navigateBrowser('https://www.printables.com');
    results.browser = {
      success: browserResult.success,
      hasNavigation: !!browserResult.data?.navigation,
      hasContent: !!browserResult.data?.content
    };

    // Тест структурованих даних
    const structuredResult = await scraper.scrapeStructuredData(
      'https://www.amazon.com/dp/B08N5WRWNW',
      'amazon_product'
    );
    results.structured = {
      success: structuredResult.success,
      hasData: !!structuredResult.data
    };

    return NextResponse.json({
      success: true,
      message: 'Тестування MCP tools завершено',
      results,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Помилка тестування',
      results,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Швидкий тест
 */
async function runQuickTest() {
  try {
    console.log('⚡ Швидкий тест Bright Data...');
    
    const testSuite = new BrightDataTestSuite();
    await testSuite.quickTest();

    return NextResponse.json({
      success: true,
      message: 'Швидкий тест завершено успішно',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Помилка швидкого тесту',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Повний тест
 */
async function runFullTest() {
  try {
    console.log('🚀 Повний тест Bright Data...');
    
    const testSuite = new BrightDataTestSuite();
    await testSuite.runAllTests();

    return NextResponse.json({
      success: true,
      message: 'Повний тест завершено успішно',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Помилка повного тесту',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
