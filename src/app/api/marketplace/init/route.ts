import { NextRequest, NextResponse } from 'next/server';
import { initializeMarketplace, isMarketplaceInitialized, forceInitializeMarketplace } from '@/lib/marketplace/auto-init';
import { getTrendingModelsLoader } from '@/lib/marketplace/trending-models-loader';

/**
 * GET - Перевіряє статус ініціалізації маркетплейсу
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'status':
        // Статус ініціалізації
        const isInitialized = isMarketplaceInitialized();
        const loader = getTrendingModelsLoader();
        const loaderStatus = loader.getStatus();

        return NextResponse.json({
          success: true,
          data: {
            isInitialized,
            autoLoading: loaderStatus,
            message: isInitialized 
              ? 'Маркетплейс ініціалізований та готовий до роботи' 
              : 'Маркетплейс не ініціалізований'
          }
        });

      default:
        // Загальна інформація
        return NextResponse.json({
          success: true,
          data: {
            message: 'API ініціалізації справжнього маркетплейсу',
            endpoints: {
              'GET ?action=status': 'Статус ініціалізації',
              'POST': 'Ініціалізація маркетплейсу',
              'PUT': 'Примусова ініціалізація'
            },
            features: [
              'Автоматична ініціалізація бази даних',
              'Запуск автоматичного завантаження трендових моделей',
              'Інтеграція з Bright Data MCP',
              'Підтримка кількох платформ скрапінгу'
            ]
          }
        });
    }

  } catch (error) {
    console.error('❌ Помилка GET /api/marketplace/init:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Помилка перевірки статусу ініціалізації',
        details: error instanceof Error ? error.message : 'Невідома помилка'
      },
      { status: 500 }
    );
  }
}

/**
 * POST - Ініціалізує справжній маркетплейс
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Ініціалізація справжнього маркетплейсу...');

    // Перевіряємо, чи вже ініціалізований
    if (isMarketplaceInitialized()) {
      return NextResponse.json({
        success: true,
        data: {
          message: 'Маркетплейс вже ініціалізований',
          status: 'already_initialized'
        }
      });
    }

    // Запускаємо ініціалізацію
    await initializeMarketplace();

    // Перевіряємо статус після ініціалізації
    const loader = getTrendingModelsLoader();
    const loaderStatus = loader.getStatus();

    return NextResponse.json({
      success: true,
      data: {
        message: 'Справжній маркетплейс успішно ініціалізований!',
        status: 'initialized',
        autoLoading: loaderStatus,
        features: [
          'База даних моделей створена',
          'Автоматичне завантаження трендових моделей запущено',
          'Bright Data MCP інтеграція активна',
          'Підтримка Printables, MakerWorld, Thangs'
        ]
      }
    });

  } catch (error) {
    console.error('❌ Помилка POST /api/marketplace/init:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Помилка ініціалізації маркетплейсу',
        details: error instanceof Error ? error.message : 'Невідома помилка'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT - Примусова ініціалізація (навіть якщо вже ініціалізований)
 */
export async function PUT(request: NextRequest) {
  try {
    console.log('🔄 Примусова ініціалізація справжнього маркетплейсу...');

    // Примусова ініціалізація
    await forceInitializeMarketplace();

    // Перевіряємо статус після ініціалізації
    const loader = getTrendingModelsLoader();
    const loaderStatus = loader.getStatus();

    return NextResponse.json({
      success: true,
      data: {
        message: 'Примусова ініціалізація завершена успішно!',
        status: 'force_initialized',
        autoLoading: loaderStatus,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Помилка PUT /api/marketplace/init:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Помилка примусової ініціалізації',
        details: error instanceof Error ? error.message : 'Невідома помилка'
      },
      { status: 500 }
    );
  }
}
