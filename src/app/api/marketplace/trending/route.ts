import { NextRequest, NextResponse } from 'next/server';
import { getTrendingModelsLoader, startTrendingModelsAutoLoad } from '@/lib/marketplace/trending-models-loader';

/**
 * GET - Отримує статус автоматичного завантаження трендових моделей
 */

// Note: Edge runtime disabled for OpenNext compatibility
// export const runtime = 'edge';
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    const loader = getTrendingModelsLoader();

    switch (action) {
      case 'status':
        // Статус завантажувача
        const status = loader.getStatus();
        return NextResponse.json({
          success: true,
          data: {
            isRunning: status.isRunning,
            lastUpdate: status.lastUpdate,
            config: status.config,
            message: status.isRunning ? 'Автоматичне завантаження активне' : 'Автоматичне завантаження зупинено'
          }
        });

      case 'load':
        // Ручне завантаження трендових моделей
        console.log('🚀 Ручний запуск завантаження трендових моделей');
        const results = await loader.loadTrendingModels();
        
        return NextResponse.json({
          success: results.success,
          data: {
            totalModels: results.totalModels,
            platformResults: results.platformResults,
            errors: results.errors,
            message: `Завантажено ${results.totalModels} трендових моделей`
          }
        });

      default:
        // Загальна інформація
        return NextResponse.json({
          success: true,
          data: {
            message: 'API автоматичного завантаження трендових моделей',
            endpoints: {
              'GET ?action=status': 'Статус завантажувача',
              'GET ?action=load': 'Ручне завантаження',
              'POST': 'Запуск автоматичного завантаження',
              'PUT': 'Оновлення конфігурації',
              'DELETE': 'Зупинка автоматичного завантаження'
            },
            platforms: ['printables', 'makerworld', 'thangs'],
            features: [
              'Автоматичне завантаження трендових моделей',
              'Регулярне оновлення',
              'Підтримка кількох платформ',
              'Збереження в D1 базу даних',
              'Конфігурація інтервалів оновлення'
            ]
          }
        });
    }

  } catch (error) {
    console.error('❌ Помилка GET /api/marketplace/trending:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Помилка отримання статусу',
        details: error instanceof Error ? error.message : 'Невідома помилка'
      },
      { status: 500 }
    );
  }
}

/**
 * POST - Запускає автоматичне завантаження трендових моделей
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      platforms = ['printables', 'makerworld', 'thangs'],
      modelsPerPlatform = 15,
      updateInterval = 120, // 2 години
      enableAI = true,
      autoStart = true
    } = body;

    console.log('🚀 Запуск автоматичного завантаження трендових моделей');

    const loader = getTrendingModelsLoader({
      platforms,
      modelsPerPlatform,
      updateInterval,
      enableAI
    });

    if (autoStart) {
      await loader.startAutoLoading();
    }

    // Перше завантаження
    const results = await loader.loadTrendingModels();

    return NextResponse.json({
      success: true,
      data: {
        message: 'Автоматичне завантаження запущено',
        config: {
          platforms,
          modelsPerPlatform,
          updateInterval,
          enableAI
        },
        initialLoad: {
          totalModels: results.totalModels,
          platformResults: results.platformResults,
          errors: results.errors
        },
        status: loader.getStatus()
      }
    });

  } catch (error) {
    console.error('❌ Помилка POST /api/marketplace/trending:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Помилка запуску автоматичного завантаження',
        details: error instanceof Error ? error.message : 'Невідома помилка'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT - Оновлює конфігурацію автоматичного завантаження
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      platforms,
      modelsPerPlatform,
      updateInterval,
      enableAI
    } = body;

    const loader = getTrendingModelsLoader();
    
    // Оновлюємо конфігурацію
    loader.updateConfig({
      platforms,
      modelsPerPlatform,
      updateInterval,
      enableAI
    });

    return NextResponse.json({
      success: true,
      data: {
        message: 'Конфігурацію оновлено',
        newConfig: loader.getStatus().config
      }
    });

  } catch (error) {
    console.error('❌ Помилка PUT /api/marketplace/trending:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Помилка оновлення конфігурації',
        details: error instanceof Error ? error.message : 'Невідома помилка'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE - Зупиняє автоматичне завантаження
 */
export async function DELETE(request: NextRequest) {
  try {
    const loader = getTrendingModelsLoader();
    loader.stopAutoLoading();

    return NextResponse.json({
      success: true,
      data: {
        message: 'Автоматичне завантаження зупинено',
        status: loader.getStatus()
      }
    });

  } catch (error) {
    console.error('❌ Помилка DELETE /api/marketplace/trending:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Помилка зупинки автоматичного завантаження',
        details: error instanceof Error ? error.message : 'Невідома помилка'
      },
      { status: 500 }
    );
  }
}
