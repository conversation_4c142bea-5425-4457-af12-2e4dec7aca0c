import { NextRequest, NextResponse } from 'next/server';
import { query, queryOne } from '@/lib/db';

// GET handler for marketplace statistics

// Note: Edge runtime disabled for OpenNext compatibility
// export const runtime = 'edge';
export async function GET(request: NextRequest) {
  try {
    // Основна статистика
    const basicStats = await queryOne(`
      SELECT 
        COUNT(*) as total_models,
        SUM(CASE WHEN is_free = 1 THEN 1 ELSE 0 END) as free_models,
        SUM(CASE WHEN is_free = 0 THEN 1 ELSE 0 END) as paid_models,
        SUM(CASE WHEN is_featured = 1 THEN 1 ELSE 0 END) as featured_models,
        SUM(download_count) as total_downloads,
        SUM(view_count) as total_views,
        SUM(like_count) as total_likes,
        SUM(CASE WHEN is_free = 0 THEN price * download_count ELSE 0 END) as total_revenue
      FROM models
    `);

    // Кількість користувачів
    const userStats = await queryOne(`
      SELECT COUNT(*) as total_users
      FROM users
    `);

    // Статистика за місяць
    const monthlyStats = await queryOne(`
      SELECT 
        COUNT(*) as models_this_month,
        SUM(download_count) as downloads_this_month
      FROM models 
      WHERE created_at >= datetime('now', '-1 month')
    `);

    const monthlyUsers = await queryOne(`
      SELECT COUNT(*) as users_this_month
      FROM users 
      WHERE created_at >= datetime('now', '-1 month')
    `);

    const monthlyRevenue = await queryOne(`
      SELECT SUM(total_amount) as revenue_this_month
      FROM orders 
      WHERE status = 'completed' 
      AND created_at >= datetime('now', '-1 month')
    `);

    // Топ категорії
    const topCategories = await query(`
      SELECT 
        category,
        COUNT(*) as count,
        (COUNT(*) * 100.0 / (SELECT COUNT(*) FROM models)) as percentage
      FROM models 
      WHERE category IS NOT NULL AND category != ''
      GROUP BY category 
      ORDER BY count DESC 
      LIMIT 5
    `);

    // Остання активність (симуляція)
    const recentActivity = await query(`
      SELECT 
        m.name as model_name,
        u.name as user_name,
        m.created_at as timestamp,
        'upload' as type
      FROM models m
      JOIN users u ON m.user_id = u.id
      ORDER BY m.created_at DESC
      LIMIT 10
    `);

    // Додаємо активність завантажень з user_models
    const downloadActivity = await query(`
      SELECT 
        m.name as model_name,
        u.name as user_name,
        um.created_at as timestamp,
        'download' as type
      FROM user_models um
      JOIN models m ON um.model_id = m.id
      JOIN users u ON um.user_id = u.id
      ORDER BY um.created_at DESC
      LIMIT 5
    `);

    // Активність покупок
    const purchaseActivity = await query(`
      SELECT 
        m.name as model_name,
        u.name as user_name,
        o.created_at as timestamp,
        'purchase' as type
      FROM orders o
      JOIN order_items oi ON o.id = oi.order_id
      JOIN models m ON oi.model_id = m.id
      JOIN users u ON o.user_id = u.id
      WHERE o.status = 'completed'
      ORDER BY o.created_at DESC
      LIMIT 5
    `);

    // Об'єднуємо всю активність
    const allActivity = [
      ...recentActivity.map((item: any) => ({
        type: item.type,
        modelName: item.model_name,
        userName: item.user_name,
        timestamp: item.timestamp
      })),
      ...downloadActivity.map((item: any) => ({
        type: item.type,
        modelName: item.model_name,
        userName: item.user_name,
        timestamp: item.timestamp
      })),
      ...purchaseActivity.map((item: any) => ({
        type: item.type,
        modelName: item.model_name,
        userName: item.user_name,
        timestamp: item.timestamp
      }))
    ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Формуємо відповідь
    const stats = {
      totalModels: (basicStats as any)?.total_models || 0,
      totalDownloads: (basicStats as any)?.total_downloads || 0,
      totalUsers: (userStats as any)?.total_users || 0,
      totalRevenue: (basicStats as any)?.total_revenue || 0,
      freeModels: (basicStats as any)?.free_models || 0,
      paidModels: (basicStats as any)?.paid_models || 0,
      featuredModels: (basicStats as any)?.featured_models || 0,
      totalViews: (basicStats as any)?.total_views || 0,
      totalLikes: (basicStats as any)?.total_likes || 0,
      averageRating: 4.5, // Поки що статичне значення
      topCategories: topCategories.map((cat: any) => ({
        name: cat.category,
        count: cat.count,
        percentage: parseFloat(cat.percentage) || 0
      })),
      recentActivity: allActivity.slice(0, 10),
      monthlyGrowth: {
        models: (monthlyStats as any)?.models_this_month || 0,
        downloads: (monthlyStats as any)?.downloads_this_month || 0,
        users: (monthlyUsers as any)?.users_this_month || 0,
        revenue: (monthlyRevenue as any)?.revenue_this_month || 0,
      }
    };

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Error fetching marketplace stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch marketplace statistics' },
      { status: 500 }
    );
  }
}
