import { NextRequest, NextResponse } from 'next/server';

interface Env {
  PROJECT_MANAGER: DurableObjectNamespace;
  DB: D1Database;
  R2_BUCKET: R2Bucket;
  ANALYTICS: AnalyticsEngineDataset;
  BACKGROUND_QUEUE: Queue;
}

function getCloudflareEnv(request: NextRequest): Env | null {
  const env = (request as any).env || (globalThis as any).env;
  return env;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    const env = getCloudflareEnv(request);
    if (!env?.PROJECT_MANAGER) {
      return NextResponse.json(
        { error: 'Cloudflare bindings not available' },
        { status: 500 }
      );
    }

    const projectId = params.projectId;
    
    // Get Durable Object instance
    const id = env.PROJECT_MANAGER.idFromName(projectId);
    const projectManager = env.PROJECT_MANAGER.get(id);

    // Forward request to Durable Object
    const response = await projectManager.fetch(new Request(request.url + '/status', {
      method: 'GET',
      headers: request.headers,
    }));

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Project manager error:', error);
    return NextResponse.json(
      { error: 'Failed to get project status' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    const env = getCloudflareEnv(request);
    if (!env?.PROJECT_MANAGER) {
      return NextResponse.json(
        { error: 'Cloudflare bindings not available' },
        { status: 500 }
      );
    }

    const projectId = params.projectId;
    const body = await request.json();
    
    // Get Durable Object instance
    const id = env.PROJECT_MANAGER.idFromName(projectId);
    const projectManager = env.PROJECT_MANAGER.get(id);

    // Create new project
    const response = await projectManager.fetch(new Request(request.url + '/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    }));

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Project creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    const env = getCloudflareEnv(request);
    if (!env?.PROJECT_MANAGER) {
      return NextResponse.json(
        { error: 'Cloudflare bindings not available' },
        { status: 500 }
      );
    }

    const projectId = params.projectId;
    const body = await request.json();
    
    // Get Durable Object instance
    const id = env.PROJECT_MANAGER.idFromName(projectId);
    const projectManager = env.PROJECT_MANAGER.get(id);

    // Update project
    const response = await projectManager.fetch(new Request(request.url + '/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    }));

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Project update error:', error);
    return NextResponse.json(
      { error: 'Failed to update project' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { projectId: string } }
) {
  try {
    const env = getCloudflareEnv(request);
    if (!env?.PROJECT_MANAGER) {
      return NextResponse.json(
        { error: 'Cloudflare bindings not available' },
        { status: 500 }
      );
    }

    const projectId = params.projectId;
    
    // Get Durable Object instance
    const id = env.PROJECT_MANAGER.idFromName(projectId);
    const projectManager = env.PROJECT_MANAGER.get(id);

    // Archive project (soft delete)
    const response = await projectManager.fetch(new Request(request.url + '/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status: 'archived' }),
    }));

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Project deletion error:', error);
    return NextResponse.json(
      { error: 'Failed to delete project' },
      { status: 500 }
    );
  }
}
