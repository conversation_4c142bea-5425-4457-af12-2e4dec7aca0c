/**
 * API endpoint для трекінгу помилок в Cloudflare Analytics
 * Інтеграція з cloudflare-observability MCP
 */

import { NextRequest, NextResponse } from 'next/server';
import { withFullApiMiddleware, CloudflareObservabilityContext } from '@/lib/cloudflare/middleware';

interface ErrorTrackingRequest {
  message: string;
  stack?: string;
  context?: string;
  userId?: string;
  timestamp?: string;
  url?: string;
  userAgent?: string;
}

async function handleErrorTracking(request: NextRequest, context: CloudflareObservabilityContext) {
  try {
    const body: ErrorTrackingRequest = await request.json();
    const { message, stack, context: errorContext, userId, timestamp, url, userAgent } = body;

    if (!message) {
      return NextResponse.json(
        { success: false, error: 'Error message is required' },
        { status: 400 }
      );
    }

    // Створюємо об'єкт помилки для трекінгу
    const error = new Error(message);
    if (stack) {
      error.stack = stack;
    }

    // Трекінг помилки через Cloudflare Analytics
    await context.trackError(error, errorContext || 'client_error');

    // Додатковий трекінг як події для детального аналізу
    await context.trackEvent('client_error', {
      message,
      stack,
      context: errorContext,
      url: url || request.headers.get('referer'),
      timestamp: timestamp || new Date().toISOString(),
      userAgent: userAgent || request.headers.get('user-agent'),
      ip: request.headers.get('cf-connecting-ip') || request.headers.get('x-forwarded-for'),
      country: request.headers.get('cf-ipcountry'),
      userId,
      severity: 'error'
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to track error:', error);
    
    // Не викликаємо context.trackError тут, щоб уникнути рекурсії
    return NextResponse.json(
      { success: false, error: 'Failed to track error' },
      { status: 500 }
    );
  }
}

export const POST = withFullApiMiddleware(handleErrorTracking);
