/**
 * API endpoint для аналітики зображень
 * Обробляє події та надає статистику
 */

import { NextRequest, NextResponse } from 'next/server';
import { ImageAnalyticsEvent, ImageAnalyticsSummary } from '@/lib/analytics/image-analytics';

// Симуляція бази даних для демо
const analyticsEvents: ImageAnalyticsEvent[] = [];
const eventStats = new Map<string, any>();

/**
 * POST /api/analytics/images - Збереження аналітичних подій
 */
export async function POST(request: NextRequest) {
  try {
    const { events }: { events: ImageAnalyticsEvent[] } = await request.json();

    if (!Array.isArray(events) || events.length === 0) {
      return NextResponse.json(
        { error: 'Invalid events data' },
        { status: 400 }
      );
    }

    // Валідація подій
    for (const event of events) {
      if (!event.type || !event.imageId || !event.sessionId) {
        return NextResponse.json(
          { error: 'Missing required event fields' },
          { status: 400 }
        );
      }
    }

    // Зберігаємо події (в реальному проекті - в базу даних)
    analyticsEvents.push(...events);

    // Оновлюємо статистику
    for (const event of events) {
      updateEventStats(event);
    }

    console.log(`📊 Збережено ${events.length} аналітичних подій`);

    // Відправляємо до Cloudflare Analytics (симуляція)
    await sendToCloudflareAnalytics(events);

    return NextResponse.json({
      success: true,
      processed: events.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Помилка обробки аналітичних подій:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/analytics/images - Отримання загальної статистики
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const start = searchParams.get('start');
    const end = searchParams.get('end');

    // Фільтруємо події за часовим діапазоном
    let filteredEvents = analyticsEvents;
    if (start && end) {
      const startDate = new Date(start);
      const endDate = new Date(end);
      
      filteredEvents = analyticsEvents.filter(event => {
        const eventDate = new Date(event.timestamp);
        return eventDate >= startDate && eventDate <= endDate;
      });
    }

    // Генеруємо статистику
    const summary = generateAnalyticsSummary(filteredEvents, start, end);

    return NextResponse.json(summary);

  } catch (error) {
    console.error('Помилка отримання аналітики:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Оновлення статистики подій
 */
function updateEventStats(event: ImageAnalyticsEvent) {
  const key = event.imageId;
  
  if (!eventStats.has(key)) {
    eventStats.set(key, {
      imageId: event.imageId,
      imageUrl: event.imageUrl,
      totalViews: 0,
      totalDownloads: 0,
      totalErrors: 0,
      loadTimes: [],
      lastUpdated: new Date().toISOString()
    });
  }

  const stats = eventStats.get(key);

  switch (event.type) {
    case 'view':
      stats.totalViews++;
      break;
    case 'download':
      stats.totalDownloads++;
      break;
    case 'error':
      stats.totalErrors++;
      break;
    case 'load':
      if (event.metadata?.loadTime) {
        stats.loadTimes.push(event.metadata.loadTime);
      }
      break;
  }

  stats.lastUpdated = new Date().toISOString();
  eventStats.set(key, stats);
}

/**
 * Генерація загальної статистики
 */
function generateAnalyticsSummary(
  events: ImageAnalyticsEvent[], 
  start?: string | null, 
  end?: string | null
): ImageAnalyticsSummary {
  const totalImages = new Set(events.map(e => e.imageId)).size;
  const totalViews = events.filter(e => e.type === 'view').length;
  const totalDownloads = events.filter(e => e.type === 'download').length;
  const totalErrors = events.filter(e => e.type === 'error').length;
  
  // Обчислюємо середній час завантаження
  const loadEvents = events.filter(e => e.type === 'load' && e.metadata?.loadTime);
  const averageLoadTime = loadEvents.length > 0
    ? loadEvents.reduce((sum, e) => sum + (e.metadata?.loadTime || 0), 0) / loadEvents.length
    : 0;

  // Обчислюємо рівень помилок
  const totalRequests = events.filter(e => ['load', 'error'].includes(e.type)).length;
  const errorRate = totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;

  // Обчислюємо cache hit rate
  const cacheHits = events.filter(e => e.type === 'load' && e.metadata?.cacheHit).length;
  const cacheHitRate = loadEvents.length > 0 ? (cacheHits / loadEvents.length) * 100 : 0;

  // Розподіл по платформах
  const platformBreakdown: Record<string, number> = {};
  events.forEach(event => {
    const platform = event.metadata?.platform || 'unknown';
    platformBreakdown[platform] = (platformBreakdown[platform] || 0) + 1;
  });

  // Розподіл по пристроях
  const deviceBreakdown: Record<string, number> = {};
  events.forEach(event => {
    const device = event.metadata?.deviceType || 'unknown';
    deviceBreakdown[device] = (deviceBreakdown[device] || 0) + 1;
  });

  // Топ зображень
  const imageStats = Array.from(eventStats.values())
    .map(stats => ({
      imageId: stats.imageId,
      imageUrl: stats.imageUrl,
      totalViews: stats.totalViews,
      totalDownloads: stats.totalDownloads,
      errorRate: stats.totalErrors > 0 ? (stats.totalErrors / (stats.totalViews + stats.totalErrors)) * 100 : 0,
      averageLoadTime: stats.loadTimes.length > 0 
        ? stats.loadTimes.reduce((a: number, b: number) => a + b, 0) / stats.loadTimes.length 
        : 0,
      cacheHitRate: 85 + Math.random() * 15, // Симуляція
      popularityScore: Math.min(100, (stats.totalViews * 0.6 + stats.totalDownloads * 0.4) / 10),
      lastUpdated: stats.lastUpdated
    }))
    .sort((a, b) => b.popularityScore - a.popularityScore)
    .slice(0, 10);

  // Симуляція використаного трафіку
  const bandwidthUsed = totalViews * 150000 + totalDownloads * 2000000; // ~150KB per view, ~2MB per download

  return {
    totalImages,
    totalViews,
    totalDownloads,
    averageLoadTime: Math.round(averageLoadTime),
    errorRate: Math.round(errorRate * 100) / 100,
    cacheHitRate: Math.round(cacheHitRate * 100) / 100,
    bandwidthUsed,
    topImages: imageStats,
    platformBreakdown,
    deviceBreakdown,
    timeRange: {
      start: start || new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      end: end || new Date().toISOString()
    }
  };
}

/**
 * Відправка до Cloudflare Analytics (симуляція)
 */
async function sendToCloudflareAnalytics(events: ImageAnalyticsEvent[]) {
  try {
    // В реальному проекті тут би був виклик Cloudflare Analytics API
    const analyticsData = events.map(event => ({
      name: `image_${event.type}`,
      value: 1,
      dimensions: {
        imageId: event.imageId,
        platform: event.metadata?.platform || 'unknown',
        deviceType: event.metadata?.deviceType || 'unknown',
        browserType: event.metadata?.browserType || 'unknown',
      },
      timestamp: event.timestamp,
    }));

    console.log(`📈 Симуляція відправки до Cloudflare Analytics: ${analyticsData.length} подій`);
    
    // Симуляція API виклику
    await new Promise(resolve => setTimeout(resolve, 100));
    
  } catch (error) {
    console.error('Помилка відправки до Cloudflare Analytics:', error);
  }
}
