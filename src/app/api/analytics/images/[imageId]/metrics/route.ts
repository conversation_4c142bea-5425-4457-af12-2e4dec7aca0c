/**
 * API endpoint для метрик окремого зображення
 * GET /api/analytics/images/[imageId]/metrics
 */

import { NextRequest, NextResponse } from 'next/server';
import { ImagePerformanceMetrics } from '@/lib/analytics/image-analytics';

interface RouteParams {
  params: {
    imageId: string;
  };
}

/**
 * GET /api/analytics/images/[imageId]/metrics - Отримання метрик зображення
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { imageId } = params;

    if (!imageId) {
      return NextResponse.json(
        { error: 'Image ID is required' },
        { status: 400 }
      );
    }

    // В реальному проекті тут би був запит до бази даних
    // Поки що генеруємо симуляцію метрик
    const metrics = generateImageMetrics(imageId);

    if (!metrics) {
      return NextResponse.json(
        { error: 'Image not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(metrics);

  } catch (error) {
    console.error('Помилка отримання метрик зображення:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Генерація симуляції метрик для зображення
 */
function generateImageMetrics(imageId: string): ImagePerformanceMetrics {
  // Генеруємо реалістичні метрики на основі imageId
  const hash = hashString(imageId);
  const seed = Math.abs(hash) % 1000;

  return {
    imageId,
    imageUrl: `https://cdn.marketplace.demo/${imageId}.jpg`,
    averageLoadTime: 800 + (seed % 800), // 800-1600ms
    totalViews: 1000 + (seed * 10) % 10000, // 1K-11K views
    totalDownloads: 50 + (seed * 2) % 500, // 50-550 downloads
    errorRate: (seed % 50) / 10, // 0-5% error rate
    cacheHitRate: 75 + (seed % 25), // 75-100% cache hit rate
    popularityScore: Math.min(100, (seed % 100) + 20), // 20-100 popularity
    lastUpdated: new Date().toISOString(),
  };
}

/**
 * Простий хеш для генерації консистентних даних
 */
function hashString(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return hash;
}
