import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

// POST - Створення пакетного завантаження
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json() as { 
      action?: 'create' | 'start' | 'cancel' | 'retry';
      modelIds?: string[];
      compressionLevel?: number;
      includeMetadata?: boolean;
      archiveFormat?: 'zip' | 'tar.gz';
      sessionId?: string;
    };
    
    const { 
      action = 'create', 
      modelIds, 
      compressionLevel, 
      includeMetadata, 
      archiveFormat,
      sessionId 
    } = body;

    // Генеруємо унікальний ID для пакетного завантаження
    const batchId = sessionId || crypto.randomUUID();
    
    // Отримуємо Durable Object для пакетного завантаження
    const durableObjectId = (globalThis as any).BATCH_DOWNLOAD_MANAGER?.idFromName(`batch-${batchId}`);
    
    if (!durableObjectId) {
      return NextResponse.json(
        { success: false, error: 'Batch download service unavailable' },
        { status: 503 }
      );
    }

    const durableObject = (globalThis as any).BATCH_DOWNLOAD_MANAGER?.get(durableObjectId);
    
    let endpoint = '';
    let requestBody: any = {};

    switch (action) {
      case 'create':
        if (!modelIds || modelIds.length === 0) {
          return NextResponse.json(
            { success: false, error: 'Model IDs are required' },
            { status: 400 }
          );
        }
        
        endpoint = 'create-batch-session';
        requestBody = {
          userId: session.user.id,
          batchRequest: {
            modelIds,
            compressionLevel: compressionLevel || 6,
            includeMetadata: includeMetadata || false,
            archiveFormat: archiveFormat || 'zip'
          }
        };
        break;
        
      case 'start':
        if (!sessionId) {
          return NextResponse.json(
            { success: false, error: 'Session ID is required' },
            { status: 400 }
          );
        }
        
        endpoint = 'start-batch-download';
        requestBody = { sessionId };
        break;
        
      case 'cancel':
        if (!sessionId) {
          return NextResponse.json(
            { success: false, error: 'Session ID is required' },
            { status: 400 }
          );
        }
        
        endpoint = 'cancel-batch-download';
        requestBody = { sessionId };
        break;
        
      case 'retry':
        if (!sessionId) {
          return NextResponse.json(
            { success: false, error: 'Session ID is required' },
            { status: 400 }
          );
        }
        
        endpoint = 'retry-failed-models';
        requestBody = { sessionId };
        break;
        
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

    const response = await durableObject.fetch(`https://batch/${endpoint}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody)
    });

    const result = await response.json();
    
    // Додаємо batchId до відповіді для create action
    if (action === 'create' && result.success) {
      result.batchId = batchId;
    }
    
    return NextResponse.json(result, { status: response.status });

  } catch (error) {
    console.error('Error with batch download:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process batch download' },
      { status: 500 }
    );
  }
}

// GET - Отримання прогресу пакетного завантаження
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { status: 400 }
      );
    }

    const durableObjectId = (globalThis as any).BATCH_DOWNLOAD_MANAGER?.idFromName(`batch-${sessionId}`);
    
    if (!durableObjectId) {
      return NextResponse.json(
        { success: false, error: 'Batch download service unavailable' },
        { status: 503 }
      );
    }

    const durableObject = (globalThis as any).BATCH_DOWNLOAD_MANAGER?.get(durableObjectId);
    
    const response = await durableObject.fetch(`https://batch/get-batch-progress?sessionId=${sessionId}`);
    const result = await response.json();

    return NextResponse.json(result, { status: response.status });

  } catch (error) {
    console.error('Error getting batch download progress:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get batch download progress' },
      { status: 500 }
    );
  }
}
