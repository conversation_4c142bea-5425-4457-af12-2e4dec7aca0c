import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

// POST - Створення резюмованої сесії завантаження
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json() as { 
      modelId: string; 
      chunkSize?: number;
      action?: 'create' | 'start' | 'pause' | 'resume' | 'verify';
      sessionId?: string;
      resumeToken?: string;
    };
    
    const { modelId, chunkSize, action = 'create', sessionId, resumeToken } = body;

    if (!modelId) {
      return NextResponse.json(
        { success: false, error: 'Model ID is required' },
        { status: 400 }
      );
    }

    // Отримуємо Durable Object для резюмованого завантаження
    const durableObjectId = (globalThis as any).RESUMABLE_DOWNLOAD_MANAGER?.idFromName(`resumable-${modelId}`);
    
    if (!durableObjectId) {
      return NextResponse.json(
        { success: false, error: 'Resumable download service unavailable' },
        { status: 503 }
      );
    }

    const durableObject = (globalThis as any).RESUMABLE_DOWNLOAD_MANAGER?.get(durableObjectId);
    
    let endpoint = '';
    let requestBody: any = {};

    switch (action) {
      case 'create':
        endpoint = 'create-resumable-session';
        requestBody = {
          modelId,
          userId: session.user.id,
          chunkSize: chunkSize || 2 * 1024 * 1024 // Default 2MB
        };
        break;
        
      case 'start':
        endpoint = 'start-download';
        requestBody = { sessionId, resumeToken };
        break;
        
      case 'pause':
        endpoint = 'pause-download';
        requestBody = { sessionId, resumeToken };
        break;
        
      case 'resume':
        endpoint = 'resume-download';
        requestBody = { sessionId, resumeToken };
        break;
        
      case 'verify':
        endpoint = 'verify-chunks';
        requestBody = { sessionId, resumeToken };
        break;
        
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

    const response = await durableObject.fetch(`https://resumable/${endpoint}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(requestBody)
    });

    const result = await response.json();
    return NextResponse.json(result, { status: response.status });

  } catch (error) {
    console.error('Error with resumable download:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process resumable download' },
      { status: 500 }
    );
  }
}

// GET - Отримання інформації про резюмовану сесію
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');
    const resumeToken = searchParams.get('resumeToken');
    const modelId = searchParams.get('modelId');

    if (!sessionId || !resumeToken || !modelId) {
      return NextResponse.json(
        { success: false, error: 'Session ID, resume token, and Model ID are required' },
        { status: 400 }
      );
    }

    const durableObjectId = (globalThis as any).RESUMABLE_DOWNLOAD_MANAGER?.idFromName(`resumable-${modelId}`);
    
    if (!durableObjectId) {
      return NextResponse.json(
        { success: false, error: 'Resumable download service unavailable' },
        { status: 503 }
      );
    }

    const durableObject = (globalThis as any).RESUMABLE_DOWNLOAD_MANAGER?.get(durableObjectId);
    
    const response = await durableObject.fetch(
      `https://resumable/get-resume-info?sessionId=${sessionId}&resumeToken=${resumeToken}`
    );
    
    const result = await response.json();
    return NextResponse.json(result, { status: response.status });

  } catch (error) {
    console.error('Error getting resumable download info:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get resumable download info' },
      { status: 500 }
    );
  }
}

// PUT - Збирання файлу з чанків
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json() as { 
      sessionId: string;
      resumeToken: string;
      modelId: string;
    };
    
    const { sessionId, resumeToken, modelId } = body;

    if (!sessionId || !resumeToken || !modelId) {
      return NextResponse.json(
        { success: false, error: 'Session ID, resume token, and Model ID are required' },
        { status: 400 }
      );
    }

    const durableObjectId = (globalThis as any).RESUMABLE_DOWNLOAD_MANAGER?.idFromName(`resumable-${modelId}`);
    
    if (!durableObjectId) {
      return NextResponse.json(
        { success: false, error: 'Resumable download service unavailable' },
        { status: 503 }
      );
    }

    const durableObject = (globalThis as any).RESUMABLE_DOWNLOAD_MANAGER?.get(durableObjectId);
    
    const response = await durableObject.fetch('https://resumable/assemble-file', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ sessionId, resumeToken })
    });

    const result = await response.json();
    return NextResponse.json(result, { status: response.status });

  } catch (error) {
    console.error('Error assembling file:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to assemble file' },
      { status: 500 }
    );
  }
}
