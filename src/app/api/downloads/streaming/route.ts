import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';

// POST - Створення сесії потокового завантаження
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json() as { 
      modelId: string; 
      chunkSize?: number;
    };
    const { modelId, chunkSize } = body;

    if (!modelId) {
      return NextResponse.json(
        { success: false, error: 'Model ID is required' },
        { status: 400 }
      );
    }

    // Отримуємо Durable Object для потокового завантаження
    const durableObjectId = (globalThis as any).STREAMING_DOWNLOAD_MANAGER?.idFromName(`streaming-${modelId}`);
    
    if (!durableObjectId) {
      return NextResponse.json(
        { success: false, error: 'Streaming service unavailable' },
        { status: 503 }
      );
    }

    const durableObject = (globalThis as any).STREAMING_DOWNLOAD_MANAGER?.get(durableObjectId);
    
    // Створюємо сесію потокового завантаження
    const response = await durableObject.fetch('https://streaming/create-streaming-session', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        modelId,
        userId: session.user.id,
        chunkSize: chunkSize || 1024 * 1024 // Default 1MB
      })
    });

    const result = await response.json();
    return NextResponse.json(result, { status: response.status });

  } catch (error) {
    console.error('Error creating streaming session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create streaming session' },
      { status: 500 }
    );
  }
}

// GET - Отримання прогресу потокового завантаження
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const sessionId = searchParams.get('sessionId');
    const modelId = searchParams.get('modelId');

    if (!sessionId || !modelId) {
      return NextResponse.json(
        { success: false, error: 'Session ID and Model ID are required' },
        { status: 400 }
      );
    }

    const durableObjectId = (globalThis as any).STREAMING_DOWNLOAD_MANAGER?.idFromName(`streaming-${modelId}`);
    
    if (!durableObjectId) {
      return NextResponse.json(
        { success: false, error: 'Streaming service unavailable' },
        { status: 503 }
      );
    }

    const durableObject = (globalThis as any).STREAMING_DOWNLOAD_MANAGER?.get(durableObjectId);
    
    const response = await durableObject.fetch(`https://streaming/get-progress?sessionId=${sessionId}`);
    const result = await response.json();

    return NextResponse.json(result, { status: response.status });

  } catch (error) {
    console.error('Error getting streaming progress:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get streaming progress' },
      { status: 500 }
    );
  }
}
