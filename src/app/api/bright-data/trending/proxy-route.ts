import { NextRequest, NextResponse } from 'next/server';
import { apiClient } from '@/lib/api/config';

/**
 * Proxy route for trending models - connects to unified worker
 * This replaces the complex scraping logic with a simple proxy to the worker
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform') || 'printables';
    const limit = searchParams.get('limit') || '12';
    const timeframe = searchParams.get('timeframe') || 'week';

    console.log(`🔥 Proxying trending models request: ${platform}, limit: ${limit}, timeframe: ${timeframe}`);

    // Use the API client to get trending models from the unified worker
    const response = await apiClient.getTrendingModels(platform, parseInt(limit));

    if (response.success && response.data) {
      return NextResponse.json({
        success: true,
        data: {
          models: response.data.models,
          platform: response.data.platform,
          timeframe: response.data.timeframe,
          total: response.data.total,
          cached: response.cached || false,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      throw new Error(response.error || 'Failed to fetch trending models from worker');
    }

  } catch (error) {
    console.error('❌ Trending models proxy error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch trending models',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
