import { BrightDataMCPClient } from '@/lib/bright-data/mcp-client';
import { ModelSource } from '@/types/models';
import { NextRequest, NextResponse } from 'next/server';


// Note: Edge runtime disabled for OpenNext compatibility
// export const runtime = 'edge';
interface TrendingModelsParams {
  platform?: ModelSource;
  limit?: number;
  timeframe?: 'day' | 'week' | 'month';
}

interface TrendingModel {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  designer: {
    name: string;
    avatar?: string;
  };
  stats: {
    views: number;
    downloads: number;
    likes: number;
    trending_score: number;
  };
  platform: ModelSource;
  originalUrl: string;
  tags: string[];
  category: string;
  isFree: boolean;
  price?: number;
  isNew?: boolean;
  publishedAt?: string;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform') as ModelSource || 'printables';
    const limit = parseInt(searchParams.get('limit') || '12');
    const timeframe = searchParams.get('timeframe') as 'day' | 'week' | 'month' || 'week';

    console.log(`🔥 Отримання трендових моделей з ${platform}`);

    const mcpClient = new BrightDataMCPClient();
    
    // Отримуємо трендові моделі з різних платформ
    const trendingModels = await getTrendingModels(mcpClient, platform, limit, timeframe);

    return NextResponse.json({
      success: true,
      data: {
        models: trendingModels,
        platform,
        timeframe,
        total: trendingModels.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Помилка отримання трендових моделей:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Не вдалося отримати трендові моделі',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

async function getTrendingModels(
  mcpClient: BrightDataMCPClient,
  platform: ModelSource,
  limit: number,
  timeframe: string
): Promise<TrendingModel[]> {

  const maxRetries = 3;
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔥 Спроба ${attempt}/${maxRetries} скрапінгу трендових моделей з ${platform}`);

      // Формуємо URL для скрапінгу в залежності від платформи та timeframe
      const platformUrls = {
        printables: buildPrintablesTrendingUrl(timeframe),
        makerworld: buildMakerworldTrendingUrl(timeframe),
        thangs: buildThangsTrendingUrl(timeframe),
        thingiverse: buildThingiverseTrendingUrl(timeframe),
        myminifactory: buildMyMiniFactoryTrendingUrl(timeframe),
        local: ''
      };

      const targetUrl = platformUrls[platform];
      if (!targetUrl) {
        throw new Error(`Непідтримувана платформа: ${platform}`);
      }

      console.log(`🌐 Скрапінг трендових моделей: ${targetUrl}`);

      // Використовуємо Bright Data MCP для скрапінгу з HTML для кращого парсингу
      const scrapedData = await mcpClient.callTool('scrape_as_html_Bright_Data', {
        url: targetUrl
      });

      if (!scrapedData.success) {
        throw new Error(`Scraping failed: ${scrapedData.error}`);
      }

      // Парсимо отримані дані та конвертуємо в наш формат
      const trendingModels = await parseTrendingModels(scrapedData.data, platform, limit, timeframe);

      if (trendingModels.length > 0) {
        console.log(`✅ Успішно отримано ${trendingModels.length} трендових моделей з ${platform}`);
        return trendingModels;
      } else {
        throw new Error('No trending models found in scraped data');
      }

    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      console.error(`❌ Спроба ${attempt} не вдалася для ${platform}:`, lastError.message);

      if (attempt < maxRetries) {
        // Експоненційна затримка між спробами
        const delay = Math.pow(2, attempt) * 1000;
        console.log(`⏳ Очікування ${delay}ms перед наступною спробою...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  console.error(`❌ Всі спроби скрапінгу трендових моделей ${platform} не вдалися. Останя помилка:`, lastError?.message);

  // Тільки як останній варіант використовуємо симульовані дані
  console.log(`⚠️ Використання fallback даних для трендових моделей ${platform}`);
  return generateSimulatedTrendingModels(platform, Math.min(limit, 3), timeframe);
}

// URL builders for different platforms
function buildPrintablesTrendingUrl(timeframe: string): string {
  let url = 'https://www.printables.com/models';
  const params = new URLSearchParams();

  params.append('sort', 'trending');
  if (timeframe && timeframe !== 'week') {
    params.append('time', timeframe);
  }

  return url + '?' + params.toString();
}

function buildMakerworldTrendingUrl(timeframe: string): string {
  return `https://makerworld.com/models?sort=trending&time=${timeframe}`;
}

function buildThangsTrendingUrl(timeframe: string): string {
  return `https://thangs.com/trending?time=${timeframe}`;
}

function buildThingiverseTrendingUrl(timeframe: string): string {
  return `https://www.thingiverse.com/trending?time=${timeframe}`;
}

function buildMyMiniFactoryTrendingUrl(timeframe: string): string {
  return `https://www.myminifactory.com/trending?time=${timeframe}`;
}

async function parseTrendingModels(
  htmlData: string,
  platform: ModelSource,
  limit: number,
  timeframe: string
): Promise<TrendingModel[]> {

  console.log(`🔍 Парсинг HTML даних трендових моделей з ${platform}`);

  try {
    switch (platform) {
      case 'printables':
        return parsePrintablesTrendingModels(htmlData, limit, timeframe);
      case 'makerworld':
        return parseMakerworldTrendingModels(htmlData, limit, timeframe);
      case 'thangs':
        return parseThangsTrendingModels(htmlData, limit, timeframe);
      case 'thingiverse':
        return parseThingiverseTrendingModels(htmlData, limit, timeframe);
      default:
        console.warn(`⚠️ Парсинг трендових моделей для платформи ${platform} ще не реалізований`);
        return [];
    }
  } catch (error) {
    console.error(`❌ Помилка парсингу трендових даних з ${platform}:`, error);
    return [];
  }
}

function parsePrintablesTrendingModels(html: string, limit: number, timeframe: string): TrendingModel[] {
  const models: TrendingModel[] = [];

  try {
    // Шукаємо JSON дані в скрипті сторінки
    const jsonMatch = html.match(/window\.__INITIAL_STATE__\s*=\s*({.*?});/);
    if (jsonMatch) {
      try {
        const initialState = JSON.parse(jsonMatch[1]);
        const modelsData = initialState?.models?.items || initialState?.data?.models || [];

        console.log(`📊 Знайдено ${modelsData.length} трендових моделей у JSON даних Printables`);

        for (const modelData of modelsData.slice(0, limit)) {
          if (modelData.id && modelData.name) {
            // Розраховуємо trending score на основі статистики
            const trendingScore = calculateTrendingScore(
              modelData.view_count || 0,
              modelData.download_count || 0,
              modelData.like_count || 0,
              modelData.published_at,
              timeframe
            );

            models.push({
              id: `printables_trending_${modelData.id}`,
              title: modelData.name,
              description: modelData.summary || modelData.description || '',
              thumbnail: modelData.image_url || modelData.thumbnail_url || '',
              designer: {
                name: modelData.user?.name || 'Unknown',
                avatar: modelData.user?.avatar_url
              },
              stats: {
                views: modelData.view_count || 0,
                downloads: modelData.download_count || 0,
                likes: modelData.like_count || 0,
                trending_score: trendingScore
              },
              platform: 'printables',
              originalUrl: `https://www.printables.com/model/${modelData.id}`,
              tags: modelData.tags || [],
              category: modelData.category?.name || 'Other',
              isFree: modelData.price === 0 || modelData.is_free,
              price: modelData.price || 0,
              isNew: isNewModel(modelData.published_at || new Date().toISOString()),
              publishedAt: modelData.published_at || new Date().toISOString()
            });
          }
        }
      } catch (jsonError) {
        console.error('❌ Помилка парсингу JSON з Printables:', jsonError);
      }
    }

    // Fallback: парсинг HTML елементів
    if (models.length === 0) {
      console.log('🔄 Fallback до HTML парсингу для Printables трендових моделей');

      const modelMatches = html.match(/<div[^>]*class="[^"]*model-card[^"]*"[^>]*>.*?<\/div>/g) ||
                          html.match(/<article[^>]*class="[^"]*model[^"]*"[^>]*>.*?<\/article>/g);

      if (modelMatches) {
        for (const match of modelMatches.slice(0, limit)) {
          const titleMatch = match.match(/<h[1-6][^>]*>.*?<a[^>]*href="\/model\/(\d+)[^"]*"[^>]*>([^<]+)<\/a>/) ||
                           match.match(/href="\/model\/(\d+)[^"]*"[^>]*title="([^"]+)"/);

          const imageMatch = match.match(/<img[^>]*src="([^"]+)"/);
          const statsMatch = match.match(/(\d+)\s*downloads?/i);
          const likesMatch = match.match(/(\d+)\s*likes?/i);

          if (titleMatch && titleMatch[1] && titleMatch[2]) {
            const downloads = parseInt(statsMatch?.[1] || '0');
            const likes = parseInt(likesMatch?.[1] || '0');
            const trendingScore = calculateTrendingScore(0, downloads, likes, new Date().toISOString(), timeframe);

            models.push({
              id: `printables_trending_${titleMatch[1]}`,
              title: titleMatch[2].trim(),
              description: '',
              thumbnail: imageMatch?.[1] || '',
              designer: { name: 'Unknown', avatar: undefined },
              stats: {
                views: 0,
                downloads,
                likes,
                trending_score: trendingScore
              },
              platform: 'printables',
              originalUrl: `https://www.printables.com/model/${titleMatch[1]}`,
              tags: [],
              category: 'Other',
              isFree: true,
              publishedAt: new Date().toISOString()
            });
          }
        }
      }
    }

  } catch (error) {
    console.error('❌ Помилка парсингу Printables трендових моделей:', error);
  }

  // Сортуємо за trending score
  const sortedModels = models.sort((a, b) => b.stats.trending_score - a.stats.trending_score);
  console.log(`✅ Успішно спарсено ${sortedModels.length} трендових моделей з Printables`);
  return sortedModels;
}

// Helper functions for trending calculations
function calculateTrendingScore(views: number, downloads: number, likes: number, publishedAt: string, timeframe: string): number {
  const now = new Date();
  const published = new Date(publishedAt);
  const ageInDays = Math.max(1, (now.getTime() - published.getTime()) / (1000 * 60 * 60 * 24));

  // Base score from engagement
  const engagementScore = (downloads * 3) + (likes * 2) + (views * 0.1);

  // Time decay factor - newer models get higher scores
  let timeDecay = 1;
  switch (timeframe) {
    case 'day':
      timeDecay = Math.max(0.1, 1 - (ageInDays / 7)); // Decay over 7 days
      break;
    case 'week':
      timeDecay = Math.max(0.1, 1 - (ageInDays / 30)); // Decay over 30 days
      break;
    case 'month':
      timeDecay = Math.max(0.1, 1 - (ageInDays / 90)); // Decay over 90 days
      break;
  }

  return Math.round(engagementScore * timeDecay);
}

function isNewModel(publishedAt: string | undefined | null): boolean {
  if (!publishedAt) {
    return false; // If no publish date, consider it not new
  }

  try {
    const now = new Date();
    const published = new Date(publishedAt);

    // Check if the date is valid
    if (isNaN(published.getTime())) {
      return false;
    }

    const ageInDays = (now.getTime() - published.getTime()) / (1000 * 60 * 60 * 24);
    return ageInDays <= 7; // Consider models published in last 7 days as "new"
  } catch (error) {
    console.warn('Error parsing date in isNewModel:', publishedAt, error);
    return false;
  }
}

function parseMakerworldTrendingModels(html: string, limit: number, timeframe: string): TrendingModel[] {
  const models: TrendingModel[] = [];

  try {
    // Similar parsing logic for MakerWorld
    const jsonMatch = html.match(/window\.__INITIAL_STATE__\s*=\s*({.*?});/) ||
                     html.match(/window\.APP_DATA\s*=\s*({.*?});/);

    if (jsonMatch) {
      try {
        const initialState = JSON.parse(jsonMatch[1]);
        const modelsData = initialState?.models || initialState?.data?.models || [];

        for (const modelData of modelsData.slice(0, limit)) {
          if (modelData.id && modelData.title) {
            const trendingScore = calculateTrendingScore(
              modelData.view_count || 0,
              modelData.download_count || 0,
              modelData.like_count || 0,
              modelData.created_at || new Date().toISOString(),
              timeframe
            );

            models.push({
              id: `makerworld_trending_${modelData.id}`,
              title: modelData.title,
              description: modelData.description || '',
              thumbnail: modelData.cover_image || modelData.thumbnail || '',
              designer: {
                name: modelData.author?.name || 'Unknown',
                avatar: modelData.author?.avatar
              },
              stats: {
                views: modelData.view_count || 0,
                downloads: modelData.download_count || 0,
                likes: modelData.like_count || 0,
                trending_score: trendingScore
              },
              platform: 'makerworld',
              originalUrl: `https://makerworld.com/model/${modelData.id}`,
              tags: modelData.tags || [],
              category: modelData.category || 'Other',
              isFree: modelData.is_free || modelData.price === 0,
              price: modelData.price || 0,
              isNew: isNewModel(modelData.created_at || new Date().toISOString()),
              publishedAt: modelData.created_at || new Date().toISOString()
            });
          }
        }
      } catch (jsonError) {
        console.error('❌ Помилка парсингу JSON з MakerWorld:', jsonError);
      }
    }

  } catch (error) {
    console.error('❌ Помилка парсингу MakerWorld трендових моделей:', error);
  }

  const sortedModels = models.sort((a, b) => b.stats.trending_score - a.stats.trending_score);
  console.log(`✅ Успішно спарсено ${sortedModels.length} трендових моделей з MakerWorld`);
  return sortedModels;
}

function parseThangsTrendingModels(html: string, limit: number, timeframe: string): TrendingModel[] {
  const models: TrendingModel[] = [];

  try {
    const jsonMatch = html.match(/window\.__INITIAL_STATE__\s*=\s*({.*?});/);

    if (jsonMatch) {
      try {
        const initialState = JSON.parse(jsonMatch[1]);
        const modelsData = initialState?.models || [];

        for (const modelData of modelsData.slice(0, limit)) {
          if (modelData.id && modelData.name) {
            const trendingScore = calculateTrendingScore(
              modelData.view_count || 0,
              modelData.download_count || 0,
              modelData.like_count || 0,
              modelData.created_at || new Date().toISOString(),
              timeframe
            );

            models.push({
              id: `thangs_trending_${modelData.id}`,
              title: modelData.name,
              description: modelData.description || '',
              thumbnail: modelData.thumbnail_url || '',
              designer: {
                name: modelData.creator?.username || 'Unknown',
                avatar: modelData.creator?.avatar_url
              },
              stats: {
                views: modelData.view_count || 0,
                downloads: modelData.download_count || 0,
                likes: modelData.like_count || 0,
                trending_score: trendingScore
              },
              platform: 'thangs',
              originalUrl: `https://thangs.com/model/${modelData.id}`,
              tags: modelData.tags || [],
              category: modelData.category || 'Other',
              isFree: modelData.is_free || true,
              isNew: isNewModel(modelData.created_at || new Date().toISOString()),
              publishedAt: modelData.created_at || new Date().toISOString()
            });
          }
        }
      } catch (jsonError) {
        console.error('❌ Помилка парсингу JSON з Thangs:', jsonError);
      }
    }

  } catch (error) {
    console.error('❌ Помилка парсингу Thangs трендових моделей:', error);
  }

  const sortedModels = models.sort((a, b) => b.stats.trending_score - a.stats.trending_score);
  console.log(`✅ Успішно спарсено ${sortedModels.length} трендових моделей з Thangs`);
  return sortedModels;
}

function parseThingiverseTrendingModels(html: string, limit: number, timeframe: string): TrendingModel[] {
  const models: TrendingModel[] = [];

  try {
    const modelMatches = html.match(/<div[^>]*class="[^"]*thing[^"]*"[^>]*>.*?<\/div>/g);

    if (modelMatches) {
      for (const match of modelMatches.slice(0, limit)) {
        const titleMatch = match.match(/href="\/thing:(\d+)[^"]*"[^>]*>([^<]+)</);
        const imageMatch = match.match(/<img[^>]*src="([^"]+)"/);
        const statsMatch = match.match(/(\d+)\s*downloads?/i);
        const likesMatch = match.match(/(\d+)\s*likes?/i);

        if (titleMatch && titleMatch[1] && titleMatch[2]) {
          const downloads = parseInt(statsMatch?.[1] || '0');
          const likes = parseInt(likesMatch?.[1] || '0');
          const trendingScore = calculateTrendingScore(0, downloads, likes, new Date().toISOString(), timeframe);

          models.push({
            id: `thingiverse_trending_${titleMatch[1]}`,
            title: titleMatch[2].trim(),
            description: '',
            thumbnail: imageMatch?.[1] || '',
            designer: { name: 'Unknown', avatar: undefined },
            stats: {
              views: 0,
              downloads,
              likes,
              trending_score: trendingScore
            },
            platform: 'thingiverse',
            originalUrl: `https://www.thingiverse.com/thing:${titleMatch[1]}`,
            tags: [],
            category: 'Other',
            isFree: true,
            publishedAt: new Date().toISOString()
          });
        }
      }
    }

  } catch (error) {
    console.error('❌ Помилка парсингу Thingiverse трендових моделей:', error);
  }

  const sortedModels = models.sort((a, b) => b.stats.trending_score - a.stats.trending_score);
  console.log(`✅ Успішно спарсено ${sortedModels.length} трендових моделей з Thingiverse`);
  return sortedModels;
}

function generateSimulatedTrendingModels(
  platform: ModelSource, 
  limit: number, 
  timeframe: string
): TrendingModel[] {
  
  console.log(`🎭 Генерація симульованих трендових моделей для ${platform}`);
  
  const models: TrendingModel[] = [];
  
  // Базові множники для різних timeframe
  const timeframeMultipliers: Record<string, { views: number; downloads: number; likes: number }> = {
    day: { views: 0.1, downloads: 0.05, likes: 0.03 },
    week: { views: 0.5, downloads: 0.3, likes: 0.2 },
    month: { views: 1.0, downloads: 1.0, likes: 1.0 }
  };

  const multiplier = timeframeMultipliers[timeframe] || timeframeMultipliers.week;
  
  for (let i = 0; i < limit; i++) {
    const baseViews = Math.floor(Math.random() * 100000) + 20000;
    const baseDownloads = Math.floor(Math.random() * 20000) + 2000;
    const baseLikes = Math.floor(Math.random() * 10000) + 1000;
    
    const model: TrendingModel = {
      id: `trending_${platform}_${Date.now()}_${i}`,
      title: generateTrendingModelTitle(platform, i),
      description: generateTrendingModelDescription(platform),
      thumbnail: generateThumbnailUrl(platform, i),
      designer: {
        name: generateDesignerName(),
        avatar: generateAvatarUrl()
      },
      stats: {
        views: Math.floor(baseViews * multiplier.views),
        downloads: Math.floor(baseDownloads * multiplier.downloads),
        likes: Math.floor(baseLikes * multiplier.likes),
        trending_score: Math.random() * 100 + 70 // Трендові моделі мають вищий score
      },
      platform,
      originalUrl: `https://${platform}.com/trending/model/${i + 1}`,
      tags: generateTrendingTags(),
      category: generateTrendingCategory(),
      isFree: Math.random() > 0.4, // Більше безкоштовних моделей у трендах
      price: Math.random() > 0.4 ? undefined : Math.floor(Math.random() * 30) + 3
    };
    
    models.push(model);
  }
  
  return models.sort((a, b) => b.stats.trending_score - a.stats.trending_score);
}

// Допоміжні функції для генерації даних
function generateTrendingModelTitle(platform: ModelSource, index: number): string {
  const trendingTitles = [
    'Viral Dragon Miniature',
    'Popular Phone Stand Pro',
    'Trending Vase Collection',
    'Hot Gaming Dice Set',
    'Viral Keychain Design',
    'Popular Tool Organizer',
    'Trending Desk Accessory',
    'Hot Miniature Base Set',
    'Viral Plant Pot Design',
    'Popular Cable Manager',
    'Trending Bookmark Set',
    'Hot Fidget Spinner'
  ];
  
  return trendingTitles[index % trendingTitles.length] + ` - ${platform.toUpperCase()}`;
}

function generateTrendingModelDescription(platform: ModelSource): string {
  const descriptions = [
    'Цей дизайн став вірусним завдяки своїй унікальності та простоті друку.',
    'Популярна модель, яка набрала тисячі завантажень за останній тиждень.',
    'Трендовий дизайн від відомого дизайнера спільноти.',
    'Модель, яка підкорила серця 3D принтерів по всьому світу.',
    'Інноваційний підхід до класичного дизайну.',
    'Функціональна модель з чудовими відгуками користувачів.'
  ];
  
  return descriptions[Math.floor(Math.random() * descriptions.length)];
}

function generateModelTitle(platform: ModelSource, index: number): string {
  const titles = [
    'Amazing Dragon Figure',
    'Functional Phone Stand',
    'Decorative Vase',
    'Gaming Dice Set',
    'Custom Keychain',
    'Tool Organizer',
    'Desk Accessory',
    'Miniature Base',
    'Plant Pot',
    'Cable Manager'
  ];
  
  return titles[index % titles.length];
}

function generateModelDescription(platform: ModelSource): string {
  return `Високоякісна 3D модель з платформи ${platform}. Ідеально підходить для 3D друку.`;
}

function generateThumbnailUrl(platform: ModelSource, index: number): string {
  return `https://picsum.photos/400/300?random=${platform}_${index}`;
}

function generateDesignerName(): string {
  const names = ['Alex Designer', 'Maria Creator', 'John Maker', 'Sarah Artist', 'Mike Builder', 'Anna Sculptor'];
  return names[Math.floor(Math.random() * names.length)];
}

function generateAvatarUrl(): string {
  return `https://picsum.photos/100/100?random=${Math.floor(Math.random() * 1000)}`;
}

function generateTags(): string[] {
  const allTags = ['3d-printing', 'miniature', 'functional', 'decorative', 'gaming', 'tool', 'art', 'hobby'];
  const count = Math.floor(Math.random() * 4) + 2;
  return allTags.sort(() => 0.5 - Math.random()).slice(0, count);
}

function generateTrendingTags(): string[] {
  const trendingTags = ['viral', 'trending', 'popular', 'hot', 'featured', 'community-choice', 'editor-pick'];
  const regularTags = generateTags();
  const trendingTag = trendingTags[Math.floor(Math.random() * trendingTags.length)];
  
  return [trendingTag, ...regularTags.slice(0, 3)];
}

function generateCategory(): string {
  const categories = ['Miniatures', 'Tools', 'Home & Garden', 'Toys & Games', 'Art', 'Automotive'];
  return categories[Math.floor(Math.random() * categories.length)];
}

function generateTrendingCategory(): string {
  const trendingCategories = ['Miniatures', 'Tools', 'Home & Garden', 'Toys & Games'];
  return trendingCategories[Math.floor(Math.random() * trendingCategories.length)];
}
