import { NextRequest, NextResponse } from 'next/server';
import { apiClient } from '@/lib/api/config';

/**
 * Proxy route for popular models - connects to unified worker
 * This replaces the complex scraping logic with a simple proxy to the worker
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform') || 'printables';
    const limit = searchParams.get('limit') || '16';
    const category = searchParams.get('category');
    const timeframe = searchParams.get('timeframe') || 'all';

    console.log(`⭐ Proxying popular models request: ${platform}, limit: ${limit}, category: ${category}, timeframe: ${timeframe}`);

    // Use the API client to get popular models from the unified worker
    const response = await apiClient.getPopularModels();

    if (response.success && response.data) {
      return NextResponse.json({
        success: true,
        models: response.data.models,
        platform,
        category,
        timeframe,
        total: response.data.total,
        timestamp: new Date().toISOString()
      });
    } else {
      throw new Error(response.error || 'Failed to fetch popular models from worker');
    }

  } catch (error) {
    console.error('❌ Popular models proxy error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch popular models',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
