import { NextRequest, NextResponse } from 'next/server';
import { apiClient } from '@/lib/api/config';

/**
 * Proxy route for recent models - connects to unified worker
 * This replaces the complex scraping logic with a simple proxy to the worker
 */

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform') || 'printables';
    const limit = searchParams.get('limit') || '10';

    console.log(`🆕 Proxying recent models request: ${platform}, limit: ${limit}`);

    // Use the API client to get recent models from the unified worker
    const response = await apiClient.getRecentModels();

    if (response.success && response.data) {
      return NextResponse.json({
        success: true,
        data: {
          models: response.data.models,
          total: response.data.total,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      throw new Error(response.error || 'Failed to fetch recent models from worker');
    }

  } catch (error) {
    console.error('❌ Recent models proxy error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch recent models',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
