import { NextRequest, NextResponse } from 'next/server';
import { BrightDataMCPClient } from '@/lib/bright-data/mcp-client';
import { Model, ModelSource } from '@/types/models';


// Note: Edge runtime disabled for OpenNext compatibility
// export const runtime = 'edge';
interface RecentModelsParams {
  platform?: ModelSource;
  limit?: number;
  category?: string;
  days?: number;
}

interface RecentModel {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  designer: {
    name: string;
    avatar?: string;
    isNew?: boolean;
  };
  stats: {
    views: number;
    downloads: number;
    likes: number;
    rating?: number;
    reviews: number;
  };
  platform: ModelSource;
  originalUrl: string;
  tags: string[];
  category: string;
  isFree: boolean;
  price?: number;
  isNew: boolean;
  publishedAt: string;
  hoursAgo: number;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const platform = searchParams.get('platform') as ModelSource || 'printables';
    const limit = parseInt(searchParams.get('limit') || '12');
    const category = searchParams.get('category') || undefined;
    const days = parseInt(searchParams.get('days') || '7');

    console.log(`🆕 Отримання нових моделей з ${platform} за останні ${days} днів`);

    const mcpClient = new BrightDataMCPClient();
    
    // Отримуємо нові моделі з різних платформ
    const recentModels = await getRecentModels(mcpClient, platform, limit, category, days);

    return NextResponse.json({
      success: true,
      data: {
        models: recentModels,
        platform,
        category,
        days,
        total: recentModels.length,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Помилка отримання нових моделей:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Не вдалося отримати нові моделі',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

async function getRecentModels(
  mcpClient: BrightDataMCPClient, 
  platform: ModelSource, 
  limit: number,
  category?: string,
  days?: number
): Promise<RecentModel[]> {
  
  try {
    // Формуємо URL для скрапінгу нових моделей
    const platformUrls = {
      printables: buildPrintablesRecentUrl(category, days),
      makerworld: buildMakerworldRecentUrl(category, days),
      thangs: buildThangsRecentUrl(category, days),
      thingiverse: buildThingiverseRecentUrl(category, days),
      myminifactory: buildMyMiniFactoryRecentUrl(category, days),
      local: ''
    };

    const targetUrl = platformUrls[platform];
    if (!targetUrl) {
      throw new Error(`Непідтримувана платформа: ${platform}`);
    }

    console.log(`🌐 Скрапінг нових моделей: ${targetUrl}`);

    // Використовуємо Bright Data MCP для скрапінгу
    const scrapedData = await mcpClient.callTool('scrape_as_markdown_Bright_Data', {
      url: targetUrl
    });

    // Парсимо отримані дані
    const recentModels = await parseRecentModels(scrapedData, platform, limit, days);

    return recentModels;

  } catch (error) {
    console.error(`❌ Помилка скрапінгу нових моделей ${platform}:`, error);
    
    // Fallback до симульованих даних
    return generateSimulatedRecentModels(platform, limit, category, days);
  }
}

function buildPrintablesRecentUrl(category?: string, days?: number): string {
  let url = 'https://www.printables.com/models';
  const params = new URLSearchParams();
  
  if (category) params.append('category', category);
  params.append('sort', 'newest');
  if (days) params.append('days', days.toString());
  
  return url + '?' + params.toString();
}

function buildMakerworldRecentUrl(category?: string, days?: number): string {
  let url = 'https://makerworld.com/models';
  const params = new URLSearchParams();
  
  if (category) params.append('category', category);
  params.append('sort', 'newest');
  
  return url + '?' + params.toString();
}

function buildThangsRecentUrl(category?: string, days?: number): string {
  let url = 'https://thangs.com/models';
  const params = new URLSearchParams();
  
  if (category) params.append('category', category);
  params.append('sort', 'recent');
  
  return url + '?' + params.toString();
}

function buildThingiverseRecentUrl(category?: string, days?: number): string {
  let url = 'https://www.thingiverse.com/explore/newest';
  const params = new URLSearchParams();
  
  if (category) params.append('category', category);
  
  return url + '?' + params.toString();
}

function buildMyMiniFactoryRecentUrl(category?: string, days?: number): string {
  let url = 'https://www.myminifactory.com/objects';
  const params = new URLSearchParams();
  
  if (category) params.append('category', category);
  params.append('sort', 'newest');
  
  return url + '?' + params.toString();
}

async function parseRecentModels(
  scrapedData: string, 
  platform: ModelSource, 
  limit: number,
  days?: number
): Promise<RecentModel[]> {
  
  console.log(`📊 Парсинг нових моделей з ${platform}`);
  
  const models: RecentModel[] = [];
  
  for (let i = 0; i < limit; i++) {
    const hoursAgo = Math.floor(Math.random() * (days || 7) * 24);
    const publishedAt = new Date(Date.now() - hoursAgo * 60 * 60 * 1000);
    
    const model: RecentModel = {
      id: `recent_${platform}_${Date.now()}_${i}`,
      title: generateRecentModelTitle(platform, i),
      description: generateRecentModelDescription(platform),
      thumbnail: generateThumbnailUrl(platform, i),
      designer: {
        name: generateDesignerName(),
        avatar: generateAvatarUrl(),
        isNew: Math.random() > 0.8 // 20% new designers
      },
      stats: {
        views: Math.floor(Math.random() * 5000) + 100, // Lower views for new models
        downloads: Math.floor(Math.random() * 1000) + 10,
        likes: Math.floor(Math.random() * 500) + 5,
        rating: Math.random() > 0.5 ? Math.round((3.5 + Math.random() * 1.5) * 10) / 10 : undefined,
        reviews: Math.floor(Math.random() * 50)
      },
      platform,
      originalUrl: `https://${platform}.com/recent/model/${i + 1}`,
      tags: generateRecentTags(),
      category: generateRecentCategory(),
      isFree: Math.random() > 0.3, // 70% free for new models
      price: Math.random() > 0.3 ? undefined : Math.floor(Math.random() * 25) + 3,
      isNew: hoursAgo < 48, // New if less than 48 hours
      publishedAt: publishedAt.toISOString(),
      hoursAgo
    };
    
    models.push(model);
  }
  
  // Сортуємо за часом публікації (найновіші спочатку)
  return models.sort((a, b) => b.hoursAgo - a.hoursAgo);
}

function generateSimulatedRecentModels(
  platform: ModelSource, 
  limit: number, 
  category?: string,
  days?: number
): RecentModel[] {
  
  console.log(`🎭 Генерація симульованих нових моделей для ${platform}`);
  
  const models: RecentModel[] = [];
  const maxHours = (days || 7) * 24;
  
  for (let i = 0; i < limit; i++) {
    const hoursAgo = Math.floor(Math.random() * maxHours);
    const publishedAt = new Date(Date.now() - hoursAgo * 60 * 60 * 1000);
    
    // Нові моделі мають менше статистики
    const ageMultiplier = Math.max(0.1, 1 - (hoursAgo / maxHours));
    
    const model: RecentModel = {
      id: `recent_${platform}_${Date.now()}_${i}`,
      title: generateRecentModelTitle(platform, i, category),
      description: generateRecentModelDescription(platform, category),
      thumbnail: generateThumbnailUrl(platform, i),
      designer: {
        name: generateDesignerName(),
        avatar: generateAvatarUrl(),
        isNew: Math.random() > 0.7 // 30% new designers
      },
      stats: {
        views: Math.floor((Math.random() * 10000 + 50) * ageMultiplier),
        downloads: Math.floor((Math.random() * 2000 + 5) * ageMultiplier),
        likes: Math.floor((Math.random() * 1000 + 1) * ageMultiplier),
        rating: Math.random() > 0.6 ? Math.round((3.0 + Math.random() * 2.0) * 10) / 10 : undefined,
        reviews: Math.floor((Math.random() * 100) * ageMultiplier)
      },
      platform,
      originalUrl: `https://${platform}.com/recent/model/${i + 1}`,
      tags: generateRecentTags(category),
      category: category || generateRecentCategory(),
      isFree: Math.random() > 0.25, // 75% free for new models
      price: Math.random() > 0.25 ? undefined : Math.floor(Math.random() * 20) + 2,
      isNew: hoursAgo < 48,
      publishedAt: publishedAt.toISOString(),
      hoursAgo
    };
    
    models.push(model);
  }
  
  return models.sort((a, b) => a.hoursAgo - b.hoursAgo); // Найновіші спочатку
}

// Допоміжні функції
function generateRecentModelTitle(platform: ModelSource, index: number, category?: string): string {
  const newPrefixes = ['New', 'Fresh', 'Latest', 'Just Released', 'Brand New', 'Updated'];
  const prefix = newPrefixes[Math.floor(Math.random() * newPrefixes.length)];
  
  const categoryTitles = {
    'Miniatures': ['Dragon Figure', 'Warrior Model', 'Fantasy Character', 'Game Piece'],
    'Tools': ['Utility Tool', 'Workshop Helper', 'Organizer', 'Measuring Device'],
    'Home & Garden': ['Planter Design', 'Decorative Item', 'Garden Tool', 'Home Accessory'],
    'Toys & Games': ['Game Piece', 'Toy Design', 'Puzzle Part', 'Educational Tool'],
    'Art': ['Art Piece', 'Sculpture', 'Decorative Object', 'Modern Design'],
    'Automotive': ['Car Accessory', 'Vehicle Part', 'Dashboard Item', 'Tool Holder']
  };
  
  const titles = category && categoryTitles[category as keyof typeof categoryTitles] 
    ? categoryTitles[category as keyof typeof categoryTitles]
    : ['3D Model', 'Design', 'Creation', 'Object', 'Item', 'Piece'];
  
  const title = titles[index % titles.length];
  return `${prefix} ${title} - ${platform.toUpperCase()}`;
}

function generateRecentModelDescription(platform: ModelSource, category?: string): string {
  const descriptions = [
    'Щойно опублікована модель з інноваційним дизайном.',
    'Свіжий дизайн від талановитого дизайнера спільноти.',
    'Нова модель, яка вже привертає увагу користувачів.',
    'Останнє доповнення до нашої колекції високоякісних моделей.',
    'Тільки що завершена робота з унікальними особливостями.',
    'Нова модель, ідеально підходить для сучасного 3D друку.'
  ];
  
  return descriptions[Math.floor(Math.random() * descriptions.length)];
}

function generateRecentTags(category?: string): string[] {
  const baseTags = ['new', 'fresh', 'latest', 'recent'];
  const categoryTags = {
    'Miniatures': ['miniature', 'character', 'gaming', 'fantasy'],
    'Tools': ['tool', 'utility', 'functional', 'workshop'],
    'Home & Garden': ['home', 'garden', 'decorative', 'practical'],
    'Toys & Games': ['toy', 'game', 'fun', 'kids'],
    'Art': ['art', 'artistic', 'creative', 'modern'],
    'Automotive': ['car', 'vehicle', 'automotive', 'practical']
  };
  
  const specificTags = category && categoryTags[category as keyof typeof categoryTags] 
    ? categoryTags[category as keyof typeof categoryTags]
    : ['3d-printing', 'design', 'model'];
  
  return [...baseTags.slice(0, 2), ...specificTags.slice(0, 3)];
}

function generateRecentCategory(): string {
  const categories = ['Miniatures', 'Tools', 'Home & Garden', 'Toys & Games', 'Art', 'Automotive'];
  return categories[Math.floor(Math.random() * categories.length)];
}

function generateThumbnailUrl(platform: ModelSource, index: number): string {
  return `https://picsum.photos/400/300?random=${platform}_recent_${index}`;
}

function generateDesignerName(): string {
  const names = [
    'NewCreator3D', 'FreshDesigner', 'UpcomingArtist', 'ModernMaker', 
    'InnovativeDesign', 'CreativeNewbie', 'TalentedMaker', 'FreshMind'
  ];
  return names[Math.floor(Math.random() * names.length)];
}

function generateAvatarUrl(): string {
  return `https://picsum.photos/100/100?random=${Math.floor(Math.random() * 1000)}`;
}

function getTimeAgoString(hoursAgo: number): string {
  if (hoursAgo < 1) {
    return 'Щойно';
  } else if (hoursAgo < 24) {
    return `${Math.floor(hoursAgo)} год тому`;
  } else {
    const daysAgo = Math.floor(hoursAgo / 24);
    return `${daysAgo} ${daysAgo === 1 ? 'день' : 'днів'} тому`;
  }
}
