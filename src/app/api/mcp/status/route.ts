/**
 * MCP Status API Endpoint
 * Health check та статус всіх MCP сервісів
 */

import { NextRequest, NextResponse } from 'next/server';
import { MCPServiceCoordinator } from '../../../../lib/mcp/service-coordinator';
import { ServiceCoordinatorConfig } from '../../../../lib/mcp/types';

interface CloudflareRequest extends NextRequest {
  env?: any;
}

// Глобальний координатор для кешування
let coordinator: MCPServiceCoordinator | null = null;

/**
 * Ініціалізація координатора
 */
async function initializeCoordinator(env?: any): Promise<MCPServiceCoordinator> {
  if (!coordinator) {
    const config: ServiceCoordinatorConfig = {
      cloudflare: {
        enabled: true,
        timeout: 30000,
        retryAttempts: 3,
        enableFallback: true,
        accountId: env?.CLOUDFLARE_ACCOUNT_ID || process.env.CLOUDFLARE_ACCOUNT_ID,
        services: {
          r2: true,
          d1: true,
          kv: true,
          analytics: true,
          durableObjects: true,
          queues: true
        }
      },
      brightData: {
        enabled: true,
        apiToken: env?.BRIGHT_DATA_MCP_TOKEN || process.env.BRIGHT_DATA_MCP_TOKEN,
        endpoint: env?.BRIGHT_DATA_MCP_ENDPOINT || process.env.BRIGHT_DATA_MCP_ENDPOINT,
        timeout: 30000,
        retryAttempts: 3,
        enableFallback: true,
        zone: env?.BRIGHT_DATA_ZONE || process.env.BRIGHT_DATA_ZONE,
        platforms: {
          printables: true,
          makerworld: true,
          thangs: true,
          thingiverse: true
        }
      },
      coordination: {
        maxConcurrentJobs: parseInt(env?.MAX_CONCURRENT_SCRAPING_JOBS || process.env.MAX_CONCURRENT_SCRAPING_JOBS || '5'),
        queueTimeout: 60000,
        retryDelay: 5000,
        enableAutoRetry: true
      }
    };

    coordinator = new MCPServiceCoordinator(config);
    await coordinator.initialize(env);
  }

  return coordinator;
}

/**
 * GET /api/mcp/status
 * Отримання статусу всіх MCP сервісів
 */
export async function GET(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'health';
    const detailed = searchParams.get('detailed') === 'true';

    const env = request.env;
    const coord = await initializeCoordinator(env);

    switch (action) {
      case 'health':
        const healthCheck = await coord.healthCheck();
        
        if (detailed) {
          return NextResponse.json({
            success: true,
            data: healthCheck,
            timestamp: new Date().toISOString()
          });
        } else {
          return NextResponse.json({
            success: true,
            data: {
              status: healthCheck.overall,
              services: healthCheck.services.length,
              healthy: healthCheck.services.filter(s => s.status === 'healthy').length,
              degraded: healthCheck.services.filter(s => s.status === 'degraded').length,
              unhealthy: healthCheck.services.filter(s => s.status === 'unhealthy').length
            },
            timestamp: new Date().toISOString()
          });
        }

      case 'metrics':
        const metrics = coord.getMetrics();
        return NextResponse.json({
          success: true,
          data: metrics,
          timestamp: new Date().toISOString()
        });

      case 'jobs':
        const status = searchParams.get('status');
        const limit = parseInt(searchParams.get('limit') || '50');
        
        let jobs = coord.getAllJobs();
        
        if (status) {
          jobs = jobs.filter(job => job.status === status);
        }
        
        jobs = jobs.slice(0, limit);
        
        return NextResponse.json({
          success: true,
          data: {
            jobs,
            total: coord.getAllJobs().length,
            filtered: jobs.length
          },
          timestamp: new Date().toISOString()
        });

      case 'job':
        const jobId = searchParams.get('jobId');
        if (!jobId) {
          return NextResponse.json({
            error: 'jobId є обов\'язковим параметром'
          }, { status: 400 });
        }

        const job = coord.getJob(jobId);
        if (!job) {
          return NextResponse.json({
            error: `Job ${jobId} не знайдено`
          }, { status: 404 });
        }

        const steps = coord.getJobSteps(jobId);
        
        return NextResponse.json({
          success: true,
          data: {
            job,
            steps
          },
          timestamp: new Date().toISOString()
        });

      case 'summary':
        const summaryHealthCheck = await coord.healthCheck();
        const summaryMetrics = coord.getMetrics();
        
        return NextResponse.json({
          success: true,
          data: {
            health: {
              overall: summaryHealthCheck.overall,
              services: summaryHealthCheck.services.map(s => ({
                service: s.service,
                status: s.status,
                responseTime: s.responseTime
              }))
            },
            jobs: summaryMetrics.jobs,
            uptime: summaryHealthCheck.uptime,
            lastCheck: summaryHealthCheck.timestamp
          },
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json({
          error: `Непідтримувана дія: ${action}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Помилка GET /api/mcp/status:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * POST /api/mcp/status
 * Створення нових jobs або виконання операцій
 */
export async function POST(request: CloudflareRequest) {
  try {
    const body = await request.json();
    const { action, type, metadata, priority, jobId } = body;

    const env = request.env;
    const coord = await initializeCoordinator(env);

    switch (action) {
      case 'create_job':
        if (!type || !metadata) {
          return NextResponse.json({
            error: 'type та metadata є обов\'язковими для створення job'
          }, { status: 400 });
        }

        const newJobId = await coord.createJob(type, metadata, priority || 'normal');
        
        return NextResponse.json({
          success: true,
          data: {
            jobId: newJobId,
            type,
            priority: priority || 'normal',
            created: true
          },
          timestamp: new Date().toISOString()
        });

      case 'retry_job':
        if (!jobId) {
          return NextResponse.json({
            error: 'jobId є обов\'язковим для retry'
          }, { status: 400 });
        }

        const job = coord.getJob(jobId);
        if (!job) {
          return NextResponse.json({
            error: `Job ${jobId} не знайдено`
          }, { status: 404 });
        }

        if (job.status !== 'failed') {
          return NextResponse.json({
            error: `Job ${jobId} не в статусі failed`
          }, { status: 400 });
        }

        // Створюємо новий job з тими ж параметрами
        const retryJobId = await coord.createJob(job.type, job.metadata, job.priority);
        
        return NextResponse.json({
          success: true,
          data: {
            originalJobId: jobId,
            retryJobId,
            created: true
          },
          timestamp: new Date().toISOString()
        });

      case 'test_scraping':
        // Тестовий job для перевірки скрапінгу
        const testJobId = await coord.createJob('scrape_and_store', {
          target: {
            platform: metadata?.platform || 'printables',
            url: metadata?.url || 'https://www.printables.com/model/1',
            type: 'model'
          },
          scrapingOptions: {
            maxResults: 1,
            includeImages: true,
            includeFiles: false
          }
        }, 'high');

        return NextResponse.json({
          success: true,
          data: {
            jobId: testJobId,
            type: 'scrape_and_store',
            test: true,
            target: metadata?.url || 'https://www.printables.com/model/1'
          },
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json({
          error: `Непідтримувана дія: ${action}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Помилка POST /api/mcp/status:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/mcp/status
 * Очищення jobs, метрик або кешу
 */
export async function DELETE(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'cache';
    const jobId = searchParams.get('jobId');

    switch (action) {
      case 'cache':
        // Очищуємо кеш координатора
        coordinator = null;
        
        return NextResponse.json({
          success: true,
          message: 'Кеш координатора очищено',
          timestamp: new Date().toISOString()
        });

      case 'job':
        if (!jobId) {
          return NextResponse.json({
            error: 'jobId є обов\'язковим для видалення job'
          }, { status: 400 });
        }

        // Тут можна додати логіку видалення job
        // Поки що просто повертаємо успіх
        return NextResponse.json({
          success: true,
          message: `Job ${jobId} видалено`,
          timestamp: new Date().toISOString()
        });

      case 'completed_jobs':
        // Очищення завершених jobs (старших за 24 години)
        const env = request.env;
        const coord = await initializeCoordinator(env);
        
        const allJobs = coord.getAllJobs();
        const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 години
        
        const oldCompletedJobs = allJobs.filter(job => 
          job.status === 'completed' && 
          new Date(job.completedAt || job.updatedAt).getTime() < cutoffTime
        );

        // Тут можна додати логіку видалення старих jobs
        
        return NextResponse.json({
          success: true,
          message: `Видалено ${oldCompletedJobs.length} старих завершених jobs`,
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json({
          error: `Непідтримувана дія: ${action}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Помилка DELETE /api/mcp/status:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}
