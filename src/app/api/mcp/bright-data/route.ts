/**
 * Bright Data MCP API Endpoint
 * API для роботи з Bright Data MCP клієнтом
 */

import { NextRequest, NextResponse } from 'next/server';
import { BrightDataMCPClient } from '../../../../lib/bright-data/real-mcp-client';
import { BrightDataMCPConfig } from '../../../../lib/mcp/types';

// Note: Edge runtime disabled for OpenNext compatibility
// export const runtime = 'edge';

// Глобальний клієнт для кешування
let mcpClient: BrightDataMCPClient | null = null;

/**
 * Ініціалізація MCP клієнта
 */
function initializeMCPClient(): BrightDataMCPClient {
  if (!mcpClient) {
    const config: BrightDataMCPConfig = {
      enabled: true,
      apiToken: process.env.BRIGHT_DATA_MCP_TOKEN,
      endpoint: process.env.BRIGHT_DATA_MCP_ENDPOINT,
      timeout: 30000,
      retryAttempts: 3,
      enableFallback: true,
      zone: process.env.BRIGHT_DATA_ZONE,
      platforms: {
        printables: true,
        makerworld: true,
        thangs: true,
        thingiverse: true
      }
    };

    mcpClient = new BrightDataMCPClient(config);
  }

  return mcpClient;
}

/**
 * GET /api/mcp/bright-data
 * Health check та отримання метрик
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'health';

    const client = initializeMCPClient();
    await client.initialize();

    switch (action) {
      case 'health':
        const healthStatus = await client.healthCheck();
        return NextResponse.json({
          success: true,
          data: healthStatus,
          timestamp: new Date().toISOString()
        });

      case 'metrics':
        const metrics = client.getMetrics();
        return NextResponse.json({
          success: true,
          data: Object.fromEntries(metrics),
          timestamp: new Date().toISOString()
        });

      case 'stats':
        // Виклик session_stats_Bright_Data MCP tool
        try {
          const statsResult = await client.scrapingOperation({
            action: 'scrape',
            target: {
              platform: 'printables',
              url: 'https://www.printables.com',
              type: 'model'
            }
          });

          return NextResponse.json({
            success: true,
            data: {
              available: statsResult.success,
              message: statsResult.success ? 'Bright Data MCP доступний' : 'Bright Data MCP недоступний',
              details: statsResult
            },
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          return NextResponse.json({
            success: false,
            data: {
              available: false,
              message: 'Bright Data MCP недоступний',
              error: error instanceof Error ? error.message : 'Невідома помилка'
            },
            timestamp: new Date().toISOString()
          });
        }

      default:
        return NextResponse.json({
          error: `Непідтримувана дія: ${action}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Помилка GET /api/mcp/bright-data:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * POST /api/mcp/bright-data
 * Виконання операцій скрапінгу
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { tool, params, operation, options } = body;

    // Якщо це прямий виклик MCP tool
    if (tool && params) {
      return await handleDirectMCPCall(tool, params);
    }

    // Якщо це операція скрапінгу
    if (operation) {
      return await handleScrapingOperation(operation, options);
    }

    return NextResponse.json({
      error: 'Потрібно вказати tool з params або operation'
    }, { status: 400 });

  } catch (error) {
    console.error('❌ Помилка POST /api/mcp/bright-data:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * Обробка прямого виклику MCP tool
 */
async function handleDirectMCPCall(tool: string, params: any): Promise<NextResponse> {
  try {
    console.log(`🔧 Прямий виклик MCP tool: ${tool}`);

    // Список підтримуваних Bright Data MCP tools
    const supportedTools = [
      'scrape_as_markdown_Bright_Data',
      'scrape_as_html_Bright_Data',
      'search_engine_Bright_Data',
      'session_stats_Bright_Data',
      'web_data_amazon_product_Bright_Data',
      'web_data_linkedin_person_profile_Bright_Data',
      'web_data_instagram_profiles_Bright_Data',
      'web_data_facebook_posts_Bright_Data',
      'web_data_x_posts_Bright_Data',
      'web_data_youtube_videos_Bright_Data'
    ];

    if (!supportedTools.includes(tool)) {
      return NextResponse.json({
        success: false,
        error: `Непідтримуваний MCP tool: ${tool}`
      }, { status: 400 });
    }

    // Спробуємо виконати реальний MCP виклик
    let result;

    // Перевіряємо чи є MCP функції в глобальному контексті
    if (typeof globalThis !== 'undefined') {
      const mcpFunction = (globalThis as any)[tool];
      if (typeof mcpFunction === 'function') {
        console.log(`🌐 Виклик глобальної MCP функції: ${tool}`);
        result = await mcpFunction(params);
        
        return NextResponse.json({
          success: true,
          data: result,
          tool,
          timestamp: new Date().toISOString()
        });
      }
    }

    // Якщо прямий виклик недоступний, використовуємо клієнт
    const client = initializeMCPClient();
    await client.initialize();

    // Конвертуємо MCP tool виклик в операцію скрапінгу
    const operation = convertMCPToolToOperation(tool, params);
    const operationResult = await client.scrapingOperation(operation);

    return NextResponse.json({
      success: operationResult.success,
      data: operationResult.data,
      tool,
      convertedFromOperation: true,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error(`❌ Помилка виклику MCP tool ${tool}:`, error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Невідома помилка MCP tool',
      tool,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

/**
 * Обробка операції скрапінгу
 */
async function handleScrapingOperation(operation: any, options?: any): Promise<NextResponse> {
  try {
    const client = initializeMCPClient();
    await client.initialize();

    const result = await client.scrapingOperation(operation, options);

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ Помилка операції скрапінгу:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Невідома помилка скрапінгу'
    }, { status: 500 });
  }
}

/**
 * Конвертація MCP tool виклику в операцію скрапінгу
 */
function convertMCPToolToOperation(tool: string, params: any): any {
  switch (tool) {
    case 'scrape_as_markdown_Bright_Data':
    case 'scrape_as_html_Bright_Data':
      return {
        action: 'scrape',
        target: {
          platform: detectPlatformFromUrl(params.url),
          url: params.url,
          type: 'model'
        },
        options: {
          format: tool.includes('markdown') ? 'markdown' : 'html'
        }
      };

    case 'search_engine_Bright_Data':
      return {
        action: 'search',
        target: {
          platform: 'printables', // За замовчуванням
          url: '',
          type: 'search',
          parameters: {
            query: params.query,
            engine: params.engine || 'google'
          }
        }
      };

    default:
      return {
        action: 'scrape',
        target: {
          platform: 'printables',
          url: params.url || '',
          type: 'model'
        }
      };
  }
}

/**
 * Визначення платформи з URL
 */
function detectPlatformFromUrl(url: string): 'printables' | 'makerworld' | 'thangs' | 'thingiverse' {
  if (url.includes('printables.com')) return 'printables';
  if (url.includes('makerworld.com')) return 'makerworld';
  if (url.includes('thangs.com')) return 'thangs';
  if (url.includes('thingiverse.com')) return 'thingiverse';
  return 'printables'; // За замовчуванням
}

/**
 * PUT /api/mcp/bright-data
 * Оновлення конфігурації клієнта
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { config } = body;

    if (!config) {
      return NextResponse.json({
        error: 'Конфігурація є обов\'язковою'
      }, { status: 400 });
    }

    // Скидаємо клієнт для повторної ініціалізації з новою конфігурацією
    mcpClient = null;

    return NextResponse.json({
      success: true,
      message: 'Конфігурація оновлена',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Помилка PUT /api/mcp/bright-data:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/mcp/bright-data
 * Очищення кешу та ресурсів
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'cache';

    switch (action) {
      case 'cache':
        // Очищуємо кеш клієнта
        mcpClient = null;
        
        return NextResponse.json({
          success: true,
          message: 'Кеш очищено',
          timestamp: new Date().toISOString()
        });

      case 'metrics':
        // Скидаємо метрики
        if (mcpClient) {
          mcpClient.getMetrics().clear();
        }
        
        return NextResponse.json({
          success: true,
          message: 'Метрики скинуто',
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json({
          error: `Непідтримувана дія: ${action}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Помилка DELETE /api/mcp/bright-data:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}
