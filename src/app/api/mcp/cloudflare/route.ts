/**
 * Cloudflare MCP API Endpoint
 * API для роботи з Cloudflare MCP клієнтом
 */

import { NextRequest, NextResponse } from 'next/server';
import { CloudflareMCPClient } from '../../../../lib/cloudflare/real-mcp-client';
import { CloudflareMCPConfig } from '../../../../lib/mcp/types';

interface CloudflareRequest extends NextRequest {
  env?: any;
}

// Глобальний клієнт для кешування
let mcpClient: CloudflareMCPClient | null = null;

/**
 * Ініціалізація MCP клієнта
 */
function initializeMCPClient(env: any): CloudflareMCPClient {
  if (!mcpClient) {
    const config: CloudflareMCPConfig = {
      enabled: true,
      timeout: 30000,
      retryAttempts: 3,
      enableFallback: true,
      accountId: env.CLOUDFLARE_ACCOUNT_ID,
      services: {
        r2: true,
        d1: true,
        kv: true,
        analytics: true,
        durableObjects: true,
        queues: true
      }
    };

    mcpClient = new CloudflareMCPClient(config);
  }

  return mcpClient;
}

/**
 * GET /api/mcp/cloudflare
 * Health check та отримання метрик
 */
export async function GET(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'health';

    const env = request.env;
    if (!env) {
      return NextResponse.json({
        error: 'Cloudflare environment не налаштований'
      }, { status: 500 });
    }

    const client = initializeMCPClient(env);
    await client.initialize(env);

    switch (action) {
      case 'health':
        const healthStatus = await client.healthCheck();
        return NextResponse.json({
          success: true,
          data: healthStatus,
          timestamp: new Date().toISOString()
        });

      case 'metrics':
        const metrics = client.getMetrics();
        return NextResponse.json({
          success: true,
          data: Object.fromEntries(metrics),
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json({
          error: `Непідтримувана дія: ${action}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Помилка GET /api/mcp/cloudflare:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * POST /api/mcp/cloudflare
 * Виконання операцій з Cloudflare сервісами
 */
export async function POST(request: CloudflareRequest) {
  try {
    const body = await request.json();
    const { service, operation, options } = body;

    if (!service || !operation) {
      return NextResponse.json({
        error: 'Поля service та operation є обов\'язковими'
      }, { status: 400 });
    }

    const env = request.env;
    if (!env) {
      return NextResponse.json({
        error: 'Cloudflare environment не налаштований'
      }, { status: 500 });
    }

    const client = initializeMCPClient(env);
    await client.initialize(env);

    let result;

    switch (service) {
      case 'r2':
        if (!operation.action || !operation.key) {
          return NextResponse.json({
            error: 'R2 операція потребує action та key'
          }, { status: 400 });
        }
        result = await client.r2Operation(operation, options);
        break;

      case 'd1':
        if (!operation.action || !operation.sql) {
          return NextResponse.json({
            error: 'D1 операція потребує action та sql'
          }, { status: 400 });
        }
        result = await client.d1Operation(operation, options);
        break;

      case 'kv':
        if (!operation.action || !operation.key) {
          return NextResponse.json({
            error: 'KV операція потребує action та key'
          }, { status: 400 });
        }
        result = await client.kvOperation(operation, options);
        break;

      case 'analytics':
        if (!operation.event) {
          return NextResponse.json({
            error: 'Analytics операція потребує event'
          }, { status: 400 });
        }
        result = await client.sendAnalyticsEvent(operation, options);
        break;

      default:
        return NextResponse.json({
          error: `Непідтримуваний сервіс: ${service}`
        }, { status: 400 });
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ Помилка POST /api/mcp/cloudflare:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * PUT /api/mcp/cloudflare
 * Оновлення конфігурації клієнта
 */
export async function PUT(request: CloudflareRequest) {
  try {
    const body = await request.json();
    const { config } = body;

    if (!config) {
      return NextResponse.json({
        error: 'Конфігурація є обов\'язковою'
      }, { status: 400 });
    }

    // Скидаємо клієнт для повторної ініціалізації з новою конфігурацією
    mcpClient = null;

    return NextResponse.json({
      success: true,
      message: 'Конфігурація оновлена',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Помилка PUT /api/mcp/cloudflare:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/mcp/cloudflare
 * Очищення кешу та ресурсів
 */
export async function DELETE(request: CloudflareRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'cache';

    switch (action) {
      case 'cache':
        // Очищуємо кеш клієнта
        mcpClient = null;
        
        return NextResponse.json({
          success: true,
          message: 'Кеш очищено',
          timestamp: new Date().toISOString()
        });

      case 'metrics':
        // Скидаємо метрики
        if (mcpClient) {
          mcpClient.getMetrics().clear();
        }
        
        return NextResponse.json({
          success: true,
          message: 'Метрики скинуто',
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json({
          error: `Непідтримувана дія: ${action}`
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Помилка DELETE /api/mcp/cloudflare:', error);
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Невідома помилка'
    }, { status: 500 });
  }
}
