import { NextRequest, NextResponse } from 'next/server';
import { withApiMiddleware } from '@/lib/cloudflare/middleware';

/**
 * GET: Тестування marketplace worker
 */
export const GET = withApiMiddleware(async (
  request: NextRequest,
  { marketplaceWorker, db, storage, kv }
) => {
  try {
    const url = new URL(request.url);
    const endpoint = url.searchParams.get('endpoint') || 'health';
    
    // Результати тестування
    const results: Record<string, any> = {};

    // Тестування прямого доступу до D1
    try {
      const dbTest = await db.prepare('SELECT 1 as test').first();
      results.direct_db = {
        available: true,
        test_result: dbTest,
      };
    } catch (error) {
      results.direct_db = {
        available: false,
        error: (error as Error).message,
      };
    }

    // Тестування marketplace worker (якщо доступний)
    if (marketplaceWorker && typeof marketplaceWorker.fetch === 'function') {
      try {
        const workerResponse = await marketplaceWorker.fetch(
          new Request(`http://marketplace-worker/api/${endpoint}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          })
        );

        if (workerResponse.ok) {
          const workerData = await workerResponse.json();
          results.marketplace_worker = {
            available: true,
            status: workerResponse.status,
            data: workerData,
          };
        } else {
          results.marketplace_worker = {
            available: false,
            status: workerResponse.status,
            error: await workerResponse.text(),
          };
        }
      } catch (error) {
        results.marketplace_worker = {
          available: false,
          error: (error as Error).message,
        };
      }
    } else {
      results.marketplace_worker = {
        available: false,
        error: 'Marketplace worker not configured or not available',
      };
    }

    // Тестування KV
    try {
      await kv.put('test_key', 'test_value');
      const kvValue = await kv.get('test_key');
      results.kv = {
        available: true,
        test_write: 'success',
        test_read: kvValue,
      };
    } catch (error) {
      results.kv = {
        available: false,
        error: (error as Error).message,
      };
    }

    // Тестування R2
    try {
      const r2List = await storage.list({ limit: 1 });
      results.r2 = {
        available: true,
        objects_count: r2List.objects?.length || 0,
      };
    } catch (error) {
      results.r2 = {
        available: false,
        error: (error as Error).message,
      };
    }

    return NextResponse.json({
      success: true,
      message: 'Marketplace worker test completed',
      endpoint_tested: endpoint,
      results,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Marketplace worker test error:', error);
    return NextResponse.json(
      {
        success: false,
        error: (error as Error).message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
});

/**
 * POST: Проксування запитів до marketplace worker
 */
export const POST = withApiMiddleware(async (
  request: NextRequest,
  { marketplaceWorker }
) => {
  try {
    const body = await request.text();
    const url = new URL(request.url);
    const endpoint = url.searchParams.get('endpoint') || 'models';

    if (!marketplaceWorker || typeof marketplaceWorker.fetch !== 'function') {
      return NextResponse.json(
        {
          success: false,
          error: 'Marketplace worker not available',
        },
        { status: 503 }
      );
    }

    // Проксування запиту до marketplace worker
    const workerResponse = await marketplaceWorker.fetch(
      new Request(`http://marketplace-worker/api/${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body,
      })
    );

    const responseData = await workerResponse.json();

    return NextResponse.json(responseData, {
      status: workerResponse.status,
    });

  } catch (error) {
    console.error('Marketplace worker proxy error:', error);
    return NextResponse.json(
      {
        success: false,
        error: (error as Error).message,
      },
      { status: 500 }
    );
  }
});
