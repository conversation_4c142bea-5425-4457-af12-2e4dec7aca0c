'use client';

/**
 * AI Arbitrage Client Page - Клієнтська сторінка AI арбітражу
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Brain, 
  TrendingUp, 
  Users, 
  DollarSign, 
  BarChart3, 
  Zap,
  Target,
  Shield,
  Sparkles,
  ArrowRight,
  RefreshCw
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface ArbitrageOpportunity {
  id: string;
  modelName: string;
  category: string;
  currentPrice: number;
  suggestedPrice: number;
  potentialProfit: number;
  confidenceScore: number;
  riskLevel: 'low' | 'medium' | 'high';
  timeLeft: string;
}

interface MarketInsight {
  category: string;
  trend: 'rising' | 'falling' | 'stable';
  demandLevel: number;
  averagePrice: number;
  growthRate: number;
}

export default function AIArbitrageClientPage() {
  const [opportunities, setOpportunities] = useState<ArbitrageOpportunity[]>([]);
  const [marketInsights, setMarketInsights] = useState<MarketInsight[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('opportunities');

  // Демо дані для початку
  useEffect(() => {
    const demoOpportunities: ArbitrageOpportunity[] = [
      {
        id: '1',
        modelName: 'Futuristic Robot Arm',
        category: 'Robotics',
        currentPrice: 25.99,
        suggestedPrice: 34.99,
        potentialProfit: 9.00,
        confidenceScore: 0.87,
        riskLevel: 'low',
        timeLeft: '2 дні'
      },
      {
        id: '2',
        modelName: 'Medieval Castle Set',
        category: 'Architecture',
        currentPrice: 45.00,
        suggestedPrice: 59.99,
        potentialProfit: 14.99,
        confidenceScore: 0.92,
        riskLevel: 'low',
        timeLeft: '5 днів'
      },
      {
        id: '3',
        modelName: 'Gaming Miniatures Pack',
        category: 'Games',
        currentPrice: 15.99,
        suggestedPrice: 22.99,
        potentialProfit: 7.00,
        confidenceScore: 0.75,
        riskLevel: 'medium',
        timeLeft: '1 день'
      }
    ];

    const demoInsights: MarketInsight[] = [
      {
        category: 'Robotics',
        trend: 'rising',
        demandLevel: 0.85,
        averagePrice: 32.50,
        growthRate: 15.3
      },
      {
        category: 'Architecture',
        trend: 'stable',
        demandLevel: 0.72,
        averagePrice: 48.75,
        growthRate: 3.2
      },
      {
        category: 'Games',
        trend: 'rising',
        demandLevel: 0.91,
        averagePrice: 19.99,
        growthRate: 22.1
      }
    ];

    setOpportunities(demoOpportunities);
    setMarketInsights(demoInsights);
  }, []);

  const handleRefreshData = async () => {
    setIsLoading(true);
    // Тут буде виклик API для отримання реальних даних
    setTimeout(() => {
      setIsLoading(false);
    }, 2000);
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'rising': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'falling': return <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />;
      default: return <BarChart3 className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-purple-900/20 dark:to-blue-900/20">
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <Brain className="h-16 w-16 text-purple-600" />
              <Sparkles className="h-6 w-6 text-yellow-500 absolute -top-1 -right-1 animate-pulse" />
            </div>
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent mb-4">
            AI Arbitrage
          </h1>
          
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            Розумна система, яка з'єднує покупців і продавців, знаходить найкращі можливості 
            та оптимізує ціни на основі аналізу ринку 3D моделей
          </p>

          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <Badge variant="secondary" className="px-4 py-2">
              <Zap className="h-4 w-4 mr-2" />
              Автоматичний аналіз
            </Badge>
            <Badge variant="secondary" className="px-4 py-2">
              <Target className="h-4 w-4 mr-2" />
              Точні прогнози
            </Badge>
            <Badge variant="secondary" className="px-4 py-2">
              <Shield className="h-4 w-4 mr-2" />
              Оцінка ризиків
            </Badge>
          </div>

          <Button 
            onClick={handleRefreshData}
            disabled={isLoading}
            size="lg"
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
          >
            {isLoading ? (
              <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
            ) : (
              <Brain className="h-5 w-5 mr-2" />
            )}
            {isLoading ? 'Аналізую ринок...' : 'Запустити AI аналіз'}
          </Button>
        </motion.div>

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-8">
              <TabsTrigger value="opportunities" className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Можливості
              </TabsTrigger>
              <TabsTrigger value="insights" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Аналітика
              </TabsTrigger>
              <TabsTrigger value="matching" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                З'єднання
              </TabsTrigger>
            </TabsList>

            {/* Opportunities Tab */}
            <TabsContent value="opportunities" className="space-y-6">
              <div className="grid gap-6">
                {opportunities.map((opportunity, index) => (
                  <motion.div
                    key={opportunity.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                  >
                    <Card className="hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-lg">{opportunity.modelName}</CardTitle>
                            <CardDescription>{opportunity.category}</CardDescription>
                          </div>
                          <Badge className={getRiskColor(opportunity.riskLevel)}>
                            {opportunity.riskLevel === 'low' ? 'Низький ризик' : 
                             opportunity.riskLevel === 'medium' ? 'Середній ризик' : 'Високий ризик'}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                          <div>
                            <p className="text-sm text-gray-600 dark:text-gray-400">Поточна ціна</p>
                            <p className="text-lg font-semibold">${opportunity.currentPrice}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600 dark:text-gray-400">Рекомендована</p>
                            <p className="text-lg font-semibold text-green-600">${opportunity.suggestedPrice}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600 dark:text-gray-400">Потенційний прибуток</p>
                            <p className="text-lg font-semibold text-blue-600">+${opportunity.potentialProfit}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600 dark:text-gray-400">Залишилось часу</p>
                            <p className="text-lg font-semibold">{opportunity.timeLeft}</p>
                          </div>
                        </div>
                        
                        <div className="mb-4">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm text-gray-600 dark:text-gray-400">Впевненість AI</span>
                            <span className="text-sm font-medium">{(opportunity.confidenceScore * 100).toFixed(0)}%</span>
                          </div>
                          <Progress value={opportunity.confidenceScore * 100} className="h-2" />
                        </div>

                        <Button className="w-full" variant="outline">
                          Переглянути деталі
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Button>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </TabsContent>

            {/* Market Insights Tab */}
            <TabsContent value="insights" className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {marketInsights.map((insight, index) => (
                  <motion.div
                    key={insight.category}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                  >
                    <Card>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">{insight.category}</CardTitle>
                          {getTrendIcon(insight.trend)}
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm text-gray-600 dark:text-gray-400">Рівень попиту</span>
                              <span className="text-sm font-medium">{(insight.demandLevel * 100).toFixed(0)}%</span>
                            </div>
                            <Progress value={insight.demandLevel * 100} className="h-2" />
                          </div>
                          
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm text-gray-600 dark:text-gray-400">Середня ціна</p>
                              <p className="text-lg font-semibold">${insight.averagePrice}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600 dark:text-gray-400">Зростання</p>
                              <p className={`text-lg font-semibold ${insight.growthRate > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {insight.growthRate > 0 ? '+' : ''}{insight.growthRate.toFixed(1)}%
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </TabsContent>

            {/* User Matching Tab */}
            <TabsContent value="matching" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Розумне з'єднання користувачів
                  </CardTitle>
                  <CardDescription>
                    AI автоматично знаходить найкращі збіги між покупцями та продавцями
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <Brain className="h-16 w-16 text-purple-600 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">Функція в розробці</h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-6">
                      Незабаром тут з'явиться інтерфейс для автоматичного з'єднання користувачів
                    </p>
                    <Button variant="outline">
                      Дізнатися більше
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>
      </div>
    </div>
  );
}
