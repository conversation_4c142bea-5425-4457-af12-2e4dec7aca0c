'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Download, 
  Activity, 
  Database, 
  Cloud, 
  Settings, 
  BarChart3,
  RefreshCw,
  Zap,
  Globe,
  HardDrive
} from 'lucide-react';

import DownloadManagerDashboard from '@/components/admin/DownloadManagerDashboard';
import ObservabilityDashboard from '@/components/admin/ObservabilityDashboard';

export default function EnhancedAdminPage() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Enhanced Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Управління покращеною системою скрапінгу та завантаження моделей
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="bg-green-50 text-green-700">
            <Zap className="h-3 w-3 mr-1" />
            Enhanced Mode
          </Badge>
          <Badge variant="outline">
            <Globe className="h-3 w-3 mr-1" />
            Cloudflare
          </Badge>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">
            <BarChart3 className="h-4 w-4 mr-2" />
            Огляд
          </TabsTrigger>
          <TabsTrigger value="downloads">
            <Download className="h-4 w-4 mr-2" />
            Завантаження
          </TabsTrigger>
          <TabsTrigger value="observability">
            <Activity className="h-4 w-4 mr-2" />
            Моніторинг
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="h-4 w-4 mr-2" />
            Налаштування
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* System Status Cards */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Bright Data MCP</CardTitle>
                <Zap className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">Активний</div>
                <p className="text-xs text-muted-foreground">
                  Реальні MCP виклики
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Model Downloads</CardTitle>
                <Download className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">Готовий</div>
                <p className="text-xs text-muted-foreground">
                  Durable Object активний
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Cloudflare R2</CardTitle>
                <Cloud className="h-4 w-4 text-orange-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">Підключено</div>
                <p className="text-xs text-muted-foreground">
                  Файли зберігаються
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Observability</CardTitle>
                <Activity className="h-4 w-4 text-purple-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">Працює</div>
                <p className="text-xs text-muted-foreground">
                  Метрики збираються
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Architecture Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Архітектура системи</CardTitle>
              <CardDescription>
                Покращена інтеграція Bright Data MCP та Cloudflare
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center space-y-2">
                  <div className="bg-blue-100 rounded-full p-4 w-16 h-16 mx-auto flex items-center justify-center">
                    <Zap className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="font-semibold">Bright Data MCP</h3>
                  <p className="text-sm text-muted-foreground">
                    Реальні виклики для скрапінгу популярних 3D моделей з платформ
                  </p>
                  <div className="flex flex-wrap gap-1 justify-center">
                    <Badge variant="secondary" className="text-xs">Printables</Badge>
                    <Badge variant="secondary" className="text-xs">MakerWorld</Badge>
                    <Badge variant="secondary" className="text-xs">Thangs</Badge>
                  </div>
                </div>

                <div className="text-center space-y-2">
                  <div className="bg-green-100 rounded-full p-4 w-16 h-16 mx-auto flex items-center justify-center">
                    <Download className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="font-semibold">Model Download Manager</h3>
                  <p className="text-sm text-muted-foreground">
                    Автоматичне завантаження 3D файлів на Cloudflare R2 з retry логікою
                  </p>
                  <div className="flex flex-wrap gap-1 justify-center">
                    <Badge variant="secondary" className="text-xs">STL</Badge>
                    <Badge variant="secondary" className="text-xs">OBJ</Badge>
                    <Badge variant="secondary" className="text-xs">3MF</Badge>
                  </div>
                </div>

                <div className="text-center space-y-2">
                  <div className="bg-purple-100 rounded-full p-4 w-16 h-16 mx-auto flex items-center justify-center">
                    <Activity className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="font-semibold">Cloudflare Observability</h3>
                  <p className="text-sm text-muted-foreground">
                    Метрики, логування та моніторинг всіх процесів в реальному часі
                  </p>
                  <div className="flex flex-wrap gap-1 justify-center">
                    <Badge variant="secondary" className="text-xs">Analytics</Badge>
                    <Badge variant="secondary" className="text-xs">KV</Badge>
                    <Badge variant="secondary" className="text-xs">Logs</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Швидкі дії</CardTitle>
              <CardDescription>
                Основні операції для управління системою
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button variant="outline" className="h-20 flex-col">
                  <RefreshCw className="h-6 w-6 mb-2" />
                  <span className="text-sm">Запустити скрапінг</span>
                </Button>
                
                <Button variant="outline" className="h-20 flex-col">
                  <Download className="h-6 w-6 mb-2" />
                  <span className="text-sm">Обробити чергу</span>
                </Button>
                
                <Button variant="outline" className="h-20 flex-col">
                  <Database className="h-6 w-6 mb-2" />
                  <span className="text-sm">Очистити кеш</span>
                </Button>
                
                <Button variant="outline" className="h-20 flex-col">
                  <HardDrive className="h-6 w-6 mb-2" />
                  <span className="text-sm">Експорт даних</span>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Остання активність</CardTitle>
              <CardDescription>
                Останні події в системі
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <div className="bg-green-100 rounded-full p-2">
                    <Download className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Завантажено 15 моделей з Printables</p>
                    <p className="text-xs text-muted-foreground">2 хвилини тому</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="bg-blue-100 rounded-full p-2">
                    <Zap className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Скрапінг MakerWorld завершено</p>
                    <p className="text-xs text-muted-foreground">5 хвилин тому</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="bg-purple-100 rounded-full p-2">
                    <Activity className="h-4 w-4 text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Записано 1,247 метрик</p>
                    <p className="text-xs text-muted-foreground">10 хвилин тому</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="downloads">
          <DownloadManagerDashboard />
        </TabsContent>

        <TabsContent value="observability">
          <ObservabilityDashboard />
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Налаштування системи</CardTitle>
              <CardDescription>
                Конфігурація покращеної системи скрапінгу та завантаження
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Bright Data MCP</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>API Token:</span>
                        <Badge variant="outline">Налаштовано</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Fallback Mode:</span>
                        <Badge variant="outline">Увімкнено</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Rate Limit:</span>
                        <span>2000ms</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Model Downloads</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Max Concurrent:</span>
                        <span>5</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Retry Attempts:</span>
                        <span>3</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Timeout:</span>
                        <span>30s</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <h4 className="font-medium mb-2">Cloudflare Services</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div className="text-center">
                      <div className="text-green-600 font-bold">✅</div>
                      <p>D1 Database</p>
                    </div>
                    <div className="text-center">
                      <div className="text-green-600 font-bold">✅</div>
                      <p>R2 Storage</p>
                    </div>
                    <div className="text-center">
                      <div className="text-green-600 font-bold">✅</div>
                      <p>KV Storage</p>
                    </div>
                    <div className="text-center">
                      <div className="text-green-600 font-bold">✅</div>
                      <p>Analytics</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
