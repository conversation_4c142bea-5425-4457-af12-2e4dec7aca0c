'use client';

import { SchedulerPanel } from '@/components/admin/scheduler-panel';
import { BrightDataScraperPanel } from '@/components/marketplace/bright-data-scraper-panel';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import {
    AlertTriangle,
    BarChart3,
    CheckCircle,
    Database,
    Globe,
    RefreshCw,
    Settings,
    TrendingUp,
    Zap
} from 'lucide-react';
import { useEffect, useState } from 'react';

interface DashboardStats {
  totalModels: number;
  scrapedToday: number;
  platforms: {
    makerworld: number;
    printables: number;
    thangs: number;
  };
  lastUpdate: string;
  status: 'active' | 'idle' | 'error';
}

export default function BrightDataAdminPage() {
  const [stats, setStats] = useState<DashboardStats>({
    totalModels: 0,
    scrapedToday: 0,
    platforms: {
      makerworld: 0,
      printables: 0,
      thangs: 0
    },
    lastUpdate: 'Ніколи',
    status: 'idle'
  });

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const response = await fetch('/api/scraped-models?stats=true');
      if (response.ok) {
        const data = await response.json();
        if (data.stats) {
          setStats({
            totalModels: data.stats.total || 0,
            scrapedToday: data.stats.today || 0,
            platforms: {
              makerworld: data.stats.byPlatform?.makerworld || 0,
              printables: data.stats.byPlatform?.printables || 0,
              thangs: data.stats.byPlatform?.thangs || 0
            },
            lastUpdate: data.stats.lastUpdate || 'Ніколи',
            status: 'active'
          });
        }
      }
    } catch (error) {
      console.error('Помилка завантаження статистики:', error);
      setStats(prev => ({ ...prev, status: 'error' }));
    }
  };

  const handleQuickScrape = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/scrape', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'bright-data-scrape', count: 10 })
      });
      
      const data = await response.json();
      if (data.success) {
        await loadStats();
      }
    } catch (error) {
      console.error('Помилка швидкого скрапінгу:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div 
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-4">
            Bright Data Scraping Panel
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Управління скрапінгом 3D моделей з makerworld.com, printables.com та thangs.com
          </p>
        </motion.div>

        {/* Stats Cards */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Всього моделей</CardTitle>
              <Database className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalModels.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                В базі даних
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Сьогодні</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.scrapedToday.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                Нових моделей
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Платформи</CardTitle>
              <Globe className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
              <p className="text-xs text-muted-foreground">
                Активних джерел
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Статус</CardTitle>
              {stats.status === 'active' ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : stats.status === 'error' ? (
                <AlertTriangle className="h-4 w-4 text-red-600" />
              ) : (
                <RefreshCw className="h-4 w-4 text-gray-400" />
              )}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <Badge variant={stats.status === 'active' ? 'default' : stats.status === 'error' ? 'destructive' : 'secondary'}>
                  {stats.status === 'active' ? 'Активний' : stats.status === 'error' ? 'Помилка' : 'Очікування'}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground">
                Оновлено: {stats.lastUpdate}
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Platform Stats */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Card className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 border-blue-200/50 dark:border-blue-700/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <span className="text-2xl">🏭</span>
                MakerWorld
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-600">{stats.platforms.makerworld.toLocaleString()}</div>
              <p className="text-sm text-gray-600 dark:text-gray-400">моделей</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200/50 dark:border-green-700/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <span className="text-2xl">🖨️</span>
                Printables
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-600">{stats.platforms.printables.toLocaleString()}</div>
              <p className="text-sm text-gray-600 dark:text-gray-400">моделей</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-200/50 dark:border-purple-700/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <span className="text-2xl">💎</span>
                Thangs
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-600">{stats.platforms.thangs.toLocaleString()}</div>
              <p className="text-sm text-gray-600 dark:text-gray-400">моделей</p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Quick Actions */}
        <motion.div 
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-yellow-500" />
                Швидкі дії
              </CardTitle>
              <CardDescription>
                Запустіть скрапінг одним кліком
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                <Button
                  onClick={handleQuickScrape}
                  disabled={isLoading}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  {isLoading ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Скрапінг...
                    </>
                  ) : (
                    <>
                      <Zap className="h-4 w-4 mr-2" />
                      Швидкий скрапінг
                    </>
                  )}
                </Button>
                
                <Button
                  onClick={loadStats}
                  variant="outline"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Оновити статистику
                </Button>
                
                <Button
                  asChild
                  variant="outline"
                >
                  <a href="/marketplace" target="_blank">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Переглянути маркетплейс
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Main Scraping Panel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <Tabs defaultValue="scraper" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="scraper">Скрапер</TabsTrigger>
              <TabsTrigger value="scheduler">Розклад</TabsTrigger>
              <TabsTrigger value="analytics">Аналітика</TabsTrigger>
              <TabsTrigger value="settings">Налаштування</TabsTrigger>
            </TabsList>

            <TabsContent value="scraper" className="space-y-6">
              <BrightDataScraperPanel />
            </TabsContent>

            <TabsContent value="scheduler" className="space-y-6">
              <SchedulerPanel />
            </TabsContent>

            <TabsContent value="analytics" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Аналітика скрапінгу
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <BarChart3 className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-2">Аналітика в розробці</h3>
                    <p className="text-gray-500">
                      Тут буде детальна аналітика ефективності скрапінгу
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Налаштування скрапінгу
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-12">
                    <Settings className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-2">Налаштування в розробці</h3>
                    <p className="text-gray-500">
                      Тут будуть налаштування частоти скрапінгу, фільтрів та інших параметрів
                    </p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>
      </div>
    </div>
  );
}
