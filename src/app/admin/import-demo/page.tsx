'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Download, 
  Heart, 
  ExternalLink, 
  Play, 
  BarChart3, 
  Package,
  Zap,
  CheckCircle,
  Clock,
  TrendingUp
} from 'lucide-react';

interface DemoModel {
  id: string;
  name: string;
  platform: string;
  url: string;
  author: string;
  category: string;
  downloads: number;
  likes: number;
  isFree: boolean;
  tags: string[];
}

interface SystemMetrics {
  scraping: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    successRate: number;
    averageResponseTime: number;
    platforms: Record<string, any>;
  };
  jobQueue: {
    totalJobs: number;
    pendingJobs: number;
    processingJobs: number;
    completedJobs: number;
    failedJobs: number;
    averageProcessingTime: number;
    queueThroughput: number;
  };
  system: {
    uptime: string;
    memoryUsage: string;
    healthScore: number;
    lastUpdate: string;
  };
}

export default function ImportDemoPage() {
  const [models, setModels] = useState<DemoModel[]>([]);
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [importStatus, setImportStatus] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchDemoData();
    fetchMetrics();
  }, []);

  const fetchDemoData = async () => {
    try {
      const response = await fetch('/api/test-import?action=demo');
      const result = await response.json() as any;
      if (result.success) {
        setModels(result.data.models);
      }
    } catch (error) {
      console.error('Failed to fetch demo data:', error);
    }
  };

  const fetchMetrics = async () => {
    try {
      const response = await fetch('/api/test-import?action=metrics');
      const result = await response.json() as any;
      if (result.success) {
        setMetrics(result.data.metrics);
      }
    } catch (error) {
      console.error('Failed to fetch metrics:', error);
    }
  };

  const startDemoImport = async (type: string) => {
    setLoading(true);
    try {
      const response = await fetch('/api/test-import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type })
      });
      
      const result = await response.json() as any;
      if (result.success) {
        setImportStatus(result.data);
        // Симуляція прогресу
        simulateImportProgress();
      }
    } catch (error) {
      console.error('Failed to start import:', error);
    } finally {
      setLoading(false);
    }
  };

  const simulateImportProgress = () => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 20;
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
        setImportStatus((prev: any) => ({
          ...prev,
          status: 'completed',
          message: '✅ Імпорт успішно завершено!'
        }));
      }
      
      setImportStatus((prev: any) => ({
        ...prev,
        progress: Math.round(progress),
        message: progress < 100 ? `Імпорт в процесі... ${Math.round(progress)}%` : '✅ Завершено!'
      }));
    }, 1000);
  };

  const getPlatformColor = (platform: string) => {
    const colors: Record<string, string> = {
      thingiverse: 'bg-blue-500',
      myminifactory: 'bg-green-500',
      printables: 'bg-orange-500',
      thangs: 'bg-purple-500',
      makerworld: 'bg-red-500'
    };
    return colors[platform] || 'bg-gray-500';
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">🚀 3D Marketplace Import Demo</h1>
        <p className="text-xl text-muted-foreground">
          Демонстрація системи імпорту найпопулярніших 3D моделей
        </p>
      </div>

      {/* Import Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            Запуск імпорту
          </CardTitle>
          <CardDescription>
            Оберіть тип імпорту для демонстрації
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-4">
            <Button 
              onClick={() => startDemoImport('sample')} 
              disabled={loading}
              className="flex items-center gap-2"
            >
              <Package className="w-4 h-4" />
              Зразки (15 моделей)
            </Button>
            <Button 
              onClick={() => startDemoImport('all')} 
              disabled={loading}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Всі популярні (50 моделей)
            </Button>
          </div>

          {importStatus && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="font-medium">Статус імпорту:</span>
                <Badge variant={importStatus.progress === 100 ? 'default' : 'secondary'}>
                  {importStatus.progress === 100 ? 'Завершено' : 'В процесі'}
                </Badge>
              </div>
              <Progress value={importStatus.progress || 0} className="h-3" />
              <p className="text-sm text-muted-foreground">
                {importStatus.message}
              </p>
              {importStatus.progress === 100 && (
                <div className="flex items-center gap-2 text-green-600">
                  <CheckCircle className="w-4 h-4" />
                  <span>Імпорт завершено успішно!</span>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Demo Models Grid */}
      <div>
        <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
          <Package className="w-6 h-6" />
          Популярні моделі для імпорту
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {models.map((model) => (
            <Card key={model.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{model.name}</CardTitle>
                    <CardDescription>by {model.author}</CardDescription>
                  </div>
                  <Badge 
                    className={`${getPlatformColor(model.platform)} text-white`}
                  >
                    {model.platform}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-1">
                    <Download className="w-4 h-4" />
                    <span>{formatNumber(model.downloads)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Heart className="w-4 h-4" />
                    <span>{formatNumber(model.likes)}</span>
                  </div>
                  <Badge variant="outline">{model.category}</Badge>
                </div>
                
                <div className="flex flex-wrap gap-1">
                  {model.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {model.tags.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{model.tags.length - 3}
                    </Badge>
                  )}
                </div>

                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  onClick={() => window.open(model.url, '_blank')}
                >
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Переглянути оригінал
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* System Metrics */}
      {metrics && (
        <div>
          <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
            <BarChart3 className="w-6 h-6" />
            Метрики системи
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Успішність скрапінгу</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {metrics.scraping.successRate}%
                </div>
                <p className="text-xs text-muted-foreground">
                  {metrics.scraping.successfulRequests} з {metrics.scraping.totalRequests} запитів
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Черга завдань</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metrics.jobQueue.completedJobs}
                </div>
                <p className="text-xs text-muted-foreground">
                  Завершено з {metrics.jobQueue.totalJobs} загалом
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Здоров'я системи</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {metrics.system.healthScore}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Час роботи: {metrics.system.uptime}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Пропускна здатність</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {metrics.jobQueue.queueThroughput}
                </div>
                <p className="text-xs text-muted-foreground">
                  завдань/годину
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Platform Statistics */}
      {metrics && (
        <Card>
          <CardHeader>
            <CardTitle>Статистика по платформах</CardTitle>
            <CardDescription>
              Продуктивність скрапінгу для кожної платформи
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(metrics.scraping.platforms).map(([platform, stats]: [string, any]) => (
                <div key={platform} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${getPlatformColor(platform)}`} />
                      <span className="font-medium capitalize">{platform}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {stats.success}/{stats.requests} запитів
                    </div>
                  </div>
                  <Progress 
                    value={(stats.success / stats.requests) * 100} 
                    className="h-2"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Успішність: {Math.round((stats.success / stats.requests) * 100)}%</span>
                    <span>Середній час: {stats.avgTime}с</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
