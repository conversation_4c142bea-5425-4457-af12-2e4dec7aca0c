/**
 * Admin Analytics Page - Сторінка аналітики для адміністраторів
 */

import { Metadata } from 'next';
import ImageAnalyticsDashboard from '@/components/analytics/ImageAnalyticsDashboard';

export const metadata: Metadata = {
  title: 'Аналітика зображень | 3D Marketplace Admin',
  description: 'Dashboard для моніторингу використання зображень та продуктивності',
};

export default function AnalyticsPage() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Аналітика зображень
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Моніторинг використання зображень, продуктивності та статистики завантажень
        </p>
      </div>

      <ImageAnalyticsDashboard />
    </div>
  );
}
