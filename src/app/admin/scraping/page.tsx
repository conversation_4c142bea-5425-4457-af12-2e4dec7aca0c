/**
 * Сторінка адміністрування автоматизованого скрапінгу
 */

import { Metadata } from 'next';
import ScrapingDashboard from '@/components/admin/scraping-dashboard';

export const metadata: Metadata = {
  title: 'Автоматизований скрапінг | 3D Marketplace Admin',
  description: 'Управління автоматизованим скрапінгом популярних 3D моделей з Bright Data MCP',
};

export default function ScrapingAdminPage() {
  return (
    <div className="container mx-auto py-8">
      <ScrapingDashboard />
    </div>
  );
}
