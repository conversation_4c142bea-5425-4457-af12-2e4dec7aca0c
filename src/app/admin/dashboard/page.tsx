'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  Users, 
  Package, 
  DollarSign, 
  Settings, 
  Shield, 
  AlertTriangle, 
  TrendingUp, 
  Download, 
  Eye,
  Activity,
  CheckCircle,
  Clock,
  Zap,
  Globe,
  Database,
  Cpu,
  HardDrive,
  Network,
  FileText,
  Star,
  ShoppingCart,
  UserCheck,
  Heart,
  ExternalLink
} from 'lucide-react';

interface DashboardStats {
  overview: {
    totalUsers: number;
    totalModels: number;
    totalDownloads: number;
    totalRevenue: number;
    activeUsers: number;
    newUsersToday: number;
    modelsAddedToday: number;
    downloadsToday: number;
  };
  system: {
    uptime: string;
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    networkTraffic: string;
    healthScore: number;
    lastBackup: string;
    activeConnections: number;
  };
  scraping: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    successRate: number;
    averageResponseTime: number;
    platformStats: Record<string, any>;
    rateLimitStatus: Record<string, any>;
  };
  content: {
    pendingModeration: number;
    reportedContent: number;
    featuredModels: number;
    categoriesCount: number;
    tagsCount: number;
    averageRating: number;
  };
}

export default function AdminDashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchDashboardStats();
    // Update every 30 seconds
    const interval = setInterval(fetchDashboardStats, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('/api/admin/dashboard-stats');
      const result = await response.json();
      if (result.success) {
        setStats(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
      // Fallback data for demonstration
      setStats({
        overview: {
          totalUsers: 1247,
          totalModels: 8934,
          totalDownloads: 45672,
          totalRevenue: 12450.75,
          activeUsers: 89,
          newUsersToday: 23,
          modelsAddedToday: 156,
          downloadsToday: 892
        },
        system: {
          uptime: '7 days 14 hours',
          cpuUsage: 45,
          memoryUsage: 67,
          diskUsage: 34,
          networkTraffic: '2.4 GB/day',
          healthScore: 96,
          lastBackup: '2 hours ago',
          activeConnections: 234
        },
        scraping: {
          totalRequests: 15678,
          successfulRequests: 14892,
          failedRequests: 786,
          successRate: 95,
          averageResponseTime: 1.2,
          platformStats: {
            thingiverse: { requests: 5234, success: 4987, avgTime: 1.1 },
            printables: { requests: 4567, success: 4321, avgTime: 0.9 },
            myminifactory: { requests: 3456, success: 3298, avgTime: 1.4 },
            thangs: { requests: 1567, success: 1456, avgTime: 1.6 },
            makerworld: { requests: 854, success: 830, avgTime: 1.3 }
          },
          rateLimitStatus: {
            thingiverse: { remaining: 8, total: 10, resetTime: '14:30' },
            printables: { remaining: 12, total: 15, resetTime: '15:00' },
            myminifactory: { remaining: 6, total: 8, resetTime: '14:45' }
          }
        },
        content: {
          pendingModeration: 23,
          reportedContent: 5,
          featuredModels: 45,
          categoriesCount: 28,
          tagsCount: 1456,
          averageRating: 4.3
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('uk-UA', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getPlatformColor = (platform: string) => {
    const colors: Record<string, string> = {
      thingiverse: 'bg-blue-500',
      printables: 'bg-orange-500',
      myminifactory: 'bg-green-500',
      thangs: 'bg-purple-500',
      makerworld: 'bg-red-500'
    };
    return colors[platform] || 'bg-gray-500';
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold">🛠️ Administrative Dashboard</h1>
          <p className="text-muted-foreground mt-2">
            3D Marketplace management and system monitoring
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-green-600 border-green-600">
            <CheckCircle className="w-3 h-3 mr-1" />
            System Online
          </Badge>
          <Button onClick={fetchDashboardStats} variant="outline" size="sm">
            <Activity className="w-4 h-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="system" className="flex items-center gap-2">
            <Cpu className="w-4 h-4" />
            System
          </TabsTrigger>
          <TabsTrigger value="scraping" className="flex items-center gap-2">
            <Globe className="w-4 h-4" />
            Scraping
          </TabsTrigger>
          <TabsTrigger value="content" className="flex items-center gap-2">
            <FileText className="w-4 h-4" />
            Content
          </TabsTrigger>
          <TabsTrigger value="tools" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Tools
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(stats?.overview.totalUsers || 0)}</div>
                <p className="text-xs text-muted-foreground">
                  +{stats?.overview.newUsersToday || 0} today
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Models</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(stats?.overview.totalModels || 0)}</div>
                <p className="text-xs text-muted-foreground">
                  +{stats?.overview.modelsAddedToday || 0} today
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Downloads</CardTitle>
                <Download className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(stats?.overview.totalDownloads || 0)}</div>
                <p className="text-xs text-muted-foreground">
                  +{stats?.overview.downloadsToday || 0} today
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(stats?.overview.totalRevenue || 0)}</div>
                <p className="text-xs text-muted-foreground">
                  Active users: {stats?.overview.activeUsers || 0}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Швидкі дії</CardTitle>
                <CardDescription>Найчастіші адміністративні завдання</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full justify-start" asChild>
                  <a href="/admin/import-demo">
                    <Zap className="w-4 h-4 mr-2" />
                    Демо імпорту моделей
                  </a>
                </Button>
                <Button className="w-full justify-start" variant="outline" asChild>
                  <a href="/admin/scraper">
                    <Globe className="w-4 h-4 mr-2" />
                    Інструменти скрапінгу
                  </a>
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Shield className="w-4 h-4 mr-2" />
                  Модерація контенту ({stats?.content.pendingModeration || 0})
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  Скарги користувачів ({stats?.content.reportedContent || 0})
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Статус системи</CardTitle>
                <CardDescription>Поточний стан всіх компонентів</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Веб-сервер</span>
                  </div>
                  <Badge variant="outline" className="text-green-600 border-green-600">Працює</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>База даних</span>
                  </div>
                  <Badge variant="outline" className="text-green-600 border-green-600">Працює</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Система скрапінгу</span>
                  </div>
                  <Badge variant="outline" className="text-green-600 border-green-600">Працює</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Cloudflare</span>
                  </div>
                  <Badge variant="outline" className="text-green-600 border-green-600">Працює</Badge>
                </div>
                <div className="pt-2 border-t">
                  <div className="flex items-center justify-between text-sm">
                    <span>Загальне здоров'я системи</span>
                    <span className="font-bold text-green-600">{stats?.system.healthScore || 96}%</span>
                  </div>
                  <Progress value={stats?.system.healthScore || 96} className="mt-2" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* System Tab */}
        <TabsContent value="system" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">CPU використання</CardTitle>
                <Cpu className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.system.cpuUsage || 45}%</div>
                <Progress value={stats?.system.cpuUsage || 45} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Пам'ять</CardTitle>
                <HardDrive className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.system.memoryUsage || 67}%</div>
                <Progress value={stats?.system.memoryUsage || 67} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Диск</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.system.diskUsage || 34}%</div>
                <Progress value={stats?.system.diskUsage || 34} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Мережа</CardTitle>
                <Network className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.system.networkTraffic || '2.4 GB'}</div>
                <p className="text-xs text-muted-foreground">за день</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Інформація про систему</CardTitle>
                <CardDescription>Основні параметри сервера</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Час роботи</span>
                  <Badge variant="outline">{stats?.system.uptime || '7 днів 14 годин'}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Активні з'єднання</span>
                  <Badge variant="outline">{stats?.system.activeConnections || 234}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Останній бекап</span>
                  <Badge variant="outline">{stats?.system.lastBackup || '2 години тому'}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Здоров'я системи</span>
                  <Badge variant="outline" className="text-green-600 border-green-600">
                    {stats?.system.healthScore || 96}%
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cloudflare статус</CardTitle>
                <CardDescription>Стан інтеграції з Cloudflare</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>D1 Database</span>
                  </div>
                  <Badge variant="outline" className="text-green-600 border-green-600">Підключено</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>KV Storage</span>
                  </div>
                  <Badge variant="outline" className="text-green-600 border-green-600">Підключено</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>R2 Storage</span>
                  </div>
                  <Badge variant="outline" className="text-green-600 border-green-600">Підключено</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Analytics Engine</span>
                  </div>
                  <Badge variant="outline" className="text-green-600 border-green-600">Підключено</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Scraping Tab */}
        <TabsContent value="scraping" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Успішність</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats?.scraping.successRate || 95}%</div>
                <p className="text-xs text-muted-foreground">
                  {stats?.scraping.successfulRequests || 14892} з {stats?.scraping.totalRequests || 15678}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Середній час</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats?.scraping.averageResponseTime || 1.2}с</div>
                <p className="text-xs text-muted-foreground">на запит</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Помилки</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{stats?.scraping.failedRequests || 786}</div>
                <p className="text-xs text-muted-foreground">невдалих запитів</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Всього запитів</CardTitle>
                <Globe className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(stats?.scraping.totalRequests || 15678)}</div>
                <p className="text-xs text-muted-foreground">за весь час</p>
              </CardContent>
            </Card>
          </div>

          {/* Platform Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Статистика по платформах</CardTitle>
              <CardDescription>Продуктивність скрапінгу для кожної платформи</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(stats?.scraping.platformStats || {}).map(([platform, platformStats]: [string, any]) => (
                  <div key={platform} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${getPlatformColor(platform)}`} />
                        <span className="font-medium capitalize">{platform}</span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {platformStats.success}/{platformStats.requests} запитів
                      </div>
                    </div>
                    <Progress
                      value={(platformStats.success / platformStats.requests) * 100}
                      className="h-2"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Успішність: {Math.round((platformStats.success / platformStats.requests) * 100)}%</span>
                      <span>Середній час: {platformStats.avgTime}с</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Rate Limits */}
          <Card>
            <CardHeader>
              <CardTitle>Статус rate limits</CardTitle>
              <CardDescription>Поточні обмеження API для кожної платформи</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Object.entries(stats?.scraping.rateLimitStatus || {}).map(([platform, limits]: [string, any]) => (
                  <div key={platform} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium capitalize">{platform}</span>
                      <Badge variant="outline">
                        {limits.remaining}/{limits.total}
                      </Badge>
                    </div>
                    <Progress value={(limits.remaining / limits.total) * 100} className="h-2" />
                    <p className="text-xs text-muted-foreground">
                      Скидання о {limits.resetTime}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Content Tab */}
        <TabsContent value="content" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">На модерації</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">{stats?.content.pendingModeration || 23}</div>
                <p className="text-xs text-muted-foreground">моделей чекають</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Скарги</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{stats?.content.reportedContent || 5}</div>
                <p className="text-xs text-muted-foreground">потребують уваги</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Рекомендовані</CardTitle>
                <Star className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{stats?.content.featuredModels || 45}</div>
                <p className="text-xs text-muted-foreground">моделей у топі</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Середній рейтинг</CardTitle>
                <Heart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats?.content.averageRating || 4.3}</div>
                <p className="text-xs text-muted-foreground">з 5 зірок</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Управління контентом</CardTitle>
                <CardDescription>Швидкі дії для модерації</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full justify-start" variant="outline">
                  <Eye className="w-4 h-4 mr-2" />
                  Переглянути моделі на модерації ({stats?.content.pendingModeration || 23})
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  Розглянути скарги ({stats?.content.reportedContent || 5})
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Star className="w-4 h-4 mr-2" />
                  Управління рекомендованими
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <FileText className="w-4 h-4 mr-2" />
                  Управління категоріями ({stats?.content.categoriesCount || 28})
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Статистика контенту</CardTitle>
                <CardDescription>Загальна інформація про контент</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Категорій</span>
                  <Badge variant="outline">{stats?.content.categoriesCount || 28}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Тегів</span>
                  <Badge variant="outline">{formatNumber(stats?.content.tagsCount || 1456)}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Рекомендованих моделей</span>
                  <Badge variant="outline">{stats?.content.featuredModels || 45}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span>Середній рейтинг</span>
                  <Badge variant="outline" className="text-green-600 border-green-600">
                    {stats?.content.averageRating || 4.3}/5
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Tools Tab */}
        <TabsContent value="tools" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  Імпорт моделей
                </CardTitle>
                <CardDescription>Інструменти для імпорту з різних платформ</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full" asChild>
                  <a href="/admin/import-demo">
                    <Package className="w-4 h-4 mr-2" />
                    Демо імпорту
                  </a>
                </Button>
                <Button className="w-full" variant="outline" asChild>
                  <a href="/admin/scraper">
                    <Globe className="w-4 h-4 mr-2" />
                    Ручний скрапінг
                  </a>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="w-5 h-5" />
                  База даних
                </CardTitle>
                <CardDescription>Управління даними та бекапи</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full" variant="outline">
                  <Database className="w-4 h-4 mr-2" />
                  Створити бекап
                </Button>
                <Button className="w-full" variant="outline">
                  <FileText className="w-4 h-4 mr-2" />
                  Експорт даних
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Моніторинг
                </CardTitle>
                <CardDescription>Системний моніторинг та логи</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full" variant="outline">
                  <Activity className="w-4 h-4 mr-2" />
                  Переглянути логи
                </Button>
                <Button className="w-full" variant="outline">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Аналітика
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Користувачі
                </CardTitle>
                <CardDescription>Управління користувачами</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full" variant="outline">
                  <Users className="w-4 h-4 mr-2" />
                  Список користувачів
                </Button>
                <Button className="w-full" variant="outline">
                  <UserCheck className="w-4 h-4 mr-2" />
                  Модерація профілів
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingCart className="w-5 h-5" />
                  Продажі
                </CardTitle>
                <CardDescription>Управління замовленнями та платежами</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full" variant="outline">
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Замовлення
                </Button>
                <Button className="w-full" variant="outline">
                  <DollarSign className="w-4 h-4 mr-2" />
                  Фінансові звіти
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  Налаштування
                </CardTitle>
                <CardDescription>Конфігурація системи</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full" variant="outline">
                  <Settings className="w-4 h-4 mr-2" />
                  Загальні налаштування
                </Button>
                <Button className="w-full" variant="outline">
                  <Shield className="w-4 h-4 mr-2" />
                  Безпека
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
