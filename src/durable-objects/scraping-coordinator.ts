/**
 * Durable Object для координації скрапінгу
 * Забезпечує централізоване управління завданнями скрапінгу
 */

export interface ScrapingState {
  activeJobs: Map<string, any>;
  scheduledTasks: Map<string, any>;
  platformStats: Map<string, any>;
  globalConfig: any;
  lastActivity: string;
}

export interface ScrapingMessage {
  type: 'START_JOB' | 'UPDATE_JOB' | 'COMPLETE_JOB' | 'CANCEL_JOB' | 
        'SCHEDULE_TASK' | 'UPDATE_STATS' | 'GET_STATUS' | 'CLEANUP';
  payload: any;
  timestamp: string;
  requestId: string;
}

export interface ScrapingResponse {
  success: boolean;
  data?: any;
  error?: string;
  timestamp: string;
  requestId: string;
}

export class ScrapingCoordinator {
  private state: DurableObjectState;
  private env: any;
  private scrapingState: ScrapingState;

  constructor(state: DurableObjectState, env: any) {
    this.state = state;
    this.env = env;
    
    // Ініціалізуємо стан
    this.scrapingState = {
      activeJobs: new Map(),
      scheduledTasks: new Map(),
      platformStats: new Map(),
      globalConfig: {
        maxConcurrentJobs: 10,
        defaultTimeout: 30000,
        retryAttempts: 3,
        cleanupInterval: 3600000 // 1 година
      },
      lastActivity: new Date().toISOString()
    };

    // Запускаємо періодичну очистку
    this.scheduleCleanup();
  }

  /**
   * Обробка HTTP запитів
   */
  async fetch(request: Request): Promise<Response> {
    try {
      const url = new URL(request.url);
      const path = url.pathname;

      // Маршрутизація запитів
      switch (path) {
        case '/status':
          return this.handleStatusRequest(request);
        case '/jobs':
          return this.handleJobsRequest(request);
        case '/schedule':
          return this.handleScheduleRequest(request);
        case '/stats':
          return this.handleStatsRequest(request);
        case '/cleanup':
          return this.handleCleanupRequest(request);
        default:
          return new Response('Not Found', { status: 404 });
      }

    } catch (error) {
      console.error('❌ Помилка ScrapingCoordinator:', error);
      return new Response(
        JSON.stringify({ 
          error: 'Internal Server Error',
          details: error instanceof Error ? error.message : 'Unknown error'
        }),
        { 
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  }

  /**
   * Обробка WebSocket з'єднань для real-time оновлень
   */
  async webSocketMessage(ws: WebSocket, message: ArrayBuffer | string): Promise<void> {
    try {
      const messageStr = typeof message === 'string' ? message : new TextDecoder().decode(message);
      const parsedMessage: ScrapingMessage = JSON.parse(messageStr);

      console.log(`📨 Отримано WebSocket повідомлення: ${parsedMessage.type}`);

      const response = await this.processMessage(parsedMessage);
      
      // Відправляємо відповідь
      ws.send(JSON.stringify(response));

      // Якщо це оновлення статусу, розсилаємо всім підключеним клієнтам
      if (parsedMessage.type === 'UPDATE_JOB' || parsedMessage.type === 'UPDATE_STATS') {
        await this.broadcastUpdate(parsedMessage);
      }

    } catch (error) {
      console.error('❌ Помилка обробки WebSocket повідомлення:', error);
      
      const errorResponse: ScrapingResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        requestId: 'unknown'
      };

      ws.send(JSON.stringify(errorResponse));
    }
  }

  /**
   * Обробка повідомлень
   */
  private async processMessage(message: ScrapingMessage): Promise<ScrapingResponse> {
    this.scrapingState.lastActivity = new Date().toISOString();

    switch (message.type) {
      case 'START_JOB':
        return await this.startJob(message.payload);
      
      case 'UPDATE_JOB':
        return await this.updateJob(message.payload);
      
      case 'COMPLETE_JOB':
        return await this.completeJob(message.payload);
      
      case 'CANCEL_JOB':
        return await this.cancelJob(message.payload);
      
      case 'SCHEDULE_TASK':
        return await this.scheduleTask(message.payload);
      
      case 'UPDATE_STATS':
        return await this.updateStats(message.payload);
      
      case 'GET_STATUS':
        return await this.getStatus(message.payload);
      
      case 'CLEANUP':
        return await this.performCleanup();
      
      default:
        return {
          success: false,
          error: `Невідомий тип повідомлення: ${message.type}`,
          timestamp: new Date().toISOString(),
          requestId: message.requestId
        };
    }
  }

  /**
   * Запуск нового завдання
   */
  private async startJob(payload: any): Promise<ScrapingResponse> {
    try {
      const { jobId, platforms, config } = payload;

      // Перевіряємо ліміти
      if (this.scrapingState.activeJobs.size >= this.scrapingState.globalConfig.maxConcurrentJobs) {
        return {
          success: false,
          error: 'Досягнуто максимальну кількість одночасних завдань',
          timestamp: new Date().toISOString(),
          requestId: payload.requestId
        };
      }

      // Створюємо завдання
      const job = {
        id: jobId,
        platforms,
        config,
        status: 'running',
        startTime: new Date().toISOString(),
        progress: 0,
        results: [],
        errors: []
      };

      this.scrapingState.activeJobs.set(jobId, job);
      
      // Зберігаємо стан
      await this.persistState();

      console.log(`✅ Запущено завдання: ${jobId}`);

      return {
        success: true,
        data: { jobId, status: 'started' },
        timestamp: new Date().toISOString(),
        requestId: payload.requestId
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Помилка запуску завдання',
        timestamp: new Date().toISOString(),
        requestId: payload.requestId
      };
    }
  }

  /**
   * Оновлення завдання
   */
  private async updateJob(payload: any): Promise<ScrapingResponse> {
    try {
      const { jobId, updates } = payload;
      
      const job = this.scrapingState.activeJobs.get(jobId);
      if (!job) {
        return {
          success: false,
          error: 'Завдання не знайдено',
          timestamp: new Date().toISOString(),
          requestId: payload.requestId
        };
      }

      // Оновлюємо завдання
      Object.assign(job, updates);
      this.scrapingState.activeJobs.set(jobId, job);
      
      await this.persistState();

      return {
        success: true,
        data: { jobId, updated: true },
        timestamp: new Date().toISOString(),
        requestId: payload.requestId
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Помилка оновлення завдання',
        timestamp: new Date().toISOString(),
        requestId: payload.requestId
      };
    }
  }

  /**
   * Завершення завдання
   */
  private async completeJob(payload: any): Promise<ScrapingResponse> {
    try {
      const { jobId, results, errors } = payload;

      const job = this.scrapingState.activeJobs.get(jobId);
      if (!job) {
        return {
          success: false,
          error: 'Завдання не знайдено',
          timestamp: new Date().toISOString(),
          requestId: payload.requestId
        };
      }

      // Завершуємо завдання
      job.status = 'completed';
      job.endTime = new Date().toISOString();
      job.results = results || [];
      job.errors = errors || [];
      job.progress = 100;

      this.scrapingState.activeJobs.set(jobId, job);

      // Оновлюємо статистику платформ
      await this.updatePlatformStats(job);

      // Запускаємо завантаження файлів моделей на R2
      await this.scheduleModelDownloads(job.results);

      await this.persistState();

      console.log(`✅ Завершено завдання: ${jobId}`);

      return {
        success: true,
        data: { jobId, status: 'completed', resultsCount: results?.length || 0 },
        timestamp: new Date().toISOString(),
        requestId: payload.requestId
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Помилка завершення завдання',
        timestamp: new Date().toISOString(),
        requestId: payload.requestId
      };
    }
  }

  /**
   * Скасування завдання
   */
  private async cancelJob(payload: any): Promise<ScrapingResponse> {
    try {
      const { jobId } = payload;
      
      const job = this.scrapingState.activeJobs.get(jobId);
      if (!job) {
        return {
          success: false,
          error: 'Завдання не знайдено',
          timestamp: new Date().toISOString(),
          requestId: payload.requestId
        };
      }

      // Скасовуємо завдання
      job.status = 'cancelled';
      job.endTime = new Date().toISOString();
      
      this.scrapingState.activeJobs.set(jobId, job);
      await this.persistState();

      console.log(`🚫 Скасовано завдання: ${jobId}`);

      return {
        success: true,
        data: { jobId, status: 'cancelled' },
        timestamp: new Date().toISOString(),
        requestId: payload.requestId
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Помилка скасування завдання',
        timestamp: new Date().toISOString(),
        requestId: payload.requestId
      };
    }
  }

  /**
   * Планування завдання
   */
  private async scheduleTask(payload: any): Promise<ScrapingResponse> {
    try {
      const { taskId, task } = payload;
      
      this.scrapingState.scheduledTasks.set(taskId, {
        ...task,
        createdAt: new Date().toISOString()
      });
      
      await this.persistState();

      return {
        success: true,
        data: { taskId, scheduled: true },
        timestamp: new Date().toISOString(),
        requestId: payload.requestId
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Помилка планування завдання',
        timestamp: new Date().toISOString(),
        requestId: payload.requestId
      };
    }
  }

  /**
   * Оновлення статистики
   */
  private async updateStats(payload: any): Promise<ScrapingResponse> {
    try {
      const { platform, stats } = payload;
      
      const currentStats = this.scrapingState.platformStats.get(platform) || {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        lastActivity: new Date().toISOString()
      };

      // Оновлюємо статистику
      Object.assign(currentStats, stats, {
        lastActivity: new Date().toISOString()
      });

      this.scrapingState.platformStats.set(platform, currentStats);
      await this.persistState();

      return {
        success: true,
        data: { platform, updated: true },
        timestamp: new Date().toISOString(),
        requestId: payload.requestId
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Помилка оновлення статистики',
        timestamp: new Date().toISOString(),
        requestId: payload.requestId
      };
    }
  }

  /**
   * Отримання статусу
   */
  private async getStatus(payload: any): Promise<ScrapingResponse> {
    try {
      const status = {
        activeJobs: Array.from(this.scrapingState.activeJobs.values()),
        scheduledTasks: Array.from(this.scrapingState.scheduledTasks.values()),
        platformStats: Object.fromEntries(this.scrapingState.platformStats),
        globalConfig: this.scrapingState.globalConfig,
        lastActivity: this.scrapingState.lastActivity,
        systemInfo: {
          timestamp: new Date().toISOString(),
          activeJobsCount: this.scrapingState.activeJobs.size,
          scheduledTasksCount: this.scrapingState.scheduledTasks.size
        }
      };

      return {
        success: true,
        data: status,
        timestamp: new Date().toISOString(),
        requestId: payload.requestId
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Помилка отримання статусу',
        timestamp: new Date().toISOString(),
        requestId: payload.requestId
      };
    }
  }

  /**
   * Очистка застарілих даних
   */
  private async performCleanup(): Promise<ScrapingResponse> {
    try {
      const now = Date.now();
      const maxAge = 24 * 60 * 60 * 1000; // 24 години
      let cleanedJobs = 0;
      let cleanedTasks = 0;

      // Очищуємо завершені завдання старше 24 годин
      for (const [jobId, job] of this.scrapingState.activeJobs) {
        if (job.endTime) {
          const endTime = new Date(job.endTime).getTime();
          if (now - endTime > maxAge) {
            this.scrapingState.activeJobs.delete(jobId);
            cleanedJobs++;
          }
        }
      }

      // Очищуємо старі заплановані завдання
      for (const [taskId, task] of this.scrapingState.scheduledTasks) {
        if (task.createdAt) {
          const createdTime = new Date(task.createdAt).getTime();
          if (now - createdTime > maxAge * 7) { // 7 днів для завдань
            this.scrapingState.scheduledTasks.delete(taskId);
            cleanedTasks++;
          }
        }
      }

      await this.persistState();

      console.log(`🧹 Очистка завершена: ${cleanedJobs} завдань, ${cleanedTasks} запланованих завдань`);

      return {
        success: true,
        data: { cleanedJobs, cleanedTasks },
        timestamp: new Date().toISOString(),
        requestId: 'cleanup'
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Помилка очистки',
        timestamp: new Date().toISOString(),
        requestId: 'cleanup'
      };
    }
  }

  /**
   * Допоміжні методи
   */

  private async updatePlatformStats(job: any): Promise<void> {
    for (const platform of job.platforms) {
      const stats = this.scrapingState.platformStats.get(platform) || {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        lastActivity: new Date().toISOString()
      };

      stats.totalRequests++;
      if (job.status === 'completed') {
        stats.successfulRequests++;
      } else {
        stats.failedRequests++;
      }

      this.scrapingState.platformStats.set(platform, stats);
    }
  }

  private async persistState(): Promise<void> {
    // Зберігаємо стан в Durable Object storage
    await this.state.storage.put('scrapingState', {
      activeJobs: Array.from(this.scrapingState.activeJobs.entries()),
      scheduledTasks: Array.from(this.scrapingState.scheduledTasks.entries()),
      platformStats: Array.from(this.scrapingState.platformStats.entries()),
      globalConfig: this.scrapingState.globalConfig,
      lastActivity: this.scrapingState.lastActivity
    });
  }

  private async loadState(): Promise<void> {
    const saved = await this.state.storage.get('scrapingState');
    if (saved) {
      this.scrapingState.activeJobs = new Map(saved.activeJobs);
      this.scrapingState.scheduledTasks = new Map(saved.scheduledTasks);
      this.scrapingState.platformStats = new Map(saved.platformStats);
      this.scrapingState.globalConfig = saved.globalConfig;
      this.scrapingState.lastActivity = saved.lastActivity;
    }
  }

  private scheduleCleanup(): void {
    // Запускаємо очистку кожну годину
    setInterval(() => {
      this.performCleanup();
    }, this.scrapingState.globalConfig.cleanupInterval);
  }

  private async broadcastUpdate(message: ScrapingMessage): Promise<void> {
    // Тут буде логіка розсилки оновлень всім підключеним WebSocket клієнтам
    console.log(`📡 Розсилка оновлення: ${message.type}`);
  }

  private async handleStatusRequest(request: Request): Promise<Response> {
    const status = await this.getStatus({ requestId: 'http-status' });
    return new Response(JSON.stringify(status), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleJobsRequest(request: Request): Promise<Response> {
    const jobs = Array.from(this.scrapingState.activeJobs.values());
    return new Response(JSON.stringify({ jobs }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleScheduleRequest(request: Request): Promise<Response> {
    const tasks = Array.from(this.scrapingState.scheduledTasks.values());
    return new Response(JSON.stringify({ tasks }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleStatsRequest(request: Request): Promise<Response> {
    const stats = Object.fromEntries(this.scrapingState.platformStats);
    return new Response(JSON.stringify({ stats }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  private async handleCleanupRequest(request: Request): Promise<Response> {
    const result = await this.performCleanup();
    return new Response(JSON.stringify(result), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * Планування завантаження файлів моделей на R2
   */
  private async scheduleModelDownloads(results: any[]): Promise<void> {
    try {
      if (!this.env.MODEL_DOWNLOAD_MANAGER) {
        console.warn('⚠️ MODEL_DOWNLOAD_MANAGER не налаштований, пропускаємо завантаження файлів');
        return;
      }

      console.log(`📥 Планування завантаження ${results.length} моделей`);

      const downloadManager = this.env.MODEL_DOWNLOAD_MANAGER.idFromName('global');
      const stub = this.env.MODEL_DOWNLOAD_MANAGER.get(downloadManager);

      let scheduledDownloads = 0;

      for (const model of results) {
        try {
          // Перевіряємо, чи є файли для завантаження
          if (!model.downloadUrls || !Array.isArray(model.downloadUrls) || model.downloadUrls.length === 0) {
            continue;
          }

          // Планируємо завантаження кожного файлу
          for (const downloadUrl of model.downloadUrls) {
            if (!downloadUrl.url || !downloadUrl.fileName) {
              continue;
            }

            const downloadJob = {
              modelId: model.id || crypto.randomUUID(),
              sourceUrl: downloadUrl.url,
              fileName: downloadUrl.fileName,
              fileType: this.extractFileType(downloadUrl.fileName),
              platform: model.platform || 'unknown'
            };

            const response = await stub.fetch('https://fake-host/download', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(downloadJob)
            });

            if (response.ok) {
              scheduledDownloads++;
              console.log(`✅ Заплановано завантаження: ${downloadJob.fileName} з ${downloadJob.platform}`);
            } else {
              console.error(`❌ Помилка планування завантаження: ${downloadJob.fileName}`);
            }
          }
        } catch (error) {
          console.error(`❌ Помилка обробки моделі для завантаження:`, error);
        }
      }

      console.log(`📊 Заплановано ${scheduledDownloads} завантажень з ${results.length} моделей`);

    } catch (error) {
      console.error('❌ Помилка планування завантажень моделей:', error);
    }
  }

  /**
   * Витягування типу файлу з назви
   */
  private extractFileType(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase();

    const supportedTypes = ['stl', 'obj', '3mf', 'ply', 'gcode', 'zip', 'rar', '7z'];

    if (extension && supportedTypes.includes(extension)) {
      return extension;
    }

    return 'unknown';
  }

  /**
   * Інтеграція з Cloudflare Observability
   */
  private async recordObservabilityMetrics(jobId: string, platform: string, success: boolean, duration: number): Promise<void> {
    try {
      // Записуємо метрики через API observability
      const metricsData = {
        platform,
        success,
        duration,
        modelsFound: success ? 1 : 0
      };

      // Тут можна додати виклик до observability API
      console.log(`📊 Метрики для ${platform}: ${JSON.stringify(metricsData)}`);

    } catch (error) {
      console.error('❌ Помилка запису метрик observability:', error);
    }
  }
}
