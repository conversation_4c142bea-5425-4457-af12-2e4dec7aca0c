/**
 * ModelDownloadManager Durable Object
 * Управління завантаженням 3D моделей на Cloudflare R2
 */

import { DurableObject } from 'cloudflare:workers';
import { CloudflareMCPClient } from '../lib/cloudflare/real-mcp-client';
import { CloudflareEnv } from '../lib/cloudflare/types';
import { CloudflareMCPConfig } from '../lib/mcp/types';

export interface DownloadJob {
  id: string;
  modelId: string;
  sourceUrl: string;
  fileName: string;
  fileType: string;
  platform: string;
  status: 'pending' | 'downloading' | 'completed' | 'failed' | 'retrying';
  progress: number;
  createdAt: string;
  updatedAt: string;
  retryCount: number;
  maxRetries: number;
  error?: string;
  r2Key?: string;
  fileSize?: number;
  downloadedSize?: number;
}

export interface DownloadStats {
  totalJobs: number;
  pendingJobs: number;
  completedJobs: number;
  failedJobs: number;
  totalDownloadedSize: number;
  averageDownloadTime: number;
}

export class ModelDownloadManager extends DurableObject {
  private jobs: Map<string, DownloadJob> = new Map();
  private activeDownloads: Set<string> = new Set();
  private maxConcurrentDownloads = 5;
  private retryDelayMs = 5000;
  private mcpClient: CloudflareMCPClient;

  constructor(ctx: DurableObjectState, env: CloudflareEnv) {
    super(ctx, env);

    // Ініціалізуємо Cloudflare MCP Client
    const config: CloudflareMCPConfig = {
      enabled: true,
      timeout: 30000,
      retryAttempts: 3,
      enableFallback: true,
      accountId: (env as any).CLOUDFLARE_ACCOUNT_ID,
      services: {
        r2: true,
        d1: true,
        kv: true,
        analytics: true,
        durableObjects: true,
        queues: true
      }
    };

    this.mcpClient = new CloudflareMCPClient(config);
    this.mcpClient.initialize(env);

    // Відновлюємо стан при створенні
    this.restoreState();
  }

  /**
   * HTTP handler для Durable Object
   */
  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;
    const method = request.method;

    try {
      switch (true) {
        case method === 'POST' && path === '/download':
          return await this.handleAddDownload(request);
          
        case method === 'GET' && path === '/jobs':
          return await this.handleGetJobs(request);
          
        case method === 'GET' && path === '/stats':
          return await this.handleGetStats();
          
        case method === 'POST' && path === '/process':
          return await this.handleProcessQueue();
          
        case method === 'DELETE' && path.startsWith('/jobs/'):
          const jobId = path.split('/')[2];
          return await this.handleDeleteJob(jobId);
          
        case method === 'POST' && path.startsWith('/jobs/') && path.endsWith('/retry'):
          const retryJobId = path.split('/')[2];
          return await this.handleRetryJob(retryJobId);
          
        default:
          return new Response('Not Found', { status: 404 });
      }
    } catch (error) {
      console.error('❌ Помилка ModelDownloadManager:', error);
      return new Response(JSON.stringify({ 
        error: error instanceof Error ? error.message : 'Невідома помилка' 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * Додавання нового завдання завантаження
   */
  private async handleAddDownload(request: Request): Promise<Response> {
    const data = await request.json() as {
      modelId: string;
      sourceUrl: string;
      fileName: string;
      fileType: string;
      platform: string;
    };

    const job: DownloadJob = {
      id: crypto.randomUUID(),
      modelId: data.modelId,
      sourceUrl: data.sourceUrl,
      fileName: data.fileName,
      fileType: data.fileType,
      platform: data.platform,
      status: 'pending',
      progress: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      retryCount: 0,
      maxRetries: 3
    };

    this.jobs.set(job.id, job);
    await this.persistState();

    console.log(`📥 Додано завдання завантаження: ${job.id} для моделі ${job.modelId}`);

    // Запускаємо обробку черги
    this.processQueueAsync();

    return new Response(JSON.stringify({ 
      success: true, 
      jobId: job.id,
      job 
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * Отримання списку завдань
   */
  private async handleGetJobs(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const status = url.searchParams.get('status');
    const platform = url.searchParams.get('platform');

    let jobs = Array.from(this.jobs.values());

    if (status) {
      jobs = jobs.filter(job => job.status === status);
    }

    if (platform) {
      jobs = jobs.filter(job => job.platform === platform);
    }

    return new Response(JSON.stringify({ 
      success: true, 
      jobs: jobs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * Отримання статистики
   */
  private async handleGetStats(): Promise<Response> {
    const jobs = Array.from(this.jobs.values());
    
    const stats: DownloadStats = {
      totalJobs: jobs.length,
      pendingJobs: jobs.filter(j => j.status === 'pending' || j.status === 'downloading').length,
      completedJobs: jobs.filter(j => j.status === 'completed').length,
      failedJobs: jobs.filter(j => j.status === 'failed').length,
      totalDownloadedSize: jobs
        .filter(j => j.status === 'completed' && j.fileSize)
        .reduce((sum, j) => sum + (j.fileSize || 0), 0),
      averageDownloadTime: 0 // TODO: розрахувати середній час завантаження
    };

    return new Response(JSON.stringify({ 
      success: true, 
      stats,
      activeDownloads: this.activeDownloads.size,
      maxConcurrentDownloads: this.maxConcurrentDownloads
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * Обробка черги завантажень
   */
  private async handleProcessQueue(): Promise<Response> {
    const processed = await this.processQueue();
    
    return new Response(JSON.stringify({ 
      success: true, 
      processedJobs: processed 
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * Видалення завдання
   */
  private async handleDeleteJob(jobId: string): Promise<Response> {
    if (!this.jobs.has(jobId)) {
      return new Response(JSON.stringify({ 
        error: 'Завдання не знайдено' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    this.jobs.delete(jobId);
    this.activeDownloads.delete(jobId);
    await this.persistState();

    return new Response(JSON.stringify({ 
      success: true 
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * Повторна спроба завантаження
   */
  private async handleRetryJob(jobId: string): Promise<Response> {
    const job = this.jobs.get(jobId);
    
    if (!job) {
      return new Response(JSON.stringify({ 
        error: 'Завдання не знайдено' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (job.retryCount >= job.maxRetries) {
      return new Response(JSON.stringify({ 
        error: 'Перевищено максимальну кількість спроб' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    job.status = 'pending';
    job.retryCount++;
    job.updatedAt = new Date().toISOString();
    job.error = undefined;

    this.jobs.set(jobId, job);
    await this.persistState();

    // Запускаємо обробку черги
    this.processQueueAsync();

    return new Response(JSON.stringify({ 
      success: true, 
      job 
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * Асинхронна обробка черги
   */
  private processQueueAsync(): void {
    // Запускаємо обробку в фоні без очікування
    this.processQueue().catch(error => {
      console.error('❌ Помилка обробки черги:', error);
    });
  }

  /**
   * Обробка черги завантажень
   */
  private async processQueue(): Promise<number> {
    const pendingJobs = Array.from(this.jobs.values())
      .filter(job => job.status === 'pending')
      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

    let processed = 0;

    for (const job of pendingJobs) {
      if (this.activeDownloads.size >= this.maxConcurrentDownloads) {
        break;
      }

      this.activeDownloads.add(job.id);
      this.downloadFileAsync(job);
      processed++;
    }

    return processed;
  }

  /**
   * Асинхронне завантаження файлу
   */
  private downloadFileAsync(job: DownloadJob): void {
    this.downloadFile(job)
      .catch(error => {
        console.error(`❌ Помилка завантаження ${job.id}:`, error);
      })
      .finally(() => {
        this.activeDownloads.delete(job.id);
      });
  }

  /**
   * Завантаження файлу на R2
   */
  private async downloadFile(job: DownloadJob): Promise<void> {
    try {
      console.log(`📥 Початок завантаження: ${job.id} з ${job.sourceUrl}`);

      job.status = 'downloading';
      job.updatedAt = new Date().toISOString();
      this.jobs.set(job.id, job);
      await this.persistState();

      // Завантажуємо файл з джерела
      const response = await fetch(job.sourceUrl, {
        headers: {
          'User-Agent': '3D-Marketplace-Bot/1.0'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentLength = response.headers.get('content-length');
      job.fileSize = contentLength ? parseInt(contentLength) : undefined;

      // Генеруємо ключ для R2
      const r2Key = this.generateR2Key(job);
      job.r2Key = r2Key;

      // Завантажуємо на R2 через Bindings MCP Client
      const body = response.body;
      if (!body) {
        throw new Error('Порожнє тіло відповіді');
      }

      const arrayBuffer = await new Response(body).arrayBuffer();

      const r2Result = await this.mcpClient.r2Operation({
        action: 'put',
        key: r2Key,
        data: arrayBuffer,
        metadata: {
          'content-type': this.getContentType(job.fileType),
          'content-disposition': `attachment; filename="${job.fileName}"`,
          'model-id': job.modelId,
          'platform': job.platform,
          'original-url': job.sourceUrl,
          'uploaded-at': new Date().toISOString()
        }
      });

      if (!r2Result.success) {
        throw new Error(`R2 upload failed: ${r2Result.error}`);
      }

      // Оновлюємо статус завдання
      job.status = 'completed';
      job.progress = 100;
      job.updatedAt = new Date().toISOString();
      this.jobs.set(job.id, job);
      await this.persistState();

      console.log(`✅ Завантаження завершено: ${job.id} -> ${r2Key}`);

    } catch (error) {
      console.error(`❌ Помилка завантаження ${job.id}:`, error);

      job.status = job.retryCount < job.maxRetries ? 'retrying' : 'failed';
      job.error = error instanceof Error ? error.message : 'Невідома помилка';
      job.updatedAt = new Date().toISOString();

      if (job.status === 'retrying') {
        job.retryCount++;
        // Планируємо повторну спробу через затримку
        setTimeout(() => {
          job.status = 'pending';
          this.jobs.set(job.id, job);
          this.processQueueAsync();
        }, this.retryDelayMs * job.retryCount);
      }

      this.jobs.set(job.id, job);
      await this.persistState();
    }
  }

  /**
   * Генерація ключа для R2
   */
  private generateR2Key(job: DownloadJob): string {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    // Структура: models/{platform}/{year}/{month}/{day}/{modelId}/{fileName}
    return `models/${job.platform}/${year}/${month}/${day}/${job.modelId}/${job.fileName}`;
  }

  /**
   * Отримання MIME типу файлу
   */
  private getContentType(fileType: string): string {
    const mimeTypes: Record<string, string> = {
      'stl': 'application/vnd.ms-pki.stl',
      'obj': 'application/x-tgif',
      '3mf': 'application/vnd.ms-package.3dmanufacturing-3dmodel+xml',
      'ply': 'application/octet-stream',
      'gcode': 'text/plain',
      'zip': 'application/zip',
      'rar': 'application/x-rar-compressed',
      '7z': 'application/x-7z-compressed'
    };

    return mimeTypes[fileType.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * Збереження стану в Durable Object storage
   */
  private async persistState(): Promise<void> {
    try {
      const jobsArray = Array.from(this.jobs.entries());
      await this.ctx.storage.put('jobs', jobsArray);
      await this.ctx.storage.put('activeDownloads', Array.from(this.activeDownloads));
    } catch (error) {
      console.error('❌ Помилка збереження стану:', error);
    }
  }

  /**
   * Відновлення стану з Durable Object storage
   */
  private async restoreState(): Promise<void> {
    try {
      const jobsArray = await this.ctx.storage.get<[string, DownloadJob][]>('jobs');
      if (jobsArray) {
        this.jobs = new Map(jobsArray);
      }

      const activeDownloads = await this.ctx.storage.get<string[]>('activeDownloads');
      if (activeDownloads) {
        this.activeDownloads = new Set(activeDownloads);
      }

      console.log(`🔄 Відновлено стан: ${this.jobs.size} завдань, ${this.activeDownloads.size} активних`);
    } catch (error) {
      console.error('❌ Помилка відновлення стану:', error);
    }
  }

  /**
   * Ініціалізація при створенні об'єкта
   */
  async alarm(): Promise<void> {
    // Періодична обробка черги
    await this.processQueue();

    // Планируємо наступну обробку через 30 секунд
    await this.ctx.storage.setAlarm(Date.now() + 30000);
  }
}
