/**
 * PrinterManager Durable Object
 * Manages real-time 3D printer status, job queues, and monitoring
 */

export interface Env {
  PRINTER_MANAGER: DurableObjectNamespace;
  DB: D1Database;
  R2_BUCKET: R2Bucket;
  ANALYTICS: AnalyticsEngineDataset;
  BACKGROUND_QUEUE: Queue;
}

interface Printer {
  id: string;
  name: string;
  type: string;
  status: 'idle' | 'printing' | 'paused' | 'error' | 'maintenance' | 'offline';
  location: string;
  capabilities: {
    maxBuildVolume: { x: number; y: number; z: number };
    supportedMaterials: string[];
    layerHeight: { min: number; max: number };
    nozzleDiameter: number[];
  };
  currentJob?: PrintJob;
  queue: PrintJob[];
  sensors: PrinterSensors;
  lastUpdate: number;
  totalPrintTime: number;
  totalFilamentUsed: number;
}

interface PrintJob {
  id: string;
  modelId: string;
  modelName: string;
  userId: string;
  status: 'queued' | 'printing' | 'paused' | 'completed' | 'failed' | 'cancelled';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  estimatedTime: number;
  actualTime?: number;
  progress: number;
  material: string;
  settings: PrintSettings;
  createdAt: number;
  startedAt?: number;
  completedAt?: number;
  errorMessage?: string;
}

interface PrintSettings {
  layerHeight: number;
  infill: number;
  printSpeed: number;
  temperature: {
    nozzle: number;
    bed: number;
  };
  supports: boolean;
  raft: boolean;
}

interface PrinterSensors {
  temperature: {
    nozzle: number;
    bed: number;
    chamber?: number;
  };
  position: {
    x: number;
    y: number;
    z: number;
  };
  filament: {
    remaining: number;
    type: string;
  };
  vibration?: number;
  humidity?: number;
}

interface PrinterCommand {
  type: 'start' | 'pause' | 'resume' | 'cancel' | 'home' | 'move' | 'heat' | 'cool';
  parameters?: any;
  timestamp: number;
  userId: string;
}

export class PrinterManager {
  private state: DurableObjectState;
  private env: Env;
  private printers: Map<string, Printer> = new Map();
  private websockets: Map<string, WebSocket> = new Map();
  private printerId: string;

  constructor(state: DurableObjectState, env: Env) {
    this.state = state;
    this.env = env;
    this.printerId = state.id.toString();
    
    // Restore state from storage
    this.state.blockConcurrencyWhile(async () => {
      const storedPrinters = await this.state.storage.get<Map<string, Printer>>('printers');
      if (storedPrinters) {
        this.printers = new Map(storedPrinters);
      }
    });

    // Start periodic status updates
    this.startStatusUpdates();
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    try {
      switch (path) {
        case '/websocket':
          return this.handleWebSocket(request);
        case '/register':
          return this.handleRegisterPrinter(request);
        case '/status':
          return this.handleGetStatus(request);
        case '/command':
          return this.handleCommand(request);
        case '/job':
          return this.handleJobManagement(request);
        case '/queue':
          return this.handleQueueManagement(request);
        case '/sensors':
          return this.handleSensorUpdate(request);
        default:
          return new Response('Not found', { status: 404 });
      }
    } catch (error) {
      console.error('PrinterManager error:', error);
      return new Response('Internal server error', { status: 500 });
    }
  }

  private async handleWebSocket(request: Request): Promise<Response> {
    const upgradeHeader = request.headers.get('Upgrade');
    if (upgradeHeader !== 'websocket') {
      return new Response('Expected websocket', { status: 400 });
    }

    const url = new URL(request.url);
    const clientId = url.searchParams.get('clientId') || crypto.randomUUID();

    const [client, server] = Object.values(new WebSocketPair());

    await this.handleWebSocketConnection(server, clientId);

    return new Response(null, {
      status: 101,
      webSocket: client,
    });
  }

  private async handleWebSocketConnection(websocket: WebSocket, clientId: string) {
    websocket.accept();
    this.websockets.set(clientId, websocket);

    // Send current printer status
    websocket.send(JSON.stringify({
      type: 'printer-status',
      data: Array.from(this.printers.values()),
    }));

    websocket.addEventListener('message', async (event) => {
      try {
        const message = JSON.parse(event.data as string);
        await this.handleWebSocketMessage(message, clientId);
      } catch (error) {
        console.error('Error handling WebSocket message:', error);
      }
    });

    websocket.addEventListener('close', () => {
      this.websockets.delete(clientId);
    });
  }

  private async handleWebSocketMessage(message: any, clientId: string) {
    switch (message.type) {
      case 'printer-update':
        await this.updatePrinterStatus(message.data);
        break;
      case 'sensor-data':
        await this.updateSensorData(message.data.printerId, message.data.sensors);
        break;
      case 'job-progress':
        await this.updateJobProgress(message.data.printerId, message.data.progress);
        break;
    }
  }

  private async handleRegisterPrinter(request: Request): Promise<Response> {
    const printer: Printer = await request.json();
    
    printer.lastUpdate = Date.now();
    printer.queue = printer.queue || [];
    printer.totalPrintTime = printer.totalPrintTime || 0;
    printer.totalFilamentUsed = printer.totalFilamentUsed || 0;

    this.printers.set(printer.id, printer);
    await this.state.storage.put('printers', Array.from(this.printers.entries()));

    // Broadcast to all connected clients
    this.broadcast({
      type: 'printer-registered',
      data: printer,
    });

    // Track analytics
    this.env.ANALYTICS?.writeDataPoint({
      blobs: [printer.id, 'printer-registered', printer.type],
      doubles: [Date.now()],
      indexes: [printer.id],
    });

    return new Response(JSON.stringify({ success: true, printerId: printer.id }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  private async handleGetStatus(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const printerId = url.searchParams.get('printerId');

    if (printerId) {
      const printer = this.printers.get(printerId);
      if (!printer) {
        return new Response('Printer not found', { status: 404 });
      }
      return new Response(JSON.stringify(printer), {
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response(JSON.stringify(Array.from(this.printers.values())), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  private async handleCommand(request: Request): Promise<Response> {
    const { printerId, command }: { printerId: string; command: PrinterCommand } = await request.json();
    
    const printer = this.printers.get(printerId);
    if (!printer) {
      return new Response('Printer not found', { status: 404 });
    }

    // Execute command
    await this.executeCommand(printer, command);

    // Broadcast command to all clients
    this.broadcast({
      type: 'printer-command',
      data: { printerId, command },
    });

    // Track analytics
    this.env.ANALYTICS?.writeDataPoint({
      blobs: [printerId, 'command', command.type],
      doubles: [Date.now()],
      indexes: [printerId],
    });

    return new Response(JSON.stringify({ success: true }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  private async executeCommand(printer: Printer, command: PrinterCommand) {
    switch (command.type) {
      case 'start':
        if (printer.queue.length > 0 && printer.status === 'idle') {
          const job = printer.queue.shift()!;
          job.status = 'printing';
          job.startedAt = Date.now();
          printer.currentJob = job;
          printer.status = 'printing';
        }
        break;

      case 'pause':
        if (printer.status === 'printing' && printer.currentJob) {
          printer.currentJob.status = 'paused';
          printer.status = 'paused';
        }
        break;

      case 'resume':
        if (printer.status === 'paused' && printer.currentJob) {
          printer.currentJob.status = 'printing';
          printer.status = 'printing';
        }
        break;

      case 'cancel':
        if (printer.currentJob) {
          printer.currentJob.status = 'cancelled';
          printer.currentJob.completedAt = Date.now();
          printer.currentJob = undefined;
          printer.status = 'idle';
        }
        break;
    }

    printer.lastUpdate = Date.now();
    this.printers.set(printer.id, printer);
    await this.state.storage.put('printers', Array.from(this.printers.entries()));
  }

  private async handleJobManagement(request: Request): Promise<Response> {
    const method = request.method;
    
    if (method === 'POST') {
      const { printerId, job }: { printerId: string; job: PrintJob } = await request.json();
      
      const printer = this.printers.get(printerId);
      if (!printer) {
        return new Response('Printer not found', { status: 404 });
      }

      job.id = crypto.randomUUID();
      job.status = 'queued';
      job.createdAt = Date.now();
      job.progress = 0;

      printer.queue.push(job);
      this.printers.set(printerId, printer);
      await this.state.storage.put('printers', Array.from(this.printers.entries()));

      // Broadcast job added
      this.broadcast({
        type: 'job-added',
        data: { printerId, job },
      });

      // Queue background processing
      await this.env.BACKGROUND_QUEUE.send({
        type: 'process-print-queue',
        printerId,
        jobId: job.id,
      });

      return new Response(JSON.stringify({ success: true, jobId: job.id }), {
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new Response('Method not allowed', { status: 405 });
  }

  private async updatePrinterStatus(printerData: Partial<Printer>) {
    const printer = this.printers.get(printerData.id!);
    if (!printer) return;

    Object.assign(printer, printerData);
    printer.lastUpdate = Date.now();

    this.printers.set(printer.id, printer);
    await this.state.storage.put('printers', Array.from(this.printers.entries()));

    // Broadcast update
    this.broadcast({
      type: 'printer-updated',
      data: printer,
    });
  }

  private async updateSensorData(printerId: string, sensors: PrinterSensors) {
    const printer = this.printers.get(printerId);
    if (!printer) return;

    printer.sensors = sensors;
    printer.lastUpdate = Date.now();

    this.printers.set(printerId, printer);

    // Broadcast sensor update
    this.broadcast({
      type: 'sensor-update',
      data: { printerId, sensors },
    });

    // Check for alerts
    await this.checkSensorAlerts(printer);
  }

  private async updateJobProgress(printerId: string, progress: number) {
    const printer = this.printers.get(printerId);
    if (!printer || !printer.currentJob) return;

    printer.currentJob.progress = progress;
    
    if (progress >= 100) {
      printer.currentJob.status = 'completed';
      printer.currentJob.completedAt = Date.now();
      printer.currentJob.actualTime = printer.currentJob.completedAt - (printer.currentJob.startedAt || 0);
      
      // Move to next job
      printer.currentJob = undefined;
      printer.status = 'idle';
      
      // Start next job if available
      if (printer.queue.length > 0) {
        const nextJob = printer.queue.shift()!;
        nextJob.status = 'printing';
        nextJob.startedAt = Date.now();
        printer.currentJob = nextJob;
        printer.status = 'printing';
      }
    }

    this.printers.set(printerId, printer);
    await this.state.storage.put('printers', Array.from(this.printers.entries()));

    // Broadcast progress update
    this.broadcast({
      type: 'job-progress',
      data: { printerId, progress, job: printer.currentJob },
    });
  }

  private async checkSensorAlerts(printer: Printer) {
    const alerts = [];

    // Temperature alerts
    if (printer.sensors.temperature.nozzle > 300) {
      alerts.push({ type: 'high-temperature', component: 'nozzle', value: printer.sensors.temperature.nozzle });
    }
    if (printer.sensors.temperature.bed > 150) {
      alerts.push({ type: 'high-temperature', component: 'bed', value: printer.sensors.temperature.bed });
    }

    // Filament alerts
    if (printer.sensors.filament.remaining < 10) {
      alerts.push({ type: 'low-filament', remaining: printer.sensors.filament.remaining });
    }

    if (alerts.length > 0) {
      this.broadcast({
        type: 'printer-alerts',
        data: { printerId: printer.id, alerts },
      });

      // Track alerts in analytics
      for (const alert of alerts) {
        this.env.ANALYTICS?.writeDataPoint({
          blobs: [printer.id, 'alert', alert.type],
          doubles: [Date.now()],
          indexes: [printer.id],
        });
      }
    }
  }

  private broadcast(message: any) {
    const messageStr = JSON.stringify(message);
    
    for (const [clientId, websocket] of this.websockets) {
      if (websocket.readyState === WebSocket.READY_STATE_OPEN) {
        try {
          websocket.send(messageStr);
        } catch (error) {
          console.error(`Error sending message to client ${clientId}:`, error);
          this.websockets.delete(clientId);
        }
      }
    }
  }

  private startStatusUpdates() {
    // Update printer status every 30 seconds
    setInterval(async () => {
      for (const printer of this.printers.values()) {
        // Check if printer is offline (no update in 5 minutes)
        if (Date.now() - printer.lastUpdate > 5 * 60 * 1000) {
          printer.status = 'offline';
          this.printers.set(printer.id, printer);
        }
      }
      
      await this.state.storage.put('printers', Array.from(this.printers.entries()));
    }, 30000);
  }

  private async handleQueueManagement(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const printerId = url.searchParams.get('printerId');
    
    if (!printerId) {
      return new Response('Missing printerId', { status: 400 });
    }

    const printer = this.printers.get(printerId);
    if (!printer) {
      return new Response('Printer not found', { status: 404 });
    }

    return new Response(JSON.stringify({
      currentJob: printer.currentJob,
      queue: printer.queue,
    }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  private async handleSensorUpdate(request: Request): Promise<Response> {
    const { printerId, sensors } = await request.json();
    await this.updateSensorData(printerId, sensors);
    
    return new Response(JSON.stringify({ success: true }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
