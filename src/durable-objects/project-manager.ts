/**
 * ProjectManager Durable Object
 * Manages real-time project collaboration, version control, and team coordination
 */

export interface Env {
  PROJECT_MANAGER: DurableObjectNamespace;
  DB: D1Database;
  R2_BUCKET: R2Bucket;
  ANALYTICS: AnalyticsEngineDataset;
  BACKGROUND_QUEUE: Queue;
}

interface Project {
  id: string;
  name: string;
  description: string;
  status: 'planning' | 'in-progress' | 'review' | 'completed' | 'archived';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  progress: number;
  createdAt: number;
  updatedAt: number;
  dueDate?: number;
  owner: TeamMember;
  collaborators: TeamMember[];
  models: ProjectModel[];
  tasks: ProjectTask[];
  comments: ProjectComment[];
  versions: ProjectVersion[];
  settings: ProjectSettings;
}

interface TeamMember {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'owner' | 'editor' | 'viewer';
  permissions: string[];
  isOnline: boolean;
  lastSeen: number;
}

interface ProjectModel {
  id: string;
  name: string;
  fileUrl: string;
  thumbnailUrl?: string;
  version: number;
  status: 'draft' | 'review' | 'approved' | 'rejected';
  uploadedBy: string;
  uploadedAt: number;
  metadata: {
    fileSize: number;
    format: string;
    dimensions?: { x: number; y: number; z: number };
    triangles?: number;
    materials?: string[];
  };
}

interface ProjectTask {
  id: string;
  title: string;
  description: string;
  status: 'todo' | 'in-progress' | 'review' | 'done';
  priority: 'low' | 'medium' | 'high';
  assignedTo?: string;
  createdBy: string;
  createdAt: number;
  dueDate?: number;
  completedAt?: number;
  dependencies: string[];
  tags: string[];
}

interface ProjectComment {
  id: string;
  content: string;
  author: string;
  createdAt: number;
  editedAt?: number;
  parentId?: string;
  attachments?: string[];
  mentions: string[];
  reactions: { [emoji: string]: string[] };
}

interface ProjectVersion {
  id: string;
  version: string;
  description: string;
  createdBy: string;
  createdAt: number;
  changes: VersionChange[];
  modelSnapshots: { [modelId: string]: string };
}

interface VersionChange {
  type: 'model-added' | 'model-updated' | 'model-removed' | 'task-added' | 'task-updated' | 'settings-changed';
  description: string;
  timestamp: number;
  userId: string;
  data: any;
}

interface ProjectSettings {
  visibility: 'private' | 'team' | 'public';
  allowComments: boolean;
  allowDownloads: boolean;
  requireApproval: boolean;
  autoBackup: boolean;
  notifications: {
    email: boolean;
    push: boolean;
    slack?: string;
  };
}

interface ProjectEvent {
  type: 'member-join' | 'member-leave' | 'model-upload' | 'task-update' | 'comment-add' | 'version-create' | 'status-change';
  userId: string;
  timestamp: number;
  data: any;
}

export class ProjectManager {
  private state: DurableObjectState;
  private env: Env;
  private project: Project | null = null;
  private websockets: Map<string, WebSocket> = new Map();
  private onlineMembers: Set<string> = new Set();
  private projectId: string;

  constructor(state: DurableObjectState, env: Env) {
    this.state = state;
    this.env = env;
    this.projectId = state.id.toString();
    
    // Restore state from storage
    this.state.blockConcurrencyWhile(async () => {
      const storedProject = await this.state.storage.get<Project>('project');
      if (storedProject) {
        this.project = storedProject;
      }
    });
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    try {
      switch (path) {
        case '/websocket':
          return this.handleWebSocket(request);
        case '/create':
          return this.handleCreateProject(request);
        case '/update':
          return this.handleUpdateProject(request);
        case '/members':
          return this.handleMemberManagement(request);
        case '/models':
          return this.handleModelManagement(request);
        case '/tasks':
          return this.handleTaskManagement(request);
        case '/comments':
          return this.handleCommentManagement(request);
        case '/versions':
          return this.handleVersionManagement(request);
        case '/status':
          return this.handleGetStatus(request);
        default:
          return new Response('Not found', { status: 404 });
      }
    } catch (error) {
      console.error('ProjectManager error:', error);
      return new Response('Internal server error', { status: 500 });
    }
  }

  private async handleWebSocket(request: Request): Promise<Response> {
    const upgradeHeader = request.headers.get('Upgrade');
    if (upgradeHeader !== 'websocket') {
      return new Response('Expected websocket', { status: 400 });
    }

    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');

    if (!userId) {
      return new Response('Missing userId', { status: 400 });
    }

    const [client, server] = Object.values(new WebSocketPair());

    await this.handleWebSocketConnection(server, userId);

    return new Response(null, {
      status: 101,
      webSocket: client,
    });
  }

  private async handleWebSocketConnection(websocket: WebSocket, userId: string) {
    websocket.accept();
    this.websockets.set(userId, websocket);
    this.onlineMembers.add(userId);

    // Update member online status
    if (this.project) {
      const member = this.project.collaborators.find(m => m.id === userId) || 
                    (this.project.owner.id === userId ? this.project.owner : null);
      if (member) {
        member.isOnline = true;
        member.lastSeen = Date.now();
        await this.saveProject();
      }
    }

    // Send current project state
    websocket.send(JSON.stringify({
      type: 'project-state',
      data: this.project,
    }));

    // Notify other members
    this.broadcast({
      type: 'member-join',
      userId,
      timestamp: Date.now(),
      data: { userId },
    }, userId);

    websocket.addEventListener('message', async (event) => {
      try {
        const message: ProjectEvent = JSON.parse(event.data as string);
        await this.handleProjectEvent(message, userId);
      } catch (error) {
        console.error('Error handling project event:', error);
      }
    });

    websocket.addEventListener('close', async () => {
      await this.handleMemberLeave(userId);
    });
  }

  private async handleProjectEvent(event: ProjectEvent, userId: string) {
    if (!this.project) return;

    switch (event.type) {
      case 'model-upload':
        await this.handleModelUpload(event.data, userId);
        break;
      case 'task-update':
        await this.handleTaskUpdate(event.data, userId);
        break;
      case 'comment-add':
        await this.handleCommentAdd(event.data, userId);
        break;
      case 'status-change':
        await this.handleStatusChange(event.data, userId);
        break;
    }

    // Track analytics
    this.env.ANALYTICS?.writeDataPoint({
      blobs: [this.projectId, userId, event.type],
      doubles: [Date.now()],
      indexes: [this.projectId],
    });
  }

  private async handleModelUpload(data: any, userId: string) {
    if (!this.project) return;

    const model: ProjectModel = {
      id: crypto.randomUUID(),
      name: data.name,
      fileUrl: data.fileUrl,
      thumbnailUrl: data.thumbnailUrl,
      version: 1,
      status: 'draft',
      uploadedBy: userId,
      uploadedAt: Date.now(),
      metadata: data.metadata,
    };

    this.project.models.push(model);
    this.project.updatedAt = Date.now();

    await this.saveProject();

    this.broadcast({
      type: 'model-upload',
      userId,
      timestamp: Date.now(),
      data: { model },
    });

    // Create version if auto-versioning is enabled
    if (this.project.settings.autoBackup) {
      await this.createVersion(`Model "${model.name}" uploaded`, userId);
    }
  }

  private async handleTaskUpdate(data: any, userId: string) {
    if (!this.project) return;

    const taskIndex = this.project.tasks.findIndex(t => t.id === data.taskId);
    if (taskIndex === -1) return;

    const task = this.project.tasks[taskIndex];
    Object.assign(task, data.updates);

    if (data.updates.status === 'done' && !task.completedAt) {
      task.completedAt = Date.now();
    }

    this.project.updatedAt = Date.now();
    await this.saveProject();

    this.broadcast({
      type: 'task-update',
      userId,
      timestamp: Date.now(),
      data: { task },
    });

    // Update project progress
    await this.updateProjectProgress();
  }

  private async handleCommentAdd(data: any, userId: string) {
    if (!this.project) return;

    const comment: ProjectComment = {
      id: crypto.randomUUID(),
      content: data.content,
      author: userId,
      createdAt: Date.now(),
      parentId: data.parentId,
      attachments: data.attachments || [],
      mentions: data.mentions || [],
      reactions: {},
    };

    this.project.comments.push(comment);
    this.project.updatedAt = Date.now();

    await this.saveProject();

    this.broadcast({
      type: 'comment-add',
      userId,
      timestamp: Date.now(),
      data: { comment },
    });

    // Send notifications to mentioned users
    for (const mentionedUserId of comment.mentions) {
      await this.sendNotification(mentionedUserId, {
        type: 'mention',
        projectId: this.projectId,
        commentId: comment.id,
        from: userId,
      });
    }
  }

  private async handleStatusChange(data: any, userId: string) {
    if (!this.project) return;

    const oldStatus = this.project.status;
    this.project.status = data.status;
    this.project.updatedAt = Date.now();

    await this.saveProject();

    this.broadcast({
      type: 'status-change',
      userId,
      timestamp: Date.now(),
      data: { oldStatus, newStatus: data.status },
    });

    // Create version for status changes
    await this.createVersion(`Status changed from ${oldStatus} to ${data.status}`, userId);
  }

  private async handleMemberLeave(userId: string) {
    this.websockets.delete(userId);
    this.onlineMembers.delete(userId);

    if (this.project) {
      const member = this.project.collaborators.find(m => m.id === userId) || 
                    (this.project.owner.id === userId ? this.project.owner : null);
      if (member) {
        member.isOnline = false;
        member.lastSeen = Date.now();
        await this.saveProject();
      }
    }

    this.broadcast({
      type: 'member-leave',
      userId,
      timestamp: Date.now(),
      data: { userId },
    });
  }

  private async updateProjectProgress() {
    if (!this.project) return;

    const totalTasks = this.project.tasks.length;
    if (totalTasks === 0) {
      this.project.progress = 0;
      return;
    }

    const completedTasks = this.project.tasks.filter(t => t.status === 'done').length;
    this.project.progress = Math.round((completedTasks / totalTasks) * 100);

    // Auto-complete project if all tasks are done
    if (this.project.progress === 100 && this.project.status !== 'completed') {
      this.project.status = 'completed';
      
      this.broadcast({
        type: 'status-change',
        userId: 'system',
        timestamp: Date.now(),
        data: { oldStatus: 'in-progress', newStatus: 'completed' },
      });
    }
  }

  private async createVersion(description: string, userId: string) {
    if (!this.project) return;

    const version: ProjectVersion = {
      id: crypto.randomUUID(),
      version: `v${this.project.versions.length + 1}.0`,
      description,
      createdBy: userId,
      createdAt: Date.now(),
      changes: [],
      modelSnapshots: {},
    };

    // Create snapshots of current models
    for (const model of this.project.models) {
      version.modelSnapshots[model.id] = model.fileUrl;
    }

    this.project.versions.push(version);
    await this.saveProject();

    this.broadcast({
      type: 'version-create',
      userId,
      timestamp: Date.now(),
      data: { version },
    });
  }

  private async sendNotification(userId: string, notification: any) {
    // Queue notification for background processing
    await this.env.BACKGROUND_QUEUE.send({
      type: 'send-notification',
      userId,
      notification,
    });
  }

  private broadcast(message: ProjectEvent, excludeUserId?: string) {
    const messageStr = JSON.stringify(message);
    
    for (const [userId, websocket] of this.websockets) {
      if (userId !== excludeUserId && websocket.readyState === WebSocket.READY_STATE_OPEN) {
        try {
          websocket.send(messageStr);
        } catch (error) {
          console.error(`Error sending message to user ${userId}:`, error);
          this.websockets.delete(userId);
        }
      }
    }
  }

  private async saveProject() {
    if (this.project) {
      await this.state.storage.put('project', this.project);
    }
  }

  private async handleCreateProject(request: Request): Promise<Response> {
    const projectData = await request.json();
    
    this.project = {
      id: this.projectId,
      ...projectData,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      progress: 0,
      collaborators: [],
      models: [],
      tasks: [],
      comments: [],
      versions: [],
      settings: {
        visibility: 'private',
        allowComments: true,
        allowDownloads: true,
        requireApproval: false,
        autoBackup: true,
        notifications: {
          email: true,
          push: true,
        },
        ...projectData.settings,
      },
    };

    await this.saveProject();

    return new Response(JSON.stringify({ success: true, project: this.project }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  private async handleUpdateProject(request: Request): Promise<Response> {
    if (!this.project) {
      return new Response('Project not found', { status: 404 });
    }

    const updates = await request.json();
    Object.assign(this.project, updates);
    this.project.updatedAt = Date.now();

    await this.saveProject();

    this.broadcast({
      type: 'status-change',
      userId: 'system',
      timestamp: Date.now(),
      data: { updates },
    });

    return new Response(JSON.stringify({ success: true, project: this.project }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  private async handleGetStatus(request: Request): Promise<Response> {
    return new Response(JSON.stringify({
      project: this.project,
      onlineMembers: Array.from(this.onlineMembers),
    }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  private async handleMemberManagement(request: Request): Promise<Response> {
    // Implementation for member management endpoints
    return new Response(JSON.stringify({ success: true }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  private async handleModelManagement(request: Request): Promise<Response> {
    // Implementation for model management endpoints
    return new Response(JSON.stringify({ success: true }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  private async handleTaskManagement(request: Request): Promise<Response> {
    // Implementation for task management endpoints
    return new Response(JSON.stringify({ success: true }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  private async handleCommentManagement(request: Request): Promise<Response> {
    // Implementation for comment management endpoints
    return new Response(JSON.stringify({ success: true }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  private async handleVersionManagement(request: Request): Promise<Response> {
    // Implementation for version management endpoints
    return new Response(JSON.stringify({ success: true }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
