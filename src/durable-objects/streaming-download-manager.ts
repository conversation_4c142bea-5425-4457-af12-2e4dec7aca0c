export interface Env {
  STREAMING_DOWNLOAD_MANAGER: DurableObjectNamespace;
  DB: D1Database;
  R2_BUCKET: R2Bucket;
  CACHE_KV: KVNamespace;
}

export interface StreamingDownloadSession {
  id: string;
  modelId: string;
  userId: string;
  status: 'pending' | 'streaming' | 'paused' | 'completed' | 'failed' | 'cancelled';
  totalSize: number;
  downloadedSize: number;
  chunkSize: number;
  currentChunk: number;
  totalChunks: number;
  downloadUrl?: string;
  resumeToken?: string;
  expiresAt: number;
  createdAt: number;
  lastActivity: number;
  speed: number; // bytes per second
  eta: number; // estimated time remaining in seconds
}

export interface DownloadChunk {
  chunkId: number;
  data: ArrayBuffer;
  size: number;
  checksum: string;
}

export class StreamingDownloadManager {
  private state: DurableObjectState;
  private env: Env;
  private sessions: Map<string, StreamingDownloadSession> = new Map();
  private activeStreams: Map<string, ReadableStream> = new Map();

  constructor(state: DurableObjectState, env: Env) {
    this.state = state;
    this.env = env;
    
    // Відновлюємо стан з persistent storage
    this.state.blockConcurrencyWhile(async () => {
      const stored = await this.state.storage.get<Map<string, StreamingDownloadSession>>('sessions');
      if (stored) {
        this.sessions = new Map(stored);
      }
    });

    // Очищуємо неактивні сесії кожні 2 хвилини
    setInterval(() => this.cleanupInactiveSessions(), 2 * 60 * 1000);
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    try {
      switch (path) {
        case '/create-streaming-session':
          return await this.createStreamingSession(request);
        case '/start-stream':
          return await this.startStream(request);
        case '/pause-stream':
          return await this.pauseStream(request);
        case '/resume-stream':
          return await this.resumeStream(request);
        case '/cancel-stream':
          return await this.cancelStream(request);
        case '/get-progress':
          return await this.getProgress(request);
        case '/download-chunk':
          return await this.downloadChunk(request);
        default:
          return new Response('Not Found', { status: 404 });
      }
    } catch (error) {
      console.error('StreamingDownloadManager error:', error);
      return new Response('Internal Server Error', { status: 500 });
    }
  }

  private async createStreamingSession(request: Request): Promise<Response> {
    const { modelId, userId, chunkSize = 1024 * 1024 } = await request.json(); // Default 1MB chunks
    
    // Отримуємо інформацію про файл
    const fileInfo = await this.getFileInfo(modelId);
    if (!fileInfo) {
      return new Response(JSON.stringify({
        success: false,
        error: 'File not found'
      }), { status: 404 });
    }

    const sessionId = crypto.randomUUID();
    const totalChunks = Math.ceil(fileInfo.size / chunkSize);
    
    const session: StreamingDownloadSession = {
      id: sessionId,
      modelId,
      userId,
      status: 'pending',
      totalSize: fileInfo.size,
      downloadedSize: 0,
      chunkSize,
      currentChunk: 0,
      totalChunks,
      expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 години
      createdAt: Date.now(),
      lastActivity: Date.now(),
      speed: 0,
      eta: 0
    };

    this.sessions.set(sessionId, session);
    await this.persistSessions();

    return new Response(JSON.stringify({
      success: true,
      sessionId,
      totalSize: fileInfo.size,
      totalChunks,
      chunkSize
    }));
  }

  private async startStream(request: Request): Promise<Response> {
    const { sessionId } = await request.json();
    const session = this.sessions.get(sessionId);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Session not found'
      }), { status: 404 });
    }

    session.status = 'streaming';
    session.lastActivity = Date.now();
    await this.persistSessions();

    // Створюємо ReadableStream для потокового завантаження
    const stream = new ReadableStream({
      start: async (controller) => {
        try {
          await this.streamFile(session, controller);
        } catch (error) {
          controller.error(error);
        }
      },
      cancel: () => {
        session.status = 'cancelled';
        this.activeStreams.delete(sessionId);
      }
    });

    this.activeStreams.set(sessionId, stream);

    return new Response(stream, {
      headers: {
        'Content-Type': 'application/octet-stream',
        'Content-Length': session.totalSize.toString(),
        'Content-Disposition': `attachment; filename="${session.modelId}.zip"`,
        'Accept-Ranges': 'bytes',
        'X-Session-Id': sessionId
      }
    });
  }

  private async streamFile(session: StreamingDownloadSession, controller: ReadableStreamDefaultController) {
    const fileObject = await this.env.R2_BUCKET.get(session.modelId);
    if (!fileObject) {
      throw new Error('File not found in storage');
    }

    const reader = fileObject.body?.getReader();
    if (!reader) {
      throw new Error('Cannot read file');
    }

    let bytesRead = 0;
    const startTime = Date.now();

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          session.status = 'completed';
          session.downloadedSize = session.totalSize;
          await this.persistSessions();
          controller.close();
          break;
        }

        // Оновлюємо прогрес
        bytesRead += value.length;
        session.downloadedSize = bytesRead;
        session.currentChunk = Math.floor(bytesRead / session.chunkSize);
        session.lastActivity = Date.now();

        // Розраховуємо швидкість та ETA
        const elapsed = (Date.now() - startTime) / 1000;
        session.speed = bytesRead / elapsed;
        session.eta = (session.totalSize - bytesRead) / session.speed;

        // Перевіряємо, чи не призупинено завантаження
        if (session.status === 'paused') {
          await this.pauseExecution();
          continue;
        }

        if (session.status === 'cancelled') {
          controller.close();
          break;
        }

        controller.enqueue(value);
        await this.persistSessions();

        // Невелика затримка для контролю навантаження
        await new Promise(resolve => setTimeout(resolve, 1));
      }
    } finally {
      reader.releaseLock();
      this.activeStreams.delete(session.id);
    }
  }

  private async pauseStream(request: Request): Promise<Response> {
    const { sessionId } = await request.json();
    const session = this.sessions.get(sessionId);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Session not found'
      }), { status: 404 });
    }

    session.status = 'paused';
    session.resumeToken = crypto.randomUUID();
    await this.persistSessions();

    return new Response(JSON.stringify({
      success: true,
      resumeToken: session.resumeToken,
      downloadedSize: session.downloadedSize
    }));
  }

  private async resumeStream(request: Request): Promise<Response> {
    const { sessionId, resumeToken } = await request.json();
    const session = this.sessions.get(sessionId);

    if (!session || session.resumeToken !== resumeToken) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid session or resume token'
      }), { status: 401 });
    }

    session.status = 'streaming';
    session.lastActivity = Date.now();
    await this.persistSessions();

    return new Response(JSON.stringify({
      success: true,
      downloadedSize: session.downloadedSize,
      remainingSize: session.totalSize - session.downloadedSize
    }));
  }

  private async cancelStream(request: Request): Promise<Response> {
    const { sessionId } = await request.json();
    const session = this.sessions.get(sessionId);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Session not found'
      }), { status: 404 });
    }

    session.status = 'cancelled';
    this.activeStreams.delete(sessionId);
    this.sessions.delete(sessionId);
    await this.persistSessions();

    return new Response(JSON.stringify({
      success: true,
      message: 'Download cancelled'
    }));
  }

  private async getProgress(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const sessionId = url.searchParams.get('sessionId');

    if (!sessionId) {
      return new Response('Session ID required', { status: 400 });
    }

    const session = this.sessions.get(sessionId);
    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Session not found'
      }), { status: 404 });
    }

    const progress = {
      sessionId: session.id,
      status: session.status,
      totalSize: session.totalSize,
      downloadedSize: session.downloadedSize,
      progress: (session.downloadedSize / session.totalSize) * 100,
      currentChunk: session.currentChunk,
      totalChunks: session.totalChunks,
      speed: session.speed,
      eta: session.eta,
      lastActivity: session.lastActivity
    };

    return new Response(JSON.stringify({
      success: true,
      progress
    }));
  }

  private async downloadChunk(request: Request): Promise<Response> {
    const { sessionId, chunkId } = await request.json();
    const session = this.sessions.get(sessionId);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Session not found'
      }), { status: 404 });
    }

    const startByte = chunkId * session.chunkSize;
    const endByte = Math.min(startByte + session.chunkSize - 1, session.totalSize - 1);

    const fileObject = await this.env.R2_BUCKET.get(session.modelId, {
      range: { offset: startByte, length: endByte - startByte + 1 }
    });

    if (!fileObject) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Chunk not found'
      }), { status: 404 });
    }

    const chunkData = await fileObject.arrayBuffer();
    const checksum = await this.calculateChecksum(chunkData);

    return new Response(chunkData, {
      headers: {
        'Content-Type': 'application/octet-stream',
        'X-Chunk-Id': chunkId.toString(),
        'X-Chunk-Size': chunkData.byteLength.toString(),
        'X-Chunk-Checksum': checksum
      }
    });
  }

  private async getFileInfo(modelId: string): Promise<{ size: number; type: string } | null> {
    try {
      const fileObject = await this.env.R2_BUCKET.head(modelId);
      return fileObject ? {
        size: fileObject.size,
        type: fileObject.httpMetadata?.contentType || 'application/octet-stream'
      } : null;
    } catch (error) {
      return null;
    }
  }

  private async calculateChecksum(data: ArrayBuffer): Promise<string> {
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  private async pauseExecution(): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, 100));
  }

  private async cleanupInactiveSessions(): Promise<void> {
    const now = Date.now();
    const inactiveThreshold = 30 * 60 * 1000; // 30 хвилин

    for (const [sessionId, session] of this.sessions.entries()) {
      if (now - session.lastActivity > inactiveThreshold) {
        this.sessions.delete(sessionId);
        this.activeStreams.delete(sessionId);
      }
    }

    await this.persistSessions();
  }

  private async persistSessions(): Promise<void> {
    await this.state.storage.put('sessions', Array.from(this.sessions.entries()));
  }
}
