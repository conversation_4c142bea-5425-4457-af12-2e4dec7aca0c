export interface Env {
  BATCH_DOWNLOAD_MANAGER: DurableObjectNamespace;
  RESUMABLE_DOWNLOAD_MANAGER: DurableObjectNamespace;
  DB: D1Database;
  R2_BUCKET: R2Bucket;
  CACHE_KV: KVNamespace;
  BACKGROUND_QUEUE: Queue;
}

export interface BatchDownloadSession {
  id: string;
  userId: string;
  modelIds: string[];
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  totalModels: number;
  completedModels: number;
  failedModels: string[];
  downloadSessions: Map<string, string>; // modelId -> resumableSessionId
  batchArchiveUrl?: string;
  compressionLevel: number;
  includeMetadata: boolean;
  createdAt: number;
  completedAt?: number;
  expiresAt: number;
  estimatedSize: number;
  actualSize: number;
  progress: number;
}

export interface BatchDownloadRequest {
  modelIds: string[];
  compressionLevel?: number; // 0-9, default 6
  includeMetadata?: boolean; // include model info JSON
  archiveFormat?: 'zip' | 'tar.gz'; // default zip
}

export interface ModelDownloadInfo {
  modelId: string;
  name: string;
  size: number;
  status: 'pending' | 'downloading' | 'completed' | 'failed';
  downloadUrl?: string;
  error?: string;
}

export class BatchDownloadManager {
  private state: DurableObjectState;
  private env: Env;
  private sessions: Map<string, BatchDownloadSession> = new Map();
  private downloadQueues: Map<string, Promise<void>> = new Map();

  constructor(state: DurableObjectState, env: Env) {
    this.state = state;
    this.env = env;
    
    // Відновлюємо стан з persistent storage
    this.state.blockConcurrencyWhile(async () => {
      const stored = await this.state.storage.get('sessions');
      if (stored) {
        const sessionsArray = stored as Array<[string, any]>;
        for (const [sessionId, sessionData] of sessionsArray) {
          sessionData.downloadSessions = new Map(sessionData.downloadSessions || []);
          this.sessions.set(sessionId, sessionData);
        }
      }
    });

    // Очищуємо застарілі сесії кожні 30 хвилин
    setInterval(() => this.cleanupExpiredSessions(), 30 * 60 * 1000);
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    try {
      switch (path) {
        case '/create-batch-session':
          return await this.createBatchSession(request);
        case '/start-batch-download':
          return await this.startBatchDownload(request);
        case '/get-batch-progress':
          return await this.getBatchProgress(request);
        case '/cancel-batch-download':
          return await this.cancelBatchDownload(request);
        case '/download-batch-archive':
          return await this.downloadBatchArchive(request);
        case '/retry-failed-models':
          return await this.retryFailedModels(request);
        default:
          return new Response('Not Found', { status: 404 });
      }
    } catch (error) {
      console.error('BatchDownloadManager error:', error);
      return new Response('Internal Server Error', { status: 500 });
    }
  }

  private async createBatchSession(request: Request): Promise<Response> {
    const { userId, batchRequest }: { userId: string; batchRequest: BatchDownloadRequest } = await request.json();
    
    if (!batchRequest.modelIds || batchRequest.modelIds.length === 0) {
      return new Response(JSON.stringify({
        success: false,
        error: 'No models specified'
      }), { status: 400 });
    }

    if (batchRequest.modelIds.length > 50) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Too many models (max 50)'
      }), { status: 400 });
    }

    // Перевіряємо доступ до всіх моделей
    const accessResults = await Promise.all(
      batchRequest.modelIds.map(modelId => this.checkModelAccess(modelId, userId))
    );

    const accessibleModels = batchRequest.modelIds.filter((_, index) => accessResults[index]);
    
    if (accessibleModels.length === 0) {
      return new Response(JSON.stringify({
        success: false,
        error: 'No accessible models'
      }), { status: 403 });
    }

    // Оцінюємо розмір пакету
    const estimatedSize = await this.estimateBatchSize(accessibleModels);

    const sessionId = crypto.randomUUID();
    const session: BatchDownloadSession = {
      id: sessionId,
      userId,
      modelIds: accessibleModels,
      status: 'pending',
      totalModels: accessibleModels.length,
      completedModels: 0,
      failedModels: [],
      downloadSessions: new Map(),
      compressionLevel: batchRequest.compressionLevel || 6,
      includeMetadata: batchRequest.includeMetadata || false,
      createdAt: Date.now(),
      expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 днів
      estimatedSize,
      actualSize: 0,
      progress: 0
    };

    this.sessions.set(sessionId, session);
    await this.persistSessions();

    return new Response(JSON.stringify({
      success: true,
      sessionId,
      totalModels: session.totalModels,
      estimatedSize: session.estimatedSize,
      accessibleModels: accessibleModels.length,
      inaccessibleModels: batchRequest.modelIds.length - accessibleModels.length
    }));
  }

  private async startBatchDownload(request: Request): Promise<Response> {
    const { sessionId } = await request.json();
    const session = this.sessions.get(sessionId);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Session not found'
      }), { status: 404 });
    }

    session.status = 'processing';
    await this.persistSessions();

    // Запускаємо пакетне завантаження в фоні
    this.downloadQueues.set(sessionId, this.processBatchDownload(session));

    return new Response(JSON.stringify({
      success: true,
      message: 'Batch download started',
      sessionId: session.id,
      totalModels: session.totalModels
    }));
  }

  private async processBatchDownload(session: BatchDownloadSession): Promise<void> {
    try {
      const concurrentDownloads = 3; // Одночасно завантажуємо 3 моделі
      const downloadPromises: Promise<void>[] = [];

      for (let i = 0; i < session.modelIds.length; i += concurrentDownloads) {
        const batch = session.modelIds.slice(i, i + concurrentDownloads);
        
        const batchPromises = batch.map(modelId => this.downloadSingleModel(session, modelId));
        downloadPromises.push(...batchPromises);

        // Чекаємо завершення поточного батчу
        await Promise.allSettled(batchPromises);
        
        // Оновлюємо прогрес
        session.progress = (session.completedModels / session.totalModels) * 100;
        await this.persistSessions();
      }

      // Створюємо архів з усіх завантажених моделей
      if (session.completedModels > 0) {
        await this.createBatchArchive(session);
        session.status = 'completed';
        session.completedAt = Date.now();
      } else {
        session.status = 'failed';
      }

    } catch (error) {
      console.error('Batch download error:', error);
      session.status = 'failed';
    }

    await this.persistSessions();
  }

  private async downloadSingleModel(session: BatchDownloadSession, modelId: string): Promise<void> {
    try {
      // Створюємо резюмовану сесію завантаження для моделі
      const resumableManager = this.env.RESUMABLE_DOWNLOAD_MANAGER.idFromName(`resumable-${modelId}`);
      const resumableObject = this.env.RESUMABLE_DOWNLOAD_MANAGER.get(resumableManager);

      const createResponse = await resumableObject.fetch('https://resumable/create-resumable-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          modelId,
          userId: session.userId,
          chunkSize: 1024 * 1024 // 1MB chunks for batch downloads
        })
      });

      const createResult = await createResponse.json();
      if (!createResult.success) {
        throw new Error(createResult.error);
      }

      const resumableSessionId = createResult.sessionId;
      const resumeToken = createResult.resumeToken;
      
      session.downloadSessions.set(modelId, resumableSessionId);

      // Запускаємо завантаження
      const startResponse = await resumableObject.fetch('https://resumable/start-download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId: resumableSessionId, resumeToken })
      });

      const startResult = await startResponse.json();
      if (!startResult.success) {
        throw new Error(startResult.error);
      }

      // Чекаємо завершення завантаження
      await this.waitForModelDownload(resumableObject, resumableSessionId, resumeToken);
      
      session.completedModels++;

    } catch (error) {
      console.error(`Failed to download model ${modelId}:`, error);
      session.failedModels.push(modelId);
    }
  }

  private async waitForModelDownload(resumableObject: DurableObjectStub, sessionId: string, resumeToken: string): Promise<void> {
    const maxWaitTime = 30 * 60 * 1000; // 30 хвилин
    const checkInterval = 5000; // 5 секунд
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const progressResponse = await resumableObject.fetch(
        `https://resumable/get-resume-info?sessionId=${sessionId}&resumeToken=${resumeToken}`
      );
      
      const progressResult = await progressResponse.json();
      
      if (progressResult.success && progressResult.sessionInfo) {
        const status = progressResult.sessionInfo.status;
        
        if (status === 'completed') {
          return; // Завантаження завершено
        } else if (status === 'failed') {
          throw new Error('Download failed');
        }
      }

      await new Promise(resolve => setTimeout(resolve, checkInterval));
    }

    throw new Error('Download timeout');
  }

  private async createBatchArchive(session: BatchDownloadSession): Promise<void> {
    // Створюємо ZIP архів з усіх завантажених моделей
    const archiveKey = `batch-archives/${session.id}.zip`;
    
    // Отримуємо всі завантажені файли
    const modelFiles: { name: string; data: ArrayBuffer }[] = [];
    
    for (const modelId of session.modelIds) {
      if (!session.failedModels.includes(modelId)) {
        try {
          // Отримуємо зібраний файл з резюмованої сесії
          const assembledFileKey = `assembled/${session.downloadSessions.get(modelId)}`;
          const fileObject = await this.env.R2_BUCKET.get(assembledFileKey);
          
          if (fileObject) {
            const modelInfo = await this.getModelInfo(modelId);
            const fileName = modelInfo ? `${modelInfo.name}.zip` : `${modelId}.zip`;
            
            modelFiles.push({
              name: fileName,
              data: await fileObject.arrayBuffer()
            });
          }
        } catch (error) {
          console.error(`Failed to get file for model ${modelId}:`, error);
        }
      }
    }

    // Додаємо метадані, якщо потрібно
    if (session.includeMetadata) {
      const metadata = await this.generateBatchMetadata(session);
      const metadataBlob = new TextEncoder().encode(JSON.stringify(metadata, null, 2));
      
      modelFiles.push({
        name: 'batch-info.json',
        data: metadataBlob.buffer
      });
    }

    // Створюємо ZIP архів (спрощена реалізація)
    const zipData = await this.createZipArchive(modelFiles, session.compressionLevel);
    
    // Зберігаємо архів в R2
    await this.env.R2_BUCKET.put(archiveKey, zipData);
    
    // Генеруємо підписане URL
    session.batchArchiveUrl = await this.generateSignedUrl(archiveKey);
    session.actualSize = zipData.byteLength;
  }

  private async getBatchProgress(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const sessionId = url.searchParams.get('sessionId');

    if (!sessionId) {
      return new Response('Session ID required', { status: 400 });
    }

    const session = this.sessions.get(sessionId);
    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Session not found'
      }), { status: 404 });
    }

    // Отримуємо детальний прогрес для кожної моделі
    const modelProgress: ModelDownloadInfo[] = [];
    
    for (const modelId of session.modelIds) {
      const modelInfo = await this.getModelInfo(modelId);
      const resumableSessionId = session.downloadSessions.get(modelId);
      
      let modelStatus: ModelDownloadInfo = {
        modelId,
        name: modelInfo?.name || modelId,
        size: modelInfo?.size || 0,
        status: 'pending'
      };

      if (resumableSessionId) {
        // Отримуємо прогрес з резюмованої сесії
        try {
          const resumableManager = this.env.RESUMABLE_DOWNLOAD_MANAGER.idFromName(`resumable-${modelId}`);
          const resumableObject = this.env.RESUMABLE_DOWNLOAD_MANAGER.get(resumableManager);
          
          const progressResponse = await resumableObject.fetch(
            `https://resumable/get-resume-info?sessionId=${resumableSessionId}&resumeToken=dummy`
          );
          
          if (progressResponse.ok) {
            const progressResult = await progressResponse.json();
            if (progressResult.success) {
              modelStatus.status = progressResult.sessionInfo.status;
            }
          }
        } catch (error) {
          // Ігноруємо помилки отримання прогресу
        }
      }

      if (session.failedModels.includes(modelId)) {
        modelStatus.status = 'failed';
        modelStatus.error = 'Download failed';
      }

      modelProgress.push(modelStatus);
    }

    return new Response(JSON.stringify({
      success: true,
      batchProgress: {
        sessionId: session.id,
        status: session.status,
        totalModels: session.totalModels,
        completedModels: session.completedModels,
        failedModels: session.failedModels.length,
        progress: session.progress,
        estimatedSize: session.estimatedSize,
        actualSize: session.actualSize,
        batchArchiveUrl: session.batchArchiveUrl,
        createdAt: session.createdAt,
        completedAt: session.completedAt,
        modelProgress
      }
    }));
  }

  private async cancelBatchDownload(request: Request): Promise<Response> {
    const { sessionId } = await request.json();
    const session = this.sessions.get(sessionId);

    if (!session) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Session not found'
      }), { status: 404 });
    }

    session.status = 'cancelled';
    
    // Скасовуємо всі активні завантаження
    for (const [modelId, resumableSessionId] of session.downloadSessions) {
      try {
        const resumableManager = this.env.RESUMABLE_DOWNLOAD_MANAGER.idFromName(`resumable-${modelId}`);
        const resumableObject = this.env.RESUMABLE_DOWNLOAD_MANAGER.get(resumableManager);
        
        await resumableObject.fetch('https://resumable/pause-download', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ sessionId: resumableSessionId, resumeToken: 'dummy' })
        });
      } catch (error) {
        // Ігноруємо помилки скасування
      }
    }

    await this.persistSessions();

    return new Response(JSON.stringify({
      success: true,
      message: 'Batch download cancelled'
    }));
  }

  private async downloadBatchArchive(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const sessionId = url.searchParams.get('sessionId');

    if (!sessionId) {
      return new Response('Session ID required', { status: 400 });
    }

    const session = this.sessions.get(sessionId);
    if (!session || session.status !== 'completed' || !session.batchArchiveUrl) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Archive not ready'
      }), { status: 404 });
    }

    // Перенаправляємо на підписане URL
    return Response.redirect(session.batchArchiveUrl, 302);
  }

  // Допоміжні методи
  private async checkModelAccess(modelId: string, userId: string): Promise<boolean> {
    // Спрощена реалізація перевірки доступу
    return true;
  }

  private async getModelInfo(modelId: string): Promise<{ name: string; size: number } | null> {
    try {
      const model = await this.env.DB.prepare(
        'SELECT name, file_size FROM models WHERE id = ?'
      ).bind(modelId).first();
      
      return model ? {
        name: model.name as string,
        size: (model.file_size as number) || 0
      } : null;
    } catch (error) {
      return null;
    }
  }

  private async estimateBatchSize(modelIds: string[]): Promise<number> {
    let totalSize = 0;
    
    for (const modelId of modelIds) {
      const modelInfo = await this.getModelInfo(modelId);
      if (modelInfo) {
        totalSize += modelInfo.size;
      }
    }
    
    // Додаємо 20% на стиснення та метадані
    return Math.floor(totalSize * 0.8);
  }

  private async generateBatchMetadata(session: BatchDownloadSession): Promise<any> {
    const models = [];
    
    for (const modelId of session.modelIds) {
      const modelInfo = await this.getModelInfo(modelId);
      if (modelInfo) {
        models.push({
          id: modelId,
          name: modelInfo.name,
          size: modelInfo.size,
          status: session.failedModels.includes(modelId) ? 'failed' : 'completed'
        });
      }
    }

    return {
      batchId: session.id,
      createdAt: new Date(session.createdAt).toISOString(),
      completedAt: session.completedAt ? new Date(session.completedAt).toISOString() : null,
      totalModels: session.totalModels,
      completedModels: session.completedModels,
      failedModels: session.failedModels.length,
      estimatedSize: session.estimatedSize,
      actualSize: session.actualSize,
      compressionLevel: session.compressionLevel,
      models
    };
  }

  private async createZipArchive(files: { name: string; data: ArrayBuffer }[], compressionLevel: number): Promise<ArrayBuffer> {
    // Спрощена реалізація створення ZIP архіву
    // В реальному проекті використовуйте бібліотеку для створення ZIP
    const totalSize = files.reduce((sum, file) => sum + file.data.byteLength, 0);
    return new ArrayBuffer(totalSize); // Заглушка
  }

  private async generateSignedUrl(objectKey: string): Promise<string> {
    // Генеруємо підписане URL для завантаження
    return `https://your-r2-domain.com/${objectKey}?expires=${Date.now() + 24 * 60 * 60 * 1000}`;
  }

  private async cleanupExpiredSessions(): Promise<void> {
    const now = Date.now();
    
    for (const [sessionId, session] of this.sessions.entries()) {
      if (now > session.expiresAt) {
        // Видаляємо архів
        if (session.batchArchiveUrl) {
          const archiveKey = `batch-archives/${session.id}.zip`;
          await this.env.R2_BUCKET.delete(archiveKey);
        }
        
        this.sessions.delete(sessionId);
      }
    }
    
    await this.persistSessions();
  }

  private async persistSessions(): Promise<void> {
    const sessionsArray = Array.from(this.sessions.entries()).map(([sessionId, session]) => [
      sessionId,
      {
        ...session,
        downloadSessions: Array.from(session.downloadSessions.entries())
      }
    ]);
    
    await this.state.storage.put('sessions', sessionsArray);
  }
}
