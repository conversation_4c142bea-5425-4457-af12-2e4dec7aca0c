/**
 * CollaborationRoom Durable Object
 * Handles real-time collaboration for 3D model editing and viewing
 */

export interface Env {
  COLLABORATION_ROOM: DurableObjectNamespace;
  DB: D1Database;
  R2_BUCKET: R2Bucket;
  ANALYTICS: AnalyticsEngineDataset;
}

interface User {
  id: string;
  name: string;
  avatar?: string;
  cursor?: { x: number; y: number; z: number };
  camera?: { position: [number, number, number]; target: [number, number, number] };
  isActive: boolean;
  joinedAt: number;
}

interface ModelState {
  id: string;
  version: number;
  lastModified: number;
  modifiedBy: string;
  changes: ModelChange[];
}

interface ModelChange {
  id: string;
  type: 'transform' | 'material' | 'geometry' | 'annotation';
  timestamp: number;
  userId: string;
  data: any;
  applied: boolean;
}

interface Message {
  type: 'user-join' | 'user-leave' | 'cursor-move' | 'camera-sync' | 'model-change' | 'chat' | 'annotation';
  userId: string;
  timestamp: number;
  data: any;
}

export class CollaborationRoom {
  private state: DurableObjectState;
  private env: Env;
  private users: Map<string, User> = new Map();
  private websockets: Map<string, WebSocket> = new Map();
  private modelState: ModelState | null = null;
  private roomId: string;

  constructor(state: DurableObjectState, env: Env) {
    this.state = state;
    this.env = env;
    this.roomId = state.id.toString();
    
    // Restore state from storage
    this.state.blockConcurrencyWhile(async () => {
      const storedUsers = await this.state.storage.get<Map<string, User>>('users');
      const storedModelState = await this.state.storage.get<ModelState>('modelState');
      
      if (storedUsers) {
        this.users = new Map(storedUsers);
      }
      if (storedModelState) {
        this.modelState = storedModelState;
      }
    });
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    try {
      switch (path) {
        case '/websocket':
          return this.handleWebSocket(request);
        case '/join':
          return this.handleJoin(request);
        case '/leave':
          return this.handleLeave(request);
        case '/state':
          return this.handleGetState(request);
        case '/model':
          return this.handleModelUpdate(request);
        default:
          return new Response('Not found', { status: 404 });
      }
    } catch (error) {
      console.error('CollaborationRoom error:', error);
      return new Response('Internal server error', { status: 500 });
    }
  }

  private async handleWebSocket(request: Request): Promise<Response> {
    const upgradeHeader = request.headers.get('Upgrade');
    if (upgradeHeader !== 'websocket') {
      return new Response('Expected websocket', { status: 400 });
    }

    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');
    const userName = url.searchParams.get('userName') || 'Anonymous';

    if (!userId) {
      return new Response('Missing userId', { status: 400 });
    }

    const [client, server] = Object.values(new WebSocketPair());

    await this.handleWebSocketConnection(server, userId, userName);

    return new Response(null, {
      status: 101,
      webSocket: client,
    });
  }

  private async handleWebSocketConnection(websocket: WebSocket, userId: string, userName: string) {
    websocket.accept();

    // Add user to room
    const user: User = {
      id: userId,
      name: userName,
      isActive: true,
      joinedAt: Date.now(),
    };

    this.users.set(userId, user);
    this.websockets.set(userId, websocket);

    // Persist user state
    await this.state.storage.put('users', Array.from(this.users.entries()));

    // Notify other users
    this.broadcast({
      type: 'user-join',
      userId,
      timestamp: Date.now(),
      data: { user },
    }, userId);

    // Send current state to new user
    websocket.send(JSON.stringify({
      type: 'room-state',
      data: {
        users: Array.from(this.users.values()),
        modelState: this.modelState,
        roomId: this.roomId,
      },
    }));

    // Handle messages
    websocket.addEventListener('message', async (event) => {
      try {
        const message: Message = JSON.parse(event.data as string);
        await this.handleMessage(message, userId);
      } catch (error) {
        console.error('Error handling message:', error);
      }
    });

    // Handle disconnect
    websocket.addEventListener('close', async () => {
      await this.handleUserLeave(userId);
    });

    // Track analytics
    this.env.ANALYTICS?.writeDataPoint({
      blobs: [this.roomId, userId, 'user-join'],
      doubles: [Date.now()],
      indexes: [this.roomId],
    });
  }

  private async handleMessage(message: Message, senderId: string) {
    const user = this.users.get(senderId);
    if (!user) return;

    switch (message.type) {
      case 'cursor-move':
        user.cursor = message.data.cursor;
        this.users.set(senderId, user);
        this.broadcast(message, senderId);
        break;

      case 'camera-sync':
        user.camera = message.data.camera;
        this.users.set(senderId, user);
        this.broadcast(message, senderId);
        break;

      case 'model-change':
        await this.handleModelChange(message, senderId);
        break;

      case 'chat':
        this.broadcast(message, senderId);
        break;

      case 'annotation':
        this.broadcast(message, senderId);
        break;
    }

    // Update user activity
    user.isActive = true;
    await this.state.storage.put('users', Array.from(this.users.entries()));
  }

  private async handleModelChange(message: Message, userId: string) {
    if (!this.modelState) {
      this.modelState = {
        id: message.data.modelId,
        version: 1,
        lastModified: Date.now(),
        modifiedBy: userId,
        changes: [],
      };
    }

    const change: ModelChange = {
      id: crypto.randomUUID(),
      type: message.data.changeType,
      timestamp: Date.now(),
      userId,
      data: message.data.changeData,
      applied: false,
    };

    this.modelState.changes.push(change);
    this.modelState.version++;
    this.modelState.lastModified = Date.now();
    this.modelState.modifiedBy = userId;

    // Persist model state
    await this.state.storage.put('modelState', this.modelState);

    // Broadcast change to all users
    this.broadcast({
      type: 'model-change',
      userId,
      timestamp: Date.now(),
      data: {
        change,
        modelState: this.modelState,
      },
    });

    // Track analytics
    this.env.ANALYTICS?.writeDataPoint({
      blobs: [this.roomId, userId, 'model-change', change.type],
      doubles: [Date.now()],
      indexes: [this.roomId],
    });
  }

  private async handleUserLeave(userId: string) {
    const user = this.users.get(userId);
    if (!user) return;

    this.users.delete(userId);
    this.websockets.delete(userId);

    // Persist updated user list
    await this.state.storage.put('users', Array.from(this.users.entries()));

    // Notify other users
    this.broadcast({
      type: 'user-leave',
      userId,
      timestamp: Date.now(),
      data: { user },
    });

    // Track analytics
    this.env.ANALYTICS?.writeDataPoint({
      blobs: [this.roomId, userId, 'user-leave'],
      doubles: [Date.now()],
      indexes: [this.roomId],
    });
  }

  private broadcast(message: Message, excludeUserId?: string) {
    const messageStr = JSON.stringify(message);
    
    for (const [userId, websocket] of this.websockets) {
      if (userId !== excludeUserId && websocket.readyState === WebSocket.READY_STATE_OPEN) {
        try {
          websocket.send(messageStr);
        } catch (error) {
          console.error(`Error sending message to user ${userId}:`, error);
          // Remove broken connection
          this.websockets.delete(userId);
        }
      }
    }
  }

  private async handleJoin(request: Request): Promise<Response> {
    const { userId, userName } = await request.json();
    
    const user: User = {
      id: userId,
      name: userName,
      isActive: true,
      joinedAt: Date.now(),
    };

    this.users.set(userId, user);
    await this.state.storage.put('users', Array.from(this.users.entries()));

    return new Response(JSON.stringify({ success: true, roomId: this.roomId }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  private async handleLeave(request: Request): Promise<Response> {
    const { userId } = await request.json();
    await this.handleUserLeave(userId);

    return new Response(JSON.stringify({ success: true }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  private async handleGetState(request: Request): Promise<Response> {
    return new Response(JSON.stringify({
      users: Array.from(this.users.values()),
      modelState: this.modelState,
      roomId: this.roomId,
    }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }

  private async handleModelUpdate(request: Request): Promise<Response> {
    const { modelId, changes } = await request.json();
    
    if (!this.modelState) {
      this.modelState = {
        id: modelId,
        version: 1,
        lastModified: Date.now(),
        modifiedBy: 'system',
        changes: [],
      };
    }

    this.modelState.changes.push(...changes);
    this.modelState.version++;
    this.modelState.lastModified = Date.now();

    await this.state.storage.put('modelState', this.modelState);

    return new Response(JSON.stringify({ success: true, modelState: this.modelState }), {
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
