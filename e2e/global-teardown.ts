/**
 * Глобальне завершення для E2E тестів
 */

import { FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Початок глобального завершення E2E тестів...');

  try {
    // Тут можна додати очистку після тестів:
    // - Видалення тестових даних
    // - Очистка кешів
    // - Зупинка mock сервісів
    // - Очистка файлів

    console.log('🗑️ Очистка тестових даних...');
    
    // Приклад очистки (якщо потрібно)
    // await cleanupTestData();
    
    console.log('📊 Генерація звіту про тести...');
    
    // Можна додати генерацію додаткових звітів
    // await generateTestReport();
    
    console.log('✅ Глобальне завершення виконано успішно');

  } catch (error) {
    console.error('❌ Помилка глобального завершення:', error);
    // Не кидаємо помилку, щоб не заблокувати завершення тестів
  }
}

export default globalTeardown;
