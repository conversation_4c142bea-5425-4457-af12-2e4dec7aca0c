/**
 * E2E тести для Enhanced Admin Dashboard
 */

import { test, expect } from '@playwright/test';

test.describe('Enhanced Admin Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Переходимо на сторінку адмін панелі
    await page.goto('/admin/enhanced');
  });

  test('повинен відобразити головну сторінку адмін панелі', async ({ page }) => {
    // Перевіряємо заголовок
    await expect(page.locator('h1')).toContainText('Enhanced Admin Dashboard');
    
    // Перевіряємо наявність вкладок
    await expect(page.locator('[data-testid="tabs"]')).toBeVisible();
    await expect(page.getByText('Огляд')).toBeVisible();
    await expect(page.getByText('Завантаження')).toBeVisible();
    await expect(page.getByText('Моніторинг')).toBeVisible();
    await expect(page.getByText('Налаштування')).toBeVisible();
  });

  test('повинен показати статус системи на вкладці Огляд', async ({ page }) => {
    // Перевіряємо статусні картки
    await expect(page.getByText('Bright Data MCP')).toBeVisible();
    await expect(page.getByText('Model Downloads')).toBeVisible();
    await expect(page.getByText('Cloudflare R2')).toBeVisible();
    await expect(page.getByText('Observability')).toBeVisible();

    // Перевіряємо архітектурну діаграму
    await expect(page.getByText('Архітектура системи')).toBeVisible();
    await expect(page.getByText('Bright Data MCP')).toBeVisible();
    await expect(page.getByText('Model Download Manager')).toBeVisible();
    await expect(page.getByText('Cloudflare Observability')).toBeVisible();
  });

  test('повинен переключатися між вкладками', async ({ page }) => {
    // Клікаємо на вкладку Завантаження
    await page.getByText('Завантаження').click();
    await expect(page.getByText('Завдання завантаження')).toBeVisible();

    // Клікаємо на вкладку Моніторинг
    await page.getByText('Моніторинг').click();
    await expect(page.getByText('Статус системи')).toBeVisible();

    // Клікаємо на вкладку Налаштування
    await page.getByText('Налаштування').click();
    await expect(page.getByText('Налаштування системи')).toBeVisible();
  });

  test('повинен відобразити дані завантажень', async ({ page }) => {
    // Переходимо на вкладку Завантаження
    await page.getByText('Завантаження').click();

    // Очікуємо завантаження даних
    await page.waitForSelector('[data-testid="download-stats"]', { timeout: 10000 });

    // Перевіряємо статистику
    await expect(page.getByText('Всього завдань')).toBeVisible();
    await expect(page.getByText('Завершено')).toBeVisible();
    await expect(page.getByText('В очікуванні')).toBeVisible();
    await expect(page.getByText('Завантажено')).toBeVisible();

    // Перевіряємо список завдань
    await expect(page.getByText('Завдання завантаження')).toBeVisible();
    
    // Перевіряємо фільтри
    await expect(page.getByText('Всі')).toBeVisible();
    await expect(page.getByText('Очікують')).toBeVisible();
    await expect(page.getByText('Завантажуються')).toBeVisible();
    await expect(page.getByText('Завершені')).toBeVisible();
    await expect(page.getByText('Помилки')).toBeVisible();
  });

  test('повинен фільтрувати завдання за статусом', async ({ page }) => {
    await page.getByText('Завантаження').click();
    
    // Очікуємо завантаження даних
    await page.waitForSelector('[data-testid="tabs-list"]', { timeout: 10000 });

    // Клікаємо на фільтр "Завершені"
    await page.getByText('Завершені').click();
    
    // Перевіряємо, що показуються тільки завершені завдання
    await page.waitForSelector('[data-testid="tab-content-completed"]', { timeout: 5000 });
  });

  test('повинен оновлювати дані при натисканні кнопки оновлення', async ({ page }) => {
    await page.getByText('Завантаження').click();
    
    // Знаходимо кнопку оновлення
    const refreshButton = page.getByText('Оновити');
    await expect(refreshButton).toBeVisible();

    // Клікаємо на кнопку оновлення
    await refreshButton.click();

    // Перевіряємо, що дані оновлюються (можна перевірити через network requests)
    await page.waitForResponse(response => 
      response.url().includes('/api/models/download-manager') && response.status() === 200
    );
  });

  test('повинен відобразити метрики на вкладці Моніторинг', async ({ page }) => {
    await page.getByText('Моніторинг').click();

    // Очікуємо завантаження компонента observability
    await page.waitForSelector('[data-testid="observability-dashboard"]', { timeout: 10000 });

    // Перевіряємо статус здоров'я
    await expect(page.getByText('Статус системи')).toBeVisible();
    
    // Перевіряємо контроли
    await expect(page.locator('select').first()).toBeVisible(); // Time range selector
    await expect(page.getByText('Оновити')).toBeVisible();

    // Перевіряємо вкладки метрик
    await expect(page.getByText('Метрики')).toBeVisible();
    await expect(page.getByText('Логи')).toBeVisible();
    await expect(page.getByText('Графіки')).toBeVisible();
  });

  test('повинен переключатися між типами даних в Observability', async ({ page }) => {
    await page.getByText('Моніторинг').click();
    await page.waitForSelector('[data-testid="observability-dashboard"]', { timeout: 10000 });

    // Клікаємо на вкладку Логи
    await page.getByText('Логи').click();
    await expect(page.getByText('Логи системи')).toBeVisible();

    // Клікаємо на вкладку Графіки
    await page.getByText('Графіки').click();
    await page.waitForSelector('[data-testid="line-chart"]', { timeout: 5000 });
  });

  test('повинен відобразити налаштування системи', async ({ page }) => {
    await page.getByText('Налаштування').click();

    // Перевіряємо секції налаштувань
    await expect(page.getByText('Налаштування системи')).toBeVisible();
    await expect(page.getByText('Bright Data MCP')).toBeVisible();
    await expect(page.getByText('Model Downloads')).toBeVisible();
    await expect(page.getByText('Cloudflare Services')).toBeVisible();

    // Перевіряємо статуси сервісів
    await expect(page.getByText('API Token:')).toBeVisible();
    await expect(page.getByText('Fallback Mode:')).toBeVisible();
    await expect(page.getByText('Max Concurrent:')).toBeVisible();
  });

  test('повинен бути responsive на мобільних пристроях', async ({ page }) => {
    // Встановлюємо розмір мобільного пристрою
    await page.setViewportSize({ width: 375, height: 667 });

    // Перевіряємо, що компоненти адаптуються
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('[data-testid="tabs"]')).toBeVisible();

    // Перевіряємо, що статистичні картки стекуються
    const cards = page.locator('[data-testid="stat-card"]');
    if (await cards.count() > 0) {
      // На мобільних пристроях картки повинні бути в одну колонку
      const firstCard = cards.first();
      const secondCard = cards.nth(1);
      
      if (await secondCard.isVisible()) {
        const firstCardBox = await firstCard.boundingBox();
        const secondCardBox = await secondCard.boundingBox();
        
        if (firstCardBox && secondCardBox) {
          // Друга картка повинна бути нижче першої (стекування)
          expect(secondCardBox.y).toBeGreaterThan(firstCardBox.y + firstCardBox.height - 10);
        }
      }
    }
  });

  test('повинен обробляти помилки API gracefully', async ({ page }) => {
    // Перехоплюємо API запити та повертаємо помилки
    await page.route('**/api/models/download-manager**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal Server Error' })
      });
    });

    await page.getByText('Завантаження').click();

    // Компонент не повинен крашитися, навіть при помилках API
    await expect(page.getByText('Завдання завантаження')).toBeVisible();
  });

  test('повинен показувати індикатори завантаження', async ({ page }) => {
    // Затримуємо API відповіді
    await page.route('**/api/models/download-manager**', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000));
      route.continue();
    });

    await page.getByText('Завантаження').click();

    // Перевіряємо наявність індикатора завантаження
    await expect(page.getByText('Завантаження...')).toBeVisible();
  });

  test('повинен зберігати стан вкладок при навігації', async ({ page }) => {
    // Переходимо на вкладку Моніторинг
    await page.getByText('Моніторинг').click();
    
    // Перезавантажуємо сторінку
    await page.reload();
    
    // Перевіряємо, що вкладка Огляд активна за замовчуванням
    await expect(page.getByText('Архітектура системи')).toBeVisible();
  });
});
