/**
 * Глобальне налаштування для E2E тестів
 */

import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Початок глобального налаштування E2E тестів...');

  // Запускаємо браузер для початкових налаштувань
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Перевіряємо, що сервер запущений
    console.log('🔍 Перевірка доступності сервера...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    console.log('✅ Сервер доступний');

    // Можна додати додаткові налаштування тут:
    // - Створення тестових даних
    // - Налаштування mock сервісів
    // - Очистка попередніх тестових даних

    // Перевіряємо основні сторінки
    console.log('🔍 Перевірка основних сторінок...');
    
    // Головна сторінка
    await page.goto('http://localhost:3000/');
    await page.waitForSelector('h1', { timeout: 10000 });
    console.log('✅ Головна сторінка доступна');

    // Enhanced Admin сторінка
    await page.goto('http://localhost:3000/admin/enhanced');
    await page.waitForSelector('h1', { timeout: 10000 });
    console.log('✅ Enhanced Admin сторінка доступна');

    // Можна додати перевірку API endpoints
    console.log('🔍 Перевірка API endpoints...');
    
    try {
      const response = await page.request.get('http://localhost:3000/api/cloudflare/observability?type=health');
      if (response.ok()) {
        console.log('✅ Observability API доступний');
      } else {
        console.log('⚠️ Observability API недоступний, але тести продовжуються');
      }
    } catch (error) {
      console.log('⚠️ Observability API недоступний, але тести продовжуються');
    }

    console.log('✅ Глобальне налаштування завершено успішно');

  } catch (error) {
    console.error('❌ Помилка глобального налаштування:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

export default globalSetup;
