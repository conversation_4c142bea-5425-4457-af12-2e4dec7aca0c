/**
 * E2E тести для головної сторінки marketplace з новими компонентами
 */

import { test, expect } from '@playwright/test';

test.describe('Marketplace Homepage with Enhanced Features', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('повинен відобразити головну сторінку з усіма компонентами', async ({ page }) => {
    // Перевіряємо Hero секцію
    await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
    
    // Перевіряємо System Status Banner
    await expect(page.getByText('Система активна')).toBeVisible();
    
    // Перевіряємо основні секції
    await expect(page.getByText('Popular Tags')).toBeVisible();
    await expect(page.getByText('Featured Models')).toBeVisible();
  });

  test('повинен відобразити System Status Banner', async ({ page }) => {
    // Очікуємо завантаження статусу системи
    await page.waitForSelector('[data-testid="system-status-banner"]', { timeout: 10000 });
    
    // Перевіряємо основні елементи
    await expect(page.getByText('Система активна')).toBeVisible();
    await expect(page.getByText('Всі сервіси працюють')).toBeVisible();
    
    // Перевіряємо статистику
    await expect(page.locator('text=/\\d+ запитів/')).toBeVisible();
    await expect(page.locator('text=/\\d+ завантажень/')).toBeVisible();
    await expect(page.locator('text=/Останній скрапінг:/')).toBeVisible();
  });

  test('повинен розгортати детальний вигляд System Status Banner', async ({ page }) => {
    await page.waitForSelector('[data-testid="system-status-banner"]', { timeout: 10000 });
    
    // Клікаємо на кнопку "Детальніше"
    await page.getByText('Детальніше').click();
    
    // Перевіряємо розгорнутий вигляд
    await expect(page.getByText('Bright Data MCP')).toBeVisible();
    await expect(page.getByText('Завантаження моделей')).toBeVisible();
    await expect(page.getByText('Cloudflare')).toBeVisible();
    await expect(page.getByText('Моніторинг')).toBeVisible();
    
    // Перевіряємо статуси сервісів
    await expect(page.locator('text=/R2 Storage:/')).toBeVisible();
    await expect(page.locator('text=/D1 Database:/')).toBeVisible();
    await expect(page.locator('text=/KV Storage:/')).toBeVisible();
    await expect(page.locator('text=/Analytics:/')).toBeVisible();
  });

  test('повинен оновлювати System Status Banner', async ({ page }) => {
    await page.waitForSelector('[data-testid="system-status-banner"]', { timeout: 10000 });
    
    // Клікаємо на кнопку оновлення
    const refreshButton = page.locator('[data-testid="system-status-banner"] button').last();
    await refreshButton.click();
    
    // Перевіряємо, що статус оновлюється
    await page.waitForTimeout(1000); // Даємо час на оновлення
  });

  test('повинен відобразити Real-time Updates panel', async ({ page }) => {
    // Перевіряємо наявність floating notification button
    const notificationButton = page.locator('[data-testid="real-time-updates-button"]');
    await expect(notificationButton).toBeVisible();
    
    // Клікаємо на кнопку сповіщень
    await notificationButton.click();
    
    // Перевіряємо панель оновлень
    await expect(page.getByText('Real-time оновлення')).toBeVisible();
    await expect(page.getByText('Підключено')).toBeVisible();
  });

  test('повинен показувати сповіщення в Real-time Updates', async ({ page }) => {
    // Відкриваємо панель сповіщень
    const notificationButton = page.locator('[data-testid="real-time-updates-button"]');
    await notificationButton.click();
    
    // Очікуємо появи сповіщень (симуляція)
    await page.waitForTimeout(6000); // Чекаємо більше 5 секунд для генерації mock оновлень
    
    // Перевіряємо, що з'явилися сповіщення
    const notifications = page.locator('[data-testid="notification-item"]');
    if (await notifications.count() > 0) {
      await expect(notifications.first()).toBeVisible();
    }
  });

  test('повинен керувати сповіщеннями в Real-time Updates', async ({ page }) => {
    const notificationButton = page.locator('[data-testid="real-time-updates-button"]');
    await notificationButton.click();
    
    // Очікуємо появи сповіщень
    await page.waitForTimeout(6000);
    
    const notifications = page.locator('[data-testid="notification-item"]');
    if (await notifications.count() > 0) {
      // Тестуємо кнопку "Прочитати всі"
      const markAllReadButton = page.getByText('Прочитати всі');
      if (await markAllReadButton.isVisible()) {
        await markAllReadButton.click();
      }
      
      // Тестуємо видалення окремого сповіщення
      const deleteButton = notifications.first().locator('button').last();
      if (await deleteButton.isVisible()) {
        await deleteButton.click();
      }
    }
  });

  test('повинен показувати індикатор підключення Real-time Updates', async ({ page }) => {
    const notificationButton = page.locator('[data-testid="real-time-updates-button"]');
    
    // Перевіряємо індикатор підключення (зелена крапка)
    const connectionIndicator = notificationButton.locator('[data-testid="connection-indicator"]');
    if (await connectionIndicator.isVisible()) {
      await expect(connectionIndicator).toHaveClass(/bg-green-500/);
    }
  });

  test('повинен відобразити Enhanced Model Cards', async ({ page }) => {
    // Прокручуємо до секції моделей
    await page.locator('text=Featured Models').scrollIntoViewIfNeeded();
    
    // Перевіряємо наявність покращених карток моделей
    const modelCards = page.locator('[data-testid="enhanced-model-card"]');
    if (await modelCards.count() > 0) {
      const firstCard = modelCards.first();
      
      // Перевіряємо елементи картки
      await expect(firstCard.locator('[data-testid="model-title"]')).toBeVisible();
      await expect(firstCard.locator('[data-testid="model-author"]')).toBeVisible();
      await expect(firstCard.locator('[data-testid="model-platform"]')).toBeVisible();
      await expect(firstCard.locator('[data-testid="model-stats"]')).toBeVisible();
    }
  });

  test('повинен взаємодіяти з Enhanced Model Cards', async ({ page }) => {
    await page.locator('text=Featured Models').scrollIntoViewIfNeeded();
    
    const modelCards = page.locator('[data-testid="enhanced-model-card"]');
    if (await modelCards.count() > 0) {
      const firstCard = modelCards.first();
      
      // Тестуємо кнопку лайка
      const likeButton = firstCard.locator('[data-testid="like-button"]');
      if (await likeButton.isVisible()) {
        await likeButton.click();
        // Перевіряємо зміну стану
        await expect(likeButton).toHaveClass(/text-red-500/);
      }
      
      // Тестуємо кнопку завантаження
      const downloadButton = firstCard.locator('[data-testid="download-button"]');
      if (await downloadButton.isVisible()) {
        await downloadButton.click();
      }
      
      // Тестуємо кнопку перегляду
      const viewButton = firstCard.locator('[data-testid="view-button"]');
      if (await viewButton.isVisible()) {
        await viewButton.click();
      }
    }
  });

  test('повинен показувати статус завантаження в Model Cards', async ({ page }) => {
    await page.locator('text=Featured Models').scrollIntoViewIfNeeded();
    
    const modelCards = page.locator('[data-testid="enhanced-model-card"]');
    if (await modelCards.count() > 0) {
      const firstCard = modelCards.first();
      
      // Перевіряємо індикатор статусу завантаження
      const statusIndicator = firstCard.locator('[data-testid="download-status"]');
      if (await statusIndicator.isVisible()) {
        await expect(statusIndicator).toBeVisible();
      }
      
      // Перевіряємо прогрес бар для завантажуваних моделей
      const progressBar = firstCard.locator('[data-testid="download-progress"]');
      if (await progressBar.isVisible()) {
        await expect(progressBar).toBeVisible();
      }
    }
  });

  test('повинен працювати на мобільних пристроях', async ({ page }) => {
    // Встановлюємо розмір мобільного пристрою
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Перевіряємо, що System Status Banner адаптується
    await page.waitForSelector('[data-testid="system-status-banner"]', { timeout: 10000 });
    await expect(page.getByText('Система активна')).toBeVisible();
    
    // Перевіряємо, що Real-time Updates button видимий
    const notificationButton = page.locator('[data-testid="real-time-updates-button"]');
    await expect(notificationButton).toBeVisible();
    
    // Перевіряємо, що модельні картки стекуються
    const modelCards = page.locator('[data-testid="enhanced-model-card"]');
    if (await modelCards.count() > 1) {
      const firstCard = modelCards.first();
      const secondCard = modelCards.nth(1);
      
      const firstCardBox = await firstCard.boundingBox();
      const secondCardBox = await secondCard.boundingBox();
      
      if (firstCardBox && secondCardBox) {
        // На мобільних картки повинні бути одна під одною
        expect(secondCardBox.y).toBeGreaterThan(firstCardBox.y + firstCardBox.height - 20);
      }
    }
  });

  test('повинен зберігати стан Real-time Updates між сесіями', async ({ page }) => {
    // Відкриваємо панель сповіщень
    const notificationButton = page.locator('[data-testid="real-time-updates-button"]');
    await notificationButton.click();
    
    // Закриваємо панель
    await page.getByText('×').click();
    
    // Перезавантажуємо сторінку
    await page.reload();
    
    // Перевіряємо, що кнопка сповіщень все ще працює
    await expect(notificationButton).toBeVisible();
  });

  test('повинен обробляти помилки завантаження gracefully', async ({ page }) => {
    // Перехоплюємо API запити та повертаємо помилки
    await page.route('**/api/cloudflare/observability**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Service Unavailable' })
      });
    });
    
    // Сторінка не повинна крашитися
    await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
    
    // System Status Banner може показувати помилку, але не крашитися
    await page.waitForSelector('[data-testid="system-status-banner"]', { timeout: 10000 });
  });

  test('повинен показувати правильні посилання в навігації', async ({ page }) => {
    // Перевіряємо наявність посилання на Enhanced Admin
    const enhancedAdminLink = page.getByText('Enhanced Admin');
    await expect(enhancedAdminLink).toBeVisible();
    
    // Клікаємо на посилання
    await enhancedAdminLink.click();
    
    // Перевіряємо, що перейшли на правильну сторінку
    await expect(page).toHaveURL('/admin/enhanced');
    await expect(page.getByText('Enhanced Admin Dashboard')).toBeVisible();
  });
});
