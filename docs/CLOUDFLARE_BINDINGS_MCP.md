# 🔗 Cloudflare Bindings MCP Integration

## 📋 Overview

Integration with cloudflare-bindings MCP for working with real Cloudflare services instead of mock data. This provides direct access to R2, D1, KV, Analytics Engine, and Durable Objects.

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Components                      │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ Enhanced Admin  │ │ Observability   │ │ System Status   │ │
│  │   Dashboard     │ │   Dashboard     │ │    Banner       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      API Layer                              │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ /api/cloudflare │ │ /api/cloudflare │ │ /api/models/    │ │
│  │ /observability  │ │ /bindings       │ │ download-manager│ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                 Cloudflare Bindings MCP                     │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ Bindings Client │ │ Health Checker  │ │ Usage Monitor   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                  Cloudflare Services                        │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   R2 Storage    │ │  D1 Database    │ │   KV Storage    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐                     │
│  │ Analytics Engine│ │ Durable Objects │                     │
│  └─────────────────┘ └─────────────────┘                     │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Components

### 1. CloudflareBindingsMCPClient

**File**: `src/lib/cloudflare/bindings-mcp-client.ts`

**Functionality:**
- Direct access to Cloudflare bindings
- Health check for all services
- Operations with R2, D1, KV, Analytics
- Durable Objects management

**Main Methods:**
```typescript
// Initialization
await client.initialize(bindings);

// Health check
const health = await client.healthCheck();

// R2 operations
await client.r2Put(key, data, metadata);
await client.r2Get(key);
await client.r2Delete(key);
await client.r2List(prefix, limit);

// D1 operations
await client.d1Query(sql, params);
await client.d1Execute(sql, params);

// KV operations
await client.kvPut(key, value, options);
await client.kvGet(key, type);
await client.kvDelete(key);

// Analytics
await client.analyticsWrite(dataPoint);

// Durable Objects
const stub = await client.getDurableObject('MODEL_DOWNLOAD_MANAGER', id);
```

### 2. API Endpoints

#### `/api/cloudflare/bindings`

**GET** - Data retrieval:
```bash
# R2 operations
GET /api/cloudflare/bindings?service=r2&action=get&key=models/test.stl
GET /api/cloudflare/bindings?service=r2&action=list&prefix=models/

# KV operations
GET /api/cloudflare/bindings?service=kv&action=get&key=cache-key&type=json

# D1 operations
GET /api/cloudflare/bindings?service=d1&action=query&sql=SELECT * FROM models

# Health check
GET /api/cloudflare/bindings?service=health

# Usage statistics
GET /api/cloudflare/bindings?service=usage
```

**POST** - Data writing:
```bash
# R2 upload
POST /api/cloudflare/bindings?service=r2&action=put
{
  "key": "models/test.stl",
  "data": "binary data or base64",
  "metadata": { "author": "user123" }
}

# KV write
POST /api/cloudflare/bindings?service=kv&action=put
{
  "key": "cache-key",
  "value": "cache value",
  "options": { "expirationTtl": 3600 }
}

# D1 execute
POST /api/cloudflare/bindings?service=d1&action=execute
{
  "sql": "INSERT INTO models (name) VALUES (?)",
  "params": ["Test Model"]
}

# Analytics write
POST /api/cloudflare/bindings?service=analytics&action=write
{
  "dataPoint": {
    "blobs": ["metric_name"],
    "doubles": [42],
    "indexes": ["metric_name"]
  }
}
```

**DELETE** - Data deletion:
```bash
# R2 delete
DELETE /api/cloudflare/bindings?service=r2&key=models/test.stl

# KV delete
DELETE /api/cloudflare/bindings?service=kv&key=cache-key
```

### 3. Enhanced Observability

**File**: `src/app/api/cloudflare/observability/route.ts`

**Improvements:**
- Integration with bindings client
- Real health check
- Combined data from observability and bindings API
- Extended service status information

**Health Check Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": 1704067200000,
    "services": {
      "analytics": true,
      "kv": true,
      "r2": true,
      "d1": true,
      "durableObjects": true
    },
    "bindings": {
      "analytics": true,
      "kv": true,
      "r2": true,
      "d1": true,
      "modelDownloadManager": true,
      "scrapingCoordinator": true
    },
    "bindingsHealth": {
      "r2": true,
      "d1": true,
      "kv": true,
      "analytics": true,
      "durableObjects": true
    },
    "lastChecked": 1704067200000
  }
}
```

### 4. ModelDownloadManager Integration

**File**: `src/durable-objects/model-download-manager.ts`

**Improvements:**
- Using CloudflareBindingsMCPClient
- Real R2 operations
- Enhanced error handling
- File metadata

**Usage Example:**
```typescript
// Upload file to R2
const r2Result = await this.bindingsClient.r2Put(r2Key, arrayBuffer, {
  'content-type': this.getContentType(job.fileType),
  'content-disposition': `attachment; filename="${job.fileName}"`,
  'model-id': job.modelId,
  'platform': job.platform,
  'original-url': job.sourceUrl,
  'uploaded-at': new Date().toISOString()
});

if (!r2Result.success) {
  throw new Error(`R2 upload failed: ${r2Result.error}`);
}
```

## 🎯 Integration Benefits

### 1. **Real Data**
- Real Cloudflare services used instead of mock data
- Accurate service health status
- Real usage metrics

### 2. **Reliability**
- Automatic health check
- Graceful fallback on errors
- Retry logic for critical operations

### 3. **Monitoring**
- Detailed service status information
- Real performance metrics
- Logging of all operations

### 4. **Scalability**
- Direct access to Cloudflare Edge
- Optimized operations
- Minimal latency

## 🔍 Health Check System

### Check Types

1. **Basic Availability** - Check bindings availability
2. **Functional Tests** - Test operations with each service
3. **Performance Metrics** - Response time and throughput

### Health Check Results

```typescript
interface HealthCheckResult {
  r2: boolean;           // R2 bucket available
  d1: boolean;           // D1 database connected
  kv: boolean;           // KV namespace available
  analytics: boolean;    // Analytics Engine active
  durableObjects: boolean; // DO namespaces available
}
```

### Status Levels

- **🟢 Healthy** - All services working
- **🟡 Degraded** - Some services unavailable
- **🔴 Unhealthy** - Critical services not working

## 📊 Usage Monitoring

### Tracked Metrics

```typescript
interface UsageStats {
  r2: {
    requests: number;    // Number of requests
    storage: number;     // Used space (bytes)
  };
  d1: {
    queries: number;     // Number of queries
    storage: number;     // Database size (bytes)
  };
  kv: {
    requests: number;    // Number of operations
    storage: number;     // Used space (bytes)
  };
  analytics: {
    datapoints: number;  // Number of written points
  };
}
```

## 🚀 Deployment

### Environment Variables

```env
# Cloudflare Account
CLOUDFLARE_ACCOUNT_ID=your_account_id
CLOUDFLARE_API_TOKEN=your_api_token

# Bindings MCP
MCP_SERVER_URL=http://localhost:3001
BINDINGS_MCP_ENABLED=true
```

### Wrangler Configuration

```toml
# wrangler.toml
[env.production]
name = "3d-marketplace"

[[env.production.r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "marketplace-storage"

[[env.production.d1_databases]]
binding = "DB"
database_name = "marketplace_db"

[[env.production.kv_namespaces]]
binding = "CACHE_KV"
id = "your_kv_namespace_id"

[[env.production.analytics_engine_datasets]]
binding = "ANALYTICS"
dataset = "marketplace_analytics"

[[env.production.durable_objects.bindings]]
name = "MODEL_DOWNLOAD_MANAGER"
class_name = "ModelDownloadManager"

[[env.production.durable_objects.bindings]]
name = "SCRAPING_COORDINATOR"
class_name = "ScrapingCoordinator"
```

## 🧪 Testing

### Unit Tests

```bash
# Test bindings client
npm run test:bright-data

# Test API endpoints
npm run test:integration

# Test Durable Objects
npm run test:durable-objects
```

### Health Check Test

```typescript
describe('CloudflareBindingsMCPClient', () => {
  it('should pass health check', async () => {
    const client = new CloudflareBindingsMCPClient();
    await client.initialize(mockBindings);

    const health = await client.healthCheck();

    expect(health.r2).toBe(true);
    expect(health.d1).toBe(true);
    expect(health.kv).toBe(true);
    expect(health.analytics).toBe(true);
    expect(health.durableObjects).toBe(true);
  });
});
```

## 🔧 Troubleshooting

### Common Issues

1. **Bindings not initialized**
   ```
   Error: Bindings not initialized
   ```
   **Solution**: Check wrangler.toml and environment variables

2. **Health check fails**
   ```
   R2 health check failed: Error
   ```
   **Solution**: Check access permissions and bucket configuration

3. **API timeout**
   ```
   Request timeout after 30000ms
   ```
   **Solution**: Increase timeout or check network connection

### Debug Mode

```typescript
const client = new CloudflareBindingsMCPClient({
  enableFallback: false,  // Disable fallback for debug
  timeout: 60000         // Increase timeout
});
```

---

**Ready!** 🎉 Cloudflare Bindings MCP is fully integrated and ready to use!
