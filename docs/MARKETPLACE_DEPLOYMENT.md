# Розгортання маркетплейсу 3D-моделей на Cloudflare

## Зміст

1. [Вступ](#вступ)
2. [Вимоги](#вимоги)
3. [Налаштування Cloudflare Pages](#налаштування-cloudflare-pages)
4. [Налаштування Cloudflare Workers](#налаштування-cloudflare-workers)
5. [Налаштування Cloudflare R2](#налаштування-cloudflare-r2)
6. [Налаштування Cloudflare D1](#налаштування-cloudflare-d1)
7. [Налаштування CI/CD](#налаштування-cicd)
8. [Налаштування домену](#налаштування-домену)
9. [Мон<PERSON>торинг та аналітика](#моніторинг-та-аналітика)
10. [Оптимізація продуктивності](#оптимізація-продуктивності)
11. [Безпека](#безпека)
12. [Вирішення проблем](#вирішення-проблем)

## Вступ

Цей документ описує процес розгортання маркетплейсу 3D-моделей на Cloudflare. Cloudflare надає повний стек сервісів для розгортання веб-додатків, включаючи хостинг статичних файлів (Pages), серверні функції (Workers), зберігання даних (D1) та об'єктне сховище (R2).

## Вимоги

Перед початком розгортання переконайтеся, що у вас є:

1. Обліковий запис Cloudflare
2. Встановлений Wrangler CLI (`npm install -g wrangler`)
3. Репозиторій з кодом маркетплейсу
4. Домен, зареєстрований в Cloudflare (опціонально)

## Налаштування Cloudflare Pages

Cloudflare Pages використовується для хостингу статичних файлів та серверних компонентів Next.js.

### Створення проекту Pages

1. Увійдіть в панель керування Cloudflare
2. Перейдіть до розділу "Pages"
3. Натисніть "Create a project"
4. Виберіть "Connect to Git"
5. Підключіть свій репозиторій GitHub/GitLab/Bitbucket
6. Налаштуйте параметри збірки:
   - Framework preset: Next.js
   - Build command: `npm run build`
   - Build output directory: `.next`
   - Root directory: `/`
   - Node.js version: 18 (або новіше)

### Налаштування змінних середовища

Додайте необхідні змінні середовища в налаштуваннях проекту:

1. Перейдіть до розділу "Settings" > "Environment variables"
2. Додайте наступні змінні:
   - `NODE_VERSION`: 18
   - `NEXT_PUBLIC_APP_URL`: URL вашого додатку
   - `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`: Публічний ключ Stripe
   - `STRIPE_SECRET_KEY`: Секретний ключ Stripe
   - `STRIPE_WEBHOOK_SECRET`: Секретний ключ для webhook'ів Stripe
   - `NEXTAUTH_URL`: URL вашого додатку
   - `NEXTAUTH_SECRET`: Секретний ключ для NextAuth.js
   - `DATABASE_URL`: URL бази даних (якщо використовується зовнішня база даних)

### Налаштування функцій Pages

Для використання серверних компонентів Next.js та API маршрутів, налаштуйте функції Pages:

1. Створіть файл `functions/[[path]].js` в кореневій директорії проекту:

```javascript
// functions/[[path]].js
export async function onRequest(context) {
  // Передача запиту до Next.js
  return await context.next();
}
```

2. Створіть файл `_routes.json` в кореневій директорії проекту:

```json
{
  "version": 1,
  "include": ["/*"],
  "exclude": [
    "/build/*",
    "/public/*",
    "/_next/*",
    "/favicon.ico",
    "/robots.txt"
  ]
}
```

### Налаштування Next.js для Cloudflare Pages

Оновіть файл `next.config.js` для сумісності з Cloudflare Pages:

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: ['your-r2-bucket.your-account.r2.cloudflarestorage.com'],
    formats: ['image/avif', 'image/webp'],
  },
  experimental: {
    serverActions: true,
  },
  // Налаштування для Cloudflare Pages
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
      };
    }
    return config;
  },
};

module.exports = nextConfig;
```

## Налаштування Cloudflare Workers

Cloudflare Workers використовуються для серверних функцій, які не можуть бути реалізовані в Next.js API Routes.

### Створення Worker для обробки webhook'ів Stripe

1. Створіть новий файл `workers/stripe-webhook.js`:

```javascript
// workers/stripe-webhook.js
import Stripe from 'stripe';

export default {
  async fetch(request, env, ctx) {
    if (request.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 });
    }

    const signature = request.headers.get('stripe-signature');
    if (!signature) {
      return new Response('Stripe signature missing', { status: 400 });
    }

    const body = await request.text();
    const stripe = new Stripe(env.STRIPE_SECRET_KEY);

    try {
      const event = stripe.webhooks.constructEvent(
        body,
        signature,
        env.STRIPE_WEBHOOK_SECRET
      );

      // Обробка подій Stripe
      switch (event.type) {
        case 'payment_intent.succeeded':
          // Обробка успішного платежу
          break;
        case 'checkout.session.completed':
          // Обробка завершеної сесії оформлення
          break;
        // Інші події...
      }

      return new Response(JSON.stringify({ received: true }), {
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (err) {
      return new Response(`Webhook Error: ${err.message}`, { status: 400 });
    }
  },
};
```

2. Налаштуйте Worker в Cloudflare:
   - Перейдіть до розділу "Workers & Pages"
   - Натисніть "Create a Worker"
   - Завантажте код Worker'а
   - Налаштуйте змінні середовища для Worker'а

### Налаштування маршрутів для Worker'ів

Налаштуйте маршрути для перенаправлення запитів до Worker'ів:

1. Перейдіть до розділу "Workers & Pages" > "Routes"
2. Додайте маршрут:
   - Route: `your-domain.com/api/webhooks/stripe`
   - Worker: `stripe-webhook`

## Налаштування Cloudflare R2

Cloudflare R2 використовується для зберігання 3D-моделей, зображень та інших файлів.

### Створення бакету R2

1. Перейдіть до розділу "R2"
2. Натисніть "Create bucket"
3. Введіть назву бакету (наприклад, `3d-marketplace-files`)
4. Натисніть "Create bucket"

### Налаштування CORS для бакету

Налаштуйте CORS для доступу до файлів з вашого домену:

1. Перейдіть до розділу "R2" > ваш бакет > "Settings" > "CORS"
2. Додайте правило CORS:
   - Origin: `https://your-domain.com`
   - Allowed methods: `GET`, `PUT`, `POST`, `DELETE`
   - Allowed headers: `*`
   - Max age: `86400`

### Створення Worker для завантаження файлів

Створіть Worker для безпечного завантаження файлів в R2:

```javascript
// workers/upload-file.js
export default {
  async fetch(request, env, ctx) {
    if (request.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 });
    }

    // Перевірка аутентифікації
    // ...

    const formData = await request.formData();
    const file = formData.get('file');
    const fileName = formData.get('fileName');
    const contentType = file.type;

    if (!file) {
      return new Response('No file uploaded', { status: 400 });
    }

    try {
      // Завантаження файлу в R2
      await env.MY_BUCKET.put(fileName, file, {
        httpMetadata: {
          contentType,
        },
      });

      // Створення публічного URL для файлу
      const publicUrl = `https://your-r2-bucket.your-account.r2.cloudflarestorage.com/${fileName}`;

      return new Response(JSON.stringify({ url: publicUrl }), {
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (err) {
      return new Response(`Upload Error: ${err.message}`, { status: 500 });
    }
  },
};
```

## Налаштування Cloudflare D1

Cloudflare D1 - це SQL база даних, яка може бути використана для зберігання даних маркетплейсу.

### Створення бази даних D1

1. Використовуйте Wrangler CLI для створення бази даних:

```bash
wrangler d1 create 3d-marketplace
```

2. Запишіть ID бази даних, який буде виведений в консоль

### Створення таблиць

Створіть файл `schema.sql` з схемою бази даних:

```sql
-- schema.sql
CREATE TABLE users (
  id TEXT PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  stripe_customer_id TEXT,
  stripe_connect_account_id TEXT
);

CREATE TABLE models (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  price REAL NOT NULL,
  category TEXT,
  tags TEXT,
  thumbnail TEXT,
  model_url TEXT,
  user_id TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE purchases (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  model_id TEXT NOT NULL,
  amount REAL NOT NULL,
  payment_intent_id TEXT,
  status TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (model_id) REFERENCES models(id)
);

CREATE TABLE membership_plans (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  price REAL NOT NULL,
  interval TEXT NOT NULL,
  stripe_product_id TEXT,
  stripe_price_id TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE user_memberships (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  plan_id TEXT NOT NULL,
  stripe_subscription_id TEXT,
  status TEXT NOT NULL,
  current_period_end TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (plan_id) REFERENCES membership_plans(id)
);
```

3. Застосуйте схему до бази даних:

```bash
wrangler d1 execute 3d-marketplace --file=schema.sql
```

### Налаштування доступу до D1 з Next.js

Для доступу до D1 з Next.js, створіть файл `src/lib/db.js`:

```javascript
// src/lib/db.js
import { D1Database } from '@cloudflare/workers-types';

let db;

export function getDb() {
  if (!db) {
    // В середовищі Cloudflare Pages, D1 доступна через env.DB
    if (typeof process.env.DB !== 'undefined') {
      db = process.env.DB;
    } else {
      throw new Error('Database not available');
    }
  }
  return db;
}

export async function query(sql, params = []) {
  const db = getDb();
  const result = await db.prepare(sql).bind(...params).all();
  return result;
}

export async function queryOne(sql, params = []) {
  const db = getDb();
  const result = await db.prepare(sql).bind(...params).first();
  return result;
}

export async function execute(sql, params = []) {
  const db = getDb();
  return await db.prepare(sql).bind(...params).run();
}
```

## Налаштування CI/CD

Налаштуйте CI/CD для автоматичного розгортання при оновленні репозиторію.

### GitHub Actions

Створіть файл `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Cloudflare Pages

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build
        run: npm run build
        env:
          NODE_ENV: production
          NEXT_PUBLIC_APP_URL: ${{ secrets.NEXT_PUBLIC_APP_URL }}
          NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${{ secrets.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY }}
          
      - name: Deploy to Cloudflare Pages
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: pages deploy .next --project-name=3d-marketplace
```

### Налаштування секретів GitHub

Додайте необхідні секрети в налаштуваннях репозиторію GitHub:

1. Перейдіть до "Settings" > "Secrets and variables" > "Actions"
2. Додайте наступні секрети:
   - `CLOUDFLARE_API_TOKEN`: API токен Cloudflare
   - `CLOUDFLARE_ACCOUNT_ID`: ID облікового запису Cloudflare
   - `NEXT_PUBLIC_APP_URL`: URL вашого додатку
   - `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`: Публічний ключ Stripe

## Налаштування домену

Налаштуйте власний домен для вашого маркетплейсу.

### Додавання домену в Cloudflare

1. Перейдіть до розділу "Websites" > "Add a Site"
2. Введіть ваш домен та дотримуйтесь інструкцій для налаштування DNS

### Налаштування домену для Pages

1. Перейдіть до розділу "Pages" > ваш проект > "Settings" > "Custom domains"
2. Натисніть "Set up a custom domain"
3. Введіть ваш домен (наприклад, `3d-marketplace.com`)
4. Дотримуйтесь інструкцій для підтвердження власності домену

### Налаштування SSL/TLS

1. Перейдіть до розділу "SSL/TLS" > "Edge Certificates"
2. Переконайтеся, що для вашого домену увімкнено "Always Use HTTPS"
3. Налаштуйте рівень шифрування SSL/TLS на "Full (strict)"

## Моніторинг та аналітика

Налаштуйте моніторинг та аналітику для відстеження продуктивності та використання вашого маркетплейсу.

### Cloudflare Analytics

1. Перейдіть до розділу "Analytics" > "Web Analytics"
2. Налаштуйте Web Analytics для вашого домену

### Інтеграція з Google Analytics

Додайте Google Analytics до вашого додатку:

1. Створіть обліковий запис Google Analytics
2. Отримайте ID відстеження
3. Додайте скрипт Google Analytics до вашого додатку

```javascript
// src/app/layout.tsx
import Script from 'next/script';

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <Script
          src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
          `}
        </Script>
      </head>
      <body>{children}</body>
    </html>
  );
}
```

## Оптимізація продуктивності

Оптимізуйте продуктивність вашого маркетплейсу для кращого користувацького досвіду.

### Кешування

Налаштуйте кешування для статичних файлів та API відповідей:

1. Перейдіть до розділу "Caching" > "Configuration"
2. Налаштуйте правила кешування для різних типів контенту

### Оптимізація зображень

Використовуйте Cloudflare Image Resizing для оптимізації зображень:

```javascript
// Приклад URL для оптимізованого зображення
const optimizedImageUrl = `https://your-domain.com/cdn-cgi/image/width=500,quality=80,format=auto/${imageUrl}`;
```

### Оптимізація 3D-моделей

Використовуйте компресію та прогресивне завантаження для 3D-моделей:

1. Конвертуйте моделі в оптимізовані формати (glTF/GLB)
2. Використовуйте рівні деталізації (LOD) для великих моделей
3. Застосовуйте компресію текстур (KTX2, DDS)

## Безпека

Забезпечте безпеку вашого маркетплейсу від різних загроз.

### Налаштування WAF (Web Application Firewall)

1. Перейдіть до розділу "Security" > "WAF"
2. Увімкніть та налаштуйте правила WAF для захисту від поширених атак

### Захист від DDoS-атак

Cloudflare надає вбудований захист від DDoS-атак:

1. Перейдіть до розділу "Security" > "DDoS"
2. Переконайтеся, що захист від DDoS-атак увімкнено

### Налаштування CSP (Content Security Policy)

Додайте заголовки CSP для захисту від XSS-атак:

```javascript
// next.config.js
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: `
              default-src 'self';
              script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com https://www.googletagmanager.com;
              style-src 'self' 'unsafe-inline';
              img-src 'self' data: https://*.r2.cloudflarestorage.com https://www.googletagmanager.com;
              font-src 'self';
              connect-src 'self' https://api.stripe.com https://www.google-analytics.com;
              frame-src 'self' https://js.stripe.com;
              object-src 'none';
            `.replace(/\s{2,}/g, ' ').trim(),
          },
        ],
      },
    ];
  },
};
```

## Вирішення проблем

### Поширені проблеми та їх вирішення

1. **Помилка 502 Bad Gateway**
   - Перевірте логи функцій Pages
   - Переконайтеся, що всі необхідні змінні середовища налаштовані
   - Перевірте, чи не перевищено ліміти часу виконання

2. **Помилка при завантаженні файлів**
   - Перевірте налаштування CORS для бакету R2
   - Переконайтеся, що Worker має доступ до бакету R2
   - Перевірте розмір файлу (максимальний розмір для R2 - 5GB)

3. **Проблеми з базою даних**
   - Перевірте підключення до бази даних
   - Переконайтеся, що схема бази даних коректна
   - Перевірте запити на наявність синтаксичних помилок

### Перегляд логів

Для діагностики проблем використовуйте логи Cloudflare:

1. Перейдіть до розділу "Workers & Pages" > ваш проект > "Logs"
2. Перегляньте логи для виявлення помилок

### Зв'язок з підтримкою Cloudflare

Якщо ви не можете вирішити проблему самостійно, зверніться до підтримки Cloudflare:

1. Перейдіть до розділу "Support" > "Create a Support Case"
2. Опишіть проблему та надайте всю необхідну інформацію
