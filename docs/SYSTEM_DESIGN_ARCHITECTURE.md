# System Design & Architecture Documentation

## Overview

This document outlines the comprehensive system architecture for the 3D Marketplace platform, including the web scraping integration, 3D visualization components, and external platform integrations.

## High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Next.js App Router]
        B[React Components]
        C[3D Visualization]
        D[UI Components]
    end
    
    subgraph "API Layer"
        E[Next.js API Routes]
        F[Scraping APIs]
        G[Model APIs]
        H[Auth APIs]
    end
    
    subgraph "Business Logic"
        I[Scraping Engine]
        J[Data Normalizer]
        K[License Detector]
        L[Rate Limiter]
    end
    
    subgraph "Data Layer"
        M[Supabase/PostgreSQL]
        N[Cloudflare R2]
        O[Redis Cache]
        P[Search Index]
    end
    
    subgraph "External Services"
        Q[Printables.com]
        R[MakerWorld.com]
        S[Thangs.com]
        T[Stripe Payments]
    end
    
    A --> E
    B --> F
    C --> G
    D --> H
    E --> I
    F --> J
    G --> K
    H --> L
    I --> M
    J --> N
    K --> O
    L --> P
    I --> Q
    I --> R
    I --> S
    G --> T
```

## Core Components

### 1. Frontend Architecture

#### Technology Stack
- **Framework**: Next.js 15 with App Router
- **UI Library**: React 19
- **Styling**: Tailwind CSS + Shadcn/ui
- **3D Rendering**: Three.js + React Three Fiber + Spline
- **State Management**: Zustand
- **Forms**: React Hook Form + Zod validation

#### Component Structure
```
src/
├── app/                    # Next.js App Router pages
│   ├── marketplace/        # Marketplace pages
│   ├── models/            # Model detail pages
│   └── api/               # API routes
├── components/            # Reusable components
│   ├── ui/                # Base UI components
│   ├── 3d-viewer/         # 3D visualization
│   ├── marketplace/       # Marketplace-specific
│   └── auth/              # Authentication
└── lib/                   # Utilities and services
    ├── api/               # API clients
    ├── scraping/          # Scraping utilities
    └── utils/             # Helper functions
```

### 2. API Architecture

#### RESTful API Design
- **Base URL**: `/api/v1/`
- **Authentication**: NextAuth.js with JWT
- **Rate Limiting**: Platform-specific limits
- **Caching**: Redis for frequently accessed data

#### Key Endpoints
```
/api/models/               # Model CRUD operations
/api/scraping/             # Web scraping operations
/api/auth/                 # Authentication
/api/upload/               # File uploads
/api/search/               # Search functionality
```

### 3. Web Scraping Engine

#### Architecture Components

```typescript
interface ScrapingEngine {
  // Platform-specific scrapers
  scrapers: {
    printables: PrintablesScraper;
    makerworld: MakerWorldScraper;
    thangs: ThangsScraper;
  };
  
  // Core services
  rateLimiter: RateLimiter;
  cache: CacheService;
  normalizer: DataNormalizer;
  licenseDetector: LicenseDetector;
  
  // Queue management
  queue: JobQueue;
  scheduler: TaskScheduler;
}
```

#### Data Flow
1. **URL Validation** → Platform detection and URL format validation
2. **Rate Limiting** → Check and enforce platform-specific limits
3. **Cache Check** → Look for recently scraped data
4. **Web Scraping** → Extract data using platform-specific logic
5. **Data Normalization** → Convert to internal model format
6. **License Detection** → Parse and validate licensing information
7. **Storage** → Save to database with proper attribution
8. **Indexing** → Update search index for discoverability

### 4. Data Models

#### Core Model Schema
```typescript
interface Model {
  // Identity
  id: string;
  title: string;
  description: string;
  
  // Media
  thumbnail: string;
  images: string[];
  
  // Source tracking
  source: 'local' | 'printables' | 'makerworld' | 'thangs';
  externalSource?: {
    platform: string;
    originalId: string;
    originalUrl: string;
    importedAt: string;
    lastSyncAt?: string;
  };
  
  // Licensing
  license?: {
    type: LicenseType;
    name: string;
    url?: string;
    allowCommercialUse: boolean;
    requireAttribution: boolean;
    allowDerivatives: boolean;
  };
  
  // Designer information
  designer: {
    id: string;
    name: string;
    avatar?: string;
  };
  
  // File information
  files: ModelFile[];
  fileFormats: string[];
  fileSize: string;
  
  // Metadata
  category: string;
  tags: string[];
  printSettings: PrintSettings;
  
  // Statistics
  likes: number;
  downloads: number;
  views: number;
  
  // Pricing
  price: number;
  isFree: boolean;
  originalPrice?: number;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
}
```

### 5. Database Design

#### Primary Tables
```sql
-- Models table
CREATE TABLE models (
  id UUID PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  thumbnail_url TEXT,
  source VARCHAR(50) NOT NULL,
  external_source JSONB,
  license JSONB,
  designer_id UUID REFERENCES users(id),
  category_id UUID REFERENCES categories(id),
  price DECIMAL(10,2) DEFAULT 0,
  is_free BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- External sources tracking
CREATE TABLE external_sources (
  id UUID PRIMARY KEY,
  model_id UUID REFERENCES models(id),
  platform VARCHAR(50) NOT NULL,
  original_id VARCHAR(255) NOT NULL,
  original_url TEXT NOT NULL,
  imported_at TIMESTAMP DEFAULT NOW(),
  last_sync_at TIMESTAMP,
  sync_status VARCHAR(50) DEFAULT 'active'
);

-- Scraping jobs queue
CREATE TABLE scraping_jobs (
  id UUID PRIMARY KEY,
  url TEXT NOT NULL,
  platform VARCHAR(50) NOT NULL,
  status VARCHAR(50) DEFAULT 'pending',
  priority INTEGER DEFAULT 0,
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3,
  result JSONB,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  started_at TIMESTAMP,
  completed_at TIMESTAMP
);
```

### 6. Caching Strategy

#### Multi-Level Caching
```typescript
interface CacheStrategy {
  // Browser cache
  browser: {
    staticAssets: '1 year';
    apiResponses: '5 minutes';
    images: '1 week';
  };
  
  // CDN cache (Cloudflare)
  cdn: {
    staticContent: '1 month';
    apiResponses: '1 hour';
    images: '1 week';
  };
  
  // Application cache (Redis)
  application: {
    scrapedData: '24 hours';
    searchResults: '1 hour';
    userSessions: '7 days';
    rateLimitCounters: '1 hour';
  };
  
  // Database query cache
  database: {
    popularModels: '30 minutes';
    categories: '1 hour';
    userProfiles: '15 minutes';
  };
}
```

### 7. Security Architecture

#### Authentication & Authorization
- **Authentication**: NextAuth.js with multiple providers
- **Authorization**: Role-based access control (RBAC)
- **API Security**: JWT tokens with refresh mechanism
- **Rate Limiting**: Platform-specific and user-based limits

#### Data Protection
- **Input Validation**: Zod schemas for all inputs
- **SQL Injection**: Parameterized queries only
- **XSS Protection**: Content Security Policy (CSP)
- **CSRF Protection**: Built-in Next.js protection

#### Scraping Ethics
- **Robots.txt Compliance**: Respect platform guidelines
- **Rate Limiting**: Conservative request rates
- **User-Agent**: Proper identification
- **Attribution**: Maintain source links and credits

### 8. Performance Optimization

#### Frontend Optimization
- **Code Splitting**: Route-based and component-based
- **Image Optimization**: Next.js Image component with WebP
- **3D Model Loading**: Progressive loading with LOD
- **Bundle Analysis**: Regular bundle size monitoring

#### Backend Optimization
- **Database Indexing**: Optimized queries for search and filtering
- **Connection Pooling**: Efficient database connections
- **Background Jobs**: Async processing for heavy operations
- **CDN Integration**: Global content delivery

#### Scraping Optimization
- **Concurrent Processing**: Parallel scraping with limits
- **Smart Caching**: Avoid redundant requests
- **Error Recovery**: Exponential backoff and retry logic
- **Resource Management**: Memory and CPU optimization

### 9. Monitoring & Observability

#### Application Monitoring
- **Performance Metrics**: Response times, throughput
- **Error Tracking**: Comprehensive error logging
- **User Analytics**: Usage patterns and behavior
- **Business Metrics**: Import success rates, user engagement

#### Scraping Monitoring
- **Platform Health**: Response times and availability
- **Success Rates**: Import success/failure ratios
- **Rate Limit Tracking**: Usage against limits
- **Data Quality**: Validation and completeness metrics

### 10. Deployment Architecture

#### Cloudflare Integration
```yaml
Production Stack:
  Frontend: Cloudflare Pages
  API: Next.js API Routes on Cloudflare
  Database: Supabase PostgreSQL
  Storage: Cloudflare R2
  Cache: Cloudflare KV + Redis
  CDN: Cloudflare CDN
  Analytics: Cloudflare Analytics
```

#### Development Environment
```yaml
Development Stack:
  Frontend: Next.js dev server
  API: Local Next.js API routes
  Database: Local PostgreSQL
  Storage: Local filesystem
  Cache: Local Redis
```

### 11. Scalability Considerations

#### Horizontal Scaling
- **Stateless Design**: No server-side session storage
- **Database Sharding**: Partition by platform or date
- **Microservices**: Separate scraping service if needed
- **Load Balancing**: Multiple API instances

#### Vertical Scaling
- **Resource Optimization**: Efficient memory usage
- **Database Optimization**: Query performance tuning
- **Caching Strategy**: Reduce database load
- **Background Processing**: Async job queues

### 12. Future Architecture Enhancements

#### Planned Improvements
1. **Real-time Sync**: WebSocket connections for live updates
2. **AI Integration**: Machine learning for model categorization
3. **Advanced Search**: Elasticsearch for complex queries
4. **Mobile Apps**: React Native applications
5. **API Gateway**: Centralized API management
6. **Event Sourcing**: Audit trail and data versioning

#### Technology Roadmap
- **Q1 2024**: Enhanced scraping with AI assistance
- **Q2 2024**: Real-time collaboration features
- **Q3 2024**: Mobile application launch
- **Q4 2024**: Advanced analytics and recommendations

## Conclusion

This architecture provides a robust, scalable foundation for the 3D Marketplace platform with comprehensive web scraping capabilities. The design emphasizes performance, security, and maintainability while supporting future growth and feature expansion.
