# 3D Marketplace Scraping System

## Overview

The 3D Marketplace Scraping System is a comprehensive solution for importing 3D models from external platforms like Printables, MakerWorld, and Thangs. The system is designed with scalability, reliability, and maintainability in mind.

## Architecture

### Core Components

1. **Base Scraper** (`src/lib/scraping/base-scraper.ts`)
   - Abstract base class for all platform scrapers
   - Provides common functionality like license detection, URL normalization, and error handling
   - Implements retry logic and timeout handling

2. **Platform Scrapers**
   - **Printables Scraper** (`src/lib/api/printables.ts`)
   - **MakerWorld Scraper** (`src/lib/api/makerworld.ts`)
   - **Thangs Scraper** (`src/lib/api/thangs.ts`)

3. **Rate Limiter** (`src/lib/scraping/rate-limiter.ts`)
   - Platform-specific rate limiting
   - Memory-based implementation with configurable windows
   - Prevents API abuse and respects platform limits

4. **Data Normalizer** (`src/lib/scraping/data-normalizer.ts`)
   - Validates scraped data
   - Sanitizes content (XSS protection)
   - Converts to internal model format

5. **Batch Processor** (`src/lib/scraping/batch-processor.ts`)
   - Handles multiple URL imports
   - Parallel processing with concurrency control
   - Progress tracking and error reporting

## API Endpoints

### Import Single Model
```
POST /api/scraping/import
```

**Request Body:**
```json
{
  "url": "https://www.printables.com/model/123456",
  "options": {
    "includeFiles": true,
    "includeImages": true,
    "validateLicense": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "modelId": "printables_123456_1234567890",
    "status": "imported",
    "model": {
      "title": "Example Model",
      "designer": "Designer Name",
      // ... other model data
    }
  }
}
```

### Batch Import
```
POST /api/scraping/batch
```

**Request Body:**
```json
{
  "urls": [
    "https://www.printables.com/model/123456",
    "https://makerworld.com/en/models/789012"
  ],
  "options": {
    "parallel": 3,
    "retryFailed": true,
    "includeFiles": true,
    "includeImages": true
  }
}
```

### Health Check
```
GET /api/scraping/health
```

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "operational",
    "platforms": {
      "printables": {
        "status": "operational",
        "responseTime": 250,
        "rateLimitRemaining": 8
      },
      "makerworld": {
        "status": "operational", 
        "responseTime": 180,
        "rateLimitRemaining": 12
      },
      "thangs": {
        "status": "degraded",
        "responseTime": 1200,
        "rateLimitRemaining": 5
      }
    }
  }
}
```

## Supported Platforms

### Printables (www.printables.com)
- **Rate Limit:** 10 requests/minute
- **URL Format:** `https://www.printables.com/model/{id}`
- **Features:** Full model data, files, images, license detection

### MakerWorld (makerworld.com)
- **Rate Limit:** 15 requests/minute  
- **URL Format:** `https://makerworld.com/en/models/{id}`
- **Features:** Model data, images, basic file info

### Thangs (thangs.com)
- **Rate Limit:** 12 requests/minute
- **URL Format:** `https://thangs.com/designer/{user}/model/{id}`
- **Features:** Model data, images, designer info

## Usage Examples

### Frontend Components

#### Import Single Model
```tsx
import { ImportModelDialog } from '@/components/marketplace/import-model-dialog'

<ImportModelDialog
  onModelImported={(model) => {
    console.log('Model imported:', model)
  }}
/>
```

#### Batch Import
```tsx
import { BatchImportDialog } from '@/components/marketplace/batch-import-dialog'

<BatchImportDialog
  onBatchCompleted={(results) => {
    console.log('Batch completed:', results)
  }}
/>
```

#### Health Status
```tsx
import { ScrapingHealthStatus } from '@/components/marketplace/scraping-health-status'

<ScrapingHealthStatus />
```

### Direct API Usage

```typescript
// Import single model
const response = await fetch('/api/scraping/import', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    url: 'https://www.printables.com/model/123456',
    options: { includeFiles: true }
  })
})

const result = await response.json()
```

## Error Handling

### Error Types

1. **INVALID_URL** - URL format is incorrect
2. **UNSUPPORTED_PLATFORM** - Platform not supported
3. **RATE_LIMIT_EXCEEDED** - Too many requests
4. **MODEL_NOT_FOUND** - Model doesn't exist
5. **SCRAPING_FAILED** - General scraping error
6. **INVALID_SCRAPED_DATA** - Data validation failed

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Try again in 30 seconds.",
    "details": {
      "retryAfter": 30,
      "platform": "printables"
    }
  }
}
```

## Rate Limiting

### Platform Limits
- **Printables:** 10 requests/minute
- **MakerWorld:** 15 requests/minute  
- **Thangs:** 12 requests/minute

### Implementation
- Memory-based rate limiter
- Per-platform tracking
- Automatic reset after time window
- Graceful error handling

## Data Validation & Sanitization

### Validation Rules
- Title: Required, 1-200 characters
- Description: Optional, max 5000 characters
- Designer: Required name
- Images: Valid URLs only
- Files: Valid formats and sizes

### Sanitization
- HTML content sanitized
- XSS protection
- URL validation
- File type validation

## Testing

### Unit Tests
```bash
npm run test:scraping
```

### Integration Tests
```bash
npm run test:e2e
```

### Coverage Report
```bash
npm run test:coverage
```

## Configuration

### Environment Variables
```env
# Rate limiting
RATE_LIMIT_PRINTABLES=10
RATE_LIMIT_MAKERWORLD=15
RATE_LIMIT_THANGS=12

# Scraping settings
SCRAPING_TIMEOUT=30000
SCRAPING_RETRY_ATTEMPTS=3
SCRAPING_RETRY_DELAY=1000
SCRAPING_USER_AGENT="3D-Marketplace-Bot/1.0"

# Platform URLs
PRINTABLES_BASE_URL=https://www.printables.com
MAKERWORLD_BASE_URL=https://makerworld.com
THANGS_BASE_URL=https://thangs.com
```

## Monitoring & Observability

### Health Checks
- Platform availability
- Response times
- Rate limit status
- Error rates

### Metrics
- Import success/failure rates
- Processing times
- Platform-specific statistics
- User activity

## Security Considerations

1. **Rate Limiting** - Prevents abuse
2. **Input Validation** - Validates all URLs and data
3. **Content Sanitization** - Prevents XSS attacks
4. **Error Handling** - No sensitive data in errors
5. **User Agent** - Identifies our bot properly

## Future Enhancements

1. **Additional Platforms**
   - Thingiverse
   - MyMiniFactory
   - Cults3D

2. **Advanced Features**
   - Scheduled imports
   - Webhook notifications
   - Advanced filtering
   - Bulk operations

3. **Performance Improvements**
   - Redis-based rate limiting
   - Caching layer
   - Queue system
   - Load balancing

## Troubleshooting

### Common Issues

1. **Rate Limit Exceeded**
   - Wait for rate limit reset
   - Check platform-specific limits
   - Consider implementing backoff

2. **Model Not Found**
   - Verify URL format
   - Check if model is public
   - Confirm platform availability

3. **Scraping Failed**
   - Check network connectivity
   - Verify platform changes
   - Review error logs

### Debug Mode
Set `DEBUG=scraping:*` to enable detailed logging.

## Contributing

1. Follow existing code patterns
2. Add tests for new features
3. Update documentation
4. Test with real URLs (carefully)
5. Consider rate limits during development
