# Web Scraping Integration Documentation

## Overview

This document describes the comprehensive web scraping system implemented in the 3D Marketplace for importing models from external platforms. The system supports multiple 3D model platforms and provides a unified interface for data extraction and normalization.

## Architecture

### System Components

```mermaid
graph TD
    A[Frontend Import UI] --> B[API Gateway]
    B --> C[Platform Router]
    C --> D[Printables Scraper]
    C --> E[MakerWorld Scraper]
    C --> F[Thangs Scraper]
    D --> G[Data Normalizer]
    E --> G
    F --> G
    G --> H[License Detector]
    H --> I[Model Storage]
    I --> J[Search Index]
```

### Core Technologies

- **Cheerio**: HTML parsing and DOM manipulation
- **Axios**: HTTP client with retry mechanisms
- **Puppeteer**: Dynamic content scraping (when needed)
- **Rate Limiter**: Request throttling and queue management
- **Cache Layer**: Redis/Memory caching for performance

## Supported Platforms

### 1. Printables.com

**Implementation Status**: ✅ Complete

**Features**:
- Model metadata extraction
- Image gallery scraping
- File information parsing
- License detection
- Designer profile data
- Print statistics

**URL Patterns**:
```
https://www.printables.com/model/{id}-{slug}
https://printables.com/model/{id}-{slug}
```

**Rate Limiting**: 10 requests/minute

**Example Implementation**:
```typescript
// src/lib/api/printables.ts
export async function scrapePrintablesModel(url: string): Promise<ScrapedModel> {
  const modelId = extractModelId(url);
  const response = await axios.get(url, {
    headers: {
      'User-Agent': 'Mozilla/5.0 (compatible; 3DMarketplace/1.0)',
    },
  });
  
  const $ = cheerio.load(response.data);
  
  return {
    title: $('h1.model-title').text().trim(),
    description: $('.model-description').html(),
    images: extractImages($),
    files: extractFiles($),
    license: detectLicense($),
    designer: extractDesigner($),
    stats: extractStats($),
  };
}
```

### 2. MakerWorld.com (Bambu Lab)

**Implementation Status**: ✅ Complete

**Features**:
- Model metadata and descriptions
- High-quality image extraction
- Print settings and parameters
- Material recommendations
- Designer information
- Download statistics

**URL Patterns**:
```
https://makerworld.com/en/models/{id}
https://www.makerworld.com/en/models/{id}
```

**Rate Limiting**: 15 requests/minute

**Special Considerations**:
- Dynamic content loading via JavaScript
- API endpoints for model data
- Authentication for premium models

### 3. Thangs.com

**Implementation Status**: ✅ Complete

**Features**:
- Model metadata extraction
- Image gallery processing
- File format detection
- Designer profile integration
- Community metrics
- Related models

**URL Patterns**:
```
https://thangs.com/designer/{designer}/model/{id}
https://www.thangs.com/designer/{designer}/model/{id}
```

**Rate Limiting**: 12 requests/minute

**Special Features**:
- Advanced search integration
- Collection support
- Premium model detection

## Data Models

### Scraped Model Interface

```typescript
interface ScrapedModel {
  // Basic Information
  title: string;
  description: string;
  summary?: string;
  
  // Media
  images: ScrapedImage[];
  thumbnail: string;
  
  // Files
  files: ScrapedFile[];
  fileFormats: string[];
  totalSize: number;
  
  // Designer
  designer: ScrapedDesigner;
  
  // Metadata
  tags: string[];
  category: string;
  license: ScrapedLicense;
  
  // Statistics
  stats: {
    views: number;
    downloads: number;
    likes: number;
    comments: number;
  };
  
  // Platform-specific
  platform: 'printables' | 'makerworld' | 'thangs';
  originalId: string;
  originalUrl: string;
  scrapedAt: string;
  
  // Print Settings
  printSettings?: {
    material: string;
    layerHeight: string;
    infill: string;
    supports: string;
    printTime: string;
  };
}
```

### License Detection

```typescript
interface ScrapedLicense {
  type: LicenseType;
  name: string;
  url?: string;
  description?: string;
  allowCommercialUse: boolean;
  requireAttribution: boolean;
  allowDerivatives: boolean;
  detected: boolean;
  confidence: number; // 0-1 confidence score
}
```

## API Endpoints

### Import Model

```http
POST /api/scraping/import
Content-Type: application/json

{
  "url": "https://www.printables.com/model/123456-example-model",
  "options": {
    "includeFiles": true,
    "includeImages": true,
    "validateLicense": true
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "modelId": "imported_123456_1234567890",
    "status": "imported",
    "model": {
      "title": "Example Model",
      "platform": "printables",
      "originalUrl": "https://www.printables.com/model/123456-example-model"
    }
  }
}
```

### Platform-Specific Endpoints

```http
POST /api/scraping/printables
POST /api/scraping/makerworld  
POST /api/scraping/thangs
```

### Batch Import

```http
POST /api/scraping/batch
Content-Type: application/json

{
  "urls": [
    "https://www.printables.com/model/123456-model-1",
    "https://makerworld.com/en/models/789012",
    "https://thangs.com/designer/user/model/345678"
  ],
  "options": {
    "parallel": 3,
    "retryFailed": true
  }
}
```

## Error Handling

### Common Error Types

1. **Rate Limit Exceeded**
   - Status: 429
   - Retry after specified time
   - Queue requests for later processing

2. **Model Not Found**
   - Status: 404
   - Invalid URL or private model
   - Return appropriate error message

3. **Parsing Error**
   - Status: 500
   - Website structure changed
   - Log for manual review

4. **License Restriction**
   - Status: 403
   - Model has restrictive license
   - Inform user of limitations

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded for printables.com",
    "details": {
      "platform": "printables",
      "retryAfter": 60,
      "requestsRemaining": 0
    }
  }
}
```

## Rate Limiting & Caching

### Rate Limiting Strategy

```typescript
// Rate limiter configuration
const rateLimiters = {
  printables: new RateLimiter(10, 60000), // 10 requests per minute
  makerworld: new RateLimiter(15, 60000), // 15 requests per minute
  thangs: new RateLimiter(12, 60000),     // 12 requests per minute
};

// Usage
await rateLimiters.printables.acquire();
const result = await scrapePrintablesModel(url);
```

### Caching Strategy

- **Model Data**: 24 hours
- **Images**: 7 days
- **Designer Info**: 1 hour
- **Error Responses**: 5 minutes

## Security & Compliance

### Best Practices

1. **Respectful Scraping**
   - Honor robots.txt
   - Implement proper delays
   - Use appropriate User-Agent

2. **Data Privacy**
   - Only scrape public data
   - Respect user privacy settings
   - Implement data retention policies

3. **Legal Compliance**
   - Respect copyright and licenses
   - Provide proper attribution
   - Link back to original sources

### User-Agent Configuration

```typescript
const USER_AGENTS = {
  default: 'Mozilla/5.0 (compatible; 3DMarketplace/1.0; +https://yoursite.com/bot)',
  printables: 'Mozilla/5.0 (compatible; 3DMarketplace-Printables/1.0)',
  makerworld: 'Mozilla/5.0 (compatible; 3DMarketplace-MakerWorld/1.0)',
  thangs: 'Mozilla/5.0 (compatible; 3DMarketplace-Thangs/1.0)',
};
```

## Monitoring & Analytics

### Metrics Tracked

- Scraping success/failure rates
- Response times per platform
- Rate limit violations
- Popular imported models
- License distribution

### Health Checks

```http
GET /api/scraping/health
```

**Response**:
```json
{
  "status": "healthy",
  "platforms": {
    "printables": {
      "status": "operational",
      "lastCheck": "2024-01-15T10:30:00Z",
      "responseTime": 245
    },
    "makerworld": {
      "status": "operational", 
      "lastCheck": "2024-01-15T10:30:00Z",
      "responseTime": 312
    },
    "thangs": {
      "status": "degraded",
      "lastCheck": "2024-01-15T10:30:00Z", 
      "responseTime": 1205
    }
  }
}
```

## Future Enhancements

### Planned Features

1. **Real-time Sync**: Webhook-based updates when available
2. **AI-Enhanced Parsing**: Machine learning for better data extraction
3. **Bulk Operations**: Import entire collections or user profiles
4. **Advanced Filtering**: Smart categorization and tagging
5. **Quality Scoring**: Automatic model quality assessment

### Additional Platforms

- Thingiverse.com (API integration)
- MyMiniFactory.com (Premium support)
- Cults3D.com (Marketplace integration)
- GrabCAD.com (Professional models)

## Troubleshooting

### Common Issues

1. **Scraping Failures**: Check platform status and rate limits
2. **Missing Data**: Verify selectors and page structure
3. **License Detection**: Review license parsing logic
4. **Performance Issues**: Monitor cache hit rates and response times

### Debug Mode

Enable debug logging:
```typescript
process.env.SCRAPING_DEBUG = 'true';
```

This provides detailed logs of:
- HTTP requests and responses
- DOM parsing steps
- Data extraction results
- Error stack traces
