# Міграція з Supabase на Cloudflare

Цей документ описує процес міграції проекту 3D-маркетплейсу з Supabase на Cloudflare для бекенду та бази даних.

## Огляд

Міграція включає наступні компоненти:

1. **База даних**: Міграція з Supabase PostgreSQL на Cloudflare D1 (SQLite)
2. **Аутентифікація**: Міграція з Supabase Auth на NextAuth.js з адаптером для D1
3. **Зберігання файлів**: Міграція з Supabase Storage на Cloudflare R2
4. **API**: Оновлення API маршрутів для роботи з Cloudflare

## Передумови

Перед початком міграції переконайтеся, що у вас є:

- Обліковий запис Cloudflare
- Встановлений Wrangler CLI (`npm install -g wrangler`)
- Авторизація в Wrangler CLI (`wrangler login`)

## Кроки міграції

### 1. Налаштування Cloudflare D1

1. **Створення бази даних D1**:

```bash
wrangler d1 create marketplace_db
```

2. **Оновлення wrangler.toml**:

Додайте налаштування D1 до файлу `wrangler.toml`:

```toml
[[d1_databases]]
binding = "DB"
database_name = "marketplace_db"
database_id = "your-database-id" # Замініть на ID вашої бази даних
```

3. **Застосування схеми бази даних**:

```bash
wrangler d1 execute marketplace_db --file=schema.sql
```

### 2. Налаштування Cloudflare R2

1. **Створення бакета R2**:

```bash
wrangler r2 bucket create marketplace-storage
```

2. **Оновлення wrangler.toml**:

Додайте налаштування R2 до файлу `wrangler.toml`:

```toml
[[r2_buckets]]
binding = "STORAGE"
bucket_name = "marketplace-storage"
```

3. **Налаштування публічного доступу до R2**:

```bash
wrangler r2 bucket create-public-access-policy marketplace-storage --policy-name=public-access
```

### 3. Міграція даних

#### Міграція користувачів

1. **Експорт користувачів з Supabase**:

```sql
-- Виконайте в SQL-редакторі Supabase
COPY (
  SELECT id, email, raw_user_meta_data->>'name' as name, 
         raw_user_meta_data->>'avatar_url' as avatar_url,
         created_at, updated_at
  FROM auth.users
) TO '/tmp/users.csv' WITH CSV HEADER;
```

2. **Імпорт користувачів у D1**:

```bash
wrangler d1 execute marketplace_db --file=users_import.sql
```

#### Міграція моделей та інших даних

Аналогічно експортуйте та імпортуйте інші таблиці з Supabase в D1.

#### Міграція файлів

1. **Завантаження файлів з Supabase Storage**:

```bash
# Використовуйте Supabase CLI для завантаження файлів
supabase storage download --bucket=models --folder=/ ./downloaded_files
```

2. **Завантаження файлів у Cloudflare R2**:

```bash
# Використовуйте AWS CLI з налаштуваннями R2
aws s3 cp ./downloaded_files s3://marketplace-storage/ --recursive --endpoint-url https://your-account-id.r2.cloudflarestorage.com
```

### 4. Оновлення змінних середовища

Оновіть файл `.env.local` з новими змінними середовища для Cloudflare:

```
# Видаліть змінні Supabase
# NEXT_PUBLIC_SUPABASE_URL=...
# NEXT_PUBLIC_SUPABASE_ANON_KEY=...
# SUPABASE_SERVICE_ROLE_KEY=...

# Додайте змінні для NextAuth.js
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-nextauth-secret

# Додайте змінні для Cloudflare R2
NEXT_PUBLIC_R2_URL=https://your-r2-public-url.com
```

### 5. Розгортання на Cloudflare Pages

1. **Налаштування проекту в Cloudflare Pages**:

```bash
# Ініціалізація проекту
wrangler pages project create 3d-marketplace

# Налаштування змінних середовища
wrangler pages deployment create --project-name=3d-marketplace --env-vars="CLOUDFLARE=true,NODE_VERSION=18,NPM_VERSION=9"
```

2. **Розгортання проекту**:

```bash
# Збірка проекту
npm run build

# Розгортання
wrangler pages deploy .next
```

## Перевірка міграції

Після завершення міграції перевірте наступне:

1. **Аутентифікація**: Реєстрація та вхід користувачів
2. **API**: Всі API маршрути працюють коректно
3. **Зберігання файлів**: Завантаження та отримання файлів
4. **Продуктивність**: Порівняйте швидкість роботи з Supabase та Cloudflare

## Відкат міграції

У разі проблем з міграцією, ви можете повернутися до Supabase:

1. Відновіть попередні змінні середовища
2. Відновіть попередні версії файлів з системи контролю версій
3. Розгорніть проект з попередніми налаштуваннями

## Додаткові ресурси

- [Документація Cloudflare D1](https://developers.cloudflare.com/d1/)
- [Документація Cloudflare R2](https://developers.cloudflare.com/r2/)
- [Документація Cloudflare Pages](https://developers.cloudflare.com/pages/)
- [Документація NextAuth.js](https://next-auth.js.org/)
