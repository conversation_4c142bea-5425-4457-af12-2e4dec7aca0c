# Deployment Guide

This guide provides instructions for deploying the 3D Printing Marketplace application to various environments.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Environment Setup](#environment-setup)
- [Deployment Options](#deployment-options)
  - [Vercel (Recommended)](#vercel-recommended)
  - [Cloudflare Pages](#cloudflare-pages)
  - [AWS Amplify](#aws-amplify)
  - [Self-Hosted](#self-hosted)
- [Database Deployment](#database-deployment)
- [Continuous Integration/Continuous Deployment](#continuous-integrationcontinuous-deployment)
- [Post-Deployment Verification](#post-deployment-verification)
- [Troubleshooting](#troubleshooting)

## Prerequisites

Before deploying, ensure you have:

- A complete and tested application
- All environment variables documented
- Database migration scripts ready
- Access to your chosen deployment platform

## Environment Setup

1. **Prepare environment variables**

   Create a production-ready `.env` file based on your `.env.example` with all the necessary variables for your deployment environment.

2. **Build the application**

   ```bash
   npm run build
   ```

   This will create an optimized production build in the `.next` directory.

3. **Test the production build locally**

   ```bash
   npm start
   ```

   Verify that everything works as expected with the production build.

## Deployment Options

### Vercel (Recommended)

[Vercel](https://vercel.com/) is the recommended deployment platform for Next.js applications.

1. **Install Vercel CLI** (optional)

   ```bash
   npm install -g vercel
   ```

2. **Deploy using Vercel Dashboard**

   - Push your code to GitHub, GitLab, or Bitbucket
   - Import your repository in the Vercel dashboard
   - Configure your project settings:
     - Set the framework preset to Next.js
     - Add all required environment variables
     - Configure build settings if needed

3. **Deploy using Vercel CLI**

   ```bash
   vercel
   ```

   Follow the prompts to configure your deployment.

4. **Set up custom domain** (optional)

   - In the Vercel dashboard, go to your project settings
   - Navigate to the "Domains" section
   - Add your custom domain and follow the instructions for DNS configuration

### Cloudflare Pages

[Cloudflare Pages](https://pages.cloudflare.com/) is a JAMstack platform for frontend developers to collaborate and deploy websites.

1. **Create a Cloudflare account**

   - Sign up at [Cloudflare.com](https://www.cloudflare.com/) if you don't have an account
   - Navigate to the Pages section in your dashboard

2. **Connect your Git repository**

   - Click "Create a project" in the Cloudflare Pages dashboard
   - Connect to your GitHub or GitLab account
   - Select your 3D Marketplace repository

3. **Configure build settings**

   - Set build command: `npm run build`
   - Set build output directory: `.next`
   - Select the appropriate Node.js version (18.x or later)
   - Add the following environment variables:
     - `NODE_VERSION`: `18` (or your preferred Node.js version)
     - `NPM_VERSION`: `9` (or your preferred npm version)

4. **Configure advanced build settings**

   - In the "Advanced" section, add the following environment variables:
     - `NEXT_PUBLIC_BASE_URL`: Your production URL
     - `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase URL
     - `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key
     - `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`: Your Stripe publishable key
     - Add all other required environment variables from your `.env.example` file

5. **Deploy your site**

   - Click "Save and Deploy"
   - Wait for the build and deployment process to complete

6. **Set up custom domain** (optional)

   - In your project's settings, go to "Custom domains"
   - Click "Set up a custom domain"
   - Enter your domain name and follow the instructions
   - Cloudflare will automatically provision an SSL certificate

7. **Configure Cloudflare for optimal performance**

   - Enable Cloudflare's CDN and caching features
   - Set up appropriate caching rules for static assets
   - Consider enabling Cloudflare's image optimization for better performance

8. **Set up Cloudflare Workers** (optional)

   If you need server-side functionality beyond what Next.js API routes provide:

   ```js
   // Example Cloudflare Worker for API functionality
   addEventListener('fetch', event => {
     event.respondWith(handleRequest(event.request))
   })

   async function handleRequest(request) {
     // Your custom API logic here
     return new Response('Hello from Cloudflare Workers!', {
       headers: { 'content-type': 'text/plain' },
     })
   }
   ```

### AWS Amplify

AWS Amplify provides a complete solution for deploying and hosting Next.js applications.

1. **Install and configure AWS Amplify CLI**

   ```bash
   npm install -g @aws-amplify/cli
   amplify configure
   ```

2. **Initialize Amplify in your project**

   ```bash
   amplify init
   ```

3. **Add hosting**

   ```bash
   amplify add hosting
   ```

4. **Deploy the application**

   ```bash
   amplify publish
   ```

5. **Configure environment variables**

   - Go to the AWS Amplify Console
   - Select your app
   - Go to "Environment Variables"
   - Add all required environment variables

### Self-Hosted

For self-hosted deployments on your own server:

1. **Set up a server** with Node.js installed (v18.0.0 or later)

2. **Clone your repository**

   ```bash
   git clone https://github.com/yourusername/3d-marketplace.git
   cd 3d-marketplace
   ```

3. **Install dependencies**

   ```bash
   npm install --production
   ```

4. **Build the application**

   ```bash
   npm run build
   ```

5. **Set up environment variables**

   Create a `.env.production.local` file with all required variables.

6. **Start the application**

   For a simple setup:

   ```bash
   npm start
   ```

   For production with process management:

   ```bash
   # Install PM2
   npm install -g pm2

   # Start with PM2
   pm2 start npm --name "3d-marketplace" -- start
   ```

7. **Set up a reverse proxy** (recommended)

   Configure Nginx or Apache as a reverse proxy to your Node.js application.

   Example Nginx configuration:

   ```nginx
   server {
       listen 80;
       server_name yourdomain.com;

       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

8. **Set up SSL** (recommended)

   Use Let's Encrypt to set up free SSL certificates:

   ```bash
   sudo certbot --nginx -d yourdomain.com
   ```

## Database Deployment

Since we're using Supabase, the database is already hosted in the cloud. Ensure your production environment is configured with the correct Supabase credentials.

If you need to migrate your database schema:

1. **Apply migrations**

   ```bash
   npm run db:migrate
   ```

2. **Verify database schema**

   Check that all tables and relationships are correctly set up in your production Supabase instance.

## Continuous Integration/Continuous Deployment

Set up CI/CD for automated testing and deployment:

### GitHub Actions

#### For Vercel Deployment

1. Create a `.github/workflows/vercel.yml` file:

```yaml
name: CI/CD for Vercel

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run lint
      - run: npm test

  deploy:
    needs: test
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
```

1. Set up the required secrets in your GitHub repository settings.

#### For Cloudflare Pages Deployment

Cloudflare Pages has built-in CI/CD that automatically deploys your site when you push to your repository. However, you can also set up a custom GitHub Actions workflow for more control:

1. Create a `.github/workflows/cloudflare.yml` file:

```yaml
name: CI/CD for Cloudflare Pages

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run lint
      - run: npm test

  deploy:
    needs: test
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: 3d-marketplace
          directory: .next
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
```

1. Set up the required secrets in your GitHub repository settings:
   - `CLOUDFLARE_API_TOKEN`: Your Cloudflare API token with Pages permissions
   - `CLOUDFLARE_ACCOUNT_ID`: Your Cloudflare account ID

## Post-Deployment Verification

After deployment, verify that:

1. **The application loads** correctly at your domain
2. **Authentication works** for signing up and logging in
3. **Database connections** are functioning properly
4. **3D model viewer** loads and displays models correctly
5. **Payment processing** works end-to-end
6. **API endpoints** are accessible and returning expected responses
7. **Cloudflare-specific checks** (if using Cloudflare Pages):
   - CDN caching is working properly
   - SSL/TLS is correctly configured
   - Image optimization is functioning
   - Workers are executing as expected (if applicable)
   - Analytics are being collected

## Troubleshooting

### Common Deployment Issues

1. **Environment variable problems**
   - Check that all required environment variables are set correctly
   - Verify that the environment variables are accessible to your application

2. **Build failures**
   - Check build logs for errors
   - Ensure all dependencies are correctly installed
   - Verify that your code doesn't have any platform-specific dependencies

3. **Database connection issues**
   - Check that your database credentials are correct
   - Verify that your server's IP is allowed in the database firewall settings

4. **Performance issues**
   - Enable caching for static assets
   - Consider using a CDN for media files
   - Optimize database queries

5. **Cloudflare-specific issues**
   - **Build failures**: Ensure you've set the correct Node.js version in environment variables
   - **API routes not working**: Check that your Next.js API routes are compatible with Cloudflare Pages
   - **Image optimization issues**: Configure Cloudflare's image optimization or adjust Next.js image configuration
   - **Caching problems**: Adjust Cloudflare's cache settings for your specific needs
   - **Worker conflicts**: If using Cloudflare Workers alongside Pages, ensure they don't conflict

### Getting Help

If you encounter issues not covered here:

1. Check the [GitHub Issues](https://github.com/yourusername/3d-marketplace/issues) for similar problems
2. Join our [Discord community](https://discord.gg/example) for real-time help
3. Create a new issue with detailed information about your deployment problem
