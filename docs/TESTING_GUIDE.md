# 🧪 Testing Guide - ФАЗА 3

## 📋 Огляд

Комплексна система тестування для Enhanced 3D Marketplace з покриттям всіх компонентів, API та UI.

## 🏗️ Архітектура тестування

### Рівні тестування

```
┌─────────────────────────────────────────────────────────────┐
│                    E2E Tests (Playwright)                   │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ Admin Dashboard │ │ Marketplace UI  │ │ Real-time Updates│ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                Integration Tests (Vitest)                   │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   API Routes    │ │ Durable Objects │ │   Components    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Unit Tests (Vitest)                      │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ Business Logic  │ │    Utilities    │ │     Helpers     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🛠️ Інструменти тестування

### Vitest (Unit & Integration)
- **Швидкість**: Blazing fast з Vite
- **TypeScript**: Нативна підтримка
- **Coverage**: V8 coverage provider
- **UI**: Веб-інтерфейс для тестів

### Playwright (E2E)
- **Cross-browser**: Chrome, Firefox, Safari
- **Mobile**: iOS та Android симуляція
- **Visual**: Screenshots та videos
- **Debugging**: Інтерактивний debug mode

### Testing Library (React)
- **User-centric**: Тестування як користувач
- **Accessibility**: A11y-friendly селектори
- **Best practices**: Рекомендовані підходи

## 📁 Структура тестів

```
src/
├── __tests__/
│   ├── setup.ts                    # Глобальні налаштування
│   ├── components/                 # Тести React компонентів
│   │   ├── admin/
│   │   │   ├── DownloadManagerDashboard.test.tsx
│   │   │   └── ObservabilityDashboard.test.tsx
│   │   └── marketplace/
│   │       ├── EnhancedModelCard.test.tsx
│   │       ├── SystemStatusBanner.test.tsx
│   │       └── RealTimeUpdates.test.tsx
│   ├── lib/                        # Тести бізнес-логіки
│   │   ├── bright-data/
│   │   │   └── enhanced-mcp-client.test.ts
│   │   └── cloudflare/
│   │       └── observability.test.ts
│   ├── durable-objects/            # Тести Durable Objects
│   │   ├── model-download-manager.test.ts
│   │   └── scraping-coordinator.test.ts
│   └── integration/                # Інтеграційні тести
│       ├── api-endpoints.test.ts
│       └── full-workflow.test.ts
e2e/
├── global-setup.ts                 # E2E налаштування
├── global-teardown.ts              # E2E завершення
├── enhanced-admin.spec.ts          # E2E тести адмін панелі
└── marketplace-homepage.spec.ts    # E2E тести головної сторінки
```

## 🚀 Запуск тестів

### Unit & Integration тести

```bash
# Всі тести
npm run test

# Watch mode
npm run test:watch

# Coverage
npm run test:coverage

# UI mode
npm run test:ui

# Специфічні категорії
npm run test:components
npm run test:durable-objects
npm run test:bright-data
npm run test:observability
npm run test:integration
```

### E2E тести

```bash
# Всі E2E тести
npm run test:e2e

# UI mode
npm run test:e2e:ui

# Headed mode (з браузером)
npm run test:e2e:headed

# Debug mode
npm run test:e2e:debug

# Звіт
npm run test:e2e:report
```

### CI/CD тести

```bash
# Повний набір для CI
npm run test:ci

# Всі тести разом
npm run test:all
```

## 📊 Coverage цілі

### Мінімальні вимоги
- **Lines**: 80%
- **Functions**: 80%
- **Branches**: 80%
- **Statements**: 80%

### Поточне покриття
```bash
npm run test:coverage
```

## 🧪 Типи тестів

### 1. Unit Tests

**Що тестуємо:**
- Чисті функції
- Утиліти
- Бізнес-логіка
- Валідація

**Приклад:**
```typescript
describe('CloudflareObservability', () => {
  it('повинен записати метрику', async () => {
    const observability = new CloudflareObservability(mockEnv);
    await observability.recordMetric('test_metric', 42);
    
    expect(mockEnv.ANALYTICS.writeDataPoint).toHaveBeenCalled();
  });
});
```

### 2. Component Tests

**Що тестуємо:**
- Рендеринг компонентів
- Взаємодія користувача
- Props та state
- Event handlers

**Приклад:**
```typescript
describe('DownloadManagerDashboard', () => {
  it('повинен відобразити статистику', async () => {
    render(<DownloadManagerDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Всього завдань')).toBeInTheDocument();
    });
  });
});
```

### 3. Integration Tests

**Що тестуємо:**
- API endpoints
- Durable Objects
- Взаємодія компонентів
- Data flow

**Приклад:**
```typescript
describe('/api/models/download-manager', () => {
  it('повинен додати завдання', async () => {
    const response = await POST(mockRequest);
    const data = await response.json();
    
    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
  });
});
```

### 4. E2E Tests

**Що тестуємо:**
- Повні user journeys
- Cross-browser сумісність
- Performance
- Accessibility

**Приклад:**
```typescript
test('повинен відобразити адмін панель', async ({ page }) => {
  await page.goto('/admin/enhanced');
  await expect(page.getByText('Enhanced Admin Dashboard')).toBeVisible();
});
```

## 🔧 Налаштування

### Vitest Configuration

```typescript
// vitest.config.ts
export default defineConfig({
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/__tests__/setup.ts'],
    coverage: {
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  }
});
```

### Playwright Configuration

```typescript
// playwright.config.ts
export default defineConfig({
  testDir: './e2e',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  projects: [
    { name: 'chromium', use: devices['Desktop Chrome'] },
    { name: 'firefox', use: devices['Desktop Firefox'] },
    { name: 'webkit', use: devices['Desktop Safari'] }
  ]
});
```

## 🎯 Best Practices

### Unit Tests
1. **AAA Pattern**: Arrange, Act, Assert
2. **Descriptive names**: Що тестується та очікуваний результат
3. **Single responsibility**: Один тест = одна функціональність
4. **Mock dependencies**: Ізолювати тестований код

### Component Tests
1. **User-centric**: Тестувати як користувач
2. **Accessibility**: Використовувати semantic selectors
3. **Async handling**: Правильно обробляти асинхронні операції
4. **Clean up**: Очищати після кожного тесту

### E2E Tests
1. **Page Object Model**: Структурувати селектори
2. **Wait strategies**: Використовувати правильні очікування
3. **Test isolation**: Кожен тест незалежний
4. **Data management**: Контролювати тестові дані

## 🐛 Debugging

### Vitest Debugging

```bash
# UI mode для інтерактивного debugging
npm run test:ui

# Watch mode з фільтрацією
npm run test:watch -- --testNamePattern="specific test"

# Debug в VS Code
# Додати breakpoint та запустити через VS Code debugger
```

### Playwright Debugging

```bash
# Debug mode з браузером
npm run test:e2e:debug

# Headed mode
npm run test:e2e:headed

# Trace viewer
npx playwright show-trace trace.zip
```

## 📈 Continuous Integration

### GitHub Actions

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test:ci
      - uses: actions/upload-artifact@v3
        with:
          name: test-results
          path: |
            coverage/
            test-results/
            playwright-report/
```

## 🔍 Test Data Management

### Mock Data

```typescript
// Глобальні утиліти в setup.ts
global.testUtils = {
  createMockJob: (overrides = {}) => ({
    id: 'test-job-123',
    modelId: 'test-model-456',
    status: 'pending',
    ...overrides
  }),
  
  createMockEnv: () => ({
    MODEL_DOWNLOAD_MANAGER: { /* mock */ },
    ANALYTICS: { /* mock */ }
  })
};
```

### Fixtures

```typescript
// e2e/fixtures.ts
export const testData = {
  validModel: {
    id: 'test-model',
    title: 'Test Model',
    platform: 'printables'
  },
  
  adminUser: {
    email: '<EMAIL>',
    role: 'admin'
  }
};
```

## 📊 Metrics & Reporting

### Coverage Reports
- **HTML**: `coverage/index.html`
- **JSON**: `coverage/coverage-final.json`
- **LCOV**: `coverage/lcov.info`

### E2E Reports
- **HTML**: `playwright-report/index.html`
- **JSON**: `test-results/results.json`
- **JUnit**: `test-results/results.xml`

### Performance Metrics
- **Test execution time**
- **Coverage percentage**
- **Flaky test detection**
- **Browser performance**

## 🚨 Common Issues

### 1. Async/Await Problems
```typescript
// ❌ Неправильно
test('async test', () => {
  someAsyncFunction(); // Не очікуємо результат
});

// ✅ Правильно
test('async test', async () => {
  await someAsyncFunction();
});
```

### 2. Mock Cleanup
```typescript
// ✅ Очищення після кожного тесту
afterEach(() => {
  vi.clearAllMocks();
});
```

### 3. E2E Timeouts
```typescript
// ✅ Правильні очікування
await page.waitForSelector('[data-testid="component"]', { timeout: 10000 });
```

## 🎓 Learning Resources

### Documentation
- [Vitest Guide](https://vitest.dev/guide/)
- [Playwright Docs](https://playwright.dev/docs/intro)
- [Testing Library](https://testing-library.com/docs/)

### Best Practices
- [Testing Trophy](https://kentcdodds.com/blog/the-testing-trophy-and-testing-classifications)
- [Common Testing Mistakes](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)

---

**Готово!** 🎉 Комплексна система тестування налаштована та готова до використання!
