# 🚀 Cloudflare Environment Setup Guide
## 3D Marketplace Complete Infrastructure Setup

### 📋 Prerequisites Verification

#### ✅ **Step 1: Verify Tools and Authentication**
```bash
# Check Wrangler version (should be 4.16+)
wrangler --version

# Verify authentication
wrangler whoami

# Update Wrangler if needed
npm install -g wrangler@latest
```

#### ✅ **Step 2: Verify Account Access**
```bash
# Check account permissions
wrangler auth list

# Verify account ID matches configuration
# Expected: 6244f6d02d9c7684386c1c849bdeaf56
```

---

### 🗄️ Phase 1: Database Infrastructure Setup

#### **D1 Database Creation**
```bash
# Create production database
wrangler d1 create 3d-marketplace-enhanced --env production

# Create staging database
wrangler d1 create 3d-marketplace-staging --env staging

# Create development database
wrangler d1 create 3d-marketplace-development --env development

# Verify database creation
wrangler d1 list
```

#### **Database Schema Migration**
```bash
# Apply main schema to production
wrangler d1 execute 3d-marketplace-enhanced --env production --file schema.sql

# Apply to staging
wrangler d1 execute 3d-marketplace-staging --env staging --file schema.sql

# Apply to development
wrangler d1 execute 3d-marketplace-development --env development --file schema.sql

# Verify schema applied
wrangler d1 execute 3d-marketplace-enhanced --env production --command "SELECT name FROM sqlite_master WHERE type='table';"
```

---

### 🪣 Phase 2: Storage Verification

#### **R2 Bucket Status** ✅ Already Created
- `3d-marketplace-models-prod` ✅
- `3d-marketplace-models-staging` ✅
- `3d-marketplace-models-dev` ✅

#### **KV Namespace Status** ✅ Already Created
- `CACHE_KV` (768cd42bbee14bce81819a0ef3666930) ✅
- `3d-marketplace-enhanced-cache` (dc0b307ecd034ccd8d44223242adea57) ✅
- `marketplace-kv` (9711cfa19bd04ce4afbd8b28bd051f7b) ✅

---

### 🔧 Phase 3: Environment Variables & Secrets

#### **Required Secrets Setup**
```bash
# Authentication secrets
wrangler secret put NEXTAUTH_SECRET --env production
wrangler secret put NEXTAUTH_URL --env production

# OAuth provider secrets
wrangler secret put GITHUB_CLIENT_ID --env production
wrangler secret put GITHUB_CLIENT_SECRET --env production
wrangler secret put GOOGLE_CLIENT_ID --env production
wrangler secret put GOOGLE_CLIENT_SECRET --env production

# Payment processing
wrangler secret put STRIPE_SECRET_KEY --env production
wrangler secret put STRIPE_WEBHOOK_SECRET --env production

# Email configuration
wrangler secret put EMAIL_SERVER_HOST --env production
wrangler secret put EMAIL_SERVER_PORT --env production
wrangler secret put EMAIL_SERVER_USER --env production
wrangler secret put EMAIL_SERVER_PASSWORD --env production
wrangler secret put EMAIL_FROM --env production

# Bright Data MCP credentials
wrangler secret put BRIGHT_DATA_TOKEN --env production
wrangler secret put BRIGHT_DATA_ZONE --env production
```

#### **Verify Secrets**
```bash
# List all secrets
wrangler secret list --env production
wrangler secret list --env staging
wrangler secret list --env development
```

---

### 🚀 Phase 4: Application Deployment

#### **Build and Deploy Workers**
```bash
# Install dependencies
npm install

# Build the application
npm run build

# Deploy Durable Objects and Workers
wrangler deploy --env production
wrangler deploy --env staging
wrangler deploy --env development

# Verify worker deployment
wrangler list
```

#### **Deploy Pages Application**
```bash
# Build for Pages
npm run pages:build

# Deploy to Pages
wrangler pages deploy .vercel/output/static --project-name=3d-marketplace --env production

# Verify Pages deployment
wrangler pages project list
```

---

### 📊 Phase 5: Analytics & Monitoring Setup

#### **Analytics Engine Configuration**
```bash
# Verify analytics datasets
wrangler analytics list

# Test analytics endpoint
curl "https://3d-marketplace.pages.dev/api/analytics/test"
```

#### **Queue Setup for Background Tasks**
```bash
# Create queues for each environment
wrangler queues create 3d-marketplace-tasks --env production
wrangler queues create 3d-marketplace-tasks-staging --env staging
wrangler queues create 3d-marketplace-tasks-dev --env development

# Verify queue creation
wrangler queues list
```

---

### ⏰ Phase 6: Cron Triggers Activation

#### **Enable Scheduled Tasks**
```bash
# Deploy with cron triggers
wrangler deploy --env production

# Verify cron triggers
wrangler cron list --env production
```

**Configured Cron Jobs:**
- `0 2 * * *` - Daily model scraping (2:00 UTC)
- `0 */6 * * *` - Trending models check (every 6 hours)
- `0 1 * * 0` - Weekly analysis (Sunday 1:00 UTC)
- `0 0 1 * *` - Monthly cleanup (1st day 00:00 UTC)

---

### 🧪 Phase 7: Testing & Verification

#### **API Endpoint Testing**
```bash
# Test core endpoints
curl "https://3d-marketplace.pages.dev/api/health"
curl "https://3d-marketplace.pages.dev/api/models"
curl "https://3d-marketplace.pages.dev/api/analytics/dashboard"

# Test Bright Data integration
curl "https://3d-marketplace.pages.dev/api/scraping/status"
curl "https://3d-marketplace.pages.dev/api/competitors/trending"

# Test real-time features
curl "https://3d-marketplace.pages.dev/api/collaboration/rooms"
```

#### **Database Connection Testing**
```bash
# Test database queries
wrangler d1 execute 3d-marketplace-enhanced --env production --command "SELECT COUNT(*) FROM models;"

# Test R2 storage access
curl "https://3d-marketplace.pages.dev/api/storage/test"

# Test KV cache
curl "https://3d-marketplace.pages.dev/api/cache/test"
```

---

### 🔍 Phase 8: Monitoring & Alerts Setup

#### **Cloudflare Analytics Configuration**
- Enable Pages analytics
- Configure Worker analytics
- Set up D1 database monitoring
- Enable R2 storage monitoring

#### **Custom Monitoring Setup**
```bash
# Run monitoring setup script
npm run setup:monitoring

# Verify monitoring endpoints
curl "https://3d-marketplace.pages.dev/api/monitoring/health"
curl "https://3d-marketplace.pages.dev/api/monitoring/metrics"
```

---

### 🚨 Troubleshooting Guide

#### **Common Issues & Solutions**

**Database Connection Errors:**
```bash
# Check database binding
wrangler d1 info 3d-marketplace-enhanced --env production

# Verify schema
wrangler d1 execute 3d-marketplace-enhanced --env production --command "PRAGMA table_info(models);"
```

**Authentication Issues:**
```bash
# Re-authenticate if needed
wrangler auth login

# Verify permissions
wrangler auth list
```

**Deployment Failures:**
```bash
# Clear cache and rebuild
npm run clean
npm install
npm run build

# Check worker logs
wrangler tail --env production
```

---

### ✅ Success Verification Checklist

- [ ] All D1 databases created and accessible
- [ ] R2 buckets configured and accessible
- [ ] KV namespaces properly bound
- [ ] All secrets configured
- [ ] Workers deployed successfully
- [ ] Pages application deployed
- [ ] Analytics tracking active
- [ ] Cron jobs scheduled
- [ ] API endpoints responding
- [ ] Database queries working
- [ ] Real-time features functional
- [ ] Monitoring alerts configured

---

### 🎯 Next Steps After Setup

1. **Configure Bright Data Scraping**
   - Set up scraping schedules
   - Configure target platforms
   - Test data collection

2. **Set Up User Authentication**
   - Configure OAuth providers
   - Test login/registration flow
   - Set up user profiles

3. **Enable Payment Processing**
   - Configure Stripe integration
   - Test payment flows
   - Set up webhooks

4. **Launch Monitoring**
   - Set up alerting rules
   - Configure dashboards
   - Monitor performance metrics

---

### 📚 Additional Resources

- **Deployment Checklist**: `DEPLOYMENT_CHECKLIST.md`
- **API Documentation**: `docs/API.md`
- **Cloudflare Integration**: `docs/CLOUDFLARE_INTEGRATION.md`
- **Bright Data Setup**: `docs/BRIGHT_DATA_INTEGRATION.md`

---

**🎉 Environment Setup Complete!**

Your 3D marketplace is now ready for production deployment with full Cloudflare infrastructure support.
