# Інтеграція Cloudflare D1 з Next.js

Цей документ описує, як інтегрувати Cloudflare D1 з Next.js для використання в серверних компонентах та API маршрутах.

## Огляд

Cloudflare D1 - це SQL-база даних, яка працює на глобальній мережі Cloudflare. Вона базується на SQLite і забезпечує:

- Глобальне розповсюдження даних
- Автоматичне масштабування
- Низьку затримку
- Високу доступність

Ця інтеграція дозволяє використовувати D1 в Next.js проекті, розгорнутому на Cloudflare Pages.

## Налаштування

### 1. Встановлення залежностей

Переконайтеся, що у вас встановлений Wrangler CLI:

```bash
npm install -g wrangler
# або
yarn global add wrangler
```

### 2. Налаштування wrangler.toml

Створіть або оновіть файл `wrangler.toml` в кореневій директорії проекту:

```toml
# Wrangler configuration for Cloudflare Pages
name = "your-project-name"
compatibility_date = "2023-10-30"
compatibility_flags = ["nodejs_compat"]

# Configure D1 database
[[d1_databases]]
binding = "DB" # Доступно як env.DB
database_name = "your_database_name"
database_id = "your-database-id" # Замініть на ID вашої бази даних після створення
```

### 3. Створення бази даних D1

```bash
# Створення бази даних
npx wrangler d1 create your_database_name

# Після створення бази даних, оновіть wrangler.toml з отриманим database_id
```

### 4. Створення схеми бази даних

Створіть файл `schema.sql` з визначенням схеми бази даних:

```sql
-- Приклад схеми
CREATE TABLE users (
  id TEXT PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Інші таблиці...
```

### 5. Застосування схеми до бази даних

```bash
# Застосування схеми до віддаленої бази даних
npx wrangler d1 execute your_database_name --file=schema.sql

# Застосування схеми до локальної бази даних
npx wrangler d1 execute your_database_name --local --file=schema.sql
```

## Використання в Next.js

### Клієнт для роботи з D1

Створіть утиліти для роботи з D1 в `src/lib/db`:

```typescript
// src/lib/db/client.ts
import { D1Database } from './types';

let db: D1Database | null = null;

export function getDb(): D1Database {
  if (!db) {
    if (process.env.DB) {
      db = process.env.DB as unknown as D1Database;
    } else if (globalThis.DB) {
      db = globalThis.DB as unknown as D1Database;
    } else {
      throw new Error('База даних недоступна');
    }
  }
  return db;
}

export async function query<T = unknown>(sql: string, params: any[] = []): Promise<T[]> {
  const db = getDb();
  const stmt = db.prepare(sql);
  const result = await stmt.bind(...params).all<T>();
  return result.results;
}

// Інші функції...
```

### Використання в серверних компонентах

```typescript
// src/app/users/page.tsx
import { query } from '@/lib/db';

interface User {
  id: string;
  name: string;
  email: string;
}

export default async function UsersPage() {
  const users = await query<User>('SELECT * FROM users');
  
  return (
    <div>
      <h1>Users</h1>
      <ul>
        {users.map(user => (
          <li key={user.id}>{user.name} ({user.email})</li>
        ))}
      </ul>
    </div>
  );
}
```

### Використання в API маршрутах

```typescript
// src/app/api/users/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { withApiMiddleware } from '@/lib/db/middleware';

export const GET = withApiMiddleware(async (
  _request: NextRequest,
  { db }
) => {
  const users = await db.prepare('SELECT * FROM users').all();
  return NextResponse.json({ success: true, data: users.results });
});
```

## Локальна розробка

### Налаштування локальної бази даних

```bash
# Створення локальної бази даних
npx wrangler d1 create your_database_name --local

# Застосування схеми до локальної бази даних
npx wrangler d1 execute your_database_name --local --file=schema.sql
```

### Запуск локального сервера з D1

```bash
# Запуск локального сервера з підключенням до D1
npx wrangler pages dev .next --d1=your_database_name
```

## Міграції

Для управління міграціями бази даних можна використовувати утиліти з `src/lib/db/migrations.ts`:

```typescript
// Приклад використання міграцій
import { migrate } from '@/lib/db/migrations';

const migrations = [
  {
    id: '001',
    name: 'Initial schema',
    up: async (db) => {
      await db.exec(`
        CREATE TABLE users (
          id TEXT PRIMARY KEY,
          email TEXT UNIQUE NOT NULL,
          name TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
    },
    down: async (db) => {
      await db.exec('DROP TABLE users');
    },
  },
  // Інші міграції...
];

// Застосування міграцій
await migrate(migrations);
```

## Розгортання на Cloudflare Pages

### Налаштування змінних середовища

В панелі керування Cloudflare Pages налаштуйте змінні середовища:

- `CLOUDFLARE`: `true`
- `NODE_VERSION`: `18` (або новіше)

### Розгортання проекту

```bash
# Збірка проекту
npm run build

# Розгортання на Cloudflare Pages
npx wrangler pages publish .next
```

## Додаткові ресурси

- [Документація Cloudflare D1](https://developers.cloudflare.com/d1/)
- [Документація Cloudflare Pages](https://developers.cloudflare.com/pages/)
- [Документація Next.js](https://nextjs.org/docs)
