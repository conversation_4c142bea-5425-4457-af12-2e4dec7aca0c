# 🚀 3D Marketplace Deployment on Cloudflare Pages

## ⚡ Quick Deployment (Recommended)

### 1. Preparation
```bash
git clone <your-repo>
cd 3d-marketplace
npm install
```

### 2. Authentication
```bash
npm install -g wrangler
wrangler login
```

### 3. Deployment
```bash
npm run deploy:cloudflare
```

## 🔧 Full Deployment from Scratch

```bash
npm run deploy:full
```

## 🔐 Secrets Configuration

```bash
wrangler secret put NEXTAUTH_SECRET
wrangler secret put ADMIN_API_KEY
```

## 🌐 Result

- **Website**: https://3d-marketplace.pages.dev
- **Dashboard**: https://3d-marketplace.pages.dev/admin/import-demo
- **API**: https://3d-marketplace.pages.dev/api/test-import

## 📊 Monitoring

- **Metrics**: https://3d-marketplace.pages.dev/api/monitoring/metrics
- **Health Check**: https://3d-marketplace.pages.dev/api/health

## 🚨 Troubleshooting

### Authentication Error:
```bash
wrangler logout && wrangler login
```

### Build Error:
```bash
npm run clean && npm install && npm run build
```

### Resource Check:
```bash
wrangler d1 list
wrangler kv:namespace list
wrangler r2 bucket list
```

---

🎉 **Done! Your 3D Marketplace is running on Cloudflare Pages!**
