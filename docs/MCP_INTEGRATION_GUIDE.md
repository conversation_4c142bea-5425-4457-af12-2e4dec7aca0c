# 🚀 MCP Integration Guide

## 📋 Огляд

Цей документ описує повну інтеграцію Cloudflare MCP та Bright Data MCP в 3D Marketplace проект. Інтеграція забезпечує реальні виклики до MCP tools замість симуляції, покращуючи функціональність скрапінгу та зберігання даних.

## 🏗️ Архітектура

### Основні компоненти

1. **CloudflareMCPClient** - Реальний клієнт для роботи з Cloudflare сервісами
2. **BrightDataMCPClient** - Реальний клієнт для Bright Data скрапінгу
3. **MCPServiceCoordinator** - Координатор між сервісами
4. **API Endpoints** - REST API для взаємодії з MCP

### Схема інтеграції

```
┌─────────────────────────────────────────────────────────────┐
│                    3D Marketplace                           │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Cloudflare MCP  │    │ Bright Data MCP │                │
│  │     Client      │    │     Client      │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           └───────┬───────────────┘                        │
│                   │                                        │
│         ┌─────────────────┐                                │
│         │ MCP Service     │                                │
│         │ Coordinator     │                                │
│         └─────────────────┘                                │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐ ┌─────────────────┐ ┌───────────────┐ │
│  │ R2 Storage      │ │ D1 Database     │ │ KV Storage    │ │
│  └─────────────────┘ └─────────────────┘ └───────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Налаштування

### 1. Environment Variables

Додайте до `.env.local` або налаштуйте в Cloudflare:

```env
# Cloudflare MCP
CLOUDFLARE_MCP_ENABLED=true
CLOUDFLARE_ACCOUNT_ID=your_account_id
CLOUDFLARE_API_TOKEN=your_api_token

# Bright Data MCP
BRIGHT_DATA_MCP_ENABLED=true
BRIGHT_DATA_MCP_TOKEN=your_bright_data_token
BRIGHT_DATA_ZONE=your_zone_name

# MCP Configuration
MCP_TIMEOUT_MS=30000
MCP_RETRY_ATTEMPTS=3
MCP_ENABLE_FALLBACK=true
```

### 2. Wrangler Configuration

Environment variables вже додано до `wrangler.toml` для всіх середовищ:

- **Production**: Максимальна продуктивність
- **Staging**: Середні налаштування для тестування
- **Development**: Мінімальні налаштування для розробки

## 📚 Використання

### Cloudflare MCP Client

```typescript
import { CloudflareMCPClient } from '../lib/cloudflare/real-mcp-client';

const client = new CloudflareMCPClient(config);
await client.initialize(env);

// R2 операції
const r2Result = await client.r2Operation({
  action: 'put',
  key: 'models/test.stl',
  data: fileBuffer,
  metadata: { 'content-type': 'application/octet-stream' }
});

// D1 операції
const d1Result = await client.d1Operation({
  action: 'query',
  sql: 'SELECT * FROM models WHERE platform = ?',
  params: ['printables']
});

// KV операції
const kvResult = await client.kvOperation({
  action: 'put',
  key: 'cache:models:123',
  value: JSON.stringify(modelData),
  options: { ttl: 3600 }
});

// Analytics
const analyticsResult = await client.sendAnalyticsEvent({
  event: 'model_downloaded',
  properties: { platform: 'printables', modelId: '123' }
});
```

### Bright Data MCP Client

```typescript
import { BrightDataMCPClient } from '../lib/bright-data/real-mcp-client';

const client = new BrightDataMCPClient(config);
await client.initialize();

// Скрапінг
const scrapingResult = await client.scrapingOperation({
  action: 'scrape',
  target: {
    platform: 'printables',
    url: 'https://www.printables.com/model/123',
    type: 'model'
  },
  options: {
    includeImages: true,
    includeFiles: true
  }
});

// Пошук
const searchResult = await client.scrapingOperation({
  action: 'search',
  target: {
    platform: 'printables',
    url: '',
    type: 'search',
    parameters: { query: '3d printer parts' }
  }
});
```

### Service Coordinator

```typescript
import { MCPServiceCoordinator } from '../lib/mcp/service-coordinator';

const coordinator = new MCPServiceCoordinator(config);
await coordinator.initialize(env);

// Створення workflow job
const jobId = await coordinator.createJob('scrape_and_store', {
  target: {
    platform: 'printables',
    url: 'https://www.printables.com/model/123',
    type: 'model'
  }
}, 'high');

// Отримання статусу job
const job = coordinator.getJob(jobId);
const steps = coordinator.getJobSteps(jobId);

// Health check
const health = await coordinator.healthCheck();
```

## 🌐 API Endpoints

### Cloudflare MCP API

**Base URL**: `/api/mcp/cloudflare`

#### GET - Health Check та Метрики
```bash
# Health check
GET /api/mcp/cloudflare?action=health

# Метрики
GET /api/mcp/cloudflare?action=metrics
```

#### POST - Виконання операцій
```bash
# R2 операція
POST /api/mcp/cloudflare
{
  "service": "r2",
  "operation": {
    "action": "put",
    "key": "models/test.stl",
    "data": "base64_encoded_data",
    "metadata": { "content-type": "application/octet-stream" }
  }
}

# D1 операція
POST /api/mcp/cloudflare
{
  "service": "d1",
  "operation": {
    "action": "query",
    "sql": "SELECT * FROM models",
    "params": []
  }
}
```

### Bright Data MCP API

**Base URL**: `/api/mcp/bright-data`

#### GET - Health Check та Статистика
```bash
# Health check
GET /api/mcp/bright-data?action=health

# Статистика
GET /api/mcp/bright-data?action=stats
```

#### POST - Скрапінг операції
```bash
# Прямий MCP tool виклик
POST /api/mcp/bright-data
{
  "tool": "scrape_as_markdown_Bright_Data",
  "params": {
    "url": "https://www.printables.com/model/123"
  }
}

# Операція скрапінгу
POST /api/mcp/bright-data
{
  "operation": {
    "action": "scrape",
    "target": {
      "platform": "printables",
      "url": "https://www.printables.com/model/123",
      "type": "model"
    }
  }
}
```

### Status API

**Base URL**: `/api/mcp/status`

#### GET - Статус системи
```bash
# Загальний health check
GET /api/mcp/status?action=health

# Детальний health check
GET /api/mcp/status?action=health&detailed=true

# Метрики
GET /api/mcp/status?action=metrics

# Jobs
GET /api/mcp/status?action=jobs&status=running&limit=10

# Конкретний job
GET /api/mcp/status?action=job&jobId=job-123

# Загальна інформація
GET /api/mcp/status?action=summary
```

#### POST - Створення jobs
```bash
# Створення scrape and store job
POST /api/mcp/status
{
  "action": "create_job",
  "type": "scrape_and_store",
  "metadata": {
    "target": {
      "platform": "printables",
      "url": "https://www.printables.com/model/123",
      "type": "model"
    }
  },
  "priority": "high"
}

# Тестовий скрапінг
POST /api/mcp/status
{
  "action": "test_scraping",
  "metadata": {
    "platform": "printables",
    "url": "https://www.printables.com/model/123"
  }
}
```

## 🔍 Моніторинг та Діагностика

### Health Check

Система автоматично перевіряє стан всіх сервісів:

- **Cloudflare Services**: R2, D1, KV, Analytics
- **Bright Data MCP**: Доступність MCP tools
- **Service Coordinator**: Стан jobs та черги

### Метрики

Збираються метрики для:

- Кількість викликів та успішність
- Час виконання операцій
- Частота помилок
- Статистика jobs

### Логування

Всі операції логуються з детальною інформацією:

```
✅ Успішний MCP виклик: scrape_as_markdown_Bright_Data
🌐 Виклик глобальної MCP функції: scrape_as_html_Bright_Data
❌ Помилка MCP операції: Timeout exceeded
⚠️ Використовуємо fallback скрапінг для printables
```

## 🚨 Error Handling

### Retry Logic

- Автоматичні повторні спроби для тимчасових помилок
- Exponential backoff для rate limiting
- Максимальна кількість спроб налаштовується

### Fallback Mechanisms

- Якщо реальні MCP tools недоступні, використовується симуляція
- HTTP API fallback для прямих викликів
- Graceful degradation функціональності

### Error Types

```typescript
interface MCPError {
  code: string;
  message: string;
  service: string;
  operation: string;
  timestamp: string;
  retryable: boolean;
}
```

## 🔧 Troubleshooting

### Поширені проблеми

1. **MCP tools недоступні**
   - Перевірте environment variables
   - Переконайтеся що MCP сервер запущений
   - Використовуйте fallback режим

2. **Cloudflare bindings не працюють**
   - Перевірте wrangler.toml конфігурацію
   - Переконайтеся що сервіси створені в Cloudflare
   - Перевірте права доступу

3. **Timeout помилки**
   - Збільште MCP_TIMEOUT_MS
   - Перевірте мережеве з'єднання
   - Зменште розмір запитів

### Діагностичні команди

```bash
# Перевірка health check
curl http://localhost:3000/api/mcp/status?action=health

# Тестовий скрапінг
curl -X POST http://localhost:3000/api/mcp/status \
  -H "Content-Type: application/json" \
  -d '{"action":"test_scraping","metadata":{"platform":"printables"}}'

# Метрики
curl http://localhost:3000/api/mcp/status?action=metrics
```

## 📈 Performance Tips

1. **Використовуйте кешування** для часто запитуваних даних
2. **Налаштуйте rate limiting** для уникнення блокування
3. **Моніторьте метрики** для оптимізації performance
4. **Використовуйте priority** для важливих jobs
5. **Налаштуйте timeout** відповідно до типу операцій

## 🔄 Оновлення та Підтримка

### Версіонування

MCP клієнти підтримують версіонування API:

- Backward compatibility для старих версій
- Graceful migration для нових features
- Feature flags для поступового rollout

### Моніторинг

Рекомендується налаштувати моніторинг для:

- Health check endpoints
- Error rates та response times
- Job completion rates
- Resource usage

---

**Примітка**: Ця інтеграція забезпечує повну функціональність MCP tools з fallback механізмами для максимальної надійності.
