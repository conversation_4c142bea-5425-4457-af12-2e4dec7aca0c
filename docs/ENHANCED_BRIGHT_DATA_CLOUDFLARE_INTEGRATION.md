# 🚀 Enhanced Bright Data & Cloudflare Integration

## 📋 Огляд

Покращена інтеграція Bright Data MCP та Cloudflare для автоматичного скрапінгу та завантаження 3D моделей з популярних платформ на Cloudflare R2.

## 🏗️ Архітектура

### Основні компоненти

1. **Enhanced Bright Data MCP Client** - Покращений клієнт з реальними MCP викликами
2. **ModelDownloadManager** - Durable Object для завантаження файлів на R2
3. **Cloudflare Observability** - Система метрик та моніторингу
4. **ScrapingCoordinator** - Покращений координатор з інтеграцією завантаження

### Нові можливості

✅ **Реальні Bright Data MCP виклики** замість симуляції
✅ **Автоматичне завантаження 3D файлів** на Cloudflare R2
✅ **Cloudflare Observability** інтеграція
✅ **Покращена координація** між скрапінгом та завантаженням
✅ **Метрики та моніторинг** в реальному часі

## 🔧 Налаштування

### 1. Environment Variables

Додайте до `.env.local`:

```env
# Bright Data MCP
BRIGHT_DATA_API_KEY=your_real_api_key
BRIGHT_DATA_ZONE=your_zone_name

# Cloudflare
CLOUDFLARE_ACCOUNT_ID=your_account_id
CLOUDFLARE_API_TOKEN=your_api_token
```

### 2. Wrangler Configuration

Нові Durable Objects додано до `wrangler.toml`:

```toml
[[durable_objects.bindings]]
name = "MODEL_DOWNLOAD_MANAGER"
class_name = "ModelDownloadManager"
script_name = "3d-marketplace"
```

### 3. Cloudflare Bindings

Переконайтеся, що налаштовані:
- **D1 Database** для метаданих
- **R2 Bucket** для файлів моделей
- **KV Storage** для кешування
- **Analytics Engine** для метрик

## 🎯 Використання

### API Endpoints

#### Model Download Manager

```bash
# Додати завдання завантаження
POST /api/models/download-manager
{
  "modelId": "model123",
  "sourceUrl": "https://example.com/model.stl",
  "fileName": "awesome-model.stl",
  "fileType": "stl",
  "platform": "printables"
}

# Отримати статус завдань
GET /api/models/download-manager?action=jobs&status=pending

# Отримати статистику
GET /api/models/download-manager?action=stats

# Повторна спроба завантаження
PATCH /api/models/download-manager?jobId=job123&action=retry
```

#### Cloudflare Observability

```bash
# Записати метрику
POST /api/cloudflare/observability?type=metric
{
  "name": "scraping_requests_total",
  "value": 1,
  "labels": {"platform": "printables", "status": "success"}
}

# Записати лог
POST /api/cloudflare/observability?type=log
{
  "level": "info",
  "message": "Скрапінг завершено успішно",
  "context": {"platform": "printables", "modelsFound": 25}
}

# Отримати метрики
GET /api/cloudflare/observability?type=metrics&startTime=1640995200000

# Отримати логи
GET /api/cloudflare/observability?type=logs&filter=error
```

### Programmatic Usage

#### Enhanced Bright Data Client

```typescript
import { EnhancedBrightDataMCPClient } from '@/lib/bright-data/enhanced-mcp-client';

const client = new EnhancedBrightDataMCPClient({
  mcpApiToken: process.env.BRIGHT_DATA_API_KEY,
  enableFallback: true
});

// Скрапінг сторінки
const result = await client.scrapePage(
  'https://www.printables.com/model/123456',
  {
    format: 'markdown',
    useStructuredData: true
  }
);
```

#### Model Download Manager

```typescript
import { addDownloadJob } from '@/app/api/models/download-manager/route';

// Додати завдання завантаження
const job = await addDownloadJob(
  env,
  'model123',
  'https://example.com/model.stl',
  'awesome-model.stl',
  'stl',
  'printables'
);
```

#### Cloudflare Observability

```typescript
import { CloudflareObservability } from '@/lib/cloudflare/observability';

const observability = new CloudflareObservability(env);

// Записати метрику скрапінгу
await observability.recordScrapingMetrics(
  'printables',
  true,
  2500,
  25
);

// Записати метрику завантаження
await observability.recordDownloadMetrics(
  'printables',
  true,
  1024000,
  3000
);
```

## 🔄 Workflow

### Автоматичний процес

1. **Скрапінг** - Enhanced Bright Data MCP Client скрапить популярні моделі
2. **Координація** - ScrapingCoordinator обробляє результати
3. **Завантаження** - ModelDownloadManager завантажує файли на R2
4. **Моніторинг** - Cloudflare Observability записує метрики
5. **Зберігання** - Метадані зберігаються в D1, файли в R2

### Структура файлів в R2

```
models/
├── printables/
│   └── 2024/
│       └── 01/
│           └── 15/
│               └── model123/
│                   ├── awesome-model.stl
│                   └── textures.zip
├── makerworld/
│   └── 2024/
│       └── 01/
│           └── 15/
│               └── model456/
│                   └── cool-gadget.3mf
└── thangs/
    └── 2024/
        └── 01/
            └── 15/
                └── model789/
                    └── professional-part.obj
```

## 📊 Моніторинг

### Ключові метрики

- `scraping_requests_total` - Загальна кількість запитів скрапінгу
- `scraping_duration_ms` - Тривалість скрапінгу
- `scraping_models_found` - Кількість знайдених моделей
- `download_requests_total` - Загальна кількість завантажень
- `download_duration_ms` - Тривалість завантаження
- `download_file_size_bytes` - Розмір завантажених файлів
- `api_requests_total` - Загальна кількість API запитів
- `errors_total` - Загальна кількість помилок

### Dashboards

Метрики автоматично відправляються в:
- **Analytics Engine** для аналізу
- **KV Storage** для кешування
- **Console logs** для debugging

## 🚨 Error Handling

### Retry Logic

- **Скрапінг**: 3 спроби з експоненційною затримкою
- **Завантаження**: 3 спроби з лінійною затримкою
- **API виклики**: Fallback до симуляції при помилках

### Fallback Mechanisms

1. **MCP виклики** - Fallback до симуляції
2. **Завантаження** - Retry з затримкою
3. **Observability** - Локальне логування при помилках

## 🔒 Security

### API Keys

- Bright Data API ключі зберігаються як Cloudflare Secrets
- Токени автоматично ротуються
- Fallback режим для розробки

### Access Control

- Durable Objects ізольовані
- API endpoints захищені
- Rate limiting на всіх рівнях

## 🧪 Testing

### Unit Tests

```bash
npm test -- --testPathPattern=bright-data
npm test -- --testPathPattern=observability
npm test -- --testPathPattern=download-manager
```

### Integration Tests

```bash
npm run test:integration
```

### Load Testing

```bash
npm run test:load
```

## 📈 Performance

### Optimizations

- **Concurrent downloads**: До 5 одночасних завантажень
- **Caching**: Агресивне кешування метаданих
- **Compression**: Автоматичне стиснення файлів
- **CDN**: Cloudflare CDN для швидкого доступу

### Benchmarks

- **Скрапінг**: ~2-5 секунд на платформу
- **Завантаження**: ~1-10 секунд залежно від розміру
- **API відповідь**: <100ms для більшості запитів

## 🔧 Troubleshooting

### Поширені проблеми

1. **MCP токен застарів** - Оновіть BRIGHT_DATA_API_KEY
2. **R2 недоступний** - Перевірте налаштування R2_BUCKET
3. **Durable Object помилки** - Перевірте wrangler.toml
4. **Метрики не записуються** - Перевірте ANALYTICS binding

### Debug Mode

```bash
# Увімкнути детальне логування
export DEBUG=bright-data:*,cloudflare:*
npm run dev
```

## 🚀 Deployment

### Production

```bash
# Встановити secrets
npx wrangler secret put BRIGHT_DATA_API_KEY
npx wrangler secret put CLOUDFLARE_API_TOKEN

# Deploy
npm run build
npx wrangler pages deploy .next
```

### Staging

```bash
npx wrangler pages deploy .next --env staging
```

---

**Готово!** 🎉 Ваша покращена інтеграція Bright Data та Cloudflare готова до роботи!
