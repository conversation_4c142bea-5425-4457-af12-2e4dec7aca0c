# 🌐 Bright Data MCP Інтеграція для 3D Marketplace

## 📋 Огляд

Повна інтеграція всіх інструментів Bright Data MCP для автоматизованого скрапінгу популярних 3D моделей з платформ Printables.com, Makerworld.com та Thangs.com.

## 🏗️ Архітектура

### Основні компоненти

1. **EnhancedBrightDataMCPClient** - Розширений клієнт для роботи з усіма Bright Data tools
2. **AutomatedScraper** - Автоматизований скрапер з AI аналізом
3. **ScrapingScheduler** - Планувальник для регулярного скрапінгу
4. **AIModelAnalyzer** - AI аналіз популярності та якості моделей
5. **ScrapingCoordinator** - Durable Object для координації завдань

### Використовувані Bright Data MCP Tools

#### 🔍 Пошук та скрапінг
- `search_engine_Bright_Data` - Пошук популярних моделей
- `scrape_as_markdown_Bright_Data` - Скрапінг в Markdown форматі
- `scrape_as_html_Bright_Data` - Скрапінг в HTML форматі

#### 🌐 Браузерний скрапінг
- `scraping_browser_navigate_Bright_Data` - Навігація до сторінки
- `scraping_browser_get_text_Bright_Data` - Отримання тексту
- `scraping_browser_get_html_Bright_Data` - Отримання HTML
- `scraping_browser_links_Bright_Data` - Отримання посилань
- `scraping_browser_wait_for_Bright_Data` - Очікування елементів
- `scraping_browser_click_Bright_Data` - Взаємодія з елементами

#### 📊 Структуровані дані
- `web_data_amazon_product_Bright_Data` - Amazon продукти
- `web_data_instagram_posts_Bright_Data` - Instagram пости
- `web_data_linkedin_company_profile_Bright_Data` - LinkedIn компанії
- `web_data_youtube_videos_Bright_Data` - YouTube відео

#### 📈 Моніторинг
- `session_stats_Bright_Data` - Статистика сесії

## 🚀 Швидкий старт

### 1. Запуск автоматизованого скрапінгу

```typescript
import { AutomatedScraper } from '@/lib/bright-data/automated-scraper';

const scraper = new AutomatedScraper();

// Запуск скрапінгу всіх платформ
const jobId = await scraper.scrapePopularModels([
  'printables.com',
  'makerworld.com', 
  'thangs.com'
]);

console.log(`Скрапінг запущено: ${jobId}`);
```

### 2. Використання планувальника

```typescript
import { ScrapingScheduler } from '@/lib/bright-data/scraping-scheduler';

const scheduler = new ScrapingScheduler();

// Запуск планувальника
scheduler.start();

// Створення користувацького завдання
const taskId = scheduler.createTask({
  name: 'Щоденний скрапінг',
  description: 'Збір популярних моделей щодня',
  cronExpression: '0 2 * * *', // Щодня о 2:00
  platforms: ['printables.com'],
  config: {
    maxModelsPerPlatform: 100,
    qualityThreshold: 0.8,
    enableAIAnalysis: true
  }
});
```

### 3. AI аналіз моделей

```typescript
import { AIModelAnalyzer } from '@/lib/bright-data/ai-model-analyzer';

const analyzer = new AIModelAnalyzer();

// Аналіз моделі
const analysis = await analyzer.analyzeModel(modelData);

console.log(`Рейтинг популярності: ${analysis.popularityScore}`);
console.log(`Рейтинг якості: ${analysis.qualityScore}`);
console.log(`Ринковий потенціал: ${analysis.marketPotential}`);
```

## 🔧 API Ендпоінти

### Автоматизований скрапінг

#### GET `/api/scraping/automated`
Отримання статусу системи скрапінгу

**Параметри:**
- `action` - Тип запиту (`status`, `jobs`, `scheduler`, `tasks`)
- `jobId` - ID конкретного завдання (опціонально)
- `taskId` - ID завдання планувальника (опціонально)

**Приклади:**
```bash
# Загальний статус
GET /api/scraping/automated?action=status

# Статус конкретного завдання
GET /api/scraping/automated?action=status&jobId=scraping_123

# Список всіх завдань
GET /api/scraping/automated?action=jobs

# Статус планувальника
GET /api/scraping/automated?action=scheduler
```

#### POST `/api/scraping/automated`
Запуск нового скрапінгу

**Тіло запиту:**
```json
{
  "platforms": ["printables.com", "makerworld.com"],
  "immediate": true,
  "enableAI": true,
  "config": {
    "maxModelsPerPlatform": 100,
    "qualityThreshold": 0.7
  }
}
```

#### PUT `/api/scraping/automated`
Управління планувальником

**Тіло запиту:**
```json
{
  "action": "start-scheduler" | "stop-scheduler" | "update-task",
  "taskId": "task_id", // для update-task
  "updates": {} // оновлення для завдання
}
```

#### DELETE `/api/scraping/automated`
Скасування завдань

**Параметри:**
- `jobId` - Скасування завдання скрапінгу
- `taskId` - Видалення завдання планувальника

### Детальний статус

#### GET `/api/scraping/automated/status`
Детальна інформація про систему

**Параметри:**
- `detailed=true` - Розширена інформація
- `jobId` - Статус конкретного завдання

## 🎛️ Адміністративна панель

Компонент `ScrapingDashboard` надає веб-інтерфейс для управління скрапінгом:

### Функції панелі:
- 📊 Моніторинг активних завдань
- ⚙️ Управління планувальником
- 📈 Статистика платформ
- 🔄 Real-time оновлення статусу
- 🚀 Запуск нового скрапінгу
- ❌ Скасування завдань

### Використання:
```tsx
import ScrapingDashboard from '@/components/admin/scraping-dashboard';

export default function AdminPage() {
  return (
    <div>
      <h1>Адміністрування</h1>
      <ScrapingDashboard />
    </div>
  );
}
```

## 🔄 Cloudflare Durable Objects

### ScrapingCoordinator
Централізований координатор для управління станом скрапінгу:

- **Зберігання стану** - Активні завдання, заплановані задачі
- **WebSocket підтримка** - Real-time оновлення
- **Автоматична очистка** - Видалення застарілих даних
- **Статистика** - Моніторинг продуктивності

### Використання:
```typescript
// Підключення до Durable Object
const coordinator = env.SCRAPING_COORDINATOR.get(
  env.SCRAPING_COORDINATOR.idFromName('main')
);

// Отримання статусу
const response = await coordinator.fetch('/status');
const status = await response.json();
```

## 📊 Моніторинг та аналітика

### Метрики системи:
- **Активні завдання** - Кількість поточних скрапінгів
- **Успішність** - Відсоток успішних завдань
- **Продуктивність** - Швидкість обробки
- **Помилки** - Моніторинг збоїв

### Статистика платформ:
- **Запити** - Загальна кількість та успішні
- **Час відповіді** - Середній час обробки
- **Стан здоров'я** - Healthy/Warning/Critical
- **Остання активність** - Час останнього запиту

## 🔧 Налаштування

### Конфігурація скрапера:
```typescript
const config = {
  timeout: 30000,           // Таймаут запитів
  retryAttempts: 3,         // Кількість повторів
  rateLimitDelay: 1000,     // Затримка між запитами
  enableFallback: true,     // Fallback до симуляції
  maxConcurrent: 5,         // Максимум одночасних завдань
  qualityThreshold: 0.7     // Мінімальний рейтинг якості
};
```

### Планувальник завдань:
```typescript
const taskConfig = {
  maxModelsPerPlatform: 100,    // Максимум моделей з платформи
  qualityThreshold: 0.7,        // Мінімальна якість
  enableAIAnalysis: true,       // Увімкнути AI аналіз
  notifyOnCompletion: true,     // Сповіщення про завершення
  retryFailedJobs: true,        // Повтор невдалих завдань
  cleanupOldData: true,         // Очистка старих даних
  dataRetentionDays: 30         // Термін зберігання
};
```

## 🚨 Обробка помилок

### Типи помилок:
- **Network errors** - Проблеми з мережею
- **Rate limiting** - Перевищення лімітів
- **Parsing errors** - Помилки парсингу
- **Timeout errors** - Таймаути запитів

### Стратегії відновлення:
- **Exponential backoff** - Збільшення затримки
- **Fallback до симуляції** - Для розробки
- **Автоматичні повтори** - До 3 спроб
- **Graceful degradation** - Часткові результати

## 📝 Логування

### Рівні логування:
- **INFO** - Загальна інформація
- **WARN** - Попередження
- **ERROR** - Помилки
- **DEBUG** - Детальна діагностика

### Приклади логів:
```
🌐 Початок скрапінгу: https://printables.com/model/123
🔄 Спроба 1/3 виклику scrape_as_markdown_Bright_Data
✅ Успішний MCP виклик: scrape_as_markdown_Bright_Data
📊 Оброблено 50/100 моделей (50%)
✅ Завдання scraping_123 завершено успішно
```

## 🔮 Майбутні покращення

### Заплановані функції:
- **Real-time WebSocket** - Миттєві оновлення
- **Advanced AI аналіз** - Машинне навчання
- **Кешування результатів** - Підвищення швидкості
- **Розподілене скрапінг** - Масштабування
- **Інтеграція з базою даних** - Постійне зберігання

### Оптимізації:
- **Паралельна обробка** - Одночасні запити
- **Інтелектуальне планування** - Адаптивні розклади
- **Предиктивна аналітика** - Прогнозування трендів
- **Автоматичне налаштування** - Самооптимізація

---

## 📞 Підтримка

Для питань та підтримки:
- 📧 Email: <EMAIL>
- 📚 Документація: `/docs`
- 🐛 Issues: GitHub Issues
- 💬 Чат: Discord сервер
