# 3D Marketplace Documentation

## Introduction

Welcome to the 3D Marketplace documentation! This project is a platform for buying, selling, and exchanging 3D models for 3D printing and other purposes. The documentation contains information about project architecture, components, development process, and deployment.

## Table of Contents

### General Documentation

- [UI/UX Documentation](./UI_UX_DOCUMENTATION.md) - Description of design system, components, and user interaction
- [Components Documentation](./COMPONENTS_DOCUMENTATION.md) - Detailed description of components used in the project
- [3D Integration](./3D_INTEGRATION_DOCUMENTATION.md) - Information about 3D technology integration in the project

### Development

- [Development Guide](./DEVELOPMENT_GUIDE.md) - Information about development environment setup, project architecture, and development process
- [shadcn/ui Usage Guide](./SHADCN_GUIDE.md) - Information about using shadcn/ui components

### Deployment

- [Cloudflare Deployment](./CLOUDFLARE_DEPLOYMENT.md) - Information about deploying the project on Cloudflare Pages

## Project Architecture

The 3D marketplace is built on the following technologies:

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, shadcn/ui
- **3D Technologies**: Spline, Three.js, React Three Fiber
- **Deployment**: Cloudflare Pages

## Project Structure

```
3d-marketplace/
├── docs/                 # Documentation
├── public/               # Static files
├── src/
│   ├── app/              # Next.js App Router pages
│   │   ├── auth/         # Authentication pages
│   │   ├── models/       # Model pages
│   │   └── ...
│   ├── components/       # React components
│   │   ├── ui/           # Base UI components (shadcn/ui)
│   │   ├── model-viewer/ # Components for viewing 3D models
│   │   └── ...
│   └── lib/              # Utilities and helper functions
├── .env.example          # Environment variables example
├── next.config.js        # Next.js configuration
├── package.json          # Project dependencies
└── tailwind.config.js    # Tailwind CSS configuration
```

## Key Features

### 3D Model Viewing

The project allows viewing 3D models in interactive mode:

- Model rotation
- Scaling
- Panning
- Lighting changes
- Background changes

### Search and Filtering

Users can search and filter 3D models by various parameters:

- Category
- Tags
- Price
- Popularity
- Date added

### Model Upload

Users can upload their 3D models to the platform:

- Support for various formats (STL, OBJ, GLTF, etc.)
- Adding images
- Specifying model details
- Print settings

## Getting Started

### Requirements

For project development you will need:

- Node.js (version 18.x or higher)
- npm (version 9.x or higher) or yarn (version 1.22.x or higher)
- Git
- Code editor (VS Code recommended)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/3d-marketplace.git
cd 3d-marketplace
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Create `.env.local` file based on `.env.example`:
```bash
cp .env.example .env.local
```

4. Run the project in development mode:
```bash
npm run dev
# or
yarn dev
```

The project will be available at [http://localhost:3000](http://localhost:3000).

## Deployment

The project is configured for deployment on Cloudflare Pages. Detailed deployment information can be found in the [Cloudflare deployment documentation](./CLOUDFLARE_DEPLOYMENT.md).

## Contributing

We welcome contributions to the project! If you want to contribute, please follow these steps:

1. Create a fork of the repository
2. Create a branch for your feature (`git checkout -b feature/amazing-feature`)
3. Make changes
4. Commit changes (`git commit -m 'Add some amazing feature'`)
5. Push changes to your fork (`git push origin feature/amazing-feature`)
6. Create a Pull Request

## License

This project is licensed under the [MIT License](../LICENSE).

## Contact

If you have questions or suggestions, please create an Issue in the repository or contact us at [<EMAIL>](mailto:<EMAIL>).
