# UI/UX Гайд маркетплейсу 3D-моделей

## Зміст

1. [Вступ](#вступ)
2. [Принципи дизайну](#принципи-дизайну)
3. [Кольорова палітра](#кольорова-палітра)
4. [Типографіка](#типографіка)
5. [Компоненти інтерфейсу](#компоненти-інтерфейсу)
6. [Іконографія](#іконографія)
7. [Сітка та відступи](#сітка-та-відступи)
8. [Адаптивний дизайн](#адаптивний-дизайн)
9. [Анімації та переходи](#анімації-та-переходи)
10. [Доступність](#доступність)
11. [Приклади використання](#приклади-використання)

## Вступ

Цей гайд описує принципи дизайну, компоненти та стилі, які використовуються в маркетплейсі 3D-моделей. Він призначений для дизайнерів та розробників, які працюють над проектом, щоб забезпечити узгодженість та якість інтерфейсу користувача.

## Принципи дизайну

### Простота

Ми прагнемо до простоти та інтуїтивності інтерфейсу. Кожен елемент повинен мати чітку мету та бути легким для розуміння.

### Фокус на контенті

3D-моделі є головним контентом нашого маркетплейсу. Дизайн повинен підкреслювати та виділяти моделі, а не відволікати від них.

### Послідовність

Всі елементи інтерфейсу повинні бути послідовними та передбачуваними. Користувачі повинні відчувати, що вони взаємодіють з єдиною системою.

### Зворотний зв'язок

Кожна дія користувача повинна мати чіткий зворотний зв'язок. Це допомагає користувачам розуміти, що відбувається в системі.

### Ефективність

Інтерфейс повинен бути ефективним та дозволяти користувачам швидко досягати своїх цілей з мінімальною кількістю кроків.

## Кольорова палітра

### Основні кольори

- **Первинний колір**: `#3B82F6` (синій)
  - Використовується для основних кнопок, посилань та акцентів
  - Варіації: `#2563EB` (темніший), `#60A5FA` (світліший)

- **Вторинний колір**: `#10B981` (зелений)
  - Використовується для успішних дій, підтверджень
  - Варіації: `#059669` (темніший), `#34D399` (світліший)

- **Акцентний колір**: `#F59E0B` (помаранчевий)
  - Використовується для виділення важливих елементів
  - Варіації: `#D97706` (темніший), `#FBBF24` (світліший)

### Нейтральні кольори

- **Чорний**: `#111827`
  - Використовується для основного тексту

- **Темно-сірий**: `#374151`
  - Використовується для вторинного тексту

- **Сірий**: `#6B7280`
  - Використовується для підписів та неактивних елементів

- **Світло-сірий**: `#E5E7EB`
  - Використовується для фону, розділювачів

- **Білий**: `#FFFFFF`
  - Використовується для фону карток, модальних вікон

### Семантичні кольори

- **Успіх**: `#10B981` (зелений)
  - Використовується для успішних дій, підтверджень

- **Помилка**: `#EF4444` (червоний)
  - Використовується для помилок, попереджень

- **Попередження**: `#F59E0B` (помаранчевий)
  - Використовується для попереджень, важливих повідомлень

- **Інформація**: `#3B82F6` (синій)
  - Використовується для інформаційних повідомлень

### Приклад використання кольорів у Tailwind CSS

```tsx
<button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
  Купити зараз
</button>

<button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
  Підтвердити
</button>

<div className="text-red-500">
  Помилка: Неправильний формат файлу
</div>
```

## Типографіка

### Шрифти

- **Основний шрифт**: Inter
  - Використовується для всього тексту в інтерфейсі
  - Варіанти: Regular (400), Medium (500), SemiBold (600), Bold (700)

- **Монопросторовий шрифт**: Roboto Mono
  - Використовується для коду, технічних деталей
  - Варіанти: Regular (400), Medium (500)

### Розміри шрифтів

- **Заголовок 1**: 2.25rem (36px)
  - Використовується для головних заголовків сторінок

- **Заголовок 2**: 1.875rem (30px)
  - Використовується для підзаголовків сторінок

- **Заголовок 3**: 1.5rem (24px)
  - Використовується для заголовків секцій

- **Заголовок 4**: 1.25rem (20px)
  - Використовується для заголовків підсекцій

- **Основний текст**: 1rem (16px)
  - Використовується для основного тексту

- **Малий текст**: 0.875rem (14px)
  - Використовується для підписів, міток

- **Дуже малий текст**: 0.75rem (12px)
  - Використовується для дрібних деталей, юридичної інформації

### Приклад використання типографіки у Tailwind CSS

```tsx
<h1 className="text-4xl font-bold text-gray-900">
  Маркетплейс 3D-моделей
</h1>

<h2 className="text-3xl font-semibold text-gray-800">
  Популярні моделі
</h2>

<p className="text-base text-gray-700">
  Знайдіть ідеальну 3D-модель для вашого проекту
</p>

<span className="text-sm text-gray-500">
  Опубліковано 2 дні тому
</span>
```

## Компоненти інтерфейсу

### Кнопки

#### Первинна кнопка

Використовується для основних дій на сторінці.

```tsx
<button className="bg-blue-500 hover:bg-blue-600 text-white font-medium px-4 py-2 rounded transition-colors">
  Купити зараз
</button>
```

#### Вторинна кнопка

Використовується для другорядних дій.

```tsx
<button className="bg-white hover:bg-gray-100 text-gray-800 font-medium border border-gray-300 px-4 py-2 rounded transition-colors">
  Скасувати
</button>
```

#### Кнопка-посилання

Використовується для навігації.

```tsx
<button className="text-blue-500 hover:text-blue-600 font-medium transition-colors">
  Дізнатися більше
</button>
```

#### Кнопка з іконкою

```tsx
<button className="bg-blue-500 hover:bg-blue-600 text-white font-medium px-4 py-2 rounded flex items-center gap-2 transition-colors">
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
  </svg>
  Додати до кошика
</button>
```

### Форми

#### Текстове поле

```tsx
<div className="mb-4">
  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
    Назва моделі
  </label>
  <input
    type="text"
    id="name"
    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
    placeholder="Введіть назву моделі"
  />
</div>
```

#### Випадаючий список

```tsx
<div className="mb-4">
  <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
    Категорія
  </label>
  <select
    id="category"
    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
  >
    <option value="">Виберіть категорію</option>
    <option value="art">Мистецтво</option>
    <option value="gadgets">Гаджети</option>
    <option value="home">Дім</option>
  </select>
</div>
```

#### Перемикач

```tsx
<div className="flex items-center mb-4">
  <input
    type="checkbox"
    id="terms"
    className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300 rounded"
  />
  <label htmlFor="terms" className="ml-2 block text-sm text-gray-700">
    Я погоджуюсь з умовами використання
  </label>
</div>
```

### Картки

#### Картка моделі

```tsx
<div className="bg-white rounded-lg shadow-md overflow-hidden">
  <img
    src="https://example.com/model-thumbnail.jpg"
    alt="Model Thumbnail"
    className="w-full h-48 object-cover"
  />
  <div className="p-4">
    <h3 className="text-lg font-semibold text-gray-800">Назва моделі</h3>
    <p className="text-sm text-gray-500">by Дизайнер</p>
    <div className="flex items-center justify-between mt-2">
      <span className="text-blue-500 font-medium">$4.99</span>
      <div className="flex items-center gap-2">
        <span className="flex items-center text-sm text-gray-500">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
          4.8
        </span>
        <span className="flex items-center text-sm text-gray-500">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
            <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
          </svg>
          1.2k
        </span>
      </div>
    </div>
  </div>
</div>
```

## Іконографія

Ми використовуємо Heroicons для іконок в інтерфейсі. Іконки повинні бути простими, зрозумілими та відповідати загальному стилю дизайну.

### Розміри іконок

- **Малі**: 16x16px (1rem)
- **Середні**: 20x20px (1.25rem)
- **Великі**: 24x24px (1.5rem)

### Приклад використання іконок

```tsx
<svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
</svg>
```

## Сітка та відступи

Ми використовуємо систему сітки Tailwind CSS з 12 колонками. Відступи базуються на шкалі з кроком 0.25rem (4px).

### Відступи

- **Дуже малі**: 0.25rem (4px)
- **Малі**: 0.5rem (8px)
- **Середні**: 1rem (16px)
- **Великі**: 1.5rem (24px)
- **Дуже великі**: 2rem (32px)

### Приклад використання сітки

```tsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  <div className="bg-white p-4 rounded-lg shadow">Елемент 1</div>
  <div className="bg-white p-4 rounded-lg shadow">Елемент 2</div>
  <div className="bg-white p-4 rounded-lg shadow">Елемент 3</div>
</div>
```

## Адаптивний дизайн

Ми підтримуємо наступні контрольні точки для адаптивного дизайну:

- **sm**: 640px
- **md**: 768px
- **lg**: 1024px
- **xl**: 1280px
- **2xl**: 1536px

### Приклад адаптивного дизайну

```tsx
<div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
  {/* Картки моделей */}
</div>
```

## Анімації та переходи

Ми використовуємо прості анімації та переходи для покращення користувацького досвіду. Анімації повинні бути ненав'язливими та природними.

### Переходи

```tsx
<button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors duration-200">
  Купити зараз
</button>
```

### Анімації

```tsx
<div className="animate-pulse bg-gray-200 h-48 w-full rounded-lg"></div>
```

## Доступність

Ми прагнемо зробити наш інтерфейс доступним для всіх користувачів, включаючи тих, хто використовує допоміжні технології.

### Контраст

Всі тексти повинні мати достатній контраст з фоном:
- Малий текст: контраст не менше 4.5:1
- Великий текст: контраст не менше 3:1

### Фокус

Всі інтерактивні елементи повинні мати чіткий стан фокусу:

```tsx
<button className="bg-blue-500 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-white px-4 py-2 rounded">
  Купити зараз
</button>
```

### Альтернативний текст

Всі зображення повинні мати альтернативний текст:

```tsx
<img src="model.jpg" alt="3D модель столу" className="w-full h-auto" />
```

## Приклади використання

### Сторінка маркетплейсу

```tsx
<div className="container mx-auto px-4 py-8">
  <h1 className="text-3xl font-bold text-gray-900 mb-6">Маркетплейс 3D-моделей</h1>
  
  <div className="flex flex-col md:flex-row gap-6">
    {/* Фільтри */}
    <div className="w-full md:w-64 bg-white p-4 rounded-lg shadow">
      <h2 className="text-lg font-semibold text-gray-800 mb-4">Фільтри</h2>
      
      <div className="mb-4">
        <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
          Категорія
        </label>
        <select
          id="category"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="all">Всі категорії</option>
          <option value="art">Мистецтво</option>
          <option value="gadgets">Гаджети</option>
          <option value="home">Дім</option>
        </select>
      </div>
      
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Ціна
        </label>
        <div className="flex items-center gap-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="price"
              value="all"
              className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300"
              defaultChecked
            />
            <span className="ml-2 text-sm text-gray-700">Всі</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="price"
              value="free"
              className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300"
            />
            <span className="ml-2 text-sm text-gray-700">Безкоштовні</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="price"
              value="paid"
              className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300"
            />
            <span className="ml-2 text-sm text-gray-700">Платні</span>
          </label>
        </div>
      </div>
    </div>
    
    {/* Список моделей */}
    <div className="flex-1">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-gray-800">Результати</h2>
        <select
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="popular">Популярні</option>
          <option value="newest">Нові</option>
          <option value="price-low">Ціна: від низької до високої</option>
          <option value="price-high">Ціна: від високої до низької</option>
        </select>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {/* Картки моделей */}
      </div>
    </div>
  </div>
</div>
```
