# UI/UX Documentation for 3D Marketplace

## Table of Contents

1. [Introduction](#introduction)
2. [Design System](#design-system)
   - [Color Scheme](#color-scheme)
   - [Typography](#typography)
   - [Components](#components)
   - [Iconography](#iconography)
3. [Page Structure](#page-structure)
   - [Home Page](#home-page)
   - [Models Page](#models-page)
   - [Model Details Page](#model-details-page)
   - [Model Upload Page](#model-upload-page)
4. [Navigation](#navigation)
   - [Main Menu](#main-menu)
   - [Submenu](#submenu)
   - [Breadcrumbs](#breadcrumbs)
5. [User Interaction](#user-interaction)
   - [Forms](#forms)
   - [Notifications](#notifications)
   - [Modal Windows](#modal-windows)
6. [3D Interaction](#3d-interaction)
   - [Model Viewing](#model-viewing)
   - [View Settings](#view-settings)
7. [Responsive Design](#responsive-design)
8. [Themes](#themes)
   - [Light Theme](#light-theme)
   - [Dark Theme](#dark-theme)
9. [Accessibility](#accessibility)
10. [Developer Recommendations](#developer-recommendations)

## Introduction

This documentation describes the UI/UX principles and components used in the 3D marketplace. It is intended for designers and developers working on the project and provides a unified approach to interface design and development.

## Design System

### Color Scheme

Our 3D marketplace uses a color system based on CSS variables, which allows for easy theme changes and design adaptation.

#### Primary Colors

```css
--primary: 220.9 39.3% 11%;      /* Primary brand color */
--primary-foreground: 210 20% 98%; /* Text on primary color */
--secondary: 220 14.3% 95.9%;     /* Secondary color */
--secondary-foreground: 220.9 39.3% 11%; /* Text on secondary color */
--background: 0 0% 100%;          /* Page background */
--foreground: 224 71.4% 4.1%;     /* Primary text */
--muted: 220 14.3% 95.9%;         /* Muted background */
--muted-foreground: 220 8.9% 46.1%; /* Muted text */
--accent: 220 14.3% 95.9%;        /* Accent color */
--accent-foreground: 220.9 39.3% 11%; /* Text on accent color */
--destructive: 0 84.2% 60.2%;     /* Color for destructive actions */
--destructive-foreground: 210 20% 98%; /* Text on destructive color */
--border: 220 13% 91%;            /* Border color */
--input: 220 13% 91%;             /* Input border color */
--ring: 224 71.4% 4.1%;           /* Focus color */
```

#### Colors for Charts and Visualizations

```css
--chart-1: 12 76% 61%;
--chart-2: 173 58% 39%;
--chart-3: 197 37% 24%;
--chart-4: 43 74% 66%;
--chart-5: 27 87% 67%;
```

### Typography

The project uses the Inter font for all text, with different weights for different elements:

- **Headings**: Inter, font-weight: 600-700
- **Body text**: Inter, font-weight: 400
- **Accent text**: Inter, font-weight: 500-600

Font sizes:
- Large headings: 2rem - 3rem (32px - 48px)
- Medium headings: 1.5rem - 2rem (24px - 32px)
- Small headings: 1.25rem - 1.5rem (20px - 24px)
- Body text: 1rem (16px)
- Small text: 0.875rem (14px)
- Very small text: 0.75rem (12px)

### Components

The project uses shadcn/ui components, which are based on Radix UI and Tailwind CSS. Main components:

#### Buttons

```tsx
<Button>Standard Button</Button>
<Button variant="secondary">Secondary Button</Button>
<Button variant="destructive">Destructive Button</Button>
<Button variant="outline">Outline Button</Button>
<Button variant="ghost">Ghost Button</Button>
<Button variant="link">Link Button</Button>
```

Button sizes:
```tsx
<Button size="sm">Small Button</Button>
<Button>Standard Button</Button>
<Button size="lg">Large Button</Button>
<Button size="icon">Icon</Button>
```

#### Cards

```tsx
<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card Description</CardDescription>
  </CardHeader>
  <CardContent>
    Card Content
  </CardContent>
  <CardFooter>
    Card Footer
  </CardFooter>
</Card>
```

#### Forms

```tsx
<div className="space-y-2">
  <Label htmlFor="email">Email</Label>
  <Input id="email" type="email" placeholder="Enter email" />
</div>
```

#### Tabs

```tsx
<Tabs defaultValue="tab1">
  <TabsList>
    <TabsTrigger value="tab1">Tab 1</TabsTrigger>
    <TabsTrigger value="tab2">Tab 2</TabsTrigger>
  </TabsList>
  <TabsContent value="tab1">Tab 1 content</TabsContent>
  <TabsContent value="tab2">Tab 2 content</TabsContent>
</Tabs>
```

### Iconography

The project uses the Lucide icon library for all icons. Usage examples:

```tsx
<Heart className="h-4 w-4" />
<Download className="h-4 w-4" />
<Search className="h-4 w-4" />
```

## Page Structure

### Home Page

The home page consists of the following sections:
1. **Hero Section** - large banner with title, subtitle, and action buttons
2. **Popular Tags** - horizontal list of popular tags
3. **Featured Models** - grid of popular 3D models
4. **Categories** - grid of 3D model categories
5. **Collections** - grid of 3D model collections
6. **How It Works** - section explaining the marketplace usage process
7. **Join Community** - call-to-action section

### Models Page

The models page contains:
1. **Filters** - sidebar with filters for searching models
2. **Sorting** - dropdown menu for sorting models
3. **Model Grid** - grid with model cards
4. **Pagination** - navigation through result pages

### Model Details Page

The model details page contains:
1. **3D View** - interactive 3D model viewer
2. **Model Information** - name, author, description, price, etc.
3. **Additional Information Tabs** - overview, print settings, reviews
4. **Recommended Models** - grid of recommended models

### Model Upload Page

The model upload page contains a multi-step form:
1. **File Upload** - uploading 3D model files
2. **Image Upload** - uploading model images
3. **Model Details** - entering model information
4. **Print Settings** - entering recommended print settings

## Navigation

### Main Menu

The main menu is located at the top of the page and contains:
- Logo (link to home page)
- Links to main site sections
- Search
- Theme toggle
- Sign in and sign up buttons

### Submenu

Submenu appears on hover over main menu items and contains additional links.

### Breadcrumbs

Breadcrumbs are displayed on internal pages and show the path from the home page to the current page.

## User Interaction

### Forms

Forms use shadcn/ui components and include:
- Input validation
- Error messages
- Automatic formatting
- User hints

### Notifications

Notifications are displayed at the top of the page and can be:
- Informational
- Success
- Warning
- Error

### Modal Windows

Modal windows are used for:
- Action confirmation
- Displaying additional information
- Forms that don't require a separate page

## 3D Interaction

### Model Viewing

The ModelViewer component is used for viewing 3D models and supports:
- Model rotation
- Scaling
- Panning
- Lighting changes
- Background changes

### View Settings

Users can configure model viewing:
- Enable/disable automatic rotation
- Change rotation speed
- Change background
- Show/hide grid

## Responsive Design

The site is adapted for different screen sizes:
- **Mobile devices** (up to 640px) - single column, simplified interface
- **Tablets** (641px - 1024px) - two columns, compact interface
- **Desktops** (from 1025px) - full interface with all features

## Themes

### Light Theme

Light theme uses white background and dark text, with primary color accents.

### Dark Theme

Dark theme uses dark background and light text, with primary color accents.

## Accessibility

The site complies with WCAG 2.1 accessibility standards:
- All interactive elements are keyboard accessible
- All images have alternative text
- Text contrast meets requirements
- Page structure is logical and understandable for screen readers

## Developer Recommendations

1. Use shadcn/ui components instead of creating custom ones
2. Add `'use client'` at the beginning of files that use client components
3. Use CSS variables for colors instead of hard-coded values
4. Follow responsive design principles
5. Ensure accessibility of all components
6. Use icons from the Lucide library
7. Test the interface on different devices and browsers
