# 3D Marketplace Components Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [Base UI Components](#base-ui-components)
   - [Button](#button)
   - [Card](#card)
   - [Input](#input)
   - [Label](#label)
   - [Tabs](#tabs)
   - [Badge](#badge)
   - [Form](#form)
   - [Select](#select)
   - [Dropdown Menu](#dropdown-menu)
3. [Specialized Components](#specialized-components)
   - [HeroSection](#herosection)
   - [FeaturedModels](#featuredmodels)
   - [CategoriesDemo](#categoriesdemo)
   - [CollectionsSection](#collectionssection)
   - [HowItWorks](#howitworks)
   - [JoinCommunity](#joincommunity)
   - [PopularTags](#populartags)
4. [3D Components](#3d-components)
   - [ModelViewer](#modelviewer)
   - [SplineScene](#splinescene)
   - [CategoryExplorer](#categoryexplorer)
5. [Navigation Components](#navigation-components)
   - [ThemeToggle](#themetoggle)
   - [ClientLayout](#clientlayout)
6. [Usage Examples](#usage-examples)

## Introduction

This documentation describes the components used in the 3D marketplace. It is intended for developers working on the project and provides understanding of component structure and usage.

## Base UI Components

Base UI components are based on the shadcn/ui library, which uses Radix UI and Tailwind CSS.

### Button

Button component with various variants and sizes.

**Import:**
```tsx
import { Button } from "@/components/ui/button";
```

**Variants:**
- `default` - primary button
- `secondary` - secondary button
- `destructive` - button for destructive actions
- `outline` - outline button
- `ghost` - transparent button
- `link` - link button

**Sizes:**
- `default` - standard size
- `sm` - small size
- `lg` - large size
- `icon` - icon button

**Example:**
```tsx
<Button>Standard Button</Button>
<Button variant="secondary" size="lg">Large Secondary Button</Button>
<Button variant="destructive">Delete</Button>
<Button variant="outline">Outline Button</Button>
<Button variant="ghost">Ghost Button</Button>
<Button variant="link">Link</Button>
<Button size="icon"><Search className="h-4 w-4" /></Button>
```

### Card

Card component for displaying content.

**Import:**
```tsx
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
```

**Example:**
```tsx
<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card Description</CardDescription>
  </CardHeader>
  <CardContent>
    Card Content
  </CardContent>
  <CardFooter>
    <Button>Action</Button>
  </CardFooter>
</Card>
```

### Input

Component for text input.

**Import:**
```tsx
import { Input } from "@/components/ui/input";
```

**Example:**
```tsx
<Input type="text" placeholder="Enter text" />
<Input type="email" placeholder="Enter email" />
<Input type="password" placeholder="Enter password" />
```

### Label

Component for form field labels.

**Import:**
```tsx
import { Label } from "@/components/ui/label";
```

**Example:**
```tsx
<div className="space-y-2">
  <Label htmlFor="email">Email</Label>
  <Input id="email" type="email" placeholder="Enter email" />
</div>
```

### Tabs

Component for creating tabs.

**Import:**
```tsx
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
```

**Example:**
```tsx
<Tabs defaultValue="overview">
  <TabsList>
    <TabsTrigger value="overview">Overview</TabsTrigger>
    <TabsTrigger value="settings">Settings</TabsTrigger>
  </TabsList>
  <TabsContent value="overview">Overview tab content</TabsContent>
  <TabsContent value="settings">Settings tab content</TabsContent>
</Tabs>
```

### Badge

Component for displaying badges.

**Import:**
```tsx
import { Badge } from "@/components/ui/badge";
```

**Variants:**
- `default` - primary badge
- `secondary` - secondary badge
- `destructive` - badge for destructive actions
- `outline` - outline badge

**Example:**
```tsx
<Badge>New</Badge>
<Badge variant="secondary">Popular</Badge>
<Badge variant="destructive">Deleted</Badge>
<Badge variant="outline">Outline</Badge>
```

### Form

Component for creating forms with validation.

**Import:**
```tsx
import { Form, FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form";
```

**Example:**
```tsx
<Form {...form}>
  <form onSubmit={form.handleSubmit(onSubmit)}>
    <FormField
      control={form.control}
      name="email"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Email</FormLabel>
          <FormControl>
            <Input placeholder="Enter email" {...field} />
          </FormControl>
          <FormDescription>Enter your email to sign in</FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
    <Button type="submit">Submit</Button>
  </form>
</Form>
```

### Select

Component for dropdown selection.

**Import:**
```tsx
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
```

**Example:**
```tsx
<Select>
  <SelectTrigger>
    <SelectValue placeholder="Select category" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="art">Art</SelectItem>
    <SelectItem value="gadgets">Gadgets</SelectItem>
    <SelectItem value="home">Home</SelectItem>
  </SelectContent>
</Select>
```

### Dropdown Menu

Component for creating dropdown menus.

**Import:**
```tsx
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
```

**Example:**
```tsx
<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button variant="outline">Menu</Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent>
    <DropdownMenuItem>Profile</DropdownMenuItem>
    <DropdownMenuItem>Settings</DropdownMenuItem>
    <DropdownMenuItem>Logout</DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
```

## Specialized Components

### HeroSection

Component for displaying the hero section on the main page.

**Import:**
```tsx
import HeroSection from "@/components/hero-section";
```

**Example:**
```tsx
<HeroSection />
```

### FeaturedModels

Component for displaying featured models.

**Import:**
```tsx
import FeaturedModels from "@/components/featured-models";
```

**Example:**
```tsx
<FeaturedModels />
```

### CategoriesDemo

Component for displaying 3D model categories.

**Import:**
```tsx
import CategoriesDemo from "@/components/categories-demo";
```

**Example:**
```tsx
<CategoriesDemo />
```

### CollectionsSection

Component for displaying 3D model collections.

**Import:**
```tsx
import CollectionsSection from "@/components/collections-section";
```

**Example:**
```tsx
<CollectionsSection collections={collections} />
```

### HowItWorks

Component for displaying the "How It Works" section.

**Import:**
```tsx
import HowItWorks from "@/components/how-it-works";
```

**Example:**
```tsx
<HowItWorks />
```

### JoinCommunity

Component for displaying the "Join Community" section.

**Import:**
```tsx
import JoinCommunity from "@/components/join-community";
```

**Example:**
```tsx
<JoinCommunity />
```

### PopularTags

Component for displaying popular tags.

**Import:**
```tsx
import PopularTags from "@/components/popular-tags";
```

**Example:**
```tsx
<PopularTags tags={popularTags} />
```

## 3D Components

### ModelViewer

Component for displaying 3D models.

**Import:**
```tsx
import { ModelViewer } from "@/components/model-viewer/model-viewer";
```

**Example:**
```tsx
<ModelViewer model={model} />
```

### SplineScene

Component for displaying 3D scenes created in Spline.

**Import:**
```tsx
import { SplineScene, SCENE_PRESETS } from "@/components/ui/splite";
```

**Example:**
```tsx
<SplineScene
  scene="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
  preset="PRODUCT_VIEWER"
/>
```

### CategoryExplorer

Component for displaying categories in 3D space.

**Import:**
```tsx
import CategoryExplorer from "@/components/model-viewer/category-explorer";
```

**Example:**
```tsx
<CategoryExplorer
  splineSceneUrl="https://prod.spline.design/kZDDjO5HuC9GJUM2/scene.splinecode"
  categories={categories}
  onCategorySelect={handleCategorySelect}
/>
```

## Navigation Components

### ThemeToggle

Component for switching between light and dark themes.

**Import:**
```tsx
import { ThemeToggle } from "@/components/theme-toggle";
```

**Example:**
```tsx
<ThemeToggle />
```

### ClientLayout

Component for displaying the general page layout.

**Import:**
```tsx
import ClientLayout from "@/app/ClientLayout";
```

**Example:**
```tsx
<ClientLayout>
  {children}
</ClientLayout>
```

## Usage Examples

### Home Page

```tsx
export default function Home() {
  return (
    <main className="flex min-h-screen flex-col bg-background">
      <HeroSection />
      <PopularTags tags={POPULAR_TAGS} />
      <FeaturedModels />
      <CategoriesDemo />
      <CollectionsSection collections={COLLECTIONS} />
      <HowItWorks />
      <JoinCommunity />
    </main>
  );
}
```

### Model Detail Page

```tsx
export default function ModelDetailPage({ params }: { params: { id: string } }) {
  const [activeTab, setActiveTab] = useState('overview');
  const model = MODEL_DATA;

  return (
    <main className="container mx-auto py-8 px-4">
      <div className="mb-6">
        <Link href="/models" className="flex items-center text-muted-foreground hover:text-primary transition-colors">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Models
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardContent className="p-0 overflow-hidden">
              <div className="h-[500px] relative">
                <ModelViewer model={model} />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{model.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      <Tabs defaultValue="overview" className="mt-8">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="print-settings">Print Settings</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
        </TabsList>
        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{model.description}</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <RecommendedModels models={recommendedModels} title="Similar Models" className="mt-12" />
    </main>
  );
}
