# shadcn/ui Guide for 3D Marketplace

This guide explains how to use shadcn/ui components in your 3D Marketplace project.

## Table of Contents

- [Introduction](#introduction)
- [Adding Components](#adding-components)
- [Using Components](#using-components)
- [Customizing Components](#customizing-components)
- [Migrating from Existing UI](#migrating-from-existing-ui)
- [Theming](#theming)
- [Best Practices](#best-practices)

## Introduction

[shadcn/ui](https://ui.shadcn.com/) is a collection of reusable components built using Radix UI and Tailwind CSS. It's not a component library but rather a collection of re-usable components that you can copy and paste into your apps.

The components are already installed and configured in this project. You can find them in `src/components/ui/`.

## Adding Components

To add more shadcn/ui components, use the CLI:

```bash
npx shadcn add [component-name]
```

For example, to add the Dialog component:

```bash
npx shadcn add dialog
```

You can also add multiple components at once:

```bash
npx shadcn add dropdown-menu select tabs
```

## Using Components

### Importing Components

Import components from the `@/components/ui` directory:

```tsx
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
```

### Basic Usage

Here's how to use some common components:

#### Button

```tsx
<Button>Default Button</Button>
<Button variant="secondary">Secondary</Button>
<Button variant="destructive">Destructive</Button>
<Button variant="outline">Outline</Button>
<Button variant="ghost">Ghost</Button>
<Button variant="link">Link</Button>

<Button size="sm">Small</Button>
<Button>Default</Button>
<Button size="lg">Large</Button>
```

#### Card

```tsx
<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card Description</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Card content goes here</p>
  </CardContent>
  <CardFooter>
    <Button>Action</Button>
  </CardFooter>
</Card>
```

#### Input

```tsx
<div className="space-y-2">
  <Label htmlFor="email">Email</Label>
  <Input id="email" type="email" placeholder="Enter your email" />
</div>
```

## Customizing Components

### Styling with Tailwind

You can customize components using Tailwind classes:

```tsx
<Button className="bg-blue-600 hover:bg-blue-700">
  Custom Button
</Button>
```

### Modifying Component Source

Since shadcn/ui components are copied into your project, you can modify them directly:

1. Open the component file in `src/components/ui/`
2. Make your changes
3. Save the file

For example, to modify the Button component, edit `src/components/ui/button.tsx`.

## Migrating from Existing UI

When migrating from your existing UI to shadcn/ui:

1. Identify the components you want to replace
2. Add the equivalent shadcn/ui components
3. Replace the old components with the new ones
4. Adjust styling as needed

### Example Migration

**Before (using custom button):**

```tsx
<button className="btn-primary">
  Click Me
</button>
```

**After (using shadcn/ui):**

```tsx
<Button>
  Click Me
</Button>
```

## Theming

shadcn/ui uses CSS variables for theming, which are defined in `src/app/globals.css`.

### Light and Dark Mode

The theme includes both light and dark mode variables:

```css
:root {
  /* Light mode variables */
  --background: 0 0% 100%;
  --foreground: 224 71.4% 4.1%;
  /* ... */
}

.dark {
  /* Dark mode variables */
  --background: 224 71.4% 4.1%;
  --foreground: 210 20% 98%;
  /* ... */
}
```

### Customizing Colors

To change the color scheme, modify the CSS variables in `src/app/globals.css`.

## Best Practices

1. **Use Client Components**: shadcn/ui components use React hooks, so they must be used in client components. Add `'use client'` at the top of your component files.

2. **Consistent Styling**: Use the provided variants instead of custom styling when possible to maintain consistency.

3. **Composition**: Compose complex UIs by combining multiple shadcn/ui components.

4. **Form Handling**: Use the Form component with React Hook Form for form validation and submission.

5. **Accessibility**: shadcn/ui components are built with accessibility in mind. Maintain this by using proper ARIA attributes and keyboard navigation.

## Demo Page

Check out the demo page at [/shadcn-demo](/shadcn-demo) to see the components in action.

## Resources

- [shadcn/ui Documentation](https://ui.shadcn.com/docs)
- [Radix UI](https://www.radix-ui.com/)
- [Tailwind CSS](https://tailwindcss.com/)
