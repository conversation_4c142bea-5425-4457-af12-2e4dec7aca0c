# API Маркетплейсу 3D-моделей

## Зміст

1. [Вступ](#вступ)
2. [Аутентифікація](#аутентифікація)
3. [Мод<PERSON>л<PERSON>](#моделі)
4. [Користувачі](#користувачі)
5. [Замовлення](#замовлення)
6. [Підписки](#підписки)
7. [Платежі](#платежі)
8. [Пошук та фільтрація](#пошук-та-фільтрація)
9. [Завантаження файлів](#завантаження-файлів)
10. [Помилки та коди відповідей](#помилки-та-коди-відповідей)

## Вступ

API маркетплейсу 3D-моделей побудоване на основі RESTful принципів та використовує JSON для передачі даних. Всі запити до API повинні бути зроблені через HTTPS.

### Базовий URL

```
https://api.3d-marketplace.com/v1
```

### Формат відповіді

Всі відповіді повертаються у форматі JSON:

```json
{
  "data": {
    // Дані відповіді
  },
  "meta": {
    "pagination": {
      "total": 100,
      "count": 10,
      "per_page": 10,
      "current_page": 1,
      "total_pages": 10
    }
  }
}
```

## Аутентифікація

API використовує JWT (JSON Web Tokens) для аутентифікації. Токен повинен бути переданий у заголовку `Authorization`:

```
Authorization: Bearer <token>
```

### Отримання токену

```
POST /auth/login
```

#### Параметри запиту

| Параметр | Тип | Опис |
|----------|-----|------|
| email | string | Email користувача |
| password | string | Пароль користувача |

#### Приклад запиту

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Приклад відповіді

```json
{
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "123",
      "email": "<EMAIL>",
      "name": "John Doe"
    }
  }
}
```

### Реєстрація

```
POST /auth/register
```

#### Параметри запиту

| Параметр | Тип | Опис |
|----------|-----|------|
| email | string | Email користувача |
| password | string | Пароль користувача |
| name | string | Ім'я користувача |

#### Приклад запиту

```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "John Doe"
}
```

## Моделі

### Отримання списку моделей

```
GET /models
```

#### Параметри запиту

| Параметр | Тип | Опис |
|----------|-----|------|
| page | integer | Номер сторінки (за замовчуванням: 1) |
| per_page | integer | Кількість моделей на сторінці (за замовчуванням: 10) |
| category | string | Фільтр за категорією |
| price | string | Фільтр за ціною (free, paid, all) |
| sort | string | Сортування (popular, downloads, newest, price-low, price-high) |

#### Приклад відповіді

```json
{
  "data": [
    {
      "id": "1",
      "title": "Low Poly Skull",
      "thumbnail": "https://example.com/thumbnails/skull.jpg",
      "designer": {
        "id": "123",
        "name": "DesignMaster"
      },
      "price": 4.99,
      "category": "Art",
      "likes": 245,
      "downloads": 1200
    },
    // Інші моделі...
  ],
  "meta": {
    "pagination": {
      "total": 100,
      "count": 10,
      "per_page": 10,
      "current_page": 1,
      "total_pages": 10
    }
  }
}
```

### Отримання деталей моделі

```
GET /models/:id
```

#### Приклад відповіді

```json
{
  "data": {
    "id": "1",
    "title": "Low Poly Skull",
    "description": "A detailed low poly skull model perfect for Halloween decorations...",
    "images": [
      "https://example.com/images/skull_1.jpg",
      "https://example.com/images/skull_2.jpg"
    ],
    "designer": {
      "id": "123",
      "name": "DesignMaster",
      "avatar": "https://example.com/avatars/designmaster.jpg",
      "models": 48,
      "followers": 1250
    },
    "price": 4.99,
    "category": "Art",
    "tags": ["skull", "halloween", "decoration", "gothic", "low-poly"],
    "likes": 245,
    "downloads": 1200,
    "dateAdded": "2023-09-15",
    "fileSize": "15.4 MB",
    "fileFormat": "STL, OBJ",
    "license": "Standard - Personal Use",
    "printSettings": {
      "material": "PLA, PETG",
      "layerHeight": "0.2mm",
      "infill": "15-20%",
      "supports": "Minimal",
      "rafts": "No"
    },
    "reviews": [
      {
        "user": "PrintMaster",
        "avatar": "https://example.com/avatars/printmaster.jpg",
        "rating": 5,
        "date": "2023-10-05",
        "comment": "Excellent model! Printed perfectly on my Ender 3 with minimal supports."
      }
      // Інші відгуки...
    ]
  }
}
```

### Створення нової моделі

```
POST /models
```

#### Параметри запиту

| Параметр | Тип | Опис |
|----------|-----|------|
| title | string | Назва моделі |
| description | string | Опис моделі |
| category | string | Категорія моделі |
| tags | array | Масив тегів |
| price | number | Ціна моделі (0 для безкоштовних) |
| license | string | Тип ліцензії |
| printSettings | object | Рекомендовані налаштування друку |

#### Приклад запиту

```json
{
  "title": "Low Poly Skull",
  "description": "A detailed low poly skull model...",
  "category": "Art",
  "tags": ["skull", "halloween", "decoration"],
  "price": 4.99,
  "license": "standard",
  "printSettings": {
    "material": "PLA, PETG",
    "layerHeight": "0.2mm",
    "infill": "15-20%",
    "supports": "Minimal",
    "rafts": "No"
  }
}
```

### Оновлення моделі

```
PUT /models/:id
```

Параметри запиту аналогічні створенню моделі.

### Видалення моделі

```
DELETE /models/:id
```

## Користувачі

### Отримання профілю користувача

```
GET /users/:id
```

#### Приклад відповіді

```json
{
  "data": {
    "id": "123",
    "name": "DesignMaster",
    "avatar": "https://example.com/avatars/designmaster.jpg",
    "bio": "3D designer specializing in low poly models",
    "website": "https://designmaster.com",
    "social": {
      "twitter": "designmaster",
      "instagram": "designmaster3d"
    },
    "stats": {
      "models": 48,
      "followers": 1250,
      "following": 120
    },
    "dateJoined": "2022-05-10"
  }
}
```

### Отримання моделей користувача

```
GET /users/:id/models
```

### Оновлення профілю

```
PUT /users/:id
```

## Замовлення

### Створення замовлення

```
POST /orders
```

#### Параметри запиту

| Параметр | Тип | Опис |
|----------|-----|------|
| modelId | string | ID моделі |
| paymentIntentId | string | ID платіжного наміру Stripe |

### Отримання замовлень користувача

```
GET /users/:id/orders
```

## Підписки

### Отримання доступних планів підписок

```
GET /memberships
```

### Підписка на план

```
POST /memberships/subscribe
```

#### Параметри запиту

| Параметр | Тип | Опис |
|----------|-----|------|
| planId | string | ID плану підписки |
| paymentMethodId | string | ID платіжного методу |

## Платежі

### Створення платіжного наміру

```
POST /payments/create-intent
```

#### Параметри запиту

| Параметр | Тип | Опис |
|----------|-----|------|
| amount | number | Сума платежу в центах |
| currency | string | Валюта платежу (за замовчуванням: usd) |
| description | string | Опис платежу |

#### Приклад відповіді

```json
{
  "data": {
    "clientSecret": "pi_3NkCc92eZvKYlo2C1gUYvSqR_secret_O2GtZ1KQBZrAgTCyzxECmTGpa"
  }
}
```

## Пошук та фільтрація

### Пошук моделей

```
GET /search
```

#### Параметри запиту

| Параметр | Тип | Опис |
|----------|-----|------|
| q | string | Пошуковий запит |
| category | string | Фільтр за категорією |
| price | string | Фільтр за ціною |
| sort | string | Сортування |

## Завантаження файлів

### Завантаження файлів моделі

```
POST /upload/model
```

Використовує `multipart/form-data` для завантаження файлів.

### Завантаження зображень

```
POST /upload/images
```

Використовує `multipart/form-data` для завантаження зображень.

## Помилки та коди відповідей

### Коди відповідей

- `200 OK` - Запит успішно виконаний
- `201 Created` - Ресурс успішно створений
- `400 Bad Request` - Неправильний запит
- `401 Unauthorized` - Необхідна аутентифікація
- `403 Forbidden` - Доступ заборонений
- `404 Not Found` - Ресурс не знайдений
- `500 Internal Server Error` - Внутрішня помилка сервера

### Формат помилок

```json
{
  "error": {
    "code": "invalid_request",
    "message": "The request was unacceptable, often due to missing a required parameter.",
    "details": {
      "field": "title",
      "reason": "required"
    }
  }
}
```
