# 🌐 Cloudflare Durable Objects Integration

## Огляд

Ваш 3D Marketplace тепер інтегрований з Cloudflare Durable Objects для забезпечення реального часу функціональності та глобальної консистентності даних.

## 🏗️ Архітектура

### Cloudflare Stack
- **Workers** - Serverless функції на edge мережі
- **Durable Objects** - Stateful objects з глобальною консистентністю
- **R2 Storage** - Об'єктне сховище для 3D моделей
- **D1 Database** - SQLite база даних на edge
- **KV Storage** - Глобальне key-value сховище
- **Analytics Engine** - Реальний час аналітика

## 🔧 Durable Objects

### 1. CollaborationRoom (`collaboration-room.ts`)

**Призначення:** Реальний час колаборація для 3D моделей

**Функції:**
- WebSocket підтримка для реального часу
- Синхронізація курсорів між користувачами
- Синхронізація камери та перегляду
- Чат та коментарі
- Версіонування змін моделі
- Управління учасниками кімнати

**API Endpoints:**
```
GET    /api/collaboration/{roomId}           - Отримати стан кімнати
POST   /api/collaboration/{roomId}           - Приєднатися до кімнати
DELETE /api/collaboration/{roomId}           - Покинути кімнату
WS     /api/collaboration/{roomId}/websocket - WebSocket з'єднання
```

**WebSocket Messages:**
```typescript
// Приєднання користувача
{ type: 'user-join', userId: string, data: { user: User } }

// Рух курсора
{ type: 'cursor-move', userId: string, data: { cursor: {x, y, z} } }

// Синхронізація камери
{ type: 'camera-sync', userId: string, data: { camera: {...} } }

// Зміна моделі
{ type: 'model-change', userId: string, data: { changeType, changeData } }

// Чат повідомлення
{ type: 'chat', userId: string, data: { content: string } }
```

### 2. PrinterManager (`printer-manager.ts`)

**Призначення:** Управління 3D принтерами в реальному часі

**Функції:**
- Моніторинг статусу принтерів
- Управління чергою друку
- Віддалене керування принтерами
- Моніторинг датчиків (температура, позиція, філамент)
- Система алертів та сповіщень
- Статистика використання

**API Endpoints:**
```
GET  /api/printers/{printerId}              - Статус принтера
POST /api/printers/{printerId}?action=register - Реєстрація принтера
POST /api/printers/{printerId}?action=command  - Команда принтеру
POST /api/printers/{printerId}?action=job      - Додати завдання
PUT  /api/printers/{printerId}              - Оновити датчики
```

**Команди принтера:**
```typescript
{ type: 'start' | 'pause' | 'resume' | 'cancel' | 'home' | 'move' | 'heat' | 'cool' }
```

### 3. ProjectManager (`project-manager.ts`)

**Призначення:** Управління проектами та командною роботою

**Функції:**
- Управління проектами та завданнями
- Система ролей та прав доступу
- Версіонування проектів
- Коментарі та обговорення
- Відстеження прогресу
- Інтеграція з моделями та принтерами

**API Endpoints:**
```
GET    /api/projects/{projectId}     - Статус проекту
POST   /api/projects/{projectId}     - Створити проект
PUT    /api/projects/{projectId}     - Оновити проект
DELETE /api/projects/{projectId}     - Архівувати проект
```

### 4. DownloadManager (`download-manager.ts`)

**Призначення:** Управління завантаженнями та аналітикою

**Функції:**
- Відстеження завантажень
- Аналітика використання
- Кешування файлів
- Безпека доступу
- Статистика популярності

## 🔌 React Hooks

### useCollaboration

```typescript
const collaboration = useCollaboration(roomId, userId, userName);

// Стан
collaboration.users          // Список користувачів
collaboration.modelState     // Стан моделі
collaboration.isConnected    // Статус з'єднання

// Дії
collaboration.updateCursor(cursor)
collaboration.syncCamera(camera)
collaboration.updateModel(changeType, changeData, modelId)
collaboration.sendChatMessage(content)
collaboration.addAnnotation(annotation)
```

### usePrinterManager

```typescript
const printerManager = usePrinterManager();

// Стан
printerManager.printers       // Список принтерів
printerManager.isConnected    // Статус з'єднання

// Дії
printerManager.registerPrinter(printer)
printerManager.sendCommand(printerId, command)
printerManager.addPrintJob(printerId, job)
printerManager.updateSensors(printerId, sensors)

// Хелпери
printerManager.getPrinterById(id)
printerManager.getActivePrinters()
printerManager.getPrintingPrinters()
```

## 🎨 React Components

### RealTimeCollaboration

Компонент для реального часу колаборації:
- Список активних користувачів
- Чат та повідомлення
- Відео/аудіо дзвінки (заглушка)
- Синхронізація курсорів та камери
- Стан моделі та версіонування

### RealTimePrinterMonitor

Компонент для моніторингу принтерів:
- Список принтерів з статусами
- Детальна інформація про принтер
- Управління завданнями друку
- Моніторинг датчиків
- Черга завдань

## 🌍 Deployment

### wrangler.toml Configuration

```toml
name = "3d-marketplace"
main = "src/index.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# Durable Objects
[[durable_objects.bindings]]
name = "COLLABORATION_ROOM"
class_name = "CollaborationRoom"
script_name = "3d-marketplace"

[[durable_objects.bindings]]
name = "PRINTER_MANAGER"
class_name = "PrinterManager"
script_name = "3d-marketplace"

[[durable_objects.bindings]]
name = "PROJECT_MANAGER"
class_name = "ProjectManager"
script_name = "3d-marketplace"

# D1 Database
[[d1_databases]]
binding = "DB"
database_name = "3d-marketplace-db"
database_id = "your-database-id"

# R2 Storage
[[r2_buckets]]
binding = "R2_BUCKET"
bucket_name = "3d-models-storage"

# KV Storage
[[kv_namespaces]]
binding = "CACHE_KV"
id = "your-kv-namespace-id"

# Analytics Engine
[[analytics_engine_datasets]]
binding = "ANALYTICS"

# Queue
[[queues]]
binding = "BACKGROUND_QUEUE"
queue = "background-tasks"
```

### Environment Variables

```bash
# Secrets
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
NEXTAUTH_SECRET=your-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=your-email
EMAIL_SERVER_PASSWORD=your-password
EMAIL_FROM=<EMAIL>

# Environment
ENVIRONMENT=production
```

## 🚀 Features

### Real-time Collaboration
- ✅ WebSocket з'єднання через Durable Objects
- ✅ Синхронізація курсорів між користувачами
- ✅ Реальний час чат
- ✅ Управління учасниками кімнати
- ✅ Версіонування змін моделі

### 3D Printer Management
- ✅ Реальний час моніторинг принтерів
- ✅ Віддалене керування
- ✅ Черга завдань друку
- ✅ Моніторинг датчиків
- ✅ Система алертів

### Project Management
- ✅ Управління проектами
- ✅ Командна робота
- ✅ Версіонування
- ✅ Завдання та коментарі
- ✅ Права доступу

### Analytics & Monitoring
- ✅ Реальний час аналітика
- ✅ Відстеження використання
- ✅ Метрики продуктивності
- ✅ Статистика завантажень

## 🔧 Development

### Local Development

```bash
# Встановити Wrangler CLI
npm install -g wrangler

# Логін в Cloudflare
wrangler login

# Запустити локально
wrangler dev

# Деплой
wrangler deploy
```

### Testing Durable Objects

```bash
# Тестування колаборації
curl -X POST http://localhost:8787/api/collaboration/test-room \
  -H "Content-Type: application/json" \
  -d '{"userId": "user1", "userName": "Test User"}'

# Тестування принтера
curl -X POST http://localhost:8787/api/printers/printer1?action=register \
  -H "Content-Type: application/json" \
  -d '{"id": "printer1", "name": "Test Printer", "type": "FDM"}'
```

## 📊 Monitoring

### Cloudflare Dashboard
- Durable Objects metrics
- Worker invocations
- Error rates
- Response times

### Analytics Engine Queries
```sql
-- Активні колаборації
SELECT COUNT(*) FROM analytics 
WHERE blob1 = 'user-join' 
AND timestamp > NOW() - INTERVAL 1 HOUR

-- Статистика принтерів
SELECT blob2, COUNT(*) as count 
FROM analytics 
WHERE blob1 = 'printer-command' 
GROUP BY blob2
```

## 🎯 Next Steps

1. **Розширення функціональності**
   - Відео/аудіо дзвінки через WebRTC
   - Розширена аналітика
   - Інтеграція з IoT пристроями

2. **Оптимізація**
   - Кешування на edge
   - Оптимізація WebSocket з'єднань
   - Покращення продуктивності

3. **Безпека**
   - Аутентифікація та авторизація
   - Rate limiting
   - Захист від DDoS

Ваш 3D Marketplace тепер має потужну Cloudflare інфраструктуру для масштабування та реального часу функціональності! 🚀
