# 🕐 Система автоматичного скрапінгу за розкладом

Повнофункціональна система для автоматичного збору 3D моделей з популярних платформ з використанням Bright Data MCP.

## 🌟 Основні можливості

### ✨ Автоматичний скрапінг
- **Розклад виконання**: Підтримка cron виразів для гнучкого налаштування часу
- **Множинні платформи**: MakerWorld, Printables, Thangs
- **Розумні затримки**: Автоматичні паузи між запитами для уникнення блокування

### 📊 Моніторинг та статистика
- **Детальні логи**: Повна історія виконання завдань
- **Статистика платформ**: Окремі метрики для кожної платформи
- **Показники успішності**: Відстеження успішних та неуспішних запусків

### 🎛️ Управління
- **Веб-інтерфейс**: Зручна панель управління в `/admin/bright-data`
- **CRUD операції**: Створення, редагування, видалення завдань
- **Ручний запуск**: Можливість запустити завдання поза розкладом

## 🏗️ Архітектура

### Основні компоненти

```
src/lib/scheduler/
├── types.ts                 # Типи та інтерфейси
├── cron-utils.ts           # Утиліти для роботи з cron
├── scheduler-manager.ts    # Основний менеджер планувальника
└── README.md              # Документація

src/app/api/scheduler/
├── route.ts               # CRUD API для завдань
├── [id]/route.ts         # API для окремих завдань
├── [id]/run/route.ts     # Ручний запуск завдань
└── [id]/toggle/route.ts  # Увімкнення/вимкнення

src/components/admin/
├── scheduler-panel.tsx        # Основна панель управління
├── scheduler-stats-card.tsx   # Картка статистики
├── jobs-list.tsx             # Список завдань
└── create-job-form.tsx       # Форма створення завдань
```

### База даних

```sql
-- Завдання планувальника
CREATE TABLE scheduler_jobs (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  cron_expression TEXT NOT NULL,
  enabled BOOLEAN DEFAULT TRUE,
  platforms TEXT NOT NULL,        -- JSON array
  models_per_platform INTEGER DEFAULT 20,
  search_query TEXT,
  category TEXT,
  config TEXT NOT NULL,          -- JSON object
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_run TIMESTAMP,
  next_run TIMESTAMP
);

-- Логи виконання
CREATE TABLE scheduler_logs (
  id TEXT PRIMARY KEY,
  job_id TEXT NOT NULL,
  start_time TIMESTAMP NOT NULL,
  end_time TIMESTAMP,
  status TEXT NOT NULL,           -- 'running', 'success', 'error', 'cancelled'
  models_scraped INTEGER DEFAULT 0,
  platform_results TEXT,         -- JSON array
  error_message TEXT,
  execution_time INTEGER,         -- milliseconds
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (job_id) REFERENCES scheduler_jobs(id) ON DELETE CASCADE
);
```

## 🚀 Використання

### 1. Створення завдання

```typescript
const jobRequest = {
  name: 'Щоденний скрапінг популярних моделей',
  description: 'Автоматичний збір популярних 3D моделей',
  cronExpression: '0 2 * * *',  // Щоденно о 2:00
  platforms: ['makerworld', 'printables', 'thangs'],
  modelsPerPlatform: 20,
  searchQuery: 'dragon miniature',  // Опціонально
  category: 'Games'                 // Опціонально
};

// Через API
const response = await fetch('/api/scheduler', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(jobRequest)
});
```

### 2. Управління завданнями

```typescript
// Отримання всіх завдань
const jobs = await fetch('/api/scheduler?stats=true&status=true');

// Увімкнення/вимкнення завдання
await fetch(`/api/scheduler/${jobId}/toggle`, {
  method: 'POST',
  body: JSON.stringify({ enabled: true })
});

// Ручний запуск
await fetch(`/api/scheduler/${jobId}/run`, { method: 'POST' });

// Видалення завдання
await fetch(`/api/scheduler/${jobId}`, { method: 'DELETE' });
```

### 3. Шаблони розкладу

```typescript
const SCHEDULE_TEMPLATES = {
  DAILY_2AM: '0 2 * * *',        // Щоденно о 2:00
  DAILY_6AM: '0 6 * * *',        // Щоденно о 6:00
  WEEKLY_SUNDAY: '0 2 * * 0',    // Щотижня в неділю
  HOURLY: '0 * * * *',           // Щогодини
  EVERY_6_HOURS: '0 */6 * * *',  // Кожні 6 годин
};
```

## 🔧 Налаштування

### Конфігурація завдання

```typescript
const jobConfig = {
  retryAttempts: 3,              // Кількість повторних спроб
  retryDelay: 5000,              // Затримка між спробами (мс)
  timeout: 300000,               // Таймаут виконання (мс)
  delayBetweenPlatforms: 2000,   // Затримка між платформами (мс)
  saveToDatabase: true,          // Зберігати результати в БД
  notifyOnError: true,           // Сповіщення про помилки
  notifyOnSuccess: false         // Сповіщення про успіх
};
```

### Інтеграція з Bright Data

Система використовує існуючий `BrightDataScraperManager` для виконання скрапінгу:

```typescript
const brightDataManager = new BrightDataScraperManager();
const models = await brightDataManager.scrapeAllPlatforms({
  modelsPerPlatform: job.modelsPerPlatform,
  platforms: job.platforms,
  delayBetweenRequests: job.config.delayBetweenPlatforms,
});
```

## 📱 Веб-інтерфейс

Доступний за адресою `/admin/bright-data` у вкладці "Розклад":

### Основні функції UI:
- 📊 **Статистика**: Загальні метрики та показники успішності
- 📋 **Список завдань**: Перегляд всіх завдань з їх статусами
- ➕ **Створення завдань**: Форма з шаблонами розкладу
- ⚙️ **Управління**: Увімкнення/вимкнення, ручний запуск, видалення

### Статуси завдань:
- 🔵 **Виконується**: Завдання зараз активне
- ✅ **Успішно**: Останнє виконання пройшло без помилок
- ❌ **Помилка**: Виникли проблеми під час виконання
- ⏸️ **Вимкнено**: Завдання призупинено
- ⏳ **Очікування**: Завдання чекає наступного запуску

## 🛡️ Безпека та надійність

### Обробка помилок
- Автоматичні повторні спроби при помилках
- Детальне логування для діагностики
- Graceful shutdown при перезапуску сервера

### Обмеження
- Валідація cron виразів
- Обмеження на кількість моделей (1-100)
- Rate limiting для запобігання перевантаження

### Моніторинг
- Health checks для перевірки стану системи
- Cleanup старих логів
- Backup конфігурації завдань

## 🎯 Приклади використання

### Щоденний збір популярних моделей
```
Назва: "Популярні моделі дня"
Розклад: "0 2 * * *" (щоденно о 2:00)
Платформи: всі
Моделей: 20 на платформу
```

### Тематичний пошук
```
Назва: "Мініатюри для D&D"
Розклад: "0 6 * * 1" (щопонеділка о 6:00)
Пошук: "dragon miniature dnd"
Категорія: "Games"
```

### Швидкий моніторинг
```
Назва: "Швидкий огляд"
Розклад: "0 */6 * * *" (кожні 6 годин)
Моделей: 5 на платформу
```

## 🔄 Інтеграція з існуючою системою

Система повністю інтегрована з:
- ✅ Bright Data MCP tools
- ✅ Існуючою базою даних моделей
- ✅ Admin панеллю
- ✅ Системою аутентифікації
- ✅ API маршрутизацією Next.js

---

**Система готова до використання!** 🎉

Перейдіть до `/admin/bright-data` → вкладка "Розклад" для початку роботи.
