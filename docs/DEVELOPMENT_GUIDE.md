# 3D Marketplace Development Guide

## Table of Contents

1. [Introduction](#introduction)
2. [Development Environment Setup](#development-environment-setup)
   - [Requirements](#requirements)
   - [Installation](#installation)
   - [Configuration](#configuration)
3. [Project Architecture](#project-architecture)
   - [Folder Structure](#folder-structure)
   - [Key Components](#key-components)
   - [Routing](#routing)
4. [Technology Stack](#technology-stack)
   - [Frontend](#frontend)
   - [3D Technologies](#3d-technologies)
   - [Styling](#styling)
5. [Development Workflow](#development-workflow)
   - [Creating New Components](#creating-new-components)
   - [Adding New Pages](#adding-new-pages)
   - [3D Model Integration](#3d-model-integration)
6. [Testing](#testing)
   - [Unit Tests](#unit-tests)
   - [Integration Tests](#integration-tests)
   - [Performance Testing](#performance-testing)
7. [Deployment](#deployment)
   - [Pre-deployment Preparation](#pre-deployment-preparation)
   - [Cloudflare Deployment](#cloudflare-deployment)
   - [CI/CD](#cicd)
8. [Recommendations and Best Practices](#recommendations-and-best-practices)
   - [Performance](#performance)
   - [Accessibility](#accessibility)
   - [SEO](#seo)

## Introduction

This guide is intended for developers working on the 3D marketplace project. It contains information about development environment setup, project architecture, development workflow, and deployment.

## Development Environment Setup

### Requirements

For project development you will need:

- Node.js (version 18.x or higher)
- npm (version 9.x or higher) or yarn (version 1.22.x or higher)
- Git
- Code editor (VS Code recommended)

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/3d-marketplace.git
cd 3d-marketplace
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Run the project in development mode:
```bash
npm run dev
# or
yarn dev
```

The project will be available at [http://localhost:3000](http://localhost:3000).

### Configuration

The project uses the following configuration files:

- `next.config.js` - Next.js configuration
- `tailwind.config.js` - Tailwind CSS configuration
- `components.json` - shadcn/ui configuration
- `.env.local` - local environment variables (not included in repository)

For local development, create a `.env.local` file based on `.env.example`:

```bash
cp .env.example .env.local
```

## Project Architecture

### Folder Structure

```
3d-marketplace/
├── docs/                 # Documentation
├── public/               # Static files
├── src/
│   ├── app/              # Next.js App Router pages
│   │   ├── auth/         # Authentication pages
│   │   ├── models/       # Model pages
│   │   └── ...
│   ├── components/       # React components
│   │   ├── ui/           # Base UI components (shadcn/ui)
│   │   ├── model-viewer/ # Components for viewing 3D models
│   │   └── ...
│   └── lib/              # Utilities and helper functions
├── .env.example          # Environment variables example
├── next.config.js        # Next.js configuration
├── package.json          # Project dependencies
└── tailwind.config.js    # Tailwind CSS configuration
```

### Key Components

- **ClientLayout** (`src/app/ClientLayout.tsx`) - General layout for all pages
- **ThemeProvider** (`src/components/theme-provider.tsx`) - Theme provider (light/dark)
- **ModelViewer** (`src/components/model-viewer/model-viewer.tsx`) - Component for viewing 3D models
- **SplineScene** (`src/components/ui/splite.tsx`) - Component for Spline integration

### Routing

The project uses App Router from Next.js 14. The route structure corresponds to the folder structure in `src/app/`:

- `/` - Home page (`src/app/page.tsx`)
- `/models` - Models page (`src/app/models/page.tsx`)
- `/models/[id]` - Model details page (`src/app/models/[id]/page.tsx`)
- `/models/upload` - Model upload page (`src/app/models/upload/page.tsx`)
- `/auth/signin` - Sign in page (`src/app/auth/signin/page.tsx`)
- `/auth/signup` - Sign up page (`src/app/auth/signup/page.tsx`)

## Technology Stack

### Frontend

- **Next.js** (version 14) - React framework with App Router
- **React** (version 18) - Library for building user interfaces
- **TypeScript** - Typed superset of JavaScript

### 3D Technologies

- **Spline** - Tool for creating and integrating 3D designs
- **Three.js** - Library for 3D graphics on the web
- **React Three Fiber** - React renderer for Three.js

### Styling

- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Component collection based on Radix UI and Tailwind CSS
- **Lucide** - Icon library

## Development Workflow

### Creating New Components

1. To create a new UI component, use shadcn/ui:

```bash
npx shadcn-ui@latest add [component-name]
```

2. To create a custom component, create a new file in the appropriate folder:

```tsx
// src/components/example-component.tsx
'use client';

import React from 'react';
import { Button } from '@/components/ui/button';

interface ExampleComponentProps {
  title: string;
}

const ExampleComponent: React.FC<ExampleComponentProps> = ({ title }) => {
  return (
    <div className="p-4 bg-muted rounded-lg">
      <h2 className="text-xl font-bold mb-4">{title}</h2>
      <Button>Click me</Button>
    </div>
  );
};

export default ExampleComponent;
```

### Adding New Pages

To add a new page, create a new `page.tsx` file in the appropriate folder:

```tsx
// src/app/example/page.tsx
'use client';

import React from 'react';
import ExampleComponent from '@/components/example-component';

export default function ExamplePage() {
  return (
    <main className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">Example Page</h1>
      <ExampleComponent title="My Component" />
    </main>
  );
}
```

### 3D Model Integration

1. For Spline scene integration:

```tsx
import { SplineScene } from '@/components/ui/splite';

<SplineScene
  scene="https://prod.spline.design/your-scene-id/scene.splinecode"
  preset="PRODUCT_VIEWER"
/>
```

2. For 3D model integration through ModelViewer:

```tsx
import { ModelViewer } from '@/components/model-viewer/model-viewer';

<ModelViewer
  model={{
    id: '1',
    name: 'Example Model',
    splineSceneUrl: 'https://prod.spline.design/your-scene-id/scene.splinecode',
  }}
/>
```

## Testing

### Unit Tests

Jest and React Testing Library are used for unit testing:

```bash
npm run test
# or
yarn test
```

Test example:

```tsx
// src/components/example-component.test.tsx
import { render, screen } from '@testing-library/react';
import ExampleComponent from './example-component';

describe('ExampleComponent', () => {
  it('renders the title correctly', () => {
    render(<ExampleComponent title="Test Title" />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });
});
```

### Integration Tests

Cypress is used for integration testing:

```bash
npm run cypress
# or
yarn cypress
```

### Performance Testing

Lighthouse is used for performance testing:

```bash
npm run lighthouse
# or
yarn lighthouse
```

## Deployment

### Pre-deployment Preparation

1. Make sure all tests pass successfully:

```bash
npm run test
# or
yarn test
```

2. Create an optimized build:

```bash
npm run build
# or
yarn build
```

3. Test the build locally:

```bash
npm run start
# or
yarn start
```

### Cloudflare Deployment

The project is configured for deployment on Cloudflare Pages:

1. Set up the project in Cloudflare Pages:
   - Connect GitHub repository
   - Set build command: `npm run build`
   - Set output directory: `.next`
   - Configure environment variables

2. Deploy the project:
   - Automatically on push to main branch
   - Or manually through Cloudflare dashboard

### CI/CD

The project uses GitHub Actions for CI/CD:

- `.github/workflows/ci.yml` - Code checking and tests
- `.github/workflows/deploy.yml` - Deployment to Cloudflare Pages

## Recommendations and Best Practices

### Performance

1. **Optimize images** - Use the `Image` component from Next.js
2. **Lazy loading** - Use `React.lazy` and `Suspense`
3. **Optimize 3D models** - Reduce polygon count and texture size
4. **Use caching** - Cache static data and API responses

### Accessibility

1. **Semantic HTML** - Use proper HTML tags
2. **ARIA attributes** - Add ARIA attributes for complex components
3. **Keyboard navigation** - Ensure keyboard navigation capability
4. **Color contrast** - Ensure sufficient contrast between text and background

### SEO

1. **Metadata** - Add metadata for each page
2. **Structured data** - Use JSON-LD for structured data
3. **Semantic HTML** - Use proper HTML tags
4. **Image optimization** - Add `alt` attributes for images
