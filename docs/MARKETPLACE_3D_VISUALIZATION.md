# 3D-візуалізація моделей у маркетплейсі

## Зміст

1. [Вступ](#вступ)
2. [Технології та бібліотеки](#технології-та-бібліотеки)
3. [Компонент 3D-переглядача](#компонент-3d-переглядача)
4. [Підтримка форматів файлів](#підтримка-форматів-файлів)
5. [Оптимізація 3D-моделей](#оптимізація-3d-моделей)
6. [Інтерактивні функції](#інтерактивні-функції)
7. [Налаштування освітлення та матеріалів](#налаштування-освітлення-та-матеріалів)
8. [Адаптивність та продуктивність](#адаптивність-та-продуктивність)
9. [Скріншоти та попередній перегляд](#скріншоти-та-попередній-перегляд)
10. [Інтеграція з AR/VR](#інтеграція-з-arvr)
11. [Приклади використання](#приклади-використання)

## Вступ

3D-візуалізація є ключовим компонентом маркетплейсу 3D-моделей, що дозволяє користувачам переглядати та взаємодіяти з моделями перед покупкою. Цей документ описує технології, компоненти та методи, які використовуються для реалізації 3D-візуалізації в маркетплейсі.

## Технології та бібліотеки

Для 3D-візуалізації моделей використовуються наступні технології та бібліотеки:

### Three.js

[Three.js](https://threejs.org/) - це JavaScript бібліотека для створення та відображення 3D-графіки у веб-браузері з використанням WebGL.

Основні переваги:
- Широкі можливості для 3D-візуалізації
- Підтримка різних форматів 3D-моделей
- Велика спільнота та документація

### React Three Fiber

[React Three Fiber](https://github.com/pmndrs/react-three-fiber) - це React рендерер для Three.js, що дозволяє використовувати декларативний підхід до створення 3D-сцен.

Основні переваги:
- Інтеграція з React екосистемою
- Декларативний підхід до створення 3D-сцен
- Спрощений API для роботи з Three.js

### Drei

[Drei](https://github.com/pmndrs/drei) - це колекція корисних хелперів та компонентів для React Three Fiber.

Основні компоненти:
- `OrbitControls` - для обертання та масштабування моделі
- `PerspectiveCamera` - для налаштування камери
- `Environment` - для налаштування освітлення
- `useGLTF` - для завантаження GLTF/GLB моделей

### Zustand

[Zustand](https://github.com/pmndrs/zustand) - це бібліотека для управління станом, що використовується для зберігання налаштувань 3D-переглядача.

## Компонент 3D-переглядача

Основний компонент для 3D-візуалізації моделей - `ModelViewer`. Він відповідає за завантаження та відображення 3D-моделей, а також надає користувачам можливість взаємодіяти з моделями.

### Базова структура компонента

```tsx
// src/components/3d-viewer/ModelViewer.tsx
import { Suspense, useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, PerspectiveCamera, Environment, useGLTF, useProgress } from '@react-three/drei';

interface ModelViewerProps {
  modelUrl: string;
  modelFormat: 'gltf' | 'glb' | 'stl' | 'obj' | '3mf';
  backgroundColor?: string;
  autoRotate?: boolean;
}

function Model({ modelUrl, modelFormat }: Pick<ModelViewerProps, 'modelUrl' | 'modelFormat'>) {
  let model;
  
  if (modelFormat === 'gltf' || modelFormat === 'glb') {
    model = useGLTF(modelUrl);
  } else if (modelFormat === 'stl') {
    // Використання STLLoader
  } else if (modelFormat === 'obj') {
    // Використання OBJLoader
  } else if (modelFormat === '3mf') {
    // Використання ThreeMFLoader
  }
  
  return (
    <primitive
      object={model.scene || model}
      position={[0, 0, 0]}
      scale={[1, 1, 1]}
    />
  );
}

function Loader() {
  const { progress } = useProgress();
  return (
    <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-80">
      <div className="text-center">
        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
        <p className="text-blue-500 font-medium">{Math.round(progress)}%</p>
      </div>
    </div>
  );
}

export default function ModelViewer({
  modelUrl,
  modelFormat,
  backgroundColor = '#f3f4f6',
  autoRotate = false,
}: ModelViewerProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  
  return (
    <div className="relative w-full h-[400px] rounded-lg overflow-hidden">
      <Canvas
        camera={{ position: [0, 0, 5], fov: 50 }}
        onCreated={() => setIsLoaded(true)}
        style={{ background: backgroundColor }}
      >
        <Suspense fallback={null}>
          <ambientLight intensity={0.5} />
          <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} intensity={1} castShadow />
          <PerspectiveCamera makeDefault />
          <Model modelUrl={modelUrl} modelFormat={modelFormat} />
          <OrbitControls
            autoRotate={autoRotate}
            autoRotateSpeed={1}
            enableZoom={true}
            enablePan={true}
            minDistance={2}
            maxDistance={10}
          />
          <Environment preset="city" />
        </Suspense>
      </Canvas>
      
      {!isLoaded && <Loader />}
      
      <div className="absolute bottom-4 right-4 flex gap-2">
        <button
          className="bg-white bg-opacity-80 p-2 rounded-full shadow hover:bg-opacity-100 transition-opacity"
          onClick={() => {/* Функція для скидання позиції камери */}}
          aria-label="Reset camera"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      </div>
    </div>
  );
}
```

## Підтримка форматів файлів

Маркетплейс підтримує наступні формати 3D-моделей:

### GLTF/GLB

[glTF](https://www.khronos.org/gltf/) (GL Transmission Format) - це відкритий формат для передачі та завантаження 3D-моделей, розроблений Khronos Group. GLB - це бінарна версія glTF.

```tsx
import { useGLTF } from '@react-three/drei';

function GLTFModel({ url }) {
  const { scene } = useGLTF(url);
  return <primitive object={scene} />;
}
```

### STL

STL (STereoLithography) - це формат файлу, який широко використовується для 3D-друку.

```tsx
import { useState, useEffect } from 'react';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader';
import { useLoader } from '@react-three/fiber';

function STLModel({ url }) {
  const geometry = useLoader(STLLoader, url);
  return (
    <mesh geometry={geometry}>
      <meshStandardMaterial color="#1E88E5" />
    </mesh>
  );
}
```

### OBJ

OBJ - це відкритий формат файлу для 3D-моделей, який зберігає тільки 3D-геометрію.

```tsx
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import { useLoader } from '@react-three/fiber';

function OBJModel({ url }) {
  const obj = useLoader(OBJLoader, url);
  return <primitive object={obj} />;
}
```

### 3MF

3MF (3D Manufacturing Format) - це формат файлу, розроблений для 3D-друку.

```tsx
import { ThreeMFLoader } from 'three/examples/jsm/loaders/3MFLoader';
import { useLoader } from '@react-three/fiber';

function ThreeMFModel({ url }) {
  const model = useLoader(ThreeMFLoader, url);
  return <primitive object={model} />;
}
```

## Оптимізація 3D-моделей

Для забезпечення швидкого завантаження та плавного відображення 3D-моделей використовуються наступні методи оптимізації:

### Рівні деталізації (LOD)

Використання різних рівнів деталізації моделі в залежності від відстані до камери.

```tsx
import { LOD } from 'three';
import { useGLTF } from '@react-three/drei';

function ModelWithLOD({ highQualityUrl, mediumQualityUrl, lowQualityUrl }) {
  const highQuality = useGLTF(highQualityUrl);
  const mediumQuality = useGLTF(mediumQualityUrl);
  const lowQuality = useGLTF(lowQualityUrl);
  
  const lod = new LOD();
  lod.addLevel(highQuality.scene, 0);
  lod.addLevel(mediumQuality.scene, 10);
  lod.addLevel(lowQuality.scene, 20);
  
  return <primitive object={lod} />;
}
```

### Компресія текстур

Використання форматів з компресією текстур (KTX2, DDS) для зменшення розміру файлів.

```tsx
import { useTexture } from '@react-three/drei';

function ModelWithCompressedTextures({ modelUrl, textureUrl }) {
  const texture = useTexture(textureUrl);
  
  return (
    <mesh>
      <boxGeometry />
      <meshStandardMaterial map={texture} />
    </mesh>
  );
}
```

### Прогресивне завантаження

Завантаження моделей з низькою роздільною здатністю спочатку, а потім заміна їх на високоякісні версії.

```tsx
import { Suspense, useState, useEffect } from 'react';
import { useGLTF } from '@react-three/drei';

function ProgressiveModel({ lowQualityUrl, highQualityUrl }) {
  const [quality, setQuality] = useState('low');
  const lowQuality = useGLTF(lowQualityUrl);
  const highQuality = useGLTF(highQualityUrl);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setQuality('high');
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);
  
  return <primitive object={quality === 'low' ? lowQuality.scene : highQuality.scene} />;
}
```

## Інтерактивні функції

Для покращення користувацького досвіду, 3D-переглядач надає різні інтерактивні функції:

### Обертання та масштабування

Використання `OrbitControls` для обертання та масштабування моделі.

```tsx
import { OrbitControls } from '@react-three/drei';

function ModelWithControls() {
  return (
    <>
      <Model />
      <OrbitControls
        enableZoom={true}
        enablePan={true}
        enableRotate={true}
        minDistance={2}
        maxDistance={10}
      />
    </>
  );
}
```

### Вибір частин моделі

Можливість вибору та виділення окремих частин моделі.

```tsx
import { useState } from 'react';
import { useGLTF } from '@react-three/drei';

function SelectableParts({ url }) {
  const { nodes } = useGLTF(url);
  const [selectedPart, setSelectedPart] = useState(null);
  
  const handleClick = (e) => {
    e.stopPropagation();
    setSelectedPart(e.object.name);
  };
  
  return (
    <group onClick={handleClick}>
      {Object.entries(nodes).map(([name, node]) => (
        <mesh
          key={name}
          geometry={node.geometry}
          material={node.material}
          name={name}
          userData={{ name }}
        >
          <meshStandardMaterial
            color={selectedPart === name ? '#FF5722' : '#1E88E5'}
            emissive={selectedPart === name ? '#FF5722' : '#000000'}
            emissiveIntensity={selectedPart === name ? 0.5 : 0}
          />
        </mesh>
      ))}
    </group>
  );
}
```

### Зміна кольорів та матеріалів

Можливість зміни кольорів та матеріалів моделі.

```tsx
import { useState } from 'react';
import { useGLTF } from '@react-three/drei';

function ModelWithCustomization({ url }) {
  const { nodes, materials } = useGLTF(url);
  const [color, setColor] = useState('#1E88E5');
  
  return (
    <>
      <mesh geometry={nodes.main.geometry}>
        <meshStandardMaterial color={color} />
      </mesh>
      
      <div className="absolute bottom-4 left-4 flex gap-2">
        <button
          className="w-8 h-8 rounded-full bg-blue-500"
          onClick={() => setColor('#1E88E5')}
          aria-label="Blue color"
        />
        <button
          className="w-8 h-8 rounded-full bg-red-500"
          onClick={() => setColor('#F44336')}
          aria-label="Red color"
        />
        <button
          className="w-8 h-8 rounded-full bg-green-500"
          onClick={() => setColor('#4CAF50')}
          aria-label="Green color"
        />
      </div>
    </>
  );
}
```

## Налаштування освітлення та матеріалів

Правильне освітлення та матеріали є ключовими для реалістичного відображення 3D-моделей.

### Освітлення

Використання різних типів джерел світла для створення реалістичного освітлення.

```tsx
function SceneWithLighting() {
  return (
    <>
      <ambientLight intensity={0.5} />
      <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} intensity={1} castShadow />
      <directionalLight position={[-10, -10, -5]} intensity={0.5} />
      <pointLight position={[0, 10, 0]} intensity={0.5} />
    </>
  );
}
```

### Матеріали

Використання різних типів матеріалів для створення реалістичних поверхонь.

```tsx
function ModelWithMaterials() {
  return (
    <>
      <mesh position={[-2, 0, 0]}>
        <boxGeometry />
        <meshStandardMaterial
          color="#1E88E5"
          roughness={0.5}
          metalness={0.5}
        />
      </mesh>
      
      <mesh position={[0, 0, 0]}>
        <boxGeometry />
        <meshPhysicalMaterial
          color="#F44336"
          roughness={0.2}
          metalness={0.8}
          clearcoat={1}
          clearcoatRoughness={0.1}
        />
      </mesh>
      
      <mesh position={[2, 0, 0]}>
        <boxGeometry />
        <meshLambertMaterial
          color="#4CAF50"
          emissive="#4CAF50"
          emissiveIntensity={0.2}
        />
      </mesh>
    </>
  );
}
```

### Середовище

Використання HDRI-карт для створення реалістичного освітлення та відображень.

```tsx
import { Environment } from '@react-three/drei';

function ModelWithEnvironment() {
  return (
    <>
      <Model />
      <Environment preset="sunset" background />
    </>
  );
}
```

## Адаптивність та продуктивність

Для забезпечення хорошої продуктивності на різних пристроях використовуються наступні методи:

### Адаптивна якість

Зміна якості рендерингу в залежності від продуктивності пристрою.

```tsx
import { useEffect, useState } from 'react';
import { AdaptiveDpr, AdaptiveEvents } from '@react-three/drei';

function AdaptiveModelViewer() {
  const [quality, setQuality] = useState('high');
  
  useEffect(() => {
    const checkPerformance = () => {
      const fps = performance.now() / 1000;
      if (fps < 30) {
        setQuality('low');
      } else if (fps < 50) {
        setQuality('medium');
      } else {
        setQuality('high');
      }
    };
    
    const interval = setInterval(checkPerformance, 5000);
    return () => clearInterval(interval);
  }, []);
  
  return (
    <Canvas>
      <AdaptiveDpr pixelated />
      <AdaptiveEvents />
      <Model quality={quality} />
    </Canvas>
  );
}
```

### Відкладене завантаження

Завантаження моделей тільки коли вони видимі на екрані.

```tsx
import { Suspense, useEffect, useState } from 'react';
import { useInView } from 'react-intersection-observer';

function LazyModelViewer({ modelUrl }) {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  
  const [shouldLoad, setShouldLoad] = useState(false);
  
  useEffect(() => {
    if (inView) {
      setShouldLoad(true);
    }
  }, [inView]);
  
  return (
    <div ref={ref} className="w-full h-[400px]">
      {shouldLoad && (
        <Canvas>
          <Suspense fallback={null}>
            <Model url={modelUrl} />
          </Suspense>
        </Canvas>
      )}
    </div>
  );
}
```

## Скріншоти та попередній перегляд

Для створення попередніх переглядів моделей використовуються наступні методи:

### Створення скріншотів

Створення скріншотів 3D-моделей для використання в якості мініатюр.

```tsx
import { useRef, useEffect } from 'react';
import { useThree } from '@react-three/fiber';

function ModelScreenshot({ modelUrl, onScreenshot }) {
  const { gl, scene, camera } = useThree();
  
  useEffect(() => {
    const timeout = setTimeout(() => {
      gl.render(scene, camera);
      const screenshot = gl.domElement.toDataURL('image/png');
      onScreenshot(screenshot);
    }, 1000);
    
    return () => clearTimeout(timeout);
  }, [gl, scene, camera, onScreenshot]);
  
  return <Model url={modelUrl} />;
}
```

### Анімовані попередні перегляди

Створення анімованих GIF або відео для попереднього перегляду моделей.

```tsx
import { useRef, useEffect } from 'react';
import { useThree } from '@react-three/fiber';
import { CCapture } from 'ccapture.js';

function ModelAnimation({ modelUrl, onAnimationReady }) {
  const { gl, scene, camera } = useThree();
  const capturer = useRef(new CCapture({ format: 'webm' }));
  
  useEffect(() => {
    capturer.current.start();
    
    let frame = 0;
    const animate = () => {
      if (frame < 60) {
        requestAnimationFrame(animate);
        gl.render(scene, camera);
        capturer.current.capture(gl.domElement);
        frame++;
      } else {
        capturer.current.stop();
        capturer.current.save((blob) => {
          const url = URL.createObjectURL(blob);
          onAnimationReady(url);
        });
      }
    };
    
    animate();
    
    return () => {
      capturer.current.stop();
    };
  }, [gl, scene, camera, onAnimationReady]);
  
  return <Model url={modelUrl} />;
}
```

## Інтеграція з AR/VR

Для розширення можливостей перегляду моделей використовується інтеграція з AR/VR технологіями.

### AR (Доповнена реальність)

Використання WebXR для перегляду моделей в доповненій реальності.

```tsx
import { ARButton, XR } from '@react-three/xr';

function ModelInAR({ modelUrl }) {
  return (
    <>
      <ARButton />
      <Canvas>
        <XR>
          <Model url={modelUrl} />
        </XR>
      </Canvas>
    </>
  );
}
```

### VR (Віртуальна реальність)

Використання WebXR для перегляду моделей у віртуальній реальності.

```tsx
import { VRButton, XR, Controllers, Hands } from '@react-three/xr';

function ModelInVR({ modelUrl }) {
  return (
    <>
      <VRButton />
      <Canvas>
        <XR>
          <Controllers />
          <Hands />
          <Model url={modelUrl} />
        </XR>
      </Canvas>
    </>
  );
}
```

## Приклади використання

### Базовий переглядач моделі

```tsx
import ModelViewer from '@/components/3d-viewer/ModelViewer';

function ModelPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold text-gray-900 mb-4">Назва моделі</h1>
      
      <ModelViewer
        modelUrl="/models/example.glb"
        modelFormat="glb"
        backgroundColor="#f3f4f6"
        autoRotate={true}
      />
      
      <div className="mt-4">
        <p className="text-gray-700">
          Опис моделі...
        </p>
      </div>
    </div>
  );
}
```

### Переглядач з налаштуваннями

```tsx
import { useState } from 'react';
import ModelViewer from '@/components/3d-viewer/ModelViewer';

function ModelPageWithSettings() {
  const [autoRotate, setAutoRotate] = useState(false);
  const [backgroundColor, setBackgroundColor] = useState('#f3f4f6');
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold text-gray-900 mb-4">Назва моделі</h1>
      
      <ModelViewer
        modelUrl="/models/example.glb"
        modelFormat="glb"
        backgroundColor={backgroundColor}
        autoRotate={autoRotate}
      />
      
      <div className="mt-4 flex gap-4">
        <div>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoRotate}
              onChange={(e) => setAutoRotate(e.target.checked)}
              className="mr-2"
            />
            Автоматичне обертання
          </label>
        </div>
        
        <div>
          <label className="mr-2">Фон:</label>
          <select
            value={backgroundColor}
            onChange={(e) => setBackgroundColor(e.target.value)}
            className="border border-gray-300 rounded px-2 py-1"
          >
            <option value="#f3f4f6">Світло-сірий</option>
            <option value="#ffffff">Білий</option>
            <option value="#111827">Чорний</option>
            <option value="#3B82F6">Синій</option>
          </select>
        </div>
      </div>
    </div>
  );
}
```
